#FROM python:3.9.17-buster as builder
FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python:3.9.17-full as builder

ARG PLATFORM=x86

ENV DM_HOME="/home/<USER>/dmp-flow/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"

COPY requirement.txt ./

RUN pip install --upgrade pip setuptools -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --no-cache-dir -r requirement.txt -i https://mirrors.aliyun.com/pypi/simple/

RUN if [ "$PLATFORM" = "arm" ]; then \
        wget -P /dmp-agent https://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/config-agent/v5/agent-arm64 && mv /dmp-agent/agent-arm64 /dmp-agent/agent && \
        wget -P /home/<USER>/dmp-flow/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/arm64/dmdbms.zip && \
        cd /home/<USER>/dmp-flow/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip; \
    else \
        wget -P /dmp-agent -o agent http://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/agent && \
        wget -P /home/<USER>/dmp-flow/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/x86/dmdbms.zip && \
        cd /home/<USER>/dmp-flow/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip; \
    fi

RUN cd /home/<USER>/dmp-flow/dmdbms/dmPython && python setup.py install && \
    cd /home/<USER>/dmp-flow/dmdbms/sqlalchemy2.0.0 && python setup.py install

#FROM python:3.9.17-slim-buster
FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python:3.9.17

ENV DM_HOME="/home/<USER>/dmp-flow/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"


COPY --from=builder /usr/local/lib/python3.9 /usr/local/lib/python3.9
COPY --from=builder /dmp-agent/agent /dmp-agent/agent
COPY --from=builder /home/<USER>/dmp-flow/dmdbms /home/<USER>/dmp-flow/dmdbms

COPY . /home/<USER>/dmp-flow
RUN chmod +x /home/<USER>/dmp-flow/run.sh && \
    chmod +x /dmp-agent/agent && \
    pip install --upgrade --no-cache-dir pip setuptools -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip uninstall py -y && \
    echo "deb https://mirrors.aliyun.com/debian/ buster main non-free contrib" > /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian/ buster main non-free contrib" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security buster/updates main" >> /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian-security buster/updates main" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ buster-updates main non-free contrib" >> /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian/ buster-updates main non-free contrib" >> /etc/apt/sources.list && \
    rm -rf /var/lib/apt/lists && \
    apt-get update && apt-get upgrade -y && apt-get install -y --allow-unauthenticated --no-install-recommends libpq-dev && \
    cp -f /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    rm -rf /var/lib/apt/lists && \
    if [ "$PLATFORM" = "arm" ]; then \
        cd /home/<USER>/dmp-flow && mv lib/HTPClient components/HTPClient;\
    fi

CMD ["/home/<USER>/dmp-flow/run.sh"]

WORKDIR /home/<USER>/dmp-flow
