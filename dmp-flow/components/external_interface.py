#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/3/22.
"""
import base64
import hashlib
import hmac
import logging
import re
import time
from datetime import datetime, timedelta
from urllib.parse import urlparse

import jwt
import requests
from requests import RequestException

from dmplib import config
from components import req
from components.enums import DataCenterAction, DatasetEditMode, MysoftNewERPDataFromType, MysoftNewERPConfigType, \
    MysoftNewERPDataBaseType
from components.data_center_api import request_data_center, query_dataset_clear_status
from components.storage_setting import get_setting, is_history_dataset
from components.cache import DataCenterCache
from components.constants import DATASET_TABLE_NAME_PREFIX
from dmplib.constants import NODE_ERP_API_TIMEOUT
from dmplib.data_source.models import MysoftNewERPConnStrModel
from dmplib.utils.errors import UserError, Skip<PERSON>leanError
from dmplib.utils.strings import _get_random_chars
from node.node_log_message import NodeMsgType
from hashlib import md5


class ResourceUnavailableException(Exception):
    pass


class API:
    """
    外部接口API
    """

    __slots__ = ['url', 'headers', 'data_set_node', 'api_timeout', 'response', 'db_code', 'db_str']

    def __init__(self, url, headers=None, data_set_node=None, db_code=None, db_str=None):
        """
        :param str url:
        :param str headers:
        """
        self.url = url
        self.headers = headers
        self.data_set_node = data_set_node
        # api请求超时
        self.api_timeout = int(NODE_ERP_API_TIMEOUT)
        self.response = None
        self.db_code = db_code
        self.db_str = db_str

    def log_info(self, node_message):
        if self.data_set_node:
            self.data_set_node.info(node_message, msg_type=NodeMsgType.DETAIL.value)
        else:
            logging.info(node_message)

    def log_error(self, node_message):
        if self.data_set_node:
            self.data_set_node.error(node_message, msg_type=NodeMsgType.DETAIL.value)
        else:
            logging.error(node_message)

    def log_warning(self, node_message):
        if self.data_set_node:
            self.data_set_node.warning(node_message, msg_type=NodeMsgType.DETAIL.value)
        else:
            logging.warning(node_message)

    def ping(self):
        """
        接口是否正常
        :return:
        """
        params = {'api': 'api.ping'}
        self.log_info("请求接口：{params}".format(params=str(params)))
        data = self.api_request(params)
        if data and isinstance(data, dict) and data.get('data'):
            return True
        return False

    def get_tables(self, keyword=None, page=1, page_size=30, table_name_prefix=None):
        """
        获取ERP数据表
        :param keyword:
        :param page:
        :param page_size:
        :param table_name_prefix:
        :return:
        """
        if self.db_code:
            params = {'api': 'api.table.list', 'page': page, 'page_size': page_size, 'db_code': self.db_code}
        else:
            params = {'api': 'api.table.list', 'page': page, 'page_size': page_size}
        if keyword:
            params['keyword'] = keyword
        if table_name_prefix:
            params['table_name_prefix'] = table_name_prefix
        self.log_info("请求接口：{params}".format(params=str(params)))
        return self.api_request(params)

    def get_table_columns(self, table_name):
        """
        根据表名获取表结构
        :param table_name:
        :return:
        """
        if self.db_code:
            params = {'api': 'api.column.list', 'db_code': self.db_code}
        else:
            params = {'api': 'api.column.list'}
        if table_name:
            params['table_name'] = table_name
        self.log_info("请求接口：{params}".format(params=str(params)))
        return self.api_request(params)

    def get_download(self, mode, table_name, col_name, timestamp_col_name, last_timestamp, queue_name):
        """
        下载表数据
        :param str mode:获取数据模式
        :param str table_name:
        :param str col_name:列名之间以英文逗号分隔
        :param str timestamp_col_name:
        :param str last_timestamp:
        :param str queue_name:
        :return:
        """
        if self.db_code:
            params = {'api': 'api.download', 'db_code': self.db_code}
        else:
            params = {'api': 'api.download'}
        if mode:
            params['mode'] = mode
        if table_name:
            params['table_name'] = table_name
        if col_name:
            params['col_name'] = col_name
        if timestamp_col_name:
            params['timestamp_col_name'] = timestamp_col_name
        if last_timestamp:
            params['last_timestamp'] = last_timestamp
        params['queue_name'] = queue_name
        self.log_info("请求接口：{params}".format(params=str(params)))
        return self.api_request(params)

    def get_data_list(self, mode, table_name, col_name, timestamp_col_name, last_timestamp, **kwargs):
        """
        获取表数据
        :param str mode:获取数据模式
        :param str table_name:
        :param str col_name:列名之间以英文逗号分隔
        :param str timestamp_col_name:
        :param str last_timestamp:
        :param int page:
        :param int max_count:
        :return:
        """
        page = kwargs.get("page", 1)
        max_count = kwargs.get("max_count")
        if self.db_code:
            params = {'api': 'api.data.list', 'db_code': self.db_code}
        else:
            params = {'api': 'api.data.list'}
        if mode:
            params['mode'] = mode
        if table_name:
            params['table_name'] = table_name
        if col_name:
            params['col_name'] = col_name
        params['max_count'] = max_count or 5000
        if timestamp_col_name:
            params['timestamp_col_name'] = timestamp_col_name
        if last_timestamp:
            params['last_timestamp'] = last_timestamp
        if page:
            params['page'] = page
        self.log_info("请求接口：{params}".format(params=str(params)))
        return self.api_request(params)

    def get_sql_count(self, sql):
        # 子类实现
        pass

    def get_sql_list(self, sql, is_download='0', queue_name=''):
        """
        根据sql语句获取数据
        :param str sql:
        :param int is_download:
        :param str queue_name:
        :return:
        """
        if self.db_code:
            params = {
                'api': 'api.sql.list',
                'sql': sql,
                'is_download': is_download,
                'queue_name': queue_name,
                'db_code': self.db_code,
            }
        else:
            params = {'api': 'api.sql.list', 'sql': sql, 'is_download': is_download, 'queue_name': queue_name}
        self.log_info("请求接口：{params}".format(params=str(params)))
        return self.api_request(params)

    def check_flow_exists(self, code, flow_id):
        """
        根据code、flow_id检查流程是否存在
        :param str code:
        :param int flow_id:
        :return:
        """
        params = {'api': 'api.checkflow', 'code': code, 'flow_id': flow_id}
        self.log_info("请求接口：{params}".format(params=str(params)))
        return self.api_request(params)

    def api_request(self, parameters=None, retries=3):
        """
        请求API
        :param retries: 重试次数, 0表示不重试
        :param parameters:
        :return:
        """
        if self.db_str:
            parameters['db_str'] = self.db_str
        try:
            requester = req.Req(
                'POST',
                self.url,
                self.headers,
                parameters,
                self.api_timeout,
                retry=req.Retry(retries, 5),
                data_set_node=self.data_set_node,
            )
            self.response = requester.request()

            if self.response.status_code == 200:
                return self.response.json()

            if self.response.status_code == 400:
                # 兼容老版本
                if parameters.get('api') == 'api.checkflow':
                    self.log_warning("{api}接口不存在，请升级接口服务。".format(api='api.checkflow'))
                    return {'errmsg': None, 'data': {'result': True, 'msg': ''}}

                msg = "请求接口状态：{status_code}，错误：{e}".format(
                    status_code=self.response.status_code, e=str(self.response.reason)
                )
                self.log_error(msg)
                raise Exception(msg)

            msg = "请求接口错误：" + str(self.response.status_code) + ' , ' + str(self.response.reason)
            self.log_error(msg)
            raise Exception(msg)
        except RequestException as e:
            msg = "发生网络错误:{},请检查datahub服务是否正常".format(str(e))
            self.log_error(msg)
            raise Exception(msg) from e
        except BaseException as be:
            msg = "请求接口错误:{}|url:{}|parameters{}".format(str(be), self.url, str(parameters))
            self.log_error(msg)
            raise Exception(msg) from be


class ErpAPI(API):
    __slots__ = ['host', 'access_id', 'access_secret', 'app_name', 'data_set_node', 'response', 'db_str']

    ACCESS_TOKEN_PATH = 'platform/api/sys/GetAccessToken.ashx'

    def __init__(self, host, access_id, access_secret, app_name, data_set_node=None, db_str: dict = None):
        """
        :param str host:接口地址
        :param str access_id:
        :param str access_secret:
        :param str app_name:接口管家应用名称，如：mybigdata,myfuwu
        """
        _url = urlparse(host)
        self.host = _url.scheme + '://' + _url.netloc
        self.access_id = access_id
        self.access_secret = access_secret
        self.app_name = app_name
        self.data_set_node = data_set_node
        self.response = None
        self.db_str = db_str

        url = self.get_url()
        super().__init__(url, data_set_node=self.data_set_node, db_str=self.db_str)

        try:
            self.ping()
        except Exception as e:
            raise ResourceUnavailableException('ERP API 当前不可用') from e

    def _get_access_token(self):
        """
        获取AccessToken
        # 返回数据格式：
        # data={'access_token': 'DdMyl/NVeOZilVgdLNZAVw==', 'expires_in': 3600}
        :return:
        """
        url = '%s/%s?appid=%s&timestamp=%s' % (self.host, self.ACCESS_TOKEN_PATH, self.access_id, self.get_timestamp())

        try:
            self.response = requests.get(url, timeout=5)
            if self.response.status_code != 200:
                # 警告异常
                msg = "url:{url}，错误状态码：{code}，错误内容：{error}".format(
                    url=url, code=str(self.response.status_code), error=self.response.reason
                )
                self.log_warning(msg)
                return None
            return self.response.json().get('access_token')
        except RequestException as e:
            # 警告异常
            msg = "url:{url}，请求接口AccessToken异常：{e}".format(url=url, e=str(e))
            self.log_warning(msg)
            return None

    def _get_signature(self):
        """
        根据AccessToken与Timestamp进行签名
        :return:
        """
        base_str = '%s#%s' % (self._get_access_token(), self.get_timestamp())
        h = hmac.new(str.encode(self.access_secret), str.encode(base_str), hashlib.sha1)
        core = str.encode('%s.%s' % (h.hexdigest(), base_str))
        return bytes.decode(base64.b64encode(core)).strip().replace('+', '-').replace('/', '_').replace('=', '')

    def get_url(self):
        url = '%s/%s?appid=%s&signature=%s' % (self.host, self.app_name, self.access_id, self._get_signature())
        return url

    def get_sql_count(self, sql):
        """
        根据sql获取总数
        :param sql:
        :return:
        """
        total_sql = 'select count(1) as total from ({sql}) a '.format(sql=sql)
        return self.get_sql_list(total_sql)

    @staticmethod
    def get_timestamp():
        return datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')


class DataHubAPI(API):
    __slots__ = ['host', 'access_secret']

    APP_ID = 'DMP'
    COLLECT_API_PATH = 'api/handle'

    def __init__(self, host, access_secret, data_set_node=None, db_code=None):
        """
        :param str host:接口地址
        :param str access_secret: 秘钥
        """
        _url = urlparse(host)
        self.host = _url.scheme + '://' + _url.netloc
        self.access_secret = access_secret
        self.data_set_node = data_set_node
        url = self.get_url()
        headers = self.get_headers()
        super().__init__(url, headers, data_set_node=self.data_set_node, db_code=db_code)

    def get_url(self):
        self.host = self.host[: len(self.host) - 1] if self.host.endswith('/') else self.host
        url = '%s/%s' % (self.host, self.COLLECT_API_PATH)
        return url

    def get_headers(self):
        access_secret_param = {"appid": self.APP_ID, "exp": self.get_exp()}
        token = jwt.encode(access_secret_param, self.access_secret, algorithm="HS256")
        headers = {"Authorization": "Bearer " + token, "x-request-id": '1', "x-request-time": str(time.time())}
        return headers

    def get_sql_list(self, sql, is_download='0', queue_name=''):
        """
        根据sql语句获取数据
        :param str sql:
        :param int is_download:
        :param str queue_name:
        :return:
        """
        sign = self._sign_sql(sql)
        if self.db_code:
            params = {
                'api': 'api.sql.list',
                'sql': sql,
                'is_download': is_download,
                'queue_name': queue_name,
                'db_code': self.db_code,
                'sign': sign
            }
        else:
            params = {'api': 'api.sql.list', 'sql': sql, 'is_download': is_download, 'queue_name': queue_name,
                      'sign': sign}
        self.log_info("请求接口：{params}".format(params=str(params)))
        return self.api_request(params)

    def get_sql_count(self, sql):
        """
        根据sql获取总数
        :param sql:
        :return:
        """
        sql = 'select count(1) as total from ({sql}) a '.format(sql=sql)
        sign = self._sign_sql(sql)
        if self.db_code:
            params = {
                'api': 'api.sql.count',
                'sql': sql,
                'db_code': self.db_code,
                'sign': sign
            }
        else:
            params = {'api': 'api.sql.count', 'sql': sql, 'sign': sign}
        self.log_info("请求接口：{params}".format(params=str(params)))
        return self.api_request(params)

    def _sign_sql(self, sql):
        return md5((md5(sql.encode()).hexdigest() + self.access_secret).encode()).hexdigest()

    @staticmethod
    def get_exp():
        """
        获取exp时间
        :return:
        """
        # 获取当前时间
        d1 = datetime.now()
        # 当前时间加上180秒
        d2 = d1 + timedelta(seconds=180)
        return d2

    @staticmethod
    def validation_url(url):
        """
        校验url合法性
        :param url:
        :return:
        """
        if re.match(r'^https?:/{2}\w.+$', url):
            return True
        return False


class NewErpAPI(API):
    __slots__ = ['data_source', 'data_set_node']

    def __init__(self, data_source, data_set_node, table_name=None):
        self.data_source = data_source
        self.data_set_node = data_set_node
        super().__init__(url="", data_set_node=self.data_set_node)

    @staticmethod
    def _get_field_mapping(edit_mode, dataset_fields, is_sync=False):
        """
        :param edit_mode:
        :param dataset_fields:
        :param is_sync:
        :return:
        """
        new_dataset_fields = []
        for field in dataset_fields:
            if is_sync:
                new_dataset_fields.append({
                    "OldColumnName": field.get("col_name"),
                    "ColumnName": field.get("col_name")
                })
            else:
                new_dataset_fields.append({
                    "OldColumnName": field.get(
                        "col_name") if edit_mode in (DatasetEditMode.Relation.value, DatasetEditMode.Graph.value) else field.get(
                        "origin_col_name"),
                    "ColumnName": field.get("col_name")
                })
        return new_dataset_fields

    def _get_extend_params(self):
        """
        获取 LastStartTime 、 TableListInSqlText 参数
        """
        with self.data_set_node.context.get_project_db() as db:
            startup_time = db.query_scalar(
                "select startup_time from dap_bi_instance where flow_id=%(flow_id)s and status='已成功' order by created_on desc limit 1",
                params={"flow_id": self.data_set_node.context.flow_id}
            )
            table_names = db.query_columns(
                "select table_name from dap_bi_dataset_used_table where dataset_id=%(dataset_id)s",
                params={'dataset_id': self.data_set_node.data_set.get("id")}
            )
            startup_time = startup_time.strftime("%Y-%m-%d %H:%M:%S") if startup_time else startup_time
            table_names = ",".join(table_names) if table_names else ''
        return startup_time, table_names

    def _build_data_center_api_params(self, table_name: str, edit_mode: str, sql: str, index_sqls: list, dataset_fields: list):
        startup_time, table_names = self._get_extend_params()
        DMPDatasetColumnModels = self._get_field_mapping(edit_mode, dataset_fields)
        logging.info("请求接口管家。DMPDatasetColumnModels：%s" % DMPDatasetColumnModels)
        logging.info("请求接口管家执行sql。sql：%s" % sql)
        return {
            "DataInfo": {
                "DataSetGUID": self.data_set_node.data_set.get("id"),
                "DataSetName": self.data_set_node.data_set.get("name"),
                "SqlText": sql,
                "IndexSqlText": index_sqls,
                "DMPDatasetColumnModels": DMPDatasetColumnModels,
                "ResultTableName": table_name,
                "DataSourceModel": self._get_data_source_info(),
                "DataSourceConnStr": self.data_source.conn_str,
                "IsNeedProcedure": True if self.data_set_node.data_set.get('is_need_procedure') == 1 else False,
                # 是否强制清洗，例如修改过SQL或者用户手动点击清洗
                "IsForceClean": not bool(self.data_set_node.context.schedule_task),
                # 上次成功清洗的开始时间
                "LastStartTime": startup_time,
                # Sql中使用到的表名，用,分割
                "TableListInSqlText": table_names
            }
        }

    def _get_data_source_info(self):
        """
        构建MysoftNewERP类型数据源的链接信息
        """
        if self.data_source.conn_str.DataFrom not in [MysoftNewERPDataFromType.MYSOFT_ERP.value,
                                                      MysoftNewERPDataFromType.MYSOFT_SAAS.value,
                                                      MysoftNewERPDataFromType.NO_MYSOFT.value]:
            raise UserError(message='数据源类型异常，获取结果失败')

        if not isinstance(self.data_source.conn_str, MysoftNewERPConnStrModel):
            raise UserError(message='数据源类型错误，获取结果失败')
        params = dict()
        if self.data_source.conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_ERP.value:
            params = self._mysoft_erp_data_source_params()
        elif self.data_source.conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_SAAS.value:
            params = self._mysoft_saas_data_source_params()
        elif self.data_source.conn_str.DataFrom == MysoftNewERPDataFromType.NO_MYSOFT.value:
            params = self._no_mysoft_data_source_params()
        # 用户信息
        db_type_info = {"ConfigType": self.data_source.conn_str.ConfigType, "DbType": self.data_source.conn_str.DbType,
                        "AppLevelCode": self.data_source.conn_str.AppLevelCode}
        params.update(db_type_info)
        return params

    def _mysoft_erp_data_source_params(self):
        """
        明源erp系统数据源请求参数
        :return:
        """
        params = dict()
        if self.data_source.conn_str.ConfigType == MysoftNewERPConfigType.CONFIG_CENTER.value:
            params = {
                "AppId": self.data_source.conn_str.AppId,
                "SiteGroupKey": self.data_source.conn_str.SiteGroupKey,
                "EnvironmentId": self.data_source.conn_str.EnvironmentId,
                "IsShuXin": self.data_source.conn_str.IsShuXin,
                "ProjectCode": self.data_source.conn_str.ProjectCode
            }
        elif self.data_source.conn_str.ConfigType == MysoftNewERPConfigType.MANUAL_INPUT.value:
            params = {
                "Server": self.data_source.conn_str.Server,
                "Port": self.data_source.conn_str.Port,
                "Database": self.data_source.conn_str.Database,
                "Uid": self.data_source.conn_str.Uid,
                "Pwd": self.data_source.conn_str.Pwd
            }
        return params

    def _mysoft_saas_data_source_params(self):
        """
        明源三云数据源请求参数
        :return:
        """
        params = dict()
        db_type = self.data_source.conn_str.DbType.lower()
        # 三云api连接
        if db_type == MysoftNewERPDataBaseType.Cloud_Mysql.value:
            params = {
                "Server": self.data_source.conn_str.Server,
                "Port": self.data_source.conn_str.Port,
                "Database": self.data_source.conn_str.Database,
                "AppLevelCode": self.data_source.conn_str.AppLevelCode,
                "Pwd": self.data_source.conn_str.Pwd
            }
        elif db_type == MysoftNewERPDataBaseType.Mysql.value:
            # 三云mysql连接
            params = {
                "Server": self.data_source.conn_str.Server,
                "Port": self.data_source.conn_str.Port,
                "Database": self.data_source.conn_str.Database,
                "Uid": self.data_source.conn_str.Uid,
                "Pwd": self.data_source.conn_str.Pwd
            }
        return params

    def _no_mysoft_data_source_params(self):
        """
        明源异构系统请求参数
        :return:
        """
        db_type = self.data_source.conn_str.DbType.lower()
        # mysql、SqlServer默认参数
        params = {
            "Server": self.data_source.conn_str.Server,
            "Port": self.data_source.conn_str.Port,
            "Database": self.data_source.conn_str.Database,
            "Uid": self.data_source.conn_str.Uid,
            "Pwd": self.data_source.conn_str.Pwd
        }
        if db_type == MysoftNewERPDataBaseType.Oracle.value.lower():
            params['DbRole'] = self.data_source.conn_str.DbRole
        return params

    def get_data_by_sql(self, sql: str, is_need_column_struct: bool = False, sql_type=1, table_place_holder=None):
        """
        MysoftNewERP数据源，通过SQL获取结果
        :param table_place_holder 复杂sql的临时表名称
        :param sql_type 1：简单，2：复杂
        :param sql 执行的SQL语句
        :param is_need_column_struct 是否需要返回查询SQL列字段信息
        """
        data_info = {"DataInfo": {
            "SqlText": sql,
            "SqlType": sql_type,
            "TablePlaceholder": table_place_holder,
            "IsNeedColumnStruct": is_need_column_struct,
            "DataSourceModel": self._get_data_source_info(),
            "DataSourceConnStr": self.data_source.conn_str
        }}
        return request_data_center(self.data_set_node.context.project_code, DataCenterAction.GetData.value, data_info,
                                   dataset_content=getattr(self.data_set_node, 'data_set_content', None))

    def _get_dataset_fields(self):
        with self.data_set_node.context.get_project_db() as db:
            fields_sql = 'select `col_name`, `origin_col_name`, `visible`  from dap_bi_dataset_field ' \
                         'where `dataset_id` = %(dataset_id)s and type=\'普通\' ORDER BY `rank`'
            return db.query(fields_sql, {'dataset_id': self.data_set_node.data_set.get('id')})

    def dataset_clean_by_sql(self, table_name: str, sql: str):
        """
        数据服务中心清洗接口，并本地存储
        :param sql:
        :param index_sqls:
        :return:
        """
        # 清理缓存
        exp = get_setting("mdc_direct_cache", "data_center")
        if exp not in ["0", 0, None]:
            cache = DataCenterCache(code=self.data_set_node.context.project_code, cache_type='m')
            cache.delete(sql, self.data_set_node.data_set.get('id'))

        # 生成新的表名
        # self.table_name = DATASET_TABLE_NAME_PREFIX + "_" + _get_random_chars(16) if not self.table_name else self.table_name

        index_sqls = self.data_set_node.get_index_sqls(table_name, True)

        dataset_fields = self._get_dataset_fields()
        # result = request_data_center(self.data_set_node.context.project_code, DataCenterAction.DatasetClean.value,
        #                     params=self._build_data_center_api_params(self.data_set_node.data_set.get("edit_mode"),
        #                                                               sql, index_sqls, dataset_fields),
        #                              dataset_content=self.data_set_node.data_set.get('content'))
        is_data_center = is_history_dataset(self.data_set_node.context.project_code,
                           conn_str=getattr(self.data_source, 'conn_str', None),
                           dataset_content=self.data_set_node.data_set.get('content'))
        if is_data_center:
            self.old_clean_by_sql(table_name, sql, index_sqls, dataset_fields)
        else:
            self.new_clean_by_sql(table_name, sql, index_sqls, dataset_fields)

    def old_clean_by_sql(self, table_name, sql, index_sqls, dataset_fields):
        # 兼容旧逻辑, 忽略请求旧数据服务中心的超时报错, 在外层进行sleep等待
        try:
            result = request_data_center(self.data_set_node.context.project_code, DataCenterAction.DatasetClean.value,
                                         params=self._build_data_center_api_params(
                                             table_name,
                                             self.data_set_node.data_set.get("edit_mode"),
                                             sql, index_sqls, dataset_fields),
                                         dataset_content=self.data_set_node.data_set.get('content'))
            # 如果跳过清洗，复用上次清洗的结果的表名, 直接抛出指定异常
            if result and result.get('IsSkipClean'):
                raise SkipCleanError(message="数据服务中心判断数据没变化，跳过清洗")
        except requests.exceptions.RequestException as e:
            self.log_warning(f"请求数据服务中心超时: {str(e)}, 忽略异常")
        except UserError as e:
            if "请求数据中台服务接口超时" in e.message:
                self.log_warning(f"请求数据服务中心超时: {str(e)}, 忽略异常")
            raise e

    def new_clean_by_sql(self, table_name, sql, index_sqls, dataset_fields):
        # 非数据服务中心, 轮询判断清洗是否完成
        result = request_data_center(self.data_set_node.context.project_code, DataCenterAction.DatasetClean.value,
                                     params=self._build_data_center_api_params(
                                         table_name,
                                         self.data_set_node.data_set.get("edit_mode"),
                                         sql, index_sqls, dataset_fields),
                                     dataset_content=self.data_set_node.data_set.get('content'))
        start = time.time()
        clear_time = self.data_set_node.data_set.get('clear_time') or 60
        while start + int(clear_time) >= time.time():
            res = query_dataset_clear_status(
                self.data_set_node.context.project_code, result.get('DatasetGUID'), result.get('DatasetVersion'),
                dataset_content=self.data_set_node.data_set.get('content')
            )
            # 0:未执行 1:执行中 2:已完成未异常 3:已完成有异常
            status = int(res.get("Status") or 0)
            if status == 2:
                return
            elif status in [0, 1]:
                time.sleep(6)
                # continue
            else:
                raise UserError(message=f"数据服务中心清洗失败: {res.get('ExceptionInfo')}")
        raise UserError(message=f"数据集清洗超时，超时时间{clear_time}")

    def execute_sql(self, sql, table_name, fields=None):
        """
        执行数据服务中心sql
        :param sql:
        :param table_name:
        :param fields:
        :return:
        """
        if fields is None:
            fields = self._get_dataset_fields()
        DMPDatasetColumnModels = self._get_field_mapping(
                        self.data_set_node.data_set.get("edit_mode"), fields,
                        is_sync=True)
        logging.info("请求接口管家。DMPDatasetColumnModels：%s" % DMPDatasetColumnModels)
        logging.info("请求接口管家执行sql。sql：%s" % sql)
        return request_data_center(
            self.data_set_node.context.project_code, DataCenterAction.DatasetClean.value,
            params={
                "DataInfo": {
                    "DataSetGUID": self.data_set_node.data_set.get("id"),
                    "DataSetName": self.data_set_node.data_set.get("name"),
                    "SqlText": sql,
                    "DMPDatasetColumnModels": DMPDatasetColumnModels,
                    "ResultTableName": table_name,
                    "DataSourceModel": self._get_data_source_info(),
                    "DataSourceConnStr": self.data_source.conn_str,
                    "IsNeedProcedure": True if self.data_set_node.data_set.get('is_need_procedure') == 1 else False
                }
            })

    def local_execute_sql(self, sql):
        return request_data_center(
            self.data_set_node.context.project_code, DataCenterAction.DMPDatasetExecuteSql.value,
            params={
                "DataInfo": {
                    "DataSourceModel": self._get_data_source_info(),
                    "ExecuteSql": sql,
                }
            },
            dataset_content=self.data_set_node.data_set.get('content')
        )

    def get_sql_list(self, sql, is_download='0', queue_name=''):
        """
        根据sql语句获取数据
        :param str sql:
        :param int is_download:
        :param str queue_name:
        :return:
        """
        rs = self.get_data_by_sql(sql, self.data_source)
        return {"data": {"data": rs.get("Data")}}

    def get_sql_count(self, sql):
        """
        根据sql获取总数
        :param sql:
        :return:
        """
        rs = self.get_data_by_sql(sql, self.data_source)
        total_count = 0
        if rs['Data'][0]:
            data = rs['Data'][0]
            if 'total_count' in data:
                total_count = data.get('total_count')

        return {"data": {"data": [{"total": total_count}]}}
