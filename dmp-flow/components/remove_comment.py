#!/usr/bin/env python3
# -*- coding: utf-8 -*-


class Token:
    def __init__(self, kind, take):
        self.take = take
        self.kind = kind


class SQLexer:
    def __init__(self, sql):
        self.sql = sql
        self.index = 0

    def lex(self):
        i = -1
        token_begin = 0
        kind = None
        tokens = []
        max_length = len(self.sql)

        for c in self.sql:
            i += 1

            if kind is None:

                kind, token_begin = self._get_next_token_kind(c, i, kind, token_begin)

            elif kind == 'string' and c == "'":
                token_end = i
                tokens.append(Token('string', take=[token_begin, token_end]))
                kind = None
                token_begin = 0

            elif kind == 'string2' and c == "\"":
                token_end = i
                tokens.append(Token('string2', take=[token_begin, token_end]))
                kind = None
                token_begin = 0

            elif kind == 'line_comment':
                kind, token_begin = self._handle_line_comment(c, i, tokens, token_begin, max_length, kind)

            elif kind == 'block_comment' and c == '/' and (i >= 1 and self.sql[i - 1] == '*'):
                token_end = i
                tokens.append(Token('block_comment', take=[token_begin, token_end]))
                kind = None
                token_begin = 0

        return tokens

    def _handle_line_comment(self, c, i, tokens, token_begin, max_length, kind):
        if c == '\n':
            token_end = i
            tokens.append(Token('line_comment', take=[token_begin, token_end]))
            kind = None
            token_begin = 0
        elif i == max_length - 1:
            token_end = i
            tokens.append(Token('line_comment', take=[token_begin, token_end]))
        return kind, token_begin

    def _get_next_token_kind(self, c, i, kind, token_begin):
        if c == '#':
            kind = 'line_comment'
            token_begin = i

        elif c in ["'", "\""]:
            kind = 'string'
            token_begin = i

        elif c == '-' and i >= 1 and self.sql[i - 1] == '-':
            kind = 'line_comment'
            token_begin = i - 1

        elif c == '*' and i >= 1 and self.sql[i - 1] == '/':
            kind = 'block_comment'
            token_begin = i - 1
        return kind, token_begin


def remove_comment(sqltext):
    lx = SQLexer(sqltext)
    result = lx.lex()
    sqltext_list = [i for i in sqltext]
    string_semicolon_num = 0
    for token in result[::-1]:
        if token and token.kind in ['line_comment', 'block_comment']:
            del sqltext_list[token.take[0] : token.take[1] + 1]
        elif token and token.kind in ['string', 'string2']:
            string_semicolon_num += (sqltext[token.take[0] : token.take[1] + 1]).count(';')
    sql = ''.join(sqltext_list)
    return sql, sql.count(";") - string_semicolon_num


if __name__=='__main__':
    sqltext = '''select * from xxx /*xxxxxx*/'''
    sqltext = remove_comment(sqltext)
    print(sqltext)

    sqltext = '''select * from xxx #xxxxxx'''
    sqltext = remove_comment(sqltext)
    print(sqltext)

    # 2019-06-20 16:35:20 --> remove_comment.SQLexer._get_next_token_kind(
    # self:<__main__.SQLexer object at 0x000002334577EF60>, c:'*', i:19, kind:None, token_begin:0) 
    # 2019-06-20 16:35:20 <-- remove_comment.SQLexer._get_next_token_kind(block_comment,18)
    # 2019-06-20 16:38:21 --> remove_comment.SQLexer._handle_line_comment(
    # self:<__main__.SQLexer object at 0x0000018A5666FE48>, c:'x', i:19, tokens:[], token_begin:18, max_length:25, kind:'line_comment')
    # 2019-06-20 16:38:21 <-- remove_comment.SQLexer._handle_line_comment(line_comment,18)
    # <-- remove_comment.remove_comment(select * from xxx ,0)