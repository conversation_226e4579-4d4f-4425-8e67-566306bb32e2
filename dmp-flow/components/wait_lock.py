#!/usr/bin/env python3
"""
并发锁
def do_something():
    with <PERSON><PERSON><PERSON><PERSON>("myid", 10) as locker:
        locker.lock()
        do something ...
"""
from dmplib.redis import RedisCache


class WaitLocker:
    def __init__(self, biz_id, timeout):
        """
        construct
        :param str biz_id:
        :param int timeout:
        """
        if not biz_id or int(timeout) <= 0:
            raise ValueError('Invalid parameters!')

        self.biz_id = biz_id
        self.timeout = timeout
        self.redis_cache = RedisCache()
        self.locked = False

    def _generate_key(self):
        return 'dmp_flow:lock:%s' % self.biz_id

    def lock(self, value=None):
        key = self._generate_key()
        value = value or 1
        self.locked = self.redis_cache.set_nx_ex(key, value, ex=self.timeout, nx=True)
        return self.locked

    def free(self):
        self.redis_cache.delete(self._generate_key())
        self.locked = False

    def __enter__(self):
        return self

    def __exit__(self, exception_type, exception_value, traceback):
        if self.locked:
            self.free()

