#!/usr/bin/env python
# pylint: skip-file
"""
    wrapper for presto

"""
import re
from traceback import FrameSummary

from pyhive import trino
from components.mysql_wrapper import ConnectException, SimpleMysql
from dmplib.hug import g
from dmplib.utils.errors import InvalidArgumentError


SQL_PROFILE_TRACE_DEEP = 7


class SimplePrestodb(SimpleMysql):
    conn = None
    cur = None

    def __init__(self, **kwargs):
        """ construct """
        self.host = kwargs.get("host", "localhost")
        self.port = kwargs.get("port", 443)
        self.db = kwargs.get("database")
        self.user = kwargs.get("user")
        self.passwd = kwargs.get("passwd")
        self.catalog = kwargs.get("catalog")
        self.keep_alive = kwargs.get("keep_alive", False)
        self.charset = kwargs.get("charset", "utf8")
        self.autocommit = kwargs.get("autocommit", False)
        # 事务标记，默认False，使用begin_transaction方法可将其开启为True。不支持事务嵌套。
        self.transaction = kwargs.get("transaction", False)
        self.connect_timeout = kwargs.get('connect_timeout', 10)

        self._check_args()

        # 重试次数
        self.retry_num = 3

    @staticmethod
    def _format_sql(sql: str):
        sql = sql.replace("`", '')
        return sql

    def _check_args(self) -> None:
        """ check args of structure"""
        if self.db is None or self.user is None:
            raise InvalidArgumentError(500, f'db参数配置不完整{self.db}, {self.user}')

    def connect(self):
        """Connect to the mysql server"""

        try:
            self.conn = trino.connect(
                catalog=self.catalog,
                username=self.user,
                password=self.passwd,
                host=self.host,
                port=int(self.port),
                schema=self.db,
            )

            self.cur = self.conn.cursor()
        except Exception as e:
            raise ConnectException(
                "%{error}. host: {host}, port: {port}, user: {user}".format(
                    error=str(e), host=self.host, port=self.port, user=self.user[:1] + '***'
                )
            )

    def _convert_to_json(self, cur_result, one=None):
        if not cur_result:
            return None if one else []
        cur_result = [cur_result] if one else cur_result

        cur = self.cur
        r = [dict((cur.description[i][0], value) for i, value in enumerate(row)) for row in cur_result]
        if one:
            return r[0] if r else None
        return r

    def query_scalar(self, sql, params=None):
        cur = self._execute(sql, params)
        result = cur.fetchone()
        result = self._convert_to_json(result, one=True)

        if result:
            return list(result.values())[0]
        return None

    def query_columns(self, sql, params=None):
        cur = self._execute(sql, params)
        result = cur.fetchall()
        result = self._convert_to_json(result)

        if result:
            return [list(t.values())[0] for t in result]
        return None

    def query_one(self, sql, params=None):
        if hasattr(g, "profiling"):
            import time

            before = time.time()
            cur = self._execute(sql, params)
            result = cur.fetchone()
            g.sqls[-1]['duration'] = time.time() - before
            return self._convert_to_json(result, one=True)
        cur = self._execute(sql, params)
        result = cur.fetchone()
        return self._convert_to_json(result, one=True)

    def query(self, sql, params=None, offset=None, limit=None):
        if (offset is not None or limit is not None) and not re.search(r'offset|limit|fetch', sql.lower()):
            sql += " OFFSET {} LIMIT {}".format(0 if offset is None else offset, 0 if limit is None else limit)

        if hasattr(g, "profiling"):
            import time

            before = time.time()
            cur = self._execute(sql, params=params)
            items = cur.fetchall()
            items = self._convert_to_json(items)
            g.sqls[-1]['duration'] = time.time() - before
        else:
            cur = self._execute(sql, params=params)
            items = cur.fetchall()
            items = self._convert_to_json(items)
        if isinstance(items, tuple):
            return list(items)
        return items

    def exec_sql(self, sql, params=None, commit=True):
        affect_row = self._execute(sql, params).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def _execute(self, sql, params=None):
        """
        check if connection is alive. if not, reconnect
        :param sql:
        :param params:
        :rtype Cursor:
        """
        sql = self._format_sql(sql)
        if not self.conn:
            self.connect()
        if hasattr(g, "profiling"):
            import traceback

            extracted_list = traceback.extract_stack(limit=SQL_PROFILE_TRACE_DEEP)
            g.sqls.append(
                {
                    "sql": sql,
                    "params": params,
                    "db": self.db,
                    "stacks": [
                        "{fname} {lineno} {name}".format(fname=frame.filename, lineno=frame.lineno, name=frame.name)
                        if isinstance(frame, FrameSummary)
                        else "{fname} {lineno} {name}".format(fname=frame[0], lineno=frame[1], name=frame[2])
                        for frame in extracted_list
                        if (isinstance(frame, (list, tuple)) and not frame[0].find('site-packages') > 0)
                        or (isinstance(frame, FrameSummary) and not frame.filename.find('site-packages') > 0)
                    ],
                }
            )
        try:
            self.cur.execute(sql, params)
        except (
            trino.OperationalError,
            trino.ProgrammingError,
            trino.NotSupportedError,
        ) as e:  # pylint: disable=no-member
            raise e
        except trino.DatabaseError:
            self._retry_execute(sql, params)
        return self.cur

    def _retry_execute(self, sql, params):
        try:
            self.connect()
            self.cur.execute(sql, params)
        except trino.DatabaseError:
            if self.retry_num > 0:
                self.retry_num -= 1
                self._retry_execute(sql, params)

    def end(self):
        """Kill the connection"""
        if self.cur:
            self.cur.close()
        if self.conn:
            self.conn.close()
