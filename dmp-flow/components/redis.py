#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on 2017年10月30日

@author: chenc04
"""
import json
import functools
import inspect
from binascii import crc32
import pickle
import random
from decimal import Decimal
from datetime import date, datetime

from dmplib.redis import RedisCache as RCache, RedisConnectionFactory
from dmplib.hug import g
from dmplib import config


class ObjectEncoder(json.JSONEncoder):
    def default(self, o):  # pylint: disable=method-hidden
        if isinstance(o, Decimal):
            return float(o)
        elif isinstance(o, datetime):
            return o.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(o, date):
            return o.strftime('%Y-%m-%d')
        else:
            return json.JSONEncoder.default(self, o)


class RedisCache:
    def __init__(self, key_prefix=None, connect_timeout=3, mode=None):
        self.key_prefix = f'{config.get("Redis.prefix", "dp")}:'
        if key_prefix:
            self.key_prefix = f'{self.key_prefix}{key_prefix}:'
        self.connect_timeout = connect_timeout or 3

        factory = RedisConnectionFactory(connect_timeout)
        conns = factory.create_connection(mode=mode)

        if isinstance(conns, tuple):
            self._master_conn, self._slave_conn = conns[0], conns[-1]
        else:
            self._master_conn = conns
            self._slave_conn = conns

        self._connection = self._master_conn

    @staticmethod
    def _loads(uvalue):
        return json.loads(uvalue.decode("UTF-8"))

    def set_data(self, key, value):
        key = self._wrapper_key(key)
        return self._master_conn.set(key, self._dumps(value))

    def set_nx_data(self, key, value, time):
        key = self._wrapper_key(key)
        return self._master_conn.set(key, value, ex=time, nx=True)

    def set_expire(self, key, time):
        key = self._wrapper_key(key)
        return self._master_conn.expire(key, time)

    def get_data(self, key):
        key = self._wrapper_key(key)
        payload = self._slave_conn.get(key)
        if payload:
            return json.loads(payload.decode("UTF-8"))
        return None

    def del_data(self, key):
        key = self._wrapper_key(key)
        return self._master_conn.delete(key)

    def keys(self):
        return self._master_conn.keys()

    def hset(self, name, key, value):
        """
        设置hash set
        :param name:
        :param key:
        :param value:
        :return: Returns 1 if HSET created a new field, otherwise 0
        """
        if not value:
            raise ValueError('value')
        name = self._wrapper_key(name)
        return self._master_conn.hset(name, key, self._dumps(value))

    def hdel(self, name, key):
        name = self._wrapper_key(name)
        return self._master_conn.hdel(name, key)

    def hkeys(self, name):
        """
        检查指定hash属性keys
        :param name:  hash键名
        :param key: 属性名称
        :return:
        """
        name = self._wrapper_key(name)
        return self._slave_conn.hkeys(name)

    def hget(self, name, key):
        name = self._wrapper_key(name)
        data = self._slave_conn.hget(name, key)
        if not data:
            return None
        return self._loads(data)

    def hgetall(self, key):
        key = self._wrapper_key(key)
        data = self._slave_conn.hgetall(key)
        if not data:
            return None
        return {k.decode('utf-8'): self._loads(v) for k, v in data.items()}

    def _wrapper_key(self, key):
        if not key:
            raise ValueError('key')

        return '%s%s' % (self.key_prefix, key) if self.key_prefix else key

    def lrange(self, name, start, stop):
        name = self._wrapper_key(name)
        data = self._slave_conn.lrange(name, start, stop)
        return [self._loads(item) if item else None for item in data]

    def delete(self, key):
        return self.del_data(key)

    @staticmethod
    def _dumps(value):
        return json.dumps(value, cls=ObjectEncoder)

    def lpush(self, name, *value):
        name = self._wrapper_key(name)
        data = [self._dumps(item) for item in value]
        return self._master_conn.lpush(name, *data)

    def set(self, key, value, time):
        if isinstance(value, (dict, list)):
            value = self._dumps(value)
        key = self._wrapper_key(key)
        return self._master_conn.setex(key, time=time, value=value)

    def get(self, key):
        key = self._wrapper_key(key)
        return self._slave_conn.get(key)

    def scan(self, cursor=0, match=None, count=None):
        """
        扫描获取存在的key
        :param cursor: 游标位置
        :param match: 可用于模糊查询或过滤的匹配模式
        :param count:
        :return:
        """
        if not isinstance(cursor, int):
            raise ValueError('cursor should be int')
        if not isinstance(match, str):
            raise ValueError('cursor should be string')
        return self._slave_conn.scan(cursor, match, count)

    def del_by_scan(self, pattern, count=10000):
        """
        通过scan的方式删除key, 避免keys阻塞的问题
        """
        result = {}
        scan_result = self.scan(match=pattern, count=count)
        if isinstance(scan_result, dict):
            scan_result = scan_result.values()
        else:
            scan_result = [scan_result]
        for cur, match_keys in scan_result:
            while match_keys or cur:
                for p in match_keys:
                    p = p.decode()
                    result[p] = self._connection.delete(p)
                match_keys = None
                if cur:
                    cur, match_keys = self.scan(cursor=cur, match=pattern, count=count)
        return result


redis_cache = RedisCache()


def stale_cache(prefix='', expire=600, random_time=30):
    def decorate(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            prefix_key = prefix or f"{prefix}:"
            cache_key = f"{prefix_key}{inspect.getfile(inspect.currentframe())}:{func.__name__}:{crc32(pickle.dumps(args)+pickle.dumps(kwargs))}"
            cache = RCache(key_prefix=getattr(g, 'code', '') or getattr(g, "project_code", ''))
            value = cache.get(cache_key)
            if value:
                return pickle.loads(value)
            value = func(*args, **kwargs)
            cache.set(cache_key, pickle.dumps(value), expire + random.randint(0, random_time))
            return value
        return wrapper

    return decorate
