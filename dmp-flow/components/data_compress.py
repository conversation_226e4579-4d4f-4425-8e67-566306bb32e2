from io import BytesIO
import gzip
from urllib.parse import quote, unquote
import json
import base64


class DataCompress:
    @staticmethod
    def _gzip_compress(buf):
        """
        二进制文件压缩
        :param data:
        :return:
        """
        out = BytesIO()
        with gzip.GzipFile(fileobj=out, mode="w") as f:
            f.write(buf)
        return out.getvalue()

    @staticmethod
    def compress(file_data, field_names):
        format_data = [field_names]
        format_data.extend([list(item) for item in file_data])
        # urlencode
        urlencode_str = quote(json.dumps(format_data, ensure_ascii=False), encoding="utf-8")
        # utf8 encode binary
        binary_content = urlencode_str.encode("utf-8")
        # gzip压缩
        gzip_binary_data = DataCompress._gzip_compress(binary_content)
        # base64加密
        endoce_data = base64.b64encode(gzip_binary_data).decode()
        return endoce_data

    @staticmethod
    def decompress(origin_data):
        if not origin_data:
            return None
        # base64解密
        b64_data = base64.b64decode(origin_data)
        # gzip解压
        g_data = gzip.decompress(b64_data)
        # url decode
        return unquote(g_data.decode("utf-8"))

