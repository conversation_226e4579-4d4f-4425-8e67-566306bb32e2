#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/6/22.
"""
import requests
import logging
from dmplib import config


class MapService:
    def __init__(self, key=None):
        self.key = key or config.get('AMap.key')
        if not self.key:
            raise Exception('缺少amap服务Key')

    def get_geo_code(self, address, retry=None):
        """
        获取地理位置编码
        :param retry:
        :param address:
        :return:
        """
        api_geocode = 'http://restapi.amap.com/v3/geocode/geo'
        try:
            response = requests.get(api_geocode, {'key': self.key,
                                                  'address': address,
                                                  'output': 'JSON'})
            if response.status_code != 200:
                raise Exception('请求错误')
            res = response.json()
            if not res:
                raise Exception('返回结果错误')
            geocodes = res.get('geocodes')
            if not geocodes:
                return None
            location = {'location': geocodes[0].get('location'), 'level': geocodes[0].get('level')}
            if not location:
                return None
            return location
        except Exception as ex:
            if not retry:
                retry = 1
            if retry > 15:
                raise ex
            retry += 1
            return self.get_geo_code(address, retry)

    def get_geo_address(self, location, retry=None):
        if isinstance(location, str):
            location = location.split(',')
        api = 'http://restapi.amap.com/v3/geocode/regeo'
        try:
            response = requests.get(api, {'key': self.key,
                                          'location': ','.join([str(x) for x in location]),
                                          'output': 'JSON'})
            if response.status_code != 200:
                raise Exception('请求错误')
            res = response.json()
            if not res:
                raise Exception('返回结果错误')
            return res
        except Exception as ex:
            if not retry:
                retry = 1
            if retry > 15:
                raise ex
            retry += 1
            return self.get_geo_address(location, retry)

    def get_address_format(self, location):
        data = self.get_geo_address(location.get('location'))
        ac = data.get('regeocode').get('addressComponent')
        return {
            'country': ac.get('country', ''),
            'province': ac.get('province', ''),
            'city': ac.get('city', '') if ac.get('city', '') else ac.get('province', ''),
            'district': ac.get('district', ''),
            'street': ac.get('township', ''),
            'level': location.get('level'),
        }
