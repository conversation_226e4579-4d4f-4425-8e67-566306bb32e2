"""
requests 超级辅助工具
"""
import requests
import time
import logging

from node.node_log_message import NodeMsgType

logger = logging.getLogger(__name__)

retry_error_keywords = ['请重新运行该事务', 'context deadline exceeded', '正在中止线程']


class InvalidParameterException(Exception):
    """
    无效的参数
    """

    pass


class BadJsonException(Exception):
    """
    非法json
    """

    pass


class ServerException(Exception):
    """
    server端异常
    """

    pass


class NotAuthorizationException(Exception):
    """
    未授权
    """

    pass


class RetryFailedException(Exception):
    """
    retry 失败
    """


class Retry:
    def __init__(self, times, sleep=1):
        self.times = times
        self.sleep = sleep


class Req:
    """
    包装requests,支持retry
    retry的时机: 1. datahub返回超时(context deadline exceeded) 2. datahub返回的errmsg包含'请重新运行该事务'关键字
    """

    def __init__(self, method, url, headers, params, timeout=120, **kwargs):
        """

        :param method:
        :param url:
        :param headers:
        :param params:
        :param timeout:
        :param Retry retry:
        """
        retry = kwargs.get("retry")
        data_set_node = kwargs.get("data_set_node")

        self._url = url
        self._method = method.upper() if method else 'POST'
        self._params = params
        self._response = None
        self._headers = headers
        self._timeout = timeout
        self._retry = retry
        self._retried = 0
        self._dataset_node = data_set_node
        # 符合条件的重试
        self._retry_error_keywords = retry_error_keywords

        if not url:
            raise InvalidParameterException('url is empty!')

    def _log_warning(self, message):
        if self._dataset_node:
            self._dataset_node.warning(message, msg_type=NodeMsgType.DETAIL.value)
        else:
            logger.warning(message)

    def _log_error(self, message):
        if self._dataset_node:
            self._dataset_node.error(message, msg_type=NodeMsgType.DETAIL.value)
        else:
            logger.error(message)

    def _retry_request(self, reason=None):
        if reason is None:
            reason = ''

        if self._retried >= self._retry.times:
            raise RetryFailedException('放弃重试, 已重试{count}次. error:{error}'.format(count=self._retried, error=reason))

        self._retried += 1

        self._log_warning('[{}]. sleep {} 秒后, 进行第 {} 重试.'.format(reason, self._retry.sleep * self._retried, self._retried))
        if self._retry.sleep > 0:
            time.sleep(self._retry.sleep * self._retried)
        self._response = self.request()

    def request(self):
        if self._method != 'POST':
            raise InvalidParameterException('not implemented {} method!'.format(self._method))

        try:
            if self._headers:
                self._response = requests.post(
                    self._url, json=self._params, headers=self._headers, timeout=self._timeout
                )
            else:
                self._response = requests.post(self._url, self._params, timeout=self._timeout)

            if self._response.status_code == 401:
                raise NotAuthorizationException("未授权或签名验证失败")

            if self._response.status_code == 200:
                try:
                    json_data = self._response.json()
                    errmsg = json_data.get('errmsg')
                    if not errmsg:
                        return self._response

                    self._retry_on_except(errmsg)

                except ValueError as e:
                    raise BadJsonException(
                        "返回无效的json, error: {err}, 返回的内容: {text}".format(err=str(e), text=self._response.text)
                    )

        except requests.RequestException as e:
            self._retry_request(str(e))

        return self._response

    def _retry_on_except(self, errmsg):
        exception_msg = "api:{api},返回errmsg: {msg}".format(api=self._url, msg=errmsg)
        self._log_warning(exception_msg)

        # 符合条件的重试
        for kw in self._retry_error_keywords:
            if errmsg.find(kw) > -1:
                self._retry_request(exception_msg)
                break

        raise Exception(exception_msg)
