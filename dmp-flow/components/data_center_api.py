#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2021/3/22.
"""
import json
import logging as logger

from dmplib.base.repository import get_master_db, get_data_scalar, get_db
from dmplib.utils.errors import UserError
from dmplib.base.models import BaseModel
from components.redis import stale_cache
from components.mysoft import CloudAPI
from components.enums import DataCenterAction, MysoftNewERPDataFromType, MysoftNewERPConfigType
from components.constants import ADMINISTRATORS_ID, ADMIN_ACCOUNT
from components.storage_setting import is_history_dataset
from components.utils import get_default_account
from dmplib.data_source.models import MysoftNewERPConnStrModel, DataSourceModel


def request_data_center(
        project_code: str, action_name: str, params: dict = None, erp_api_info_id=None, dataset_content=None
):
    """
    请求数据服务中心公共方法
    :param project_code
    :param action_name 接口名称
    :param params 接口参数
    :param erp_api_info_id 接口参数
    :param dataset_content 接口参数
    """
    if params is None:
        params = dict()
    params['action_name'] = action_name
    # 获取接口管家地址
    try:
        if not erp_api_info_id:
            conn_str = params.get('DataInfo', {}).get('DataSourceConnStr') or {}
            if isinstance(conn_str, str):
                conn_str = json.loads(conn_str)
            elif isinstance(conn_str, BaseModel):
                conn_str = conn_str.get_dict()
            erp_api_info_id = conn_str.get('erp_api_info_id') or ''
    except Exception as e:
        logger.info(f"未获取到 数据源绑定的接口管家: {e}")
        erp_api_info_id = ''
    finally:
        try:
            del params['DataInfo']['DataSourceConnStr']
        except:
            pass
    params['IsDataCenter'] = is_history_dataset(project_code, erp_api_info_id=erp_api_info_id,
                                                dataset_content=dataset_content)
    erp_site = _get_erp(project_code, erp_api_info_id=erp_api_info_id, dataset_content=dataset_content)
    if erp_site['erpapi_host'] is None:
        raise UserError(message='未配置接口管家地址!')
    erp_site['erpapi_access_id'] = '' if erp_site['erpapi_access_id'] is None else erp_site['erpapi_access_id']
    erp_site['erpapi_access_secret'] = (
        '' if erp_site['erpapi_access_secret'] is None else erp_site['erpapi_access_secret']
    )
    api = CloudAPI(
        erp_site['erpapi_host'],
        erp_site['erpapi_access_id'],
        erp_site['erpapi_access_secret'],
        CloudAPI.DATA_CENTER_PATH,
        project_code
    )

    account = get_default_account(project_code)

    # 用户信息
    user_info = {
        "UserInfo": {"UserCode": account,
                     "ThirdUserCode": {
                         "8011": account,
                         "80111": account,
                         "80112": account,
                         "80113": account,
                         "80114": account,
                         "8006": account,
                     },
                     "ThirdUserId": {
                         "8011": ADMINISTRATORS_ID,
                         "80111": ADMINISTRATORS_ID,
                         "80112": ADMINISTRATORS_ID,
                         "80113": ADMINISTRATORS_ID,
                         "80114": ADMINISTRATORS_ID,
                         "8006": ADMINISTRATORS_ID,
                     }}
    }
    params.update(user_info)
    # 请求接口
    return api.datacenter_request(params, 'post')


def query_dataset_clear_status(project_code, task_id, dataset_version, dataset_content=None):
    """
    获取数据集清洗状态的接口
    """
    from dmplib.data_source.services import data_source_service
    from components.data_center_api import get_data_source_info
    params = {
        "DataInfo": {
            "DataSourceModel": get_data_source_info(data_source_service.get_epr_datasource(dataset_content)),
            "DatasetGUID": task_id,
            "DatasetVersion": dataset_version
        }
    }
    return request_data_center(project_code, DataCenterAction.QueryDatasetTaskClean.value, params,
                               dataset_content=dataset_content)


def get_data_source_info(data_source: DataSourceModel):
    """
    构建MysoftNewERP类型数据源的链接信息
    """
    if data_source.conn_str.DataFrom not in [MysoftNewERPDataFromType.MYSOFT_ERP.value,
                                             MysoftNewERPDataFromType.MYSOFT_SAAS.value,
                                             MysoftNewERPDataFromType.NO_MYSOFT.value]:
        raise UserError(message='数据源类型异常，获取结果失败')

    if not isinstance(data_source.conn_str, MysoftNewERPConnStrModel):
        raise UserError(message='数据源类型错误，获取结果失败')
    params = dict()
    if data_source.conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_ERP.value:
        params = mysoft_erp_data_source_params(data_source)
    elif data_source.conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_SAAS.value:
        params = mysoft_erp_data_source_params(data_source)
    elif data_source.conn_str.DataFrom == MysoftNewERPDataFromType.NO_MYSOFT.value:
        params = mysoft_erp_data_source_params(data_source)
    # 用户信息
    db_type_info = {"ConfigType": data_source.conn_str.ConfigType, "DbType": data_source.conn_str.DbType,
                    "AppLevelCode": data_source.conn_str.AppLevelCode}
    params.update(db_type_info)
    return params


def mysoft_erp_data_source_params(data_source: DataSourceModel):
    """
    明源erp系统数据源请求参数
    :return:
    """
    params = dict()
    if data_source.conn_str.ConfigType == MysoftNewERPConfigType.CONFIG_CENTER.value:
        params = {
            "AppId": data_source.conn_str.AppId,
            "SiteGroupKey": data_source.conn_str.SiteGroupKey,
            "EnvironmentId": data_source.conn_str.EnvironmentId,
            "IsShuXin": data_source.conn_str.IsShuXin,
            "ProjectCode": data_source.conn_str.ProjectCode
        }
    elif data_source.conn_str.ConfigType == MysoftNewERPConfigType.MANUAL_INPUT.value:
        params = {
            "Server": data_source.conn_str.Server,
            "Port": data_source.conn_str.Port,
            "Database": data_source.conn_str.Database,
            "Uid": data_source.conn_str.Uid,
            "Pwd": data_source.conn_str.Pwd
        }
    return params


@stale_cache(prefix="dmp-flow-erp", expire=30)
def _get_erp(project_code: str, erp_api_info_id=None, dataset_content=None):
    if not erp_api_info_id and dataset_content:
        if isinstance(dataset_content, str):
            dataset_content = json.loads(dataset_content)
        bind_source_id = dataset_content.get("bind_source_id")
        if bind_source_id:
            # builtins.code = project_code
            conn_str = get_data_scalar('dap_m_data_source', {'id': bind_source_id}, 'conn_str')
            if conn_str:
                erp_api_info_id = json.loads(conn_str).get('erp_api_info_id')
    if erp_api_info_id:
        with get_db(project_code) as db:
            erp_api_info = db.query_one("select * from dap_bi_erp_api_info where id=%(id)s", {'id': erp_api_info_id}) or {}
            host = erp_api_info.get("erp_api_host")
            if host:
                return {
                    'erpapi_host': host,
                    'erpapi_access_id': erp_api_info.get('erp_api_access_id'),
                    'erpapi_access_secret': erp_api_info.get('erp_api_access_secret')
                }
    with get_master_db() as db:
        return db.query_one(
            'SELECT erpapi_host, erpapi_access_id, erpapi_access_secret FROM dap_bi_tenant_setting WHERE code=%(code)s ', {'code': project_code}
        )
