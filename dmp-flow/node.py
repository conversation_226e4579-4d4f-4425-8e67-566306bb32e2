# -*- coding: UTF-8 -*-
"""
Created on 2018年6月27日

@author: chenc04
"""

import logging
import sys
from dmplib.utils.loggers import init_logging

init_logging()
logger = logging.getLogger(__name__)
from flow.flow_context import FlowContext
from flow.flow_repository import FlowRepository
from flow.flow_launcher import NodeExecutor
from node.node import NodeInstance

RET_STATE = {
    "KILL": 143,
    "FAIL": -1,
    "OK": 0,
    "RUN": 1,
    "RETRY": 2
}

if __name__ == '__main__':
    args = sys.argv
    if len(args) <= 3:
        print("请输入项目code和流程id和流程实例id")
        logger.error("请输入项目code和流程id")
        sys.exit(RET_STATE['FAIL'])

    project_code = args[1]
    node_id = args[2]
    activity_id = args[3]
    # 初始化数据
    context = FlowContext(project_code, "")
    flow_repository = FlowRepository(context)
    node_data = flow_repository.get_node(node_id)
    node = flow_repository.dict_to_node(node_data)
    context.flow_id = node.flow_id
    activity_data = flow_repository.get_node_instance(activity_id)
    node_nstance = NodeInstance(**activity_data)
    context.flow_instance_id = node_nstance.instance_id
    # 执行节点
    node_executor = NodeExecutor(context, flow_repository, node, node_nstance)
    node_result = node_executor.execution()

    if node_result and not node_result.result:
        raise Exception(node_result.message)
