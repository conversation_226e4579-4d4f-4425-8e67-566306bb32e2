#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/25.
"""
import json
import re

from odps.errors import ODPSError

from node.node_execution import NodeExecution
from node.node_result import NodeResult


class ODPSSQLNode(NodeExecution):
    def __init__(self, context, cur_node):
        """
        ODPS SQL 节点
        :param flow.flow_context.FlowContext context:
        :param node.node.Node cur_node:
        """
        super().__init__(context, cur_node)
        self.sql_list = []

    def _load_sql(self):
        if not self.cur_node.content:
            raise ODPSSQLNodeException('SQL节点内容为空')
        try:
            content = json.loads(self.cur_node.content)
        except json.JSONDecodeError as e:
            raise ODPSSQLNodeException('SQL节点内容解析错误：' + str(e))
        sql = content.get('sql')
        if not sql:
            raise ODPSSQLNodeException('SQL为空')
        # 去除SQL语句中的注释内容
        pattern = r'--.*'
        sql = re.subn(pattern, '', sql)[0].strip()
        self.sql_list = self._split_sql(sql)

    @staticmethod
    def _split_sql(sql):
        result = []
        tmp_sql = ''
        default = False
        for c in sql:
            if not default and c == ';' and tmp_sql:
                if tmp_sql:
                    result.append(tmp_sql.strip())
                tmp_sql = ''
                continue
            if c == '\'':
                if not default:
                    default = True
                else:
                    default = False
            tmp_sql += c
        if tmp_sql:
            result.append(tmp_sql.strip())
        return result

    def execution(self):
        self._load_sql()
        if not self.sql_list:
            return NodeResult(False, '没有可执行SQL')
        for sql in self.sql_list:
            self.info('执行SQL：' + sql)
            try:
                self.context.get_odps().execute_sql(sql)
            except ODPSError as e:
                self.exception(e)
                return NodeResult(False, 'SQL执行出错：' + str(e))
        return NodeResult(True, '执行成功')


class ODPSSQLNodeException(Exception):
    def __init__(self, *args, **kwargs):
        pass
