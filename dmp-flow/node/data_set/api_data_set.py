# -*- coding: UTF-8 -*-
"""
Created on 2021年03月16日

@author: songh02
"""
import re
import json
import logging

from components.message_queue import MessageQueue
from components.constants import API_DATASET_QUEUE_NAME_PREFIX
from components.external_interface import ResourceUnavailableException
from components.dmp_openapi import get_data_of_api_dataset
from components.wait_lock import WaitLocker
from dmplib.sql_adapter import adapter_sql
from dmplib import config
from dmplib.utils.errors import UserError
from dmplib.utils.fast_api import get_queue_name

logger = logging.getLogger(__name__)


class APIDataSet:
    primary_key = "MdcDataGUID"
    __slots__ = ["data_set_node", "data_source", "tmp_table_name", "columns", "queue_name", "deadline"]

    def __init__(self, data_set_node, data_source):
        """
        API数据集
        :param str tmp_table_name:
        """
        self.columns = None
        self.data_set_node = data_set_node
        self.data_source = data_source
        table_name = self.data_set_node.data_set.get("table_name")
        self.tmp_table_name = (
            self.data_set_node.data_set_content.get('tmp_table_name')
            if self.data_set_node.data_set_content.get('tmp_table_name')
            else 'tmp_' + table_name
        )
        self.queue_name = ""
        # 接口消息回执最大超时时间（秒）
        self.deadline = 600

    # def drop_tmp_table_name(self):
    #     drop_table_sql = 'drop table if exists {table_name} '.format(table_name=self.tmp_table_name)
    #     with self.data_set_node.context.get_project_data_db() as db:
    #         db.exec(drop_table_sql)
    #
    # def create_data_structure(self):
    #     """
    #     创建数据集临时表
    #     :return:
    #     """
    #     # 创建表之前先删除历史tmp表
    #     self.drop_tmp_table_name()
    #     create_table_sql = self.data_set_node.data_set_content.get("create_table_sql")
    #     create_table_sql = self.get_sql_add_primary_key(create_table_sql)
    #
    #     with self.data_set_node.context.get_project_data_db() as db:
    #         query_col_sql = adapter_sql('table_columns_no_primary', db.db_type)
    #         if config.get('DB.db_type') == 'DM':
    #             create_table_sql = create_table_sql.replace('`', '"')
    #         db.exec(create_table_sql)
    #         self.columns = db.query_columns(query_col_sql,
    #                                         {"table_name": self.tmp_table_name, 'primary_key': self.primary_key})
    #
    # def get_sql_add_primary_key(self, sql):
    #     rep = ('create table if not exists {table_name} ('
    #            '`{primarykeyname}` bigint primary key auto_increment,'
    #            ).format(table_name=self.tmp_table_name, primarykeyname=self.primary_key)
    #     return re.sub(r"create table if not exists %s\s+\(" % self.tmp_table_name, rep, sql, flags=re.I)

    def generate(self):
        # 创建临时表
        # self.create_data_structure()

        # 通过openapi即时调用dmp的api数据集取数及同步
        node = self.data_set_node.cur_node.get_dict()
        node.pop('node_instance',None)
        params = {
            'code': self.data_set_node.context.project_code,
            'dmp_flow_queue_name': get_queue_name(),
            'dataset_id': self.data_set_node.data_set.get("id"),
            'data_source_id': self.data_source.id,
            'tmp_table_name': self.tmp_table_name,
            'columns': self.columns,
            "flow_instance_id": self.data_set_node.context.flow_instance_id,
            "node_instance_id":self.data_set_node.cur_node.node_instance.id,
            "node_id": self.data_set_node.cur_node.id,
            "flow_id": self.data_set_node.context.flow_id
        }
        get_data_of_api_dataset(self.data_set_node.context.project_code, params)

        # # mq监控清洗状态
        # self.queue_name = "{}_{}".format(API_DATASET_QUEUE_NAME_PREFIX, self.data_set_node.context.flow_instance_id)
        # self.queue_name = self.queue_name.replace('-', '_')
        # logger.info("queue_name: %r", self.queue_name)
        # mq = MessageQueue()
        # # mq.receive_message(self.queue_name, self.flow_consumer_callback, durable=False, auto_delete=True)
        # mq.receive_message_timeout(
        #     self.queue_name,
        #     self.flow_consumer_callback,
        #     self.deadline,
        #     self.consumer_timeout_callback,
        #     durable=False,
        #     auto_delete=True
        # )
        return self.tmp_table_name

    def consumer_timeout_callback(self):
        mq = MessageQueue()
        mq.queue_delete(self.queue_name)
        msg = "api数据集回调超时，请检查接口服务是否正常。"
        logger.error(msg)
        # 删除临时表
        self.drop_tmp_table_name()
        raise Exception(msg)

    @staticmethod
    def flow_consumer_callback(*args):
        """
        Flow 消费者回调
        :param args:
                consumer_callback(channel, method, properties, body)
                        channel: BlockingChannel
                        method: spec.Basic.Deliver
                        properties: spec.BasicProperties
                        body: str or unicode
        :return:
        """
        channel = args[0]
        method = args[1]
        body = args[3]
        try:
            result = json.loads(body.decode('utf-8'))
            # 更新
            if result.get("flow_status") != 1:
                if result.get('error'):
                    raise BaseException(f"流程运行失败:{result.get('error')}")
                raise BaseException("流程运行失败")
        except json.JSONDecodeError as e:
            logger.error('解释json失败,无效的json数据. error:%r', str(e))
            raise ResourceUnavailableException(e) from e
        except BaseException as e:
            logger.exception(e)
            raise e from e
        finally:
            if channel:
                channel.basic_ack(delivery_tag=method.delivery_tag)
                channel.stop_consuming()
