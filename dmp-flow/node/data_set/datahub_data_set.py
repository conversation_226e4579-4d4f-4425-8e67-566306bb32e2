# -*- coding: UTF-8 -*-
"""
Created on 2016年10月21日

@author: chenc04
"""
from dmplib.utils.strings import is_number

oracle_to_mysql = {
    # 数值
    'number': 'decimal',
    'integer': 'int',
    'decimal': 'decimal',
    'float': 'float',
    'binary_float': 'float',
    'binary_double': 'double',
    'bigint': 'bigint',
    'double': 'double',
    'numeric': 'decimal',
    'real': 'real',
    'smallint': 'smallint',
    'tinyint': 'tinyint',
    'int': 'int',
    # 日期
    'date': 'datetime',
    'timestamp': 'timestamp',
    # 字符串
    'char': 'varchar',
    'varchar': 'varchar',
    'varchar2': 'varchar',
    'nchar': 'varchar',
    'nvarchar': 'varchar',
    'nvarchar2': 'varchar',
    'clob': 'text',
    'nclob': 'text',
    'tinytext': 'text',
    'blob': 'varchar',
    'mediumblob': 'varchar',
    'mediumtext': 'varchar',
    'longblob': 'text',
    'longtext': 'text',
    'long': 'text',
}

sql_server_to_mysql = {
    # 数值
    'bigint': 'bigint',
    'decimal': 'decimal',
    'float': 'float',
    'int': 'int',
    'money': 'float',
    'smallmoney': 'float',
    'numeric': 'decimal',
    'real': 'float',
    'smallint': 'smallint',
    'tinyint': 'tinyint',
    'varbinary': 'varbinary',
    'binary_float': 'float',
    # 日期
    'date': 'varchar(100)',
    'datetime': 'varchar(100)',
    'datetime2': 'varchar(100)',
    'datetimeoffset': 'varchar(100)',
    'smalldatetime': 'varchar(100)',
    'time': 'varchar(100)',
    'timestamp': 'varchar(100)',
    # 字符串
    'bit': 'char(100)',
    'binary': 'text',
    'char': 'char',
    'nchar': 'char',
    'ntext': 'text',
    'nvarchar': 'varchar',
    'text': 'text',
    'uniqueidentifier': 'varchar(40)',
    'varchar': 'varchar',
    'xml': 'text',
}


def replace_parenthesis(col_name):
    return col_name.replace('(', '_').replace(')', '_') if col_name else ""


class DataHubOracle:
    def __init__(self, data_set_node, tmp_table_name, write_data_db):
        """
        datahub-oracle
        :param str tmp_table_name:
        """
        self.data_set_node = data_set_node
        self.tmp_table_name = tmp_table_name
        self.write_data_db = write_data_db

    def create_table(self, columns):
        """
        创建数据集临时表
        :return:
        """
        drop_table_sql = 'drop table if exists {table_name} '.format(table_name=self.tmp_table_name)
        create_table_sql = (
            'create table if not exists {table_name} ('
            '{cols}'
            ')'.format(table_name=self.tmp_table_name, cols=','.join([col for col in self._get_columns(columns)]))
        )
        if self.write_data_db:
            with self.data_set_node.context.get_project_data_db() as db:
                db.exec(drop_table_sql)
                db.exec(create_table_sql)
        else:
            with self.data_set_node.context.get_project_db() as db:
                db.exec(drop_table_sql)
                db.exec(create_table_sql)

    def drop_table(self):
        sql = 'drop table if exists {table_name} '.format(table_name=self.tmp_table_name)

        try:
            if self.write_data_db:
                with self.data_set_node.context.get_project_data_db() as db:
                    db.exec(sql)
        except BaseException as e:
            self.data_set_node.error("创建临时表错误：{sql}".format(sql=sql))
            raise e

    def _get_columns(self, columns):
        """
        SQL列
        :return:
        """
        mysql_columns = []
        for column in columns:
            if column.get("data_type"):
                mysql_columns.append(
                    '`%s` %s '
                    % (replace_parenthesis(column.get("col_name")), self._get_data_type(column.get("data_type")))
                )
            else:
                mysql_columns.append('`%s` %s ' % (replace_parenthesis(column.get("col_name")), "varchar(500)"))
        return mysql_columns

    @staticmethod
    def _get_data_type(data_type):
        if not data_type:
            return "text"
        data_type = data_type.lower()
        params = data_type.split("(")
        data = oracle_to_mysql.get(params[0])

        if not data:
            return "text"

        if data == "text" or len(params) == 1:
            result = data
        elif data == "decimal":
            if '-' in params[1] or ',' not in params[1] or (',' in params[1] and int(params[1].split(",")[0]) > 65):
                result = data
            else:
                result = "{d}({p}".format(d=data, p=params[1])
        else:
            if data == 'varchar' and is_number(params[1].replace(")", "")) and int(params[1].replace(")", "")) >= 4000:
                result = "text"
            else:
                result = "{d}({p}".format(d=data, p=params[1])

        return result

    def limit_sql(self, sql, limit=None):
        new_sql = " select * from ( {sql} ) a " "where rownum < {max_data_limit} ".format(
            sql=sql, max_data_limit=limit if limit else self.data_set_node.data_limit
        )
        return new_sql


class DataHubMysql(DataHubOracle):
    def limit_sql(self, sql, limit=None):
        new_sql = " select * from ( {sql} ) a limit {max_data_limit} ".format(
            sql=sql, max_data_limit=limit if limit else self.data_set_node.data_limit
        )
        return new_sql


class DataHubSQLServer(DataHubOracle):
    @staticmethod
    def _get_data_type(data_type):
        if not data_type:
            return "varchar(500)"
        data_type = data_type.lower()

        params = data_type.split("(")
        data = sql_server_to_mysql.get(params[0])

        if not data:
            return "varchar(500)"

        if data == "text" or len(params) == 1:
            result = data
        elif data == "decimal":
            if '-' in params[1] or ',' not in params[1] or (',' in params[1] and int(params[1].split(",")[0]) > 65):
                result = data
            else:
                result = "{d}({p}".format(d=data, p=params[1])
        else:
            if data == 'varchar' and is_number(params[1].replace(")", "")) and int(params[1].replace(")", "")) >= 4000:
                result = "text"
            else:
                result = "{d}({p}".format(d=data, p=params[1])

        return result

    def limit_sql(self, sql, limit=None):
        new_sql = " select top {max_data_limit} * " "from ( {sql} ) a ".format(
            sql=sql, max_data_limit=limit if limit else self.data_set_node.data_limit
        )
        return new_sql
