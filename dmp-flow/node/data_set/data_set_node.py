#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    数据集处理节点
    <NAME_EMAIL> on 2017/6/15.
"""
import copy
import datetime
import json
import random
import re
import time
import traceback

import pymysql

from requests.exceptions import RequestException

from dmplib.constants import NODE_DATASET_COUNT_LIMIT
from dmplib.hug import g
from dmplib.sql_adapter import adapter_sql
from dmplib import config
from components.fast_logger import FastLogger
from components.redis import stale_cache
from components.enums import DatasetStorageType
from components.constants import GET_UNION_REPLACE_SQL
from components.storage_setting import get_storage_type
from components.constants import UPDATE_FIELD_URL, DATASET_TABLE_NAME_PREFIX, DATASET_ROW_PERMISSION_USER_ID
from components.dmp_openapi import api_request, flow_delete_schedule
from components.enums import DataCenterAction, MysoftERPDataType
from components.redis import RedisCache
from components.storage_setting import is_local_storage, compatible_local_type, get_epr_datasource_model, \
    is_history_dataset
from components.send_message_openapi import data_source_message, data_set_message
from components.external_interface import NewErpAPI
from dmplib.base import repository
from dmplib.base.enums import (
    DatasetFieldType,
    DatasetType,
    DatasetVersionType,
    DatasetVersionNote,
    DataSourceType
)
from dmplib.data_set import DatasetVersionModel, DatasetCleanTableModel
from dmplib.data_source.services import data_source_service
from dmplib.utils.errors import UserError, DataSourceError, DataSetError, SkipCleanError
from dmplib.utils.strings import seq_id, _get_random_chars
from node.comment.clean_dataset_version import CleanDatasetVersion
from node.comment.data_center_util import DataCenterUtil
from node.data_set.data_set_inspection import DataSetInspectionService
from node.data_set.excel_data_set import ExcelDataSet
from node.data_set.interface_data_set import InterfaceDataSet
from node.data_set.mysql_data_set import MysqlDataSet
from node.data_set.dataset_factory import DataSetFactory
from node.node_execution import NodeExecution
from node.node_log_message import NodeMsgType
from node.node_result import NodeResult
from components.external_interface import ResourceUnavailableException
from node.data_cache.extra_cache_redis import ExtraCacheRedis


class DataSetNode(NodeExecution):
    def __init__(self, context, cur_node):
        """
        数据集节点
        :param flow.flow_context.FlowContext context:
        :param flow.node.Node cur_node:
        """
        super().__init__(context, cur_node)
        self.data_set = {}
        self.data_set_content = None
        self.data_limit = int(NODE_DATASET_COUNT_LIMIT)
        self.inspection_flag = True
        self.subject_id = ''
        self.subject_use_history = False
        self.inspect_parent_id = ''
        self.inspection_msg = ''
        self.table_name = ''
        self.__new_erp_instance = None
        self.__new_erp_datasource_instance = None
        self._data_source = None
        self._is_sleep = False

    def execution_dataset_subject(self):
        self.info("启动主题包数据同步节点", msg_type=NodeMsgType.STEP.value)
        # 查询该主题包所有数据集
        self.subject_id = self.context.flow_id
        subject_version = ''
        datasets = self.get_datasets_by_folder_id()
        self.inspect_parent_id = seq_id()
        need_update_datasets = copy.copy(datasets)
        # 循序执行, 获取巡检结果，只有全部通过才同步正式版本
        for dataset in datasets:
            self.data_set = dataset
            self.context.flow_id = dataset.get("id")
            content = json.loads(self.data_set.get("content"))
            dataset["tmp_table_name"] = content.get("tmp_table_name")
            # 主题包中数据集有可能减少，这种情况下 , temp_table_name是不存在的
            table_exists = self.table_is_exists(dataset["tmp_table_name"])
            # 主题包没有生产temp_table_name，不做任何处理
            if not table_exists:
                need_update_datasets.remove(dataset)
                continue
            subject_version = content.get("version")
            result, dataset["inspection_id"], inspection_result = self.execution_single(dataset["tmp_table_name"])
            if (not result or not inspection_result) and self.inspection_flag:
                self.inspection_flag = False
            # 执行过程中报错
            if isinstance(inspection_result, NodeResult):
                return inspection_result
        # 同步更新主题包version (不管是否应用最新版本)
        self.update_dataset_subject_verison(subject_version)
        # 保存 inspection_result
        self.update_dataset_inspection_result()
        # 巡检通过
        if self.inspection_flag:
            self.info("数据写入dmp数据集", msg_type=NodeMsgType.STEP.value)
            for dataset in need_update_datasets:
                self.data_set = dataset
                self.sync_dataset_data_release(dataset.get("tmp_table_name"), dataset.get("inspection_id"))
        # 保存巡检异常记录
        else:
            # 使用历史版本的时候，不更新主题包巡检
            if not self.subject_use_history:
                self.update_subject_instance()

    def clean_data_table(self):
        from components.dmp_openapi import get_clean_data_table
        from dmplib.redis import RedisCache as Cache
        cache = Cache()
        env = 'local' if is_local_storage(self.context.project_code) else 'cloud'
        meta_key = 'dmp_flow:clean_table:{code}:{env}'.format(code=self.context.project_code, env=env)
        lock = cache.set_nx_ex(meta_key, 1, ex=21600, nx=True)
        if lock:
            try:
                # self.info("开始清理{}租户历史数据表".format(self.context.project_code), msg_type=NodeMsgType.STEP.value)
                get_clean_data_table(self.context.project_code, env)
                # self.info("已注册清理历史表数据，具体日志请查看celery日志", msg_type=NodeMsgType.STEP.value)
            except Exception as e:
                self.info("注册清理历史表任务失败:{}".format(str(e)), msg_type=NodeMsgType.STEP.value)

    def execution_single(self, tmp_table_name):
        self.info("启动数据集节点", msg_type=NodeMsgType.STEP.value)
        try:
            self._load_data_set()
            if self.data_set.get('type') == 'SQL':
                msg = "数据集id:{}，名称：{}，类型：SQL".format(self.data_set.get("id"), self.data_set.get("name"))
                self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            elif self.data_set.get('type') == 'EXCEL':
                msg = "数据集id:{}，名称：{}，类型：EXCEL".format(self.data_set.get("id"), self.data_set.get("name"))
                self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            elif self.data_set.get('type') == 'UNION':
                msg = "数据集id:{}，名称：{}，类型：组合数据集".format(self.data_set.get("id"), self.data_set.get("name"))
                self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            elif self.data_set.get('type') == 'API':
                msg = "数据集id:{}，名称：{}，类型：API".format(self.data_set.get("id"), self.data_set.get("name"))
                self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            else:
                msg = '数据集类型错误，只支持SQL、EXCEL、UNION类型'
                self.error(msg, msg_type=NodeMsgType.DETAIL.value)
                return '', '', NodeResult(False, '数据集类型错误，只支持SQL、EXCEL、UNION类型')

            return self.sync_dataset_data_rc(tmp_table_name)

        except (UserError, RequestException, pymysql.ProgrammingError) as error:
            self.message_processing(error)
            err = '数据集生成失败:' + (
                '数据源连接失败' if isinstance(error, pymysql.ProgrammingError) else error.message)
            self._record_log(err)
            return (
                '',
                '',
                NodeResult(False, err),
            )
        except BaseException as e:
            data_set_message(
                self.context.project_code,
                {
                    'title': self.data_set.get('name') + '数据集生成失败',
                    'id': self.context.flow_instance_id,
                    'name': self.data_set.get('name'),
                },
            )
            err = '数据集生成失败:' + str(e)
            self._record_log(err)
            return '', '', NodeResult(False, err)

    def execution(self):
        g.project_code = self.context.project_code
        g.dataset_id = self.context.flow_id
        # 判断是否为主题包
        dataset_subject = self.is_dataset_subject()
        if dataset_subject:
            subject_inspection = self.dataset_subject_inspection(dataset_subject.get('subject_id'))
            if subject_inspection and subject_inspection.get('status', '') == "巡检中":
                subject_id = subject_inspection.get('subject_id')
                subject_name = subject_inspection.get('subject_name')
                msg = '主题id: {id}, 名称: {name}版本正在生成中，需要等待当前版本执行完成！'.format(id=subject_id,
                                                                                                  name=subject_name)
                self.error(msg, msg_type=NodeMsgType.DETAIL.value)
                return NodeResult(False, '版本正在生成中，需要等待当前版本执行完成！')
            result = self.execution_dataset_subject()
            return result if result else NodeResult(True, '数据集生成成功')
        self.info("启动数据集节点", msg_type=NodeMsgType.STEP.value)
        try:
            self._load_data_set()
            tmp_table_name, is_suc = self.get_tmp_table()
            # （临时逻辑，重构需要去掉）API的调度需要在celery中继续执行。flow不托管后续的流程。
            if (
                    self.data_set.get('type') in ['API' ] or
                    (self.data_source and self.data_source.type in ['MysoftShuXin15'])
                    ):
                self.info("调度正在进行中，需要等待执行完成", msg_type=NodeMsgType.STEP.value)
                # raise ResourceUnavailableException('调度正在进行中，需要等待执行完成')
                return NodeResult(True, '调度正在生成中，需要等待执行完成！')
            if not is_suc:
                return tmp_table_name
            self.sync_data_set(tmp_table_name)
            self.clean_data_table()

        except ResourceUnavailableException as e:
            dataset_name = self.data_set.get('name')
            err = dataset_name + '数据集生成失败, ERP API 当前不可用'
            data_set_message(
                self.context.project_code,
                {
                    'title': err,
                    'id': self.context.flow_instance_id,
                    'name': dataset_name,
                },
            )
            self._record_log(err)
            raise e
        except SkipCleanError as e:
            dataset_name = self.data_set.get('name')
            err = dataset_name + e.message
            self.info(err, msg_type=NodeMsgType.STEP.value)
            self._record_log(err)
            raise e

        except (UserError, RequestException, pymysql.ProgrammingError) as error:
            print(traceback.print_exc())
            err = '数据集生成失败:' + (
                '数据源连接失败' if isinstance(error, pymysql.ProgrammingError) else error.message)
            self._record_log(err)
            self.message_processing(error)
            print(traceback.format_exc())
            return NodeResult(
                False, err
            )
        except BaseException as e:
            print(traceback.format_exc())
            data_set_message(
                self.context.project_code,
                {
                    'title': self.data_set.get('name') + '数据集生成失败',
                    'id': self.context.flow_instance_id,
                    'name': self.data_set.get('name'),
                },
            )
            err = '数据集生成失败:' + str(e)
            self._record_log(err)
            return NodeResult(False, err)
        return NodeResult(True, '数据集生成成功')

    def get_tmp_table(self):
        g.dataset_type = self.data_set.get('type')
        g.datasource_type = ""
        if self.data_set.get('type') == 'SQL':
            msg = "数据集id:{}，名称：{}，类型：SQL".format(self.data_set.get("id"), self.data_set.get("name"))
            self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            tmp_table_name = self.generate_sql_data_set()
        elif self.data_set.get('type') == 'EXCEL':
            msg = "数据集id:{}，名称：{}，类型：EXCEL".format(self.data_set.get("id"), self.data_set.get("name"))
            self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            tmp_table_name = self.generate_excel_data_set()
        elif self.data_set.get('type') == 'UNION':
            msg = "数据集id:{}，名称：{}，类型：组合数据集".format(self.data_set.get("id"), self.data_set.get("name"))
            self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            tmp_table_name = self.generate_union_dataset()
        elif self.data_set.get('type') == 'API':
            msg = "数据集id:{}，名称：{}，类型：API".format(self.data_set.get("id"), self.data_set.get("name"))
            self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            tmp_table_name = self.generate_sql_data_set()
        else:
            msg = '数据集类型错误，只支持SQL、EXCEL、UNION、API、ADS类型'
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            return NodeResult(False, '数据集类型错误，只支持SQL、EXCEL、UNION、API、ADS类型'), False
        # 记录生成的表名
        self.add_dataset_clean_table(tmp_table_name)
        return tmp_table_name, True

    def sync_data_set(self, tmp_table_name):  # NOSONAR
        # 云端存储模式才执行同步数据集操作
        if all([
            is_local_storage(self.context.project_code),
            is_history_dataset(self.context.project_code, conn_str=getattr(self.data_source, 'conn_str', None),
                               dataset_content=self.data_set.get('content')),
            self.data_set.get("type") != 'EXCEL'
        ]):
            # 历史租户采用原sleep的方案， 新开租户采用轮询获取状态的方案
            clear_time = self.data_set.get("clear_time") or 30
            time.sleep(int(clear_time))
        self.info("数据写入dmp数据集", msg_type=NodeMsgType.STEP.value)

        if tmp_table_name:
            # rc版本保存
            self.info("生成预发布版本并巡检", msg_type=NodeMsgType.DETAIL.value)
            version_id = self.save_rc_version(tmp_table_name)
            # 数据巡检
            # self.info("开始数据巡检", msg_type=NodeMsgType.DETAIL.value)
            dataset_fields = self._get_data_fields()
            inspection_result, inspection_id = DataSetInspectionService(
                self.context, self.data_set, tmp_table_name, dataset_fields
            ).inspect()
            self.update_version_inspection_result(
                inspection_id, version_id, status='正常' if inspection_result else '巡检失败'
            )
            # self.info("更新数据巡检结果", msg_type=NodeMsgType.DETAIL.value)
            # todo 添加自动审核机制
            if self.use_dataset_history_version():
                self.info("用户已指定数据集版本，不更新正式版本数据，最新数据已同步预发布版本。",
                          msg_type=NodeMsgType.DETAIL.value)
                return
            # 更新数据集数据表
            if inspection_result:
                try:
                    total_count = self._get_dataset_count(table_name=tmp_table_name)
                except Exception as e:
                    import logging
                    logging.error("获取总数失败")
                    logging.exception(e)
                    if all([
                        is_local_storage(self.context.project_code),
                        is_history_dataset(self.context.project_code,
                                           conn_str=getattr(self.data_source, 'conn_str', None),
                                           dataset_content=self.data_set.get('content')),
                    ]):
                        msg = '数据集同步执行失败，数据清洗未能在指定时间清洗完成，请调整数据集的最大执行时长(具体错误：{})'.format(
                            str(e))
                        self.error(msg=msg, msg_type=NodeMsgType.DETAIL.value)
                        raise DataSetError(message=msg)
                    total_count = "(本地模式清洗未完成)，0"
                self.info("临时表重命名为正式表。", msg_type=NodeMsgType.DETAIL.value)
                self.tmp_table_to_release(tmp_table_name, inspection_id)
                # 清空缓存
                # self.info("清空缓存", msg_type=NodeMsgType.DETAIL.value)
                self._clean_cache()
                # 打印结束日志
                msg = "数据集同步执行成功，已同步数据总数：{}条".format(str(total_count))
                self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            else:
                self.warning("数据巡检存在异常数据，请查看巡检结果。", msg_type=NodeMsgType.DETAIL.value)
        else:
            self.info("数据集同步执行成功，已同步数据总数：0条", msg_type=NodeMsgType.DETAIL.value)
            # 清空缓存
            self._clean_cache()

    def table_is_exists(self, table_name: str) -> bool:
        # 主题包中数据集有可能减少，这种情况下,temp_table_name是不存在的
        with self.context.get_project_data_db() as db:
            table_exists_sql = adapter_sql('show_col', db_type=db.db_type).format(table_name=table_name)
            return db.exec(table_exists_sql)

    def sync_dataset_data_rc(self, tmp_table_name):
        dataset_fields = self._get_data_fields()
        inspection_result, inspection_id = DataSetInspectionService(
            self.context, self.data_set, tmp_table_name, dataset_fields, inspect_parent_id=self.inspect_parent_id
        ).inspect()
        # rc版本保存
        version_id = self.save_rc_version(tmp_table_name, use_subject=True)
        # 数据巡检
        self.update_version_inspection_result(inspection_id, version_id,
                                              status='正常' if inspection_result else '巡检失败')
        # todo 添加自动审核机制
        # 一旦主题包内有一个使用了历史版本，都是提示这个
        if self.subject_use_history or self.use_dataset_history_version():
            self.subject_use_history = True
            self.inspection_msg = "已锁定历史版本，未应用为正式版本"
            self.info("用户已指定数据集版本，不更新正式版本数据，最新数据已同步预发布版本。",
                      msg_type=NodeMsgType.DETAIL.value)
            return inspection_result, inspection_id, False
        if not inspection_result or not self.inspection_flag:
            self.inspection_msg = "数据巡检不通过，未应用为正式版本"
            self.warning("数据巡检存在异常数据或同一主题包下有数据巡检异常，请查看巡检结果。",
                         msg_type=NodeMsgType.DETAIL.value)
            return inspection_result, inspection_id, False
        return inspection_result, inspection_id, True

    def compare_struct_update_dmp(self):
        # 调dmp openapi进行字段类型变更
        result = api_request(self.context.project_code, UPDATE_FIELD_URL, 'post', dataset_id=self.data_set.get("id"))
        if not result:
            self.info("调dmp openapi进行字段类型变更失败。", msg_type=NodeMsgType.DETAIL.value)

    def compare_struct_update(self):
        dataset_id = self.data_set.get("id")
        # 历史结构从dataset_field中取
        dataset_fields = self._get_data_fields()
        new_dataset_fields = self.get_fields_subject_table(dataset_id)

        rc_fields = {field.get("col_name"): field for field in new_dataset_fields}
        for field in dataset_fields:
            col_name = field.get("col_name")
            data_type = field.get("data_type")

            if rc_fields.get(col_name):
                if data_type == rc_fields.get(col_name).get("data_type"):
                    continue
                # 更新field type类型
                repository.update_data(
                    "dap_bi_dataset_field",
                    {"data_type": data_type, "field_group": field.get("field_group")},
                    {"col_name": col_name, "dataset_id": dataset_id},
                )
            else:
                # 删除类型
                repository.delete_data("dap_bi_dataset_field", {"col_name": col_name, "dataset_id": dataset_id})
        # 新增类型
        old_fields = {field.get("col_name"): field for field in dataset_fields}
        for field in new_dataset_fields:
            col_name = field.get("col_name")
            if not old_fields.get(col_name):
                # 补充一些必要字段
                _field = dict()
                _field["id"] = seq_id()
                _field["dataset_id"] = dataset_id
                _field["col_name"] = col_name
                _field["data_type"] = field.get("data_type")
                _field["`alias_name`"] = field.get("alias_name")
                _field["rank"] = self.get_max_rank() + 1
                _field["visible"] = 1
                _field["type"] = DatasetFieldType.Normal.value
                _field["field_group"] = field.get("field_group")
                repository.add_data("dap_bi_dataset_field", _field)

    def get_max_rank(self):
        # 这里用本来使用max(rank) 获取最大 rank,使用的这个库似乎不支持，只能手动取最大
        results = repository.get_data(
            "dap_bi_dataset_field", {"dataset_id": self.data_set.get("id")}, ["`rank`"], multi_row=True
        )
        return max([_.get("rank") for _ in results]) or 0

    def sync_dataset_data_release(self, tmp_table_name, inspection_id):
        # 更新结构
        self.compare_struct_update_dmp()
        # 创建临时表索引
        self.create_temp_table_index(tmp_table_name)

        self.tmp_table_to_release(tmp_table_name, inspection_id)
        # 清空缓存
        self._clean_cache(dataset_id=self.data_set.get("id"))
        # 打印结束日志
        total_count = self._get_dataset_count(table_name=self.table_name)
        msg = "数据集[{}]同步执行成功，已同步数据总数：{}条".format(self.data_set.get("name"), str(total_count))
        self.info(msg, msg_type=NodeMsgType.DETAIL.value)

    def create_temp_table_index(self, tmp_table_name):
        index_sql = "SELECT `index_name`, `column_list`, `system`, `index_statement` FROM dap_bi_dataset_index WHERE dataset_id=%(id)s"
        with self.context.get_project_db() as proj_db:
            index_data = proj_db.query(index_sql, {'id': self.data_set.get("id")})
            self.data_set["index_data"] = index_data

        with self.context.get_project_data_db() as db:
            self.create_index(db, tmp_table_name)

    def update_subject_instance(self):
        with self.context.get_project_db() as db:
            return db.update(
                'dap_bi_instance', {'inspection_id': self.inspect_parent_id or seq_id()}, {'id': self.context.flow_instance_id}
            )

    def use_dataset_history_version(self):
        # 是否使用数据集的历史版本作为正式版
        return repository.data_is_exists("dap_bi_dataset_current_version", condition={"dataset_id": self.data_set.get("id")})

    def save_rc_version(self, tmp_table_name, use_subject=False):
        if self.data_set.get("type") == 'EXCEL':
            self._is_sleep = True
        # 临时表变成RC版，复制一份主要考虑：1.区分预发布版本和正式版的data表 2.tmp_table_name后面可以直接rename为正式版
        rc_table_name = self.data_set.get("table_name") + "_rc"
        version_id = self.add_dataset_version(
            1, rc_table_name, version_type=DatasetVersionType.RC.value, use_subject=use_subject
        )
        # self.info("添加RC版本", msg_type=NodeMsgType.DETAIL.value)
        # 记录RC表名
        self.add_dataset_clean_table(rc_table_name)
        # self.info("记录RC表名", msg_type=NodeMsgType.DETAIL.value)
        # 调整顺序，因为添加预发布版本有一个控制，删除旧的预发布，这里调整为添加后，生成表
        self.create_table_struct(rc_table_name, tmp_table_name)
        # self.info("创建RC表结构", msg_type=NodeMsgType.DETAIL.value)
        self.create_table_data(rc_table_name, tmp_table_name)
        # self.info("创建RC表数据", msg_type=NodeMsgType.DETAIL.value)
        return version_id

    def update_version_inspection_result(self, inspection_id, version_id, status='正常'):
        with self.context.get_project_db() as db:
            return db.update('dap_bi_dataset_version', {'inspection_id': inspection_id, 'status': status}, {'id': version_id})

    def update_version_inspection_status(self, version_id, status):
        with self.context.get_project_db() as db:
            return db.update('dap_bi_dataset_version', {'status': status}, {'id': version_id})

    def update_dataset_subject_verison(self, subject_version):
        with self.context.get_project_db() as db:
            return db.update('dap_bi_dataset', {'version': subject_version}, condition={'id': self.subject_id})

    def update_dataset_inspection_result(self):
        with self.context.get_project_db() as db:
            return db.update('dap_bi_dataset', {'inspection_result': self.inspection_msg}, condition={'id': self.subject_id})

    def get_version_table_name(self):
        """
        获取自动版本表名
        :return:
        """
        # todo management 平台上 DatasetConfig 配置在flow中也要添加配置,或者写公共配置
        version_number_list = self.get_dataset_version_no()
        if version_number_list:
            version_number = max(version_number_list) + 1
            # 处理多余的版本
            self.deal_redundant_data_version()
        else:
            version_number = 1
        version_table_name = "{table_name}_{version_number}".format(
            table_name=self.data_set.get("table_name"), version_number=version_number
        )
        version_table_name += "_auto"
        return version_number, version_table_name

    @staticmethod
    def get_fields_subject_table(dataset_id):
        result = (
                repository.get_data("dap_bi_dataset_subject_table", {"dataset_id": dataset_id}, ["dataset_meta_columns"]) or {}
        )
        if result:
            dataset_meta_columns = json.loads(result.get("dataset_meta_columns"))
            return dataset_meta_columns
        return []

    def update_rc_struct(self, dataset_fields):
        rc_update_fields = self.get_fields_subject_table(self.data_set.get("id"))
        rc_fields = {field.get("col_name"): field for field in rc_update_fields}

        need_update_fields = copy.copy(dataset_fields)

        for field in dataset_fields:
            col_name = field.get("col_name")
            data_type = field.get("data_type")

            if rc_fields.get(col_name):
                rc_field = rc_fields.get(col_name)
                if data_type == rc_fields.get(col_name).get("data_type"):
                    continue
                field["data_type"] = rc_field.get("data_type")
                field["field_group"] = rc_field.get("field_group")
            else:
                # 删除类型
                need_update_fields.remove(field)
        # 新增类型
        old_fields = {field.get("col_name"): field for field in dataset_fields}
        for field in rc_update_fields:
            col_name = field.get("col_name")
            if not old_fields.get(col_name):
                # 补充一些必要字段
                _field = dict()
                _field["id"] = seq_id()
                _field["dataset_id"] = self.data_set.get("id")
                _field["col_name"] = col_name
                _field["data_type"] = field.get("data_type")
                _field["alias_name"] = field.get("alias_name")
                _field["rank"] = self.get_max_rank() + 1
                _field["visible"] = 1
                _field["type"] = DatasetFieldType.Normal.value
                _field["field_group"] = field.get("field_group")
                need_update_fields.append(_field)
        return need_update_fields

    def add_dataset_clean_table(self, table_name):
        table_env = is_local_storage(self.context.project_code)
        model = DatasetCleanTableModel()
        model.id = seq_id()
        model.dataset_id = self.data_set.get('id')
        model.table_name = table_name
        model.table_env = 'local' if table_env else 'cloud'
        repository.add_model(model.__TABLE_NAME__, model)

    def add_dataset_version(
            self,
            version_number,
            table_name,
            version_type=DatasetVersionType.HISTORY.value,
            version_content=DatasetVersionNote.Schedule.value,
            inspection_id=None,
            use_subject=False,
    ):
        """
        :自动添加数据集版本
        :param dataset.models.DatasetVersionModel model:
        :return :
        """
        model = DatasetVersionModel()
        model.id = seq_id()
        model.dataset_id = self.data_set.get("id")
        model.table_name = table_name
        model.type = '自动'
        model.version_number = version_number
        model.version_type = version_type
        model.version_name = (
            datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S").replace("PM", "下午").replace("AM", "上午")
            # datetime.datetime.now().strftime("%Y/%m/%d %p%l:%M:%S").replace("PM", "下午").replace("AM", "上午")
        )

        # 获取数据集字段结构
        dataset_fields = self.get_dataset_field({"type": DatasetFieldType.Normal.value})
        model.field_struct = json.dumps(dataset_fields)
        if use_subject:
            # 重新生成field_struct
            model.field_struct = json.dumps(self.update_rc_struct(dataset_fields))

        # 获取数据集源名称
        data_source_name = ''
        content = json.loads(self.data_set.get("content"))
        if self.data_set.get("type") == DatasetType.Sql.value:
            data_source_model = data_source_service.get_data_source(content.get('data_source_id'))
            if data_source_model:
                data_source_name = data_source_model.name
        elif self.data_set.get("type") == DatasetType.Excel.value:
            data_source_name = content.get("file_name")
        elif self.data_set.get("type") == DatasetType.Union.value:
            pattern = re.compile(r'\{([^\{\}]*)\}')
            dataset_names = pattern.findall(content.get("sql"))
            data_source_name = ','.join(dataset_names) if dataset_names else ''
        model.data_source_name = data_source_name
        model.content = version_content
        # 添加dataset_content, content这个字段已经被当成描述使用了
        model.dataset_content = self.data_set.get('content')
        model.inspection_id = inspection_id

        # 预发布版、正式版只保存一个
        if version_type in [DatasetVersionType.RC.value, DatasetVersionType.RELEASE.value]:
            self.deal_redundant_data_version_no_history(version_type=version_type)

        fields = [
            'id',
            'dataset_id',
            'version_number',
            'version_name',
            'table_name',
            'content',
            'field_struct',
            'data_source_name',
            'type',
            'version_type',
            'dataset_content',
            'inspection_id',
        ]
        repository.add_model("dap_bi_dataset_version", model, fields)
        return model.id

    def get_dataset_field(self, condition=None):
        """
        获取数据集字段信息
        :param condition:
        :return:
        """
        dataset_fields = [
            'id',
            'dataset_id',
            'alias_name',
            'col_name',
            'origin_col_name',
            'origin_field_type',
            'origin_table_id',
            'origin_table_comment',
            'origin_table_name',
            'origin_table_alias_name',
            'data_type',
            'group_type',
            'rank',
            'visible',
            'field_group',
            'format',
            'type',
            'expression',
            'expression_advance',
            'inspection_rules',
            'note',
        ]
        sql = 'SELECT {cols} FROM dap_bi_dataset_field'.format(cols='`' + '`,`'.join(dataset_fields) + '`')
        sql += ' WHERE dataset_id=%(dataset_id)s '
        if condition:
            where = ' AND '.join([key + '=' + "'" + value + "'" for key, value in condition.items()])
            sql += " AND " + where
        sql += ' ORDER BY `rank`'

        params = {'dataset_id': self.data_set.get("id")}
        with self.context.get_project_db() as db:
            return db.query(sql, params)

    @compatible_local_type
    def drop_table_data(self, table_name):
        drop_sql = " DROP TABLE IF EXISTS {table_name} ;  ".format(table_name=table_name)
        with self.context.get_project_data_db() as db:
            db.exec(drop_sql)

    def drop_table_data_local(self, table_name):
        # 数据服务中心如果没有更新代码会报异常，不用抛异常，保证流程正常清洗，只是数据服务中心存在脏数据而已
        try:
            from dmplib.data_source.services import data_source_service
            from components.data_center_api import get_data_source_info
            DataCenterUtil.request_data_center(
                self.context.project_code,
                DataCenterAction.DeleteDataSet.value,
                {
                    "DataSourceModel": get_data_source_info(
                        data_source_service.get_epr_datasource(dataset_content=self.data_set_content)),
                    "DataSetTableName": table_name
                },
                dataset_content=self.data_set_content
            )
        except:
            pass

    @property
    def new_erp_api(self):
        """
        数据服务中心API
        :return:
        """
        if self.__new_erp_instance is None:
            self.__new_erp_instance = NewErpAPI(data_source=self.new_erp_datasource_model, data_set_node=self)
        return self.__new_erp_instance

    @property
    def new_erp_datasource_model(self):
        """
        数据服务中心数据源
        :return:
        """
        if self.__new_erp_datasource_instance is None:
            self.__new_erp_datasource_instance = get_epr_datasource_model(self.data_set.get('content'))
        return self.__new_erp_datasource_instance

    def create_table_struct_local(self, new_table_name, old_table_name):
        pass

    @compatible_local_type
    def create_table_struct(self, new_table_name, old_table_name):
        # 先判断存在删除
        self.drop_table_data(new_table_name)
        copy_struct_sql = " CREATE TABLE {new_table_name} LIKE {old_table_name}; ".format(
            new_table_name=new_table_name, old_table_name=old_table_name
        )
        with self.context.get_project_data_db() as db:
            db.exec(copy_struct_sql)

    def create_table_data_local(self, new_table_name, old_table_name):
        """
        调用数据服务中心数据清洗接口
        :param new_table_name:
        :param old_table_name:
        :return:
        """
        if is_history_dataset(self.context.project_code, conn_str=getattr(self.data_source, 'conn_str', None),
                              dataset_content=self.data_set.get('content')):
            sql, fields = self.get_field_sql_text(old_table_name)
            res = self.get_datacenter_clear(sql, new_table_name, fields)
        else:
            data_source = self.get_epr_datasource_model()
            self.info("data_source:dir(data_source):%s" % dir(data_source))
            try:
                db_type = data_source.db_type.lower()
            except:
                # MysoftNewERPConnStrModel的格式需要这样获取db_type
                db_type = data_source.conn_str.DbType.lower()
            if db_type == MysoftERPDataType.Mysql.value:
                sql = '''create table `{}` (PRIMARY KEY (`MdcDataGUID`)) select * from `{}`'''.format(new_table_name,
                                                                                                      old_table_name)
            elif db_type == MysoftERPDataType.DM.value:
                sql = '''begin 
                   execute IMMEDIATE 'create table {new_table_name}  as select * from {old_table_name}'; 
                   execute IMMEDIATE 'ALTER TABLE {new_table_name} ADD Constraint PK_MdcDataGUID_{new_table_name} primary key(MdcDataGUID)';
                        end;    
                    '''.format(
                    new_table_name=new_table_name, old_table_name=old_table_name
                )
            else:
                sql = '''select * into [{new_table_name}] from [{old_table_name}];
                ALTER TABLE [{new_table_name}] ADD Constraint PK_MdcDataGUID primary key([MdcDataGUID]);'''.format(
                    new_table_name=new_table_name, old_table_name=old_table_name
                )
            res = self.new_erp_api.local_execute_sql(sql)
        # self.info("创建预发布版本：{}".format(res))

    @compatible_local_type
    def create_table_data(self, new_table_name, old_table_name):
        if config.get('DB.db_type') == 'DM':
            # 达梦语法 create table like select * from table_name 包含创建表结构和数据的功能
            return
        copy_data_sql = " INSERT INTO {new_table_name} SELECT * FROM {old_table_name};  ".format(
            new_table_name=new_table_name, old_table_name=old_table_name)
        with self.context.get_project_data_db() as db:
            db.exec(copy_data_sql)

    def get_dataset_version_no(self):
        """
        根据数据集id获取版本号
        :return:
        """
        version_numbers = repository.get_data(
            'dap_bi_dataset_version',
            {'dataset_id': self.data_set.get("id"), 'type': "自动", "version_type": DatasetVersionType.HISTORY.value},
            ['version_number'],
            multi_row=True,
        )
        return [version_number.get('version_number') for version_number in version_numbers] if version_numbers else []

    def message_processing(self, error):
        """
        消息分类处理
        :param error:
        :return:
        """
        # todo (使用字典对应条件)
        source_error = False
        set_error = False
        data_source_detail = data_source_service.get_data_source(self.data_set_content.get('data_source_id'))
        if isinstance(error, (DataSourceError, RequestException)):
            title = data_source_detail.name + (
                '数据源连接失败' if isinstance(error, RequestException) else error.message)
            source_error = True
        # 历史原因，都是使用UserError，更改怕其他地方出错，简单对message进行处理
        elif isinstance(error, UserError):
            if '连接失败' in error.message:
                title = data_source_detail.name + '数据源连接失败'
                source_error = True
            else:
                title = self.data_set.get('name') + '数据集生成失败' + error.message
                set_error = True
        elif isinstance(error, pymysql.ProgrammingError):
            # 执行SQL出错
            title = self.data_set.get('name') + '数据集生成失败，执行SQL出错'
            set_error = True
        # 暂时统一处理
        else:
            title = self.data_set.get('name') + '数据集生成失败' + error.message
            set_error = True
        if source_error:
            data_source_message(
                self.context.project_code,
                {
                    'title': title,
                    'id': self.data_set_content.get('data_source_id'),
                    'source_type': data_source_detail.type,
                },
            )
        elif set_error:
            data_set_message(
                self.context.project_code,
                {'title': title, 'id': self.context.flow_instance_id, 'name': self.data_set.get('name')},
            )

    def _clean_cache(self, dataset_id=None):
        """
        清空所有单图依赖数据集的缓存数据
        :return:
        """
        dataset_id = dataset_id if dataset_id else self.context.flow_id
        # sql = ' select id from dashboard_chart where source =%(id)s  '
        # with self.context.get_project_db() as db:
        #     chart_id_list = db.query(sql, {'id': dataset_id})
        # if chart_id_list:
        #     for chart_id in chart_id_list:
        #         redis_cache.del_data(chart_id.get('id'))
        #         # 报告发布的单图缓存删除(带条件的模糊匹配并删除)
        #         key = 'dmp:screen:released:' + chart_id.get('id')
        #         redis_cache.del_by_scan(pattern=key + '*')
        #         redis_cache.del_data(key)
        #         # keys = redis_cache._connection.keys(pattern=key + '*')
        #         # if keys:
        #         #     redis_cache._connection.delete(*keys)
        #         # else:
        #         #     redis_cache.del_data(key)

        # 清空数据集查询结果数据缓存
        prefix = '{project_code}:{cache_key}:{object_name}'.format(
            project_code=self.context.project_code,
            cache_key=config.get('Cache.released_dashboard_metadata_cache_key', 'dmp'),
            object_name="dataset",
        )

        meta_key = '{prefix}:{object_id}'.format(prefix=prefix, object_id=dataset_id)
        # 修改为删除整个缓存
        # self.info("缓存key信息, project_code: {}, g.code: {}, meta_key: {}".format(
        #     self.context.project_code,
        #     getattr(g, 'code', ''),
        #     meta_key
        # ), msg_type=NodeMsgType.DETAIL.value)

        # 实例化cache
        cache = RedisCache()

        del_result = cache.del_data(meta_key)
        # self.info("缓存删除结果：{}".format(del_result), msg_type=NodeMsgType.DETAIL.value)

        self._del_sql_cache(prefix, dataset_id)

        # 修改数据集元数据的data_version
        new_version = '%s_%s' % (time.strftime('%Y%m%d%H%M%S'), random.randint(1, 1000))
        meta_key = '{prefix}:{object_id}'.format(prefix=prefix, object_id=dataset_id)
        cache.hset(meta_key, 'data_version', new_version)
        # 只有当同步的是主题包数据集时，才更新数据集元数据信息
        if self.subject_id:
            cache.hset(meta_key, 'version', new_version)
        # 清空数据集权限缓存
        self.__refresh_dataset_row_permissions()

    def _del_sql_cache(self, prefix, dataset_id):
        extra_cache = ExtraCacheRedis.create_priority_extra_redis()
        self.__del_sql_cache(extra_cache, prefix, dataset_id=dataset_id)
        is_extra_redis = getattr(extra_cache, 'is_extra_redis', 0)
        # self.info(f"使用额外的redis:<{is_extra_redis}>", msg_type=NodeMsgType.DETAIL.value)
        # 如果使用的额外的redis，说明两个redis的数据都需要清清除
        if is_extra_redis:
            self.__del_sql_cache(RedisCache(), prefix, dataset_id=dataset_id)

    def __del_sql_cache(self, redis_cache, prefix, dataset_id):
        result_key = '{prefix}:{object_id}'.format(prefix=prefix, object_id="sql_result_" + dataset_id)
        sql_key = '{prefix}:{object_id}'.format(prefix=prefix, object_id="sql_keys_" + dataset_id)
        redis_cache.del_data(result_key)
        redis_cache.del_data(sql_key)

    def __refresh_dataset_row_permissions(self):
        # 刷新数据集行列权限
        try:
            RedisCache(key_prefix=self.context.project_code + ":").delete(DATASET_ROW_PERMISSION_USER_ID)
        except Exception:
            pass

    def _load_data_set(self):
        """
        加载数据集配置
        :return:
        """
        self.data_set = self._get_data_set()
        if (
                self.data_set.get('type') != 'SQL'
                and self.data_set.get('type') != 'EXCEL'
                and self.data_set.get('type') != 'UNION'
                and self.data_set.get('type') != 'API'
        ):
            msg = '只支持SQL、EXCEL、API、UNION数据集节点'
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise DataSetError(message=msg)
        content = self.data_set.get('content')
        if not content:
            msg = '数据集配置内容为空'
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise DataSetError(message=msg)
        try:
            self.data_set_content = json.loads(content)
        except json.JSONDecodeError as e:
            msg = '数据集配置内容解析错误：' + str(e)
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise DataSetError(message=msg)

    def _get_data_set(self):
        """
        获取数据集
        :return dict:
        """
        sql = 'SELECT `id`,`name`,`table_name`,`type`,`content`,`relation_sql`,`edit_mode`, `clear_time`, `is_need_procedure` FROM dap_bi_dataset WHERE id=%(id)s LIMIT 1 '
        # 查询索引信息
        index_sql = """
        SELECT `index_name`, `column_list`, `system`, `index_statement` FROM dap_bi_dataset_index WHERE dataset_id=%(id)s
        """
        with self.context.get_project_db() as db:
            data_set = db.query_one(sql, {'id': self.context.flow_id})
            if not data_set:
                msg = '数据集' + self.context.flow_id + '不存在'
                self.error(msg, msg_type=NodeMsgType.DETAIL.value)
                # 找不到数据集之后，还需要删除调度任务
                flow_delete_schedule(self.context.project_code, self.context.flow_id)
                raise DataSetError(message=msg)
            index_data = db.query(index_sql, {'id': self.context.flow_id})
            data_set["index_data"] = index_data

        if not data_set.get('table_name'):
            raise Exception('数据集数据表名为空')

        return data_set

    def get_datasets_by_folder_id(self):
        sql = """ select `id`, `name`, `table_name`, `content`, `type` from dap_bi_dataset where `parent_id`=%(parent_id)s"""
        with self.context.get_project_db() as db:
            datasets = db.query(sql, {'parent_id': self.context.flow_id})
            return datasets

    def is_dataset_subject(self):
        """
        判断是否为主题包
        :return:
        """
        sql = """ SELECT `id`, `name` from dap_bi_dataset_subject WHERE dataset_folder_id=%(dataset_folder_id)s LIMIT 1"""
        with self.context.get_project_db() as db:
            dataset_subject = db.query_one(sql, {'dataset_folder_id': self.context.flow_id})
            return dataset_subject if dataset_subject else False

    def dataset_subject_inspection(self, subject_id):
        sql = """SELECT subject_id, subject_name, `status` from dap_bi_dataset_subject_inspection WHERE subject_id=%(subject_id)s"""
        with self.context.get_project_db() as db:
            return db.query_one(sql, {'subject_id': subject_id})

    @compatible_local_type
    def _get_dataset_count(self, table_name=None):
        """
        获取数据集数据总数
        :return dict:
        """
        table_name = table_name if table_name else self.data_set.get('table_name')
        sql = 'SELECT count(1) as total_count FROM  ' + table_name
        with self.context.get_project_data_db() as db:
            return db.query_scalar(sql)

    def rename_table_of_local(self, new_table_name, old_table_name, is_history_table=False):
        """
        本地模式重命名表
        """
        if is_history_dataset(self.context.project_code, getattr(self.data_source, 'conn_str', None)):
            sql, fields = self.get_field_sql_text(old_table_name, is_history_table)
            self.get_datacenter_clear(sql, new_table_name, fields)
        else:
            sql = self.get_rename_sql_by_table_name(new_table_name, old_table_name)
            self.new_erp_api.local_execute_sql(sql)

    def get_rename_sql_by_table_name(self, new_table_name, old_table_name):
        data_source = get_epr_datasource_model(self.data_set_content)
        if data_source.db_type.lower() == MysoftERPDataType.Mysql.value:
            sql = '''alter table `{}` rename to `{}`'''.format(old_table_name, new_table_name)
        else:
            sql = """exec sp_rename '{}', '{}'""".format(old_table_name, new_table_name)
        return sql

    def get_datacenter_clear(self, sql, table_name, fields=None):
        res = self.new_erp_api.execute_sql(sql, table_name, fields)
        if self._is_sleep:
            sleep_time = self.data_set.get("clear_time", 30)
            time.sleep(sleep_time)
            self._is_sleep = False
        return res

    def get_field_sql_text(self, table_name, is_history_table=False):
        data_source = get_epr_datasource_model(self.data_set_content)
        fields = None
        try:
            db_type = data_source.db_type.lower()
        except:
            # MysoftNewERPConnStrModel的格式需要这样获取db_type
            db_type = data_source.conn_str.DbType.lower()
        if is_history_table:
            if db_type == MysoftERPDataType.Mysql.value:
                sql = 'select * from {} limit 1;'.format(table_name)
            else:
                sql = 'select top 1 * from {}'.format(table_name)
            data = self.new_erp_api.get_data_by_sql(sql, is_history_table)
            fields = data.get('ColumnDataType', [])
            col_name = [column.get('col_name') for column in fields] or '*'
        else:
            col_name = repository.get_columns('dap_bi_dataset_field', {'dataset_id': self.data_set.get('id'), 'type': '普通'},
                                              'col_name')
        if db_type == MysoftERPDataType.Mysql.value and col_name:
            sql = '''select `{}` from {}'''
            field = "`, `".join(col_name)
            sql = sql.format(field, table_name)
        else:
            sql = '''select [{}] from {}'''
            field = "], [".join(col_name)
            sql = sql.format(field, table_name)
        return sql, fields

    def _get_dataset_count_local(self, table_name=None):
        """
        获取数据集 数据总数
        :return dict:
        """
        sql = 'SELECT count(1) as total_count FROM  ' + table_name
        result = self.new_erp_api.get_data_by_sql(sql)
        return result.get("Data")[0]['total_count']

    def _get_data_fields(self):  # NOSONAR
        with self.context.get_project_db() as db:
            sql = (
                'select `col_name`,`data_type`,`format`,`type`,`alias_name`,`inspection_rules`,`inspection_wheres` '
                ' from dap_bi_dataset_field '
                'where `dataset_id` = %(dataset_id)s and type=\'普通\' ORDER BY `rank`'
            )
            return db.query(sql, {"dataset_id": self.data_set.get("id")})

    def _release_to_history(self, db):
        version_number, history_table_name = self.get_version_table_name()

        if is_local_storage(self.context.project_code) is False:
            sql = 'drop table if exists `%s`' % (history_table_name,)
            db.exec(sql)

        # 生成历史版本表名
        history_table_name = self.generate_dataset_table_name()

        # 原正式版rename为历史版
        if is_local_storage(self.context.project_code):
            self.rename_table_of_local(history_table_name, self.data_set.get('table_name'), True)
        else:
            self.info("正式表重命名为历史表", msg_type=NodeMsgType.DETAIL.value)
            sql = 'alter table `%s` rename to `%s` ' % (self.data_set.get('table_name'), history_table_name)
            db.exec(sql)

        # 修改dataset_version记录， 将正式版记录修改为历史版记录
        repository.update_data(
            "dap_bi_dataset_version",
            {
                "version_type": DatasetVersionType.HISTORY.value,
                "version_number": version_number,
                "table_name": history_table_name,
            },
            {"version_type": DatasetVersionType.RELEASE.value, "dataset_id": self.data_set.get("id")},
        )
        self.deal_redundant_data_version()

    def _tmp_table_to_release(self, tmp_table_name: str, inspection_id: str):
        """
        1、正式版切换成历史版，且添加历史版记录
        2、临时表rename成正式版
        3、更新正式版版本记录
        :return:
        """
        # 判断是否第一次同步, 根据dataset中原来table_name是否存在
        with self.context.get_project_data_db() as db:
            first_sync_sql = adapter_sql('table_is_exist', db_type=db.db_type).format(owner=db.database)
            table_exists = db.query_scalar(first_sync_sql, {"table_name": self.data_set.get('table_name')})
            if table_exists:
                # 1、原正式版切换成历史版本
                self._release_to_history(db)
            # 2、临时表rename为正式版
            db.exec(sql='alter table `%s` rename to `%s` ' % (tmp_table_name, self.data_set.get('table_name')))
            # 3、生成正式版记录
            version_id = self.add_dataset_version(
                1,
                self.data_set.get('table_name'),
                version_type=DatasetVersionType.RELEASE.value,
                version_content=DatasetVersionNote.Schedule.value,
            )
            # 更新巡检记录
            self.update_version_inspection_result(inspection_id, version_id)

    def _tmp_table_to_release_local(self, tmp_table_name: str, inspection_id: str):
        """
        1、正式版切换成历史版，且添加历史版记录
        2、临时表rename成正式版
        3、更新正式版版本记录
        :return:
        """
        self.info("本地模式临时表切换为正式表", msg_type=NodeMsgType.STEP.value)
        data_source = get_epr_datasource_model(self.data_set_content)
        # 判断是否第一次同步, 根据dataset中原来table_name是否存在
        if data_source.db_type.lower() == MysoftERPDataType.Mysql.value:
            first_sync_sql = '''SELECT * FROM information_schema.TABLES 
            WHERE table_schema= DATABASE() AND `table_name`="{}"'''.format(self.data_set.get("table_name"))
        elif data_source.db_type.lower() == MysoftERPDataType.DM.value:
            first_sync_sql = '''SELECT * FROM dba_tables WHERE owner = sys_context('userenv','current_schema') 
            AND TABLE_NAME = '{}' '''.format(self.data_set.get("table_name"))
        else:
            first_sync_sql = "select top 1 * from sysObjects where Id=OBJECT_ID(N'{}') and xtype='U'".format(
                self.data_set.get("table_name"))
        table_exists = self.new_erp_api.get_data_by_sql(first_sync_sql).get("Data")
        with self.context.get_project_data_db() as db:
            if table_exists:
                self._is_sleep = True
                # 1、原正式版切换成历史版本
                self.info('原正式版切换成历史版本', msg_type=NodeMsgType.DETAIL.value)
                self._release_to_history(db)
            # 2、临时表rename为正式版
            self.info('临时表rename为正式版', msg_type=NodeMsgType.DETAIL.value)
            self.rename_table_of_local(self.data_set.get('table_name'), tmp_table_name)
            # 3、生成正式版记录
            self.info('生成正式版记录', msg_type=NodeMsgType.DETAIL.value)
            version_id = self.add_dataset_version(
                1,
                self.data_set.get('table_name'),
                version_type=DatasetVersionType.RELEASE.value,
                version_content=DatasetVersionNote.Schedule.value,
            )
            # 更新巡检记录
            self.info('更新巡检记录', msg_type=NodeMsgType.DETAIL.value)
            self.update_version_inspection_result(inspection_id, version_id)

        # 判断是否第一次同步, 根据dataset中原来table_name是否存在
        self.info("本地模式下确保清洗后云端excel数据集表名不变", msg_type=NodeMsgType.STEP.value)
        first_sync_sql = "show tables like '%s';" % (self.data_set.get('table_name'))
        with self.context.get_project_data_db() as db:
            table_exists = db.exec(first_sync_sql)
            if table_exists:
                # 如果存在则删除旧版本
                self.info('如果存在则删除旧版本', msg_type=NodeMsgType.DETAIL.value)
                db.exec('drop table if exists `%s`' % (self.data_set.get('table_name'),))
            # 临时表rename为正式版
            self.info('临时表rename为正式版', msg_type=NodeMsgType.DETAIL.value)
            db.exec(sql='rename table `%s` to `%s` ' % (tmp_table_name, self.data_set.get('table_name')))

    @compatible_local_type
    def tmp_table_to_release(self, tmp_table_name: str, inspection_id: str):
        """
        替换tmp_table_rename函数，这里不在使用rename,生成表名后，更新dataset表中table_name
        :return:
        """
        # excel数据集不更新表名
        if self.data_set.get("type") in [DatasetType.Excel.value]:
            return self._tmp_table_to_release(tmp_table_name, inspection_id)
        self.table_name = release_table_name = self.generate_dataset_table_name()
        # 记录正式表名
        self.add_dataset_clean_table(release_table_name)
        # 判断是否第一次同步, 根据dataset中原来table_name是否存在
        with self.context.get_project_data_db() as db:
            first_sync_sql = adapter_sql('table_is_exist', db.db_type).format(owner=db.database)
            table_exists = db.query_scalar(first_sync_sql, {'table_name': self.data_set.get("table_name")})
            # 1.更新正式表
            self.update_release_table(db, tmp_table_name, release_table_name)
            if table_exists:
                # 2.正式版切换成历史版本
                self.release_to_history(db)

            # 生成正式版本记录
            version_id = self.add_dataset_version(
                1,
                release_table_name,
                version_type=DatasetVersionType.RELEASE.value,
                version_content=DatasetVersionNote.Schedule.value,
            )
            # 更新巡检记录
            self.update_version_inspection_result(inspection_id, version_id)

    def get_datasource_model(self, data_set_content):
        if self.data_set.get("type") in [DatasetType.Api.value]:
            return self._tmp_table_to_release_local(tmp_table_name, inspection_id)

    def tmp_table_to_release_local(self, tmp_table_name: str, inspection_id: str):
        """
        替换tmp_table_rename函数，这里不在使用rename,生成表名后，更新dataset表中table_name
        :return:
        """
        # excel数据集不更新表名
        if self.data_set.get("type") in [DatasetType.Excel.value]:
            return self._tmp_table_to_release_local(tmp_table_name, inspection_id)

        self.table_name = release_table_name = tmp_table_name
        # 判断是否第一次同步, 根据dataset中原来table_name是否存在
        data_source = get_epr_datasource_model(self.data_set_content)
        if data_source.db_type.lower() == DataSourceType.Mysql.value.lower():
            first_sync_sql = "select TABLE_NAME from INFORMATION_SCHEMA.TABLES where TABLE_NAME='{}';".format(
                self.data_set.get("table_name")
            )
        elif data_source.db_type.lower() == DataSourceType.DM.value.lower():
            first_sync_sql = """select count(*) from dba_tables 
            where owner = sys_context('userenv','current_schema') and TABLE_NAME='{}'""".format(
                self.data_set.get("table_name")
            )
        else:
            first_sync_sql = "select top 1 * from sysObjects where Id=OBJECT_ID(N'{}') and xtype='U'".format(
                self.data_set.get("table_name"))

        table_exists = self.new_erp_api.get_data_by_sql(first_sync_sql).get("Data")
        with self.context.get_project_data_db() as db:
            # 1.更新正式表
            self.update_release_table(db, tmp_table_name, release_table_name)
            if table_exists:
                # 2.正式版切换成历史版本
                self.release_to_history(db)

            # 生成正式版本记录
            version_id = self.add_dataset_version(
                1,
                release_table_name,
                version_type=DatasetVersionType.RELEASE.value,
                version_content=DatasetVersionNote.Schedule.value,
            )
            # 更新巡检记录
            self.update_version_inspection_result(inspection_id, version_id)

    def update_release_table_local(self, db, tmp_table_name, release_table_name):
        # 先把正式表处理好
        # self.info("临时表重命名为正式表", msg_type=NodeMsgType.DETAIL.value)

        # 更新dataset中table_name
        repository.update_data("dap_bi_dataset", {"table_name": release_table_name}, {"id": self.data_set.get("id")})

    @compatible_local_type
    def update_release_table(self, db, tmp_table_name, release_table_name):
        # 先把正式表处理好
        # self.info("临时表重命名为正式表", msg_type=NodeMsgType.DETAIL.value)
        sql = 'ALTER TABLE %s rename to `%s` ' % (tmp_table_name, release_table_name)
        db.exec(sql)

        # 更新dataset中table_name
        repository.update_data("dap_bi_dataset", {"table_name": release_table_name}, {"id": self.data_set.get("id")})

    @compatible_local_type
    def release_to_history(self, db):
        version_number, history_table_name = self.get_version_table_name()

        sql = 'drop table if exists `%s`' % (history_table_name,)
        db.exec(sql)
        # 这里正式版本不进行rename，直接存入历史版本
        history_table_name = self.data_set.get('table_name')

        repository.update_data(
            "dap_bi_dataset_version",
            {
                "version_type": DatasetVersionType.HISTORY.value,
                "version_number": version_number,
                "table_name": history_table_name,
            },
            {"version_type": DatasetVersionType.RELEASE.value, "dataset_id": self.data_set.get("id")},
        )
        self.deal_redundant_data_version()

    def release_to_history_local(self, db):
        version_number, history_table_name = self.get_version_table_name()

        # sql = 'drop table if exists `%s`' % (history_table_name,)
        # db.exec(sql)
        # 这里正式版本不进行rename，直接存入历史版本
        history_table_name = self.data_set.get('table_name')

        repository.update_data(
            "dap_bi_dataset_version",
            {
                "version_type": DatasetVersionType.HISTORY.value,
                "version_number": version_number,
                "table_name": history_table_name,
            },
            {"version_type": DatasetVersionType.RELEASE.value, "dataset_id": self.data_set.get("id")},
        )
        self.deal_redundant_data_version()

    def tmp_table_rename(self, tmp_table_name, inspection_id):
        """
        临时表重命名, 现在不进行rename。更改为生成好的
        :return:
        """
        table_exists_sql = "show tables like '%s';" % (self.data_set.get('table_name'))
        with self.context.get_project_data_db() as db:
            table_exists = db.exec(table_exists_sql)
            # 创建索引
            if table_exists:
                # 获取下一次历史版本的名称和表名
                version_number, history_table_name = self.get_version_table_name()

                sql = 'drop table if exists `%s`' % (history_table_name,)
                db.exec(sql)

                self.info("正式表重命名为历史版本", msg_type=NodeMsgType.DETAIL.value)
                sql = 'rename table `%s` to `%s` ' % (self.data_set.get('table_name'), history_table_name)
                db.exec(sql)

                self.info("临时表重命名为正式表", msg_type=NodeMsgType.DETAIL.value)
                sql = 'rename table `%s` to `%s` ' % (tmp_table_name, self.data_set.get('table_name'))
                db.exec(sql)

                # 正式版切换成历史版本，并更改version_number
                repository.update_data(
                    "dap_bi_dataset_version",
                    {
                        "version_type": DatasetVersionType.HISTORY.value,
                        "version_number": version_number,
                        "table_name": history_table_name,
                    },
                    {"version_type": DatasetVersionType.RELEASE.value, "dataset_id": self.data_set.get("id")},
                )
                self.deal_redundant_data_version()
            # 第一次同步数据
            else:
                sql = 'rename table `%s` to `%s` ' % (tmp_table_name, self.data_set.get('table_name'))
                db.exec(sql)

                sql = 'drop table if exists `%s`' % (tmp_table_name,)
                db.exec(sql)
            # 正式版
            version_id = self.add_dataset_version(
                1,
                self.data_set.get('table_name'),
                version_type=DatasetVersionType.RELEASE.value,
                version_content=DatasetVersionNote.Schedule.value,
            )
            self.update_version_inspection_result(inspection_id, version_id)

    def deal_redundant_data_version(self):
        return CleanDatasetVersion(self.data_set.get("id")).clean(self.context, self.data_set_content)

    def create_index(self, db, tmp_table_name):
        """
               创建索引
               :param db:
               :param tmp_table_name:
               :return:
               """
        if not self.data_set.get("index_data"):
            return

        for index in self.data_set.get("index_data"):
            if index.get('system') != 0:
                continue

            _cloumn_list = ['`%s`' % row for row in json.loads(index.get("column_list"))]
            cloumn_list = ",".join(_cloumn_list)
            try:
                if not index.get("index_name") or not cloumn_list:
                    continue
                single_index = "CREATE INDEX `{index_name}` ON `{table_name}` ({cloumn_list});".format(
                    index_name=index.get("index_name"), cloumn_list=cloumn_list, table_name=tmp_table_name
                )
                db.exec(single_index)
            except pymysql.OperationalError:
                try:
                    single_index = "CREATE FULLTEXT INDEX `{index_name}` ON `{table_name}` ({cloumn_list});".format(
                        index_name=index.get("index_name"), cloumn_list=cloumn_list, table_name=tmp_table_name
                    )
                    # 尝试建立全文索引
                    db.exec(single_index)
                except Exception as e:
                    # 索引建立失败，不影响流程，只记录日志
                    msg = "索引建立失败,原因:%s" % str(e)
                    self.info(msg, msg_type=NodeMsgType.DETAIL.value)
            except Exception as e:
                # 索引建立失败，不影响流程，只记录日志
                msg = "索引建立失败,原因:%s" % str(e)
                self.info(msg, msg_type=NodeMsgType.DETAIL.value)

    def deal_redundant_data_version_no_history(self, version_type=DatasetVersionType.RC.value):
        # 处理预发布版本和正式版本
        dataset_id = self.data_set.get("id")
        delete_verison = repository.get_data(
            "dap_bi_dataset_version", {"dataset_id": dataset_id, "version_type": version_type}, fields=["id", "table_name"]
        )
        if delete_verison and delete_verison.get("id"):
            if version_type == DatasetVersionType.RC.value:
                self.drop_table_data(delete_verison.get("table_name"))
            repository.delete_data("dap_bi_dataset_version", {"id": delete_verison.get("id")})

    def get_data_source_model(self):
        """
        获取datasource model
        :return:
        """
        data_source_id = self.data_set_content.get('data_source_id')
        if not data_source_id and self.data_set.get('type') in ["EXCEL", "UNION"]:
            return None
        if not data_source_id:
            msg = '数据源缺少data_source_id'
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise Exception(msg)
        data_source = data_source_service.get_data_source(data_source_id)
        if not data_source:
            msg = '数据源不存在，数据源id：' + str(data_source_id)
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise Exception(msg)
        data_source.conn_str_to_model()
        return data_source

    def get_epr_datasource_model(self):
        """
        获取数据服务中心数据源
        :return:
        """
        # MysoftNewERP需要获取特定的数据源
        # 获取mysoftNewErpSource
        self.info("MysoftNewERP需要获取特定的数据源: self.data_set_content:%s" % self.data_set_content)
        data_source = data_source_service.get_epr_datasource(dataset_content=self.data_set_content)
        if not data_source:
            msg = '数据服务中心未配置'
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise Exception(msg)
        self.info("MysoftNewERP需要获取特定的数据源: data_source:%s" % data_source)
        data_source.conn_str_to_model()
        return data_source

    @property
    def data_source(self):
        if not self._data_source:
            self._data_source = self.get_data_source_model()
        return self._data_source

    def generate_sql_data_set(self):
        """
        生成SQL数据集
        """
        tmp_table_name = ''
        # data_source = self.get_data_source_model()
        # 添加数据集工厂
        dataset = DataSetFactory(self, self.data_source)()
        if dataset:
            tmp_table_name = dataset.generate()
        return tmp_table_name

    def generate_excel_data_set(self):
        """
        生成EXCEL数据集
        """
        return ExcelDataSet(self).generate()

    def generate_union_dataset(self):  # NOSONAR
        """
        生成union数据集
        :return:
        """

        @stale_cache('union_depend', expire=60)
        def _get_depend_dataset(dataset_id):
            return repository.get_data('dap_bi_dataset', {'id': dataset_id}, fields=['type', 'content'])

        @stale_cache('union_source', expire=60)
        def __get_source_by_id(content):
            try:
                source_id = json.loads(content).get("data_source_id")
            except Exception as e:
                raise UserError(message=f"数据集content错误：{str(e)}")
            source = repository.get_data('dap_m_data_source', {'id': source_id}, ['type', 'conn_str'])
            if not source:
                raise UserError(message=f"数据源不存在")
            return source

        def get_running_way(source_dataset_ids, project_code):
            """
            获取组合数据集引用的数据集绑定数据源
            sql、api数据集， 如果数据源是云端则走云端，否则本地
            组合数据集：新创建的则 获取组合数据集状态，历史的则根据当前是本地还是云端
            excel数据集：当前是本地还是云端
            :return:
            """
            pre_way = None
            way = None
            for depend_dataset_id in source_dataset_ids:
                depend_dataset = _get_depend_dataset(depend_dataset_id)
                if not depend_dataset:
                    continue
                if depend_dataset.get('type') in [DatasetType.Sql.value, DatasetType.Api.value]:
                    data_source = __get_source_by_id(depend_dataset.get('content'))
                    if data_source.get('type') == DataSourceType.MysoftNewERP.value:
                        conn_str = json.loads(data_source.get('conn_str'))
                        erp_api_info_id = conn_str.get('erp_api_info_id')
                        is_master_local_db = conn_str.get('is_master_local_db')
                        if is_master_local_db in ['1', 1]:
                            way = 'local'
                        else:
                            way = f"local_{erp_api_info_id}" if erp_api_info_id else 'local'
                    else:
                        way = 'cloud'
                elif depend_dataset.get('type') == DatasetType.Union.value:
                    way = json.loads(depend_dataset.get('content')).get('running_way')
                    if not way:
                        way = 'local' if get_storage_type(
                            project_code) == DatasetStorageType.DatasetStorageOfLocal.value else 'cloud'
                else:
                    # 本地模式不判断excel数据集
                    if get_storage_type(g.code) == DatasetStorageType.DatasetStorageOfCloud.value:
                        way = DatasetStorageType.DatasetStorageOfCloud.value
                if pre_way and pre_way != way:
                    raise UserError(message="数据集落地库不同源，不能组合")
                pre_way = way
            way = way or get_storage_type(g.code)
            if way and way.startswith('local'):
                way = 'local'
            return way

        running_way = self.data_set_content.get('running_way')
        if not running_way:
            running_way = get_running_way(self.data_set_content.get('source_dataset_ids'), self.context.project_code)
            self.data_set_content['running_way'] = running_way
            repository.update_data('dap_bi_dataset', {'content': json.dumps(self.data_set_content)},
                                   {'id': self.data_set.get('id')})
        g.running_way = running_way
        if running_way == "local":
            data_source = self.get_epr_datasource_model()
            return InterfaceDataSet(self, data_source).generate()
        else:
            return MysqlDataSet(self, {}).generate()

    def generate_dataset_table_name(self) -> str:
        """
        生成数据集表名
        :return:
        """
        table_name = DATASET_TABLE_NAME_PREFIX + "_" + _get_random_chars(16)
        with self.context.get_project_data_db() as db:
            table_exists_sql = adapter_sql('table_is_exist', db.db_type).format(owner=db.database)
            table_exists = db.query_scalar(table_exists_sql, {'table_name': table_name})
            # 创建索引
            if table_exists:
                return self.generate_dataset_table_name()
            return table_name

    def get_sql_of_union(self):
        """
        获取组合数据集sql
        """
        # 组合数据集的sql需要实时获取
        replace_sql = self.data_set_content.get('replace_sql')
        try:
            dmp_replace_sql = api_request(
                self.context.project_code,
                GET_UNION_REPLACE_SQL,
                'get',
                dataset_id=self.data_set.get("id"),
            )
            replace_sql = dmp_replace_sql
        except BaseException as e:
            msg = "调用dmp openapi错误：{}".format(str(e))
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
        return replace_sql

    def get_query_sql(self):
        if self.data_set['type'] != 'SQL' and self.data_set['type'] != "API" and self.data_set['type'] != "UNION":
            raise Exception('该方法支持API、SQL数据集调用！')

        if self.data_set['edit_mode'] in ('relation', 'graph'):
            if self.data_set.get('type') == 'UNION':
                return self.get_sql_of_union()
            relation_sql_content = self.data_set['relation_sql']
            if not relation_sql_content:
                self.error('视图模式下，relation_sql为空', msg_type=NodeMsgType.DETAIL.value)
                raise Exception('视图模式下，relation_sql为空')
            else:
                return json.loads(relation_sql_content)['sql']
        elif self.data_set.get('type') == 'UNION':
            return self.get_sql_of_union()
        else:

            return self.data_set_content.get('sql')

    def get_index_sqls(self, table_name, is_storage_local=None):

        index_sqls = []
        if not self.data_set.get("index_data"):
            return index_sqls

        for index in self.data_set.get("index_data"):
            if not is_storage_local:
                index_sql = self.get_index_sql(index, "mysql", table_name)
            else:
                # 数据服务中心数据源
                data_source = get_epr_datasource_model(self.data_set_content)
                index_sql = self.get_index_sql(index, data_source.DbType, table_name)
            if not index_sql:
                continue
            index_sqls.append(index_sql)
        return index_sqls

    def get_index_sql(self, index, db_type, table_name):
        paren_left = "["
        paren_right = "]"
        if db_type.lower() == DataSourceType.Mysql.value.lower():
            paren_left = "`"
            paren_right = "`"
        elif db_type.lower() == DataSourceType.DM.value.lower():
            paren_left = "\""
            paren_right = "\""
        _cloumn_list = ['%s%s%s' % (paren_left, row, paren_right) for row in json.loads(index.get("column_list"))]
        cloumn_list = ",".join(_cloumn_list)
        if not index.get("index_name") or not cloumn_list:
            return None
        return "CREATE INDEX {left}{index_name}{right} ON {left}{table_name}{right} ({cloumn_list});".format(
            left=paren_left, index_name=index.get("index_name"), right=paren_right, cloumn_list=cloumn_list,
            table_name=table_name)

    def _record_log(self, error):
        biz_id = self.data_set.get('id', '')
        biz_name = self.data_set.get('name', '')
        dataset_type = self.data_set.get('type', '')
        log_dict = {
            'module_type': FastLogger.ModuleType.DATASET,
            'biz_type': FastLogger.BizType.DATASET_SYNC,
            'biz_id': biz_id,
            'biz_name': biz_name,
            'error_type': dataset_type + FastLogger.ErrorType.DATASET_SYNC_ERROR,
            'error_msg': error,
            'error_traceback': traceback.format_exc(),
            'org_code': self.context.project_code
        }
        FastLogger.BizErrorFastLogger(**log_dict).record()


class DataSetException(Exception):
    pass
