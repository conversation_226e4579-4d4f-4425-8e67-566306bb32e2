import datetime
import json
import random
from json import <PERSON><PERSON><PERSON>ecodeError

from components.cache import BaseCache, VersionHashCache
from components.redis import RedisCache
from dmplib.base.repository import get_db
from node.data_cache import common
from node.data_cache.cache_condition import load_conditions
from node.data_cache.sql_query_data import SqlQueryData
from node.node_execution import NodeExecution
from node.node_log_message import NodeMsgType
from node.node_result import NodeR<PERSON>ult
from dmplib import config
from dmplib.base.enums import DataSourceType
from node.data_cache.mysoft_new_erp_query_data import MysoftNewErpQueryData
from .extra_cache_redis import ExtraCacheRedis

from components.constants import DATASET_SQL_RESULT, DATASET_SQL_KEYS, DATASET_SQL_KEYS_VISIT
from dmplib import config
from typing import List

RESULT_CACHE_EXPIRE_TIME = 24 * 60 * 60

class OldDatasetCache(VersionHashCache):
    def __init__(self, conn, key):
        prefix_cache_key = config.get('Cache.released_dashboard_metadata_cache_key', 'dmp')
        cache_key = '{prefix_key}:dataset:{key}'.format(prefix_key=prefix_cache_key, key=key)
        super().__init__(conn, cache_key)


class ScheduleDatasetCache(BaseCache):
    def __init__(self, conn, key):
        prefix_cache_key = config.get('Cache.schedule_dataset_cache_prefix_key', 'schedule_sql_prefix_key')
        cache_key = '{prefix_key}:dataset:{key}'.format(prefix_key=prefix_cache_key, key=key)
        super().__init__(conn, cache_key)

class DatasetPreCacheNode(NodeExecution):
    def __init__(self, context, cur_node):
        super().__init__(context, cur_node)

        self.datasets: List[dict] = []
        self.conditions = load_conditions('Cache.direct_dataset_sql_cache_condition')
        self.conn: RedisCache = None
        self.sql_keys: [] = None
        # self.conn = RedisCache(key_prefix=self.context.project_code + ':')
        self.conn = self.get_redis_conn()

        self.today_date = datetime.datetime.now()
        self.sql_time_out = int(config.get("Cache.schedule_dataset_sql_time_out") or 60)

        # context
        self._visit_count = {}
        self.dataset: dict = None
        self.dataset_id: str = None

        self.key_cache: ScheduleDatasetCache = None
        self.key_expire_cache: OldDatasetCache = None
        self.result_cache: OldDatasetCache = None

    def get_redis_conn(self):
        extra_cache = ExtraCacheRedis(key_prefix=self.context.project_code + ':')
        is_extra_redis = getattr(extra_cache, 'is_extra_redis', 0)
        self.info(f"使用额外的redis:<{is_extra_redis}>", msg_type=NodeMsgType.DETAIL.value)
        return extra_cache

    def execution(self):
        self.info("启动数据集预缓存结点", msg_type=NodeMsgType.STEP.value)
        try:
            self.load_and_validate_datasets()
        except Exception as e:
            self.error('缓存数据集数据失败: ' + str(e), msg_type=NodeMsgType.DETAIL.value)
            return NodeResult(False, '缓存数据集数据失败')

        for dataset in self.datasets:
            try:
                self.init_dataset_context(dataset)
                self.info("缓存数据集<{dataset_name}>".format(dataset_name=self.dataset.get('name')), msg_type=NodeMsgType.STEP.value)
                self.info("数据集id: {dataset_id}".format(dataset_id=self.dataset_id), msg_type=NodeMsgType.DETAIL.value)
                self.clean_cache()
                self.load_sql_keys()
                if len(self.sql_keys) == 0:
                    self.info("需要缓存的sqlkey数量为0, 跳过缓存", msg_type=NodeMsgType.DETAIL.value)
                    continue
                self.refresh_cache()
            except Exception as e:
                self.error('缓存数据集数据失败: ' + str(e), msg_type=NodeMsgType.DETAIL.value)
        return NodeResult(True, "缓存数据集数据成功")

    @property
    def log_prefix(self):
        return "数据集<{dataset_id}, {dataset_name}>".format(dataset_id=self.dataset_id, dataset_name=self.dataset.get('name'))

    def init_dataset_context(self, dataset):
        self.dataset = dataset
        self.dataset_id = dataset.get('id')
        self._visit_count = {}
        self.init_cache_conn()

    def get_dataset_id_from_redis_cache(self):
        # 从redis缓存中获取涉及的数据集id
        # 过去30天的访问keys
        last_days = 30
        days_str = [
            (datetime.datetime.now() - datetime.timedelta(days=i)).strftime('%Y-%m-%d') for i in range(0, last_days)
        ]
        dataset_ids_set = set()
        for day in days_str:
            visit_key = '{prefix}{date}'.format(prefix=DATASET_SQL_KEYS_VISIT,  date=day)
            visit_cache = ScheduleDatasetCache(self.conn, visit_key)
            keys = visit_cache.hkeys() or []
            for key in keys:
                # key: '3a0523fc-f78c-bd0f-ec6b-b5b6b1d9731f_907dd73c3573c9ec2db3aa365da2ca3dea3cf8a2'
                if isinstance(key, bytes):
                    key = key.decode()
                dataset_id = key[:36]
                dataset_ids_set.add(dataset_id)
        self.info(f"缓存关系分析到相关数据集个数, 总计: {len(dataset_ids_set)}", msg_type=NodeMsgType.DETAIL.value)
        return list(dataset_ids_set)

    def load_and_validate_datasets(self):
        self.info("加载数据集", msg_type=NodeMsgType.STEP.value)
        # sql = "select id, name, type, content from dataset where (connect_type != '直连' or connect_type is null) and type in ('SQL', 'EXCEL', 'UNION', 'TEMPLATE', 'LABEL')"
        # with self.context.get_project_db() as db:
        #     rows = db.query(sql)

        # 以前的逻辑是先查询所有的数据集，时间太长太慢
        # 现在改成从redis中直接取出相关的数据集id
        dataset_ids_list = self.get_dataset_id_from_redis_cache()
        if dataset_ids_list:
            sql = "select `id`, `name`, `type`, `content` from `dap_bi_dataset` where (`connect_type` != '直连' or connect_type is null) and `type` in ('SQL', 'EXCEL', 'UNION', 'TEMPLATE', 'LABEL') and `id` in %(ids)s"
            with self.context.get_project_db() as db:
                rows = db.query(sql, {'ids': dataset_ids_list})
        else:
            rows = []

        self.info("总调度模式数据集数量: {count}".format(count=len(rows)), msg_type=NodeMsgType.DETAIL.value)
        datasets = []
        for dataset in rows:
            if not dataset.get("content"):
                self.error(f"数据集<{dataset.get('id')}>内容content不能为空：{str(dataset)}", msg_type=NodeMsgType.DETAIL.value)
                continue
            try:
                json.loads(dataset.get("content"))
            except JSONDecodeError:
                self.error(f"数据集<{dataset.get('id')}>内容content解析失败: {str(dataset)}", msg_type=NodeMsgType.DETAIL.value)
                continue
            datasets.append(dataset)
        self.datasets = datasets
        self.info("过滤数据集内容解析失败后的数据集数量: {count}".format(count=len(self.datasets)), msg_type=NodeMsgType.DETAIL.value)

    @property
    def key_expire_time(self):
        return (datetime.datetime.now() + datetime.timedelta(days=1) + datetime.timedelta(hours=random.randint(1, 24))).timestamp()

    def get_data_source(self, dataset_info):
        from dmplib.data_source.services.data_source_service import get_data_source
        try:
            data_source_id = json.loads(dataset_info.get('content', '{}')).get('data_source_id', '')
        except Exception as e:
            self.error(f'根据数据集获取数据源失败：数据集id: {dataset_info.get("id")}, 原因是：{str(e)}')
            data_source_id = ''
        model = get_data_source(data_source_id)
        return model

    def refresh_cache(self):
        data_source_model = self.get_data_source(self.dataset)
        if not data_source_model:
            self.error(f'根据数据集获取数据源model为空，跳过这个数据集的缓存更新：数据集id: {self.dataset_id}')
            return
        if data_source_model.type == DataSourceType.MysoftNewERP.value:
            client = MysoftNewErpQueryData(data_source_model, dataset_node=self)
        else:
            client = SqlQueryData(get_db(self.context.project_code, 'data'))
        succ = 0
        failed = 0
        skip = 0
        for sql_key, content in self.sql_keys.items():
            sql = content.get('sql')
            try:
                data, _ = client.get_query_data(sql)
                if data:
                    # 设置结果缓存
                    self.key_expire_cache.set_prop(sql_key, self.key_expire_time)
                    self.result_cache.set_prop(sql_key, data, expire_flag=True)
                    succ += 1
                    continue
                skip += 1
            except Exception as e:
                self.error(f"缓存<{sql}>失败, 原因: {str(e)}", msg_type=NodeMsgType.DETAIL.value)
                failed += 1
                continue
        client.end()
        self.result_cache.set_expire(RESULT_CACHE_EXPIRE_TIME)
        self.info(f"缓存结果: 成功<{succ}>, 失败<{failed}>, 跳过<{skip}>, 总计<{len(self.sql_keys)}>", msg_type=NodeMsgType.DETAIL.value)

    def load_sql_keys(self):
        self.sql_keys = self.key_cache.hgetall()
        if self.sql_keys is None:
            self.sql_keys = {}
            return
        ori_count = len(self.sql_keys)
        self.filter_sql_keys()
        self.info("加载到/过滤后的sql数量: <{ori_count}, {count}>".format(ori_count=ori_count, count=len(self.sql_keys)), msg_type=NodeMsgType.DETAIL.value)

    def filter_sql_keys(self):
        """过滤出达到条件的sql_key"""
        sql_keys = {}
        now_date = datetime.datetime.now()
        for k, content in self.sql_keys.items():
            try:
                expire_time = self.key_expire_cache.hget(k)
                if (isinstance(expire_time, float) and datetime.datetime.fromtimestamp(expire_time) <= now_date):
                    self.key_cache.hdel(k)
                    self.key_expire_cache.hdel(k)
                    continue
                content = json.loads(content)
                sql = content.get('sql')
                if common.is_uncache_sql(sql):
                    self.key_cache.hdel(k)
                    self.key_expire_cache.hdel(k)
                    continue
                if not self.reach_conditions(k):
                    continue
                sql_keys[k] = content
            except JSONDecodeError:
                self.error("sqlkey<{sql_key}>内容解析失败: {content}".format(sql_key=k, content=content), msg_type=NodeMsgType.DETAIL.value)
                continue
            except Exception as e:
                self.error("sqlkey<{sql_key}>过滤失败，内部错误: {msg}".format(sql_key=k, msg=str(e)), msg_type=NodeMsgType.DETAIL.value)
                continue
        self.sql_keys = sql_keys

    def reach_conditions(self, sql_key):
        for expect_count, day in self.conditions:
            count = self.nday_visit_count(sql_key, day)
            if count >= expect_count:
                return True
        return False

    def nday_visit_count(self, sql_key, day) -> int:
        """前n天的sql访问次数"""
        total = 0
        # for i in range(1, day+1):
        for i in range(0, day):
            date = (self.today_date - datetime.timedelta(days=i)).strftime("%Y-%m-%d")
            total += int(self.get_visit_count(sql_key, date))
        return total

    def get_visit_count(self, sql_key, date):
        count = self._visit_count.get((sql_key, date))
        if count is None:
            visit_key = '{prefix}{date}'.format(prefix=DATASET_SQL_KEYS_VISIT, dataset_id=self.dataset_id,
                                                             date=date)
            visit_cache = ScheduleDatasetCache(self.conn, visit_key)
            count = visit_cache.hget('{dataset_id}_{sql_key}'.format(dataset_id=self.dataset_id, sql_key=sql_key))
            if count is None:
                count = 0
            self._visit_count[(sql_key, date)] = count
        return count

    def clean_cache(self):
        self.result_cache.remove()

    def init_cache_conn(self):
        sql_keys_key = '{prefix}{dataset_id}'.format(prefix=DATASET_SQL_KEYS, dataset_id=self.dataset_id)
        result_key = '{prefix}{dataset_id}'.format(prefix=DATASET_SQL_RESULT, dataset_id=self.dataset_id)

        self.key_cache = ScheduleDatasetCache(self.conn, sql_keys_key)
        self.key_expire_cache = OldDatasetCache(self.conn, sql_keys_key)
        self.result_cache = OldDatasetCache(self.conn, result_key)
