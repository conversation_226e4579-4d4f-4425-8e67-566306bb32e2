from typing import Tuple, List, Union

from components.postgresql_wrapper import SimplePostgreSQL
from components.timeout import CustomTimeoutError, timeout
from dmplib.base.repository import SimpleMysql

class SqlQueryData:
    __slots__ = ['connection_db', 'sql_time_out']

    def __init__(self, connection_db: Union[SimpleMysql, SimplePostgreSQL], timeout=60):
        self.connection_db = connection_db
        self.sql_time_out = timeout

    def end(self):
        self.connection_db.end()

    def get_data(self, sql):
        return self.connection_db.query(sql)

    def get_data_with_timeout(self, sql):
        get_data_with_timeout = timeout(self.sql_time_out)(self.get_data)
        return get_data_with_timeout(sql)

    def get_query_data(self, sql) -> Tuple[List, str]:
        try:
            return self.get_data_with_timeout(sql), sql
        except CustomTimeoutError as e:
            msg = "运行sql时长超过{}秒，{}".format(str(self.sql_time_out), "sql执行超时")
            raise Exception(msg) from e
        except Exception as e:
            msg = "直连数据集查询数据错误：{}，sql：{}".format(str(e), sql)
            raise Exception(msg) from e
