import redis as pyredis
import logging as logger

from components.redis import RedisCache
from dmplib import config

g_extra_redis_connection_pool = None


class ExtraCacheRedis(RedisCache):
    def __init__(self, key_prefix=None, connect_timeout=None):
        super().__init__(key_prefix, connect_timeout)
        extra_cache = ExtraCacheRedis.create_priority_extra_redis()
        self._connection = extra_cache._connection
        self.is_extra_redis = getattr(extra_cache, 'is_extra_redis', 0)

    @staticmethod
    def create_priority_extra_redis():
        # 先检查组件SQL缓存专用的redis，乜有， 在使用默认的redis
        # 如果没有额外的redis，返回默认的redis实例
        default_cache = RedisCache()
        connect_timeout = default_cache.connect_timeout
        host = config.get('CacheRedis.host')
        port = config.get('CacheRedis.port')

        # 如果配置里面里面写了配置，就默认优先使用配置的额外的redis
        if host and port:
            global g_extra_redis_connection_pool
            if g_extra_redis_connection_pool is None:
                g_extra_redis_connection_pool = pyredis.ConnectionPool.from_url(
                    url='redis://{username}:{password}@{host}:{port}/{db}'.format(
                        host=host,
                        port=port,
                        username=config.get('CacheRedis.username') or '',
                        db=int(config.get('CacheRedis.db', 2)),
                        password=config.get('CacheRedis.password') or ''
                    ),
                    socket_timeout=connect_timeout,
                    max_connections=50,
                )
            extra_cache = RedisCache()
            extra_cache._connection = pyredis.StrictRedis(connection_pool=g_extra_redis_connection_pool)
            if ExtraCacheRedis._ping(extra_cache._connection):
                setattr(extra_cache, 'is_extra_redis', 1)
                return extra_cache
            else:
                return default_cache
        else:
            return default_cache

    @staticmethod
    def _ping(conn):
        try:
            conn.ping()
            return True
        except Exception as e:
            logger.error(f'ping extra redis error: {str(e)}')
            return False
