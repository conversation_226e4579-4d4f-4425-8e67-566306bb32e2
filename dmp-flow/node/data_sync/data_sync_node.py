#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/26.
"""
import datetime
import json

from components.data_x import Job, JobODPSReader, JobODPSReaderParameter, JobMysqlWriter, JobMysqlWriterParameter, \
    JobContent
from dmplib.base.enums import DataSourceType
from dmplib.constants import DATA_SOURCE_ODPS_ID
from dmplib.data_source.services import data_source_service
from dmplib.flow.models import SyncNodeContentModel

from node.node_execution import NodeExecution
from node.node_result import NodeResult


class DataSyncNode(NodeExecution):
    def __init__(self, context, cur_node):
        """
        数据同步节点
        :param context:
        :param cur_node:
        """
        self.content = None
        self.target_source = None
        self.target_source_conn = None
        super().__init__(context, cur_node)

    def _load_content(self):
        if not self.cur_node.content:
            raise DataSyncException('同步节点内容为空')
        try:
            content = json.loads(self.cur_node.content)
        except json.JSONDecodeError as e:
            raise DataSyncException('同步节点内容解析错误：' + str(e))
        self.content = SyncNodeContentModel(**content)
        # 源数据源只支持ODPS到Project Data
        # 目标数据源支持所有MySQL
        if self.content.source.source_id != DATA_SOURCE_ODPS_ID:
            raise DataSyncException('源表只支持ODPS数据源')
        if not self.context.get_odps().exist_table(self.content.source.table):
            raise DataSyncException('源表不存在')

        self.target_source = data_source_service.get_data_source(self.content.target.source_id)
        self.target_source.conn_str_to_model()

        if self.target_source.type != DataSourceType.Mysql.value:
            raise DataSyncException('目标表只支持MySQL数据源')

    def _get_source_partition(self):
        if not self.content.source.partition or not isinstance(self.content.source.partition, dict):
            return None
        return ','.join([k + '=' + (v if v != '${datetime}' else datetime.datetime.now().strftime('%Y%m%d'))
                         for k, v in self.content.source.partition.items()])

    def execution(self):
        self._load_content()
        partition_spec = self._get_source_partition()
        job = Job()
        job_content = JobContent()
        job.setting.errorLimit = int(self.content.error_limit)
        reader_parameter = JobODPSReaderParameter()
        reader_parameter.set_odps_config(self.context.get_odps_config())
        reader_parameter.table = self.content.source.table
        reader_parameter.column = self.content.source.column
        if partition_spec:
            reader_parameter.partition.append(partition_spec)
        job_content.reader = JobODPSReader(parameter=reader_parameter)
        writer_parameter = JobMysqlWriterParameter()
        writer_parameter.set_connection(self.target_source.conn_str.get_dict(), [self.content.target.table])
        writer_parameter.writeMode = self.content.target.write_mode
        writer_parameter.column = self.content.target.column
        if self.content.target.pre_sql:
            writer_parameter.preSql.append(self.content.target.pre_sql)
        if self.content.target.post_sql:
            writer_parameter.postSql.append(self.content.target.post_sql)
        job_content.writer = JobMysqlWriter(parameter=writer_parameter)
        job.content.append(job_content)
        self.info(job.run(self.cur_node.node_instance.instance_id))
        return NodeResult(True, '同步成功')


class DataSyncException(Exception):
    def __init__(self, *args, **kwargs):
        pass
