#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by wangl10 on 2016/11/3.
"""
import datetime
import json
import math
from multiprocessing.pool import ThreadPool

from dmplib.constants import (DATA_CLEAN_PRIMARY_TABLE_NAME,
    DATA_CLEAN_PRIMARY_KEY_NAME,
    DATA_CLEAN_PRIMARY_TO_ORGANIZATION_TABLE_NAME,
    DATA_CLEAN_ORGANIZATION_KEY_NAME,
    GEO_CODE_TABLE_NAME,
    DATA_CLEAN_ORGANIZATION_CODE_TABLE_NAME)
from node.label_cleaning.label_check_data_cleaning import LabelCheckDataCleaning
from node.label_cleaning.label_data_cleaning import LabelDataCleaning
from node.label_cleaning.label_detail_data_cleaning import LabelDetailDataCleaning
from node.label_cleaning.label_statistics_data_cleaning import LabelStatisticsDataCleaning
from node.node_execution import NodeExecution
from node.node_result import NodeResult


class LabelCleaningNode(NodeExecution):
    def __init__(self, context, cur_node):

        """
        标签清洗节点
        :param flow.flow_context.FlowContext context:
        :param node.Node cur_node:
        """
        super().__init__(context, cur_node)
        self.label_id = context.flow_id
        self.label = None
        self.indicator_template_id = None
        self.label_table_name = None
        self.label_org_id = None
        self.label_mode = None
        self.indicators = []
        self.indicator_table_names = []
        self.indicator_col_names = []
        self.dimension_indicators = []
        self.key_tables = None
        self.tmp_label_table_name = None
        self.organ_code = None
        # 别名序列，使用后需要加1
        self.alias_no = 1
        self._pool = ThreadPool(processes=3)

    def pool_error_callback(self, processes_error):
        try:
            self._pool.terminate()
            raise processes_error
        except Exception as e:
            self.exception(e)
            raise e

    def _init_data(self):
        self.label = self._get_label(self.label_id)
        self.indicator_template_id = self.label.get('tmpl_id')
        self.label_table_name = self.label.get('label_tablename')
        self.label_org_id = self.label.get('org_id')
        self.label_mode = self.label.get('mode')
        self.key_tables = self._get_key_tables(self.indicator_template_id)
        self.tmp_label_table_name = 'tmp_' + self.label_table_name
        self.organ_code = self._get_organ_code(self.label_org_id)

    def _get_organ_code(self, organ_id):
        sql = 'SELECT `code` FROM  `' + DATA_CLEAN_ORGANIZATION_CODE_TABLE_NAME + '` WHERE id= %(organ_id)s '
        with self.context.get_project_db() as db:
            organ_code = db.query_scalar(sql, {'organ_id': organ_id})
        if not organ_code:
            raise self.context.UserException('标签所属机构code为空')
        return organ_code

    def execution(self):
        try:
            self._init_data()

            # 加载指标信息
            self._load_indicator()

            # 创建标签数据临时表
            self._generate_label_tmp_table()

            # 多进程处理标签清洗
            dc = self._pool.apply_async(self._label_data_cleaning, error_callback=self.pool_error_callback)
            ddc = self._pool.apply_async(self._label_detail_data_cleaning, error_callback=self.pool_error_callback)
            cc = self._pool.apply_async(self._label_check_data_cleaning, error_callback=self.pool_error_callback)
            sc = self._pool.apply_async(self._label_statistics_data_cleaning, error_callback=self.pool_error_callback)
            self._pool.close()
            self._pool.join()
            if dc.successful():
                self.info('标签明细清洗处理完成')
            if ddc.successful():
                self.info('标签明细数据集清洗处理完成')
            if cc.successful():
                self.info('标签数据巡检清洗处理完成')
            if sc.successful():
                self.info('标签对比分析清洗处理完成')
        except Exception as e:
            self.exception(e)
            return NodeResult(False, str(e))
        finally:
            # 删除临时表
            self._drop_tmp_table()

        return NodeResult(True, '清洗成功')

    def _load_indicator(self):
        self.indicators = self._get_all_indicator()
        for indicator in self.indicators:
            odps_table = indicator.get('odps_table')
            odps_field = indicator.get('odps_field')
            self.indicator_table_names.append(odps_table)
            self.indicator_col_names.append(odps_table + '.' + odps_field)
            if indicator.get('type') == '维度':
                indicator['col_name'] = odps_table + '_' + odps_field
                self.dimension_indicators.append(indicator)

        self.indicator_table_names = list(set(self.indicator_table_names))
        fact_master_table = self.key_tables.get(DATA_CLEAN_PRIMARY_TABLE_NAME)
        if fact_master_table in self.indicator_table_names:
            self.indicator_table_names.remove(fact_master_table)
        self.indicator_col_names = list(set(self.indicator_col_names))

    def _generate_label_tmp_table(self):
        """
        创建标签临时数据表
        :return:
        """
        col_names = [col_name + ' AS ' + col_name.replace('.', '_') for col_name in self.indicator_col_names]
        sql = 'CREATE TABLE IF NOT EXISTS {tmp_label_table_name} AS  ' \
              'SELECT {master_table}.{master_table_id},{indicator_col_names} FROM {master_table} ' \
              ' LEFT OUTER JOIN  {master_to_organization_table}' \
              ' ON {master_table}.{master_table_id} = {master_to_organization_table}.{master_table_id} ' \
              ' LEFT OUTER JOIN  (SELECT id FROM {tmp_organ_table_name} WHERE code LIKE \'{org_code}%\') lable_organ' \
              ' ON lable_organ.id = {master_to_organization_table}.{org_id} ' \
              ''.format(master_table=self.key_tables.get(DATA_CLEAN_PRIMARY_TABLE_NAME),
                        master_table_id=DATA_CLEAN_PRIMARY_KEY_NAME,
                        master_to_organization_table=self.key_tables.get(DATA_CLEAN_PRIMARY_TO_ORGANIZATION_TABLE_NAME),
                        org_id=DATA_CLEAN_ORGANIZATION_KEY_NAME,
                        org_code=self.organ_code,
                        tmp_label_table_name=self.tmp_label_table_name,
                        tmp_organ_table_name=DATA_CLEAN_ORGANIZATION_CODE_TABLE_NAME,
                        indicator_col_names=','.join(col_names))

        where = " WHERE lable_organ.id IS NOT NULL"
        for table_name in self.indicator_table_names:
            sql += ' LEFT OUTER JOIN {table_name} ' \
                   ' ON {table_name}.master_id = {master_table}.{master_table_id} ' \
                   ' AND {table_name}.{org_id}=lable_organ.id ' \
                   ''.format(table_name=table_name,
                             master_table=self.key_tables.get(DATA_CLEAN_PRIMARY_TABLE_NAME),
                             master_table_id=DATA_CLEAN_PRIMARY_KEY_NAME,
                             org_id=DATA_CLEAN_ORGANIZATION_KEY_NAME)

        if self.label_mode == '基础':
            where += self._get_basic_label_condition()
        elif self.label_mode == '高级':
            condition = self._get_advanced_label_condition()
            if condition:
                condition, join_tables = condition
                where += condition
                sql += ' '.join(list(set(join_tables)))
        else:
            raise Exception('未知标签类型')
        sql += where + ';'
        self.info('标签临时表生成SQL:' + sql)
        instance = self.context.get_odps().run_sql(sql)

        # 增加log view日志打印，@chenchao 20170307
        self.info("Instance ID = " + instance.id)
        self.info("Log view: " + instance.get_logview_address())

        instance.wait_for_success()

    def _get_basic_label_condition(self):
        where = ''
        label_dimensions = self._get_label_dimensions()
        if label_dimensions:
            dimension_group = self.context.list_dict_group_by(label_dimensions, ['odps_table', 'odps_field'])
            if dimension_group:
                for key, dimensions in dimension_group.items():
                    if len(dimensions) > 1:
                        values = ','.join(['\'' + dim.get('dimension_name') + '\'' for dim in dimensions])
                        where += ' AND ' + key + ' IN (' + values + ')'
                    else:
                        where += ' AND ' + key + ' =\'' + dimensions[0].get('dimension_name') + '\''
        return where

    def _get_advanced_label_condition(self):
        """
        获取高级标签条件
        :return:
        """
        condition = self._parsing_expression_group_dict()
        if not condition:
            return None
        expression_group_dict, join_tables = condition
        logical_expression = self.label.get('logical_expression')
        where = ''
        if logical_expression and expression_group_dict:
            tmp_exp_list = logical_expression.split(' ')
            where += ' AND '
            for part_exp in tmp_exp_list:
                p_exp = self.get_expression_content(part_exp)
                if not p_exp:
                    where += ' ' + part_exp
                    continue
                flag, v_exp = p_exp
                if flag == 'g':
                    where += ' ' + expression_group_dict.get(v_exp, '')
        return where, join_tables

    def _parsing_expression_group_dict(self):
        """
        解析标签表达式组
        :return:
        """
        expression_group = self._get_label_expression_group()
        if not expression_group:
            return None
        expression_group_dict = {}
        indicator_dict = self.context.list_dict_group_by(self.indicators, 'id')
        dimension_dict = None
        join_tables = []
        for exp in expression_group:
            exp_type = exp.get('type')
            expression = json.loads(exp.get('expression'))
            if exp_type == '维度':
                if not dimension_dict:
                    dimension_dict = self._get_all_dimension_dict()
                condition = self._parsing_dimension_expression(expression, indicator_dict, dimension_dict)
            elif exp_type == '描述':
                condition = self._parsing_text_expression(expression, indicator_dict)
            elif exp_type == '数值':
                condition = self._parsing_number_expression(expression, indicator_dict)
            elif exp_type == '日期':
                condition = self._parsing_date_expression(expression, indicator_dict)
            elif exp_type == '地址':
                condition, join_table = self._parsing_address_expression(expression, indicator_dict)
                join_tables.append(join_table)
            else:
                condition = None
            expression_group_dict[exp.get('id')] = condition
        return expression_group_dict, join_tables

    def is_numeric(self, var):
        """
        判断是否为数字型数据，包括字符串型数字，整数和浮点数都包括
        :return:
        """
        try:
            float(var)
            return True
        except ValueError:
            return False

    def _parsing_number_expression(self, expression, indicator_dict):
        """
        解析数值表达式
        :param dict expression:
        :param dict indicator_dict:
        :return:
        """
        expression = expression.get('expression').split(' ')  # 根据空格拆分表达式
        real_exp = ''
        sign_list = ['+', '-']
        sign_flag = self._check_sign_flag(sign_list, expression)
        for exp in expression:
            # 解析表达式
            content = self.get_expression_content(exp)
            if not content:
                real_exp += ' ' + exp
                continue
            flag, v = content
            if flag == 'i':
                indicator = indicator_dict.get(v)[0]
                if sign_flag:
                    field_name = ' regexp_replace(' + indicator.get('odps_table') + '.' + indicator.get('odps_field') + ' , "^$", "0", 0 ) '
                else:
                    field_name = ' ' + indicator.get('odps_table') + '.' + indicator.get('odps_field')
                real_exp += field_name
            elif flag == 'v':
                if self.is_numeric(v):
                    real_exp += v
                else:
                    real_exp += ' \'' + v + '\''
            else:
                real_exp += ' ' + exp
        return real_exp

    def _check_sign_flag(self, sign_list, expression):
        for sign_item in sign_list:
            if sign_item in expression:
                return True
        return False

    @staticmethod
    def _parsing_dimension_expression(expression, indicator_dict, dimension_dict):
        """
        解析维度表达式
        :param dict expression:
        :param dict indicator_dict:
        :param dict dimension_dict:
        :return:
        """
        indicator_id = expression.get('indicator_id')
        indicator = indicator_dict.get(indicator_id)[0]
        col = indicator.get('odps_table') + '.' + indicator.get('odps_field')  # 条件列
        value = expression.get('value')
        value = ','.join(['\'' + dimension_dict.get(v, '') + '\'' for v in value])
        operator = expression.get('operator').lower()
        if operator in ['in', 'not in']:
            value = '(' + value + ')'
        return ' %s %s %s ' % (col, operator, value)

    @staticmethod
    def _parsing_text_expression(expression, indicator_dict):
        """
        解析描述表达式
        :param dict expression:
        :param dict indicator_dict:
        :return:
        """
        indicator_id = expression.get('indicator_id')
        indicator = indicator_dict.get(indicator_id)[0]
        col = indicator.get('odps_table') + '.' + indicator.get('odps_field')  # 条件列
        value = expression.get('value')
        value = ','.join(['\'' + v + '\'' for v in value])
        operator = expression.get('operator').lower()
        if operator in ['in', 'not in']:
            value = '(' + value + ')'
        return ' %s %s %s ' % (col, operator, value)

    def _parsing_address_expression(self, expression, indicator_dict):
        """
        解析地址表达式
        :param dict expression:
        :param dict indicator_dict:
        :return:
        """
        indicator_id = expression.get('indicator_id')
        indicator = indicator_dict.get(indicator_id)[0]
        col = indicator.get('odps_table') + '.' + indicator.get('odps_field')  # 条件列
        center = expression.get('center')
        distance = int(expression.get('radius'))
        map_radius = 6378.137  # 地球半球，单位公里
        join_table = ' LEFT OUTER JOIN {geo_table} {geo_table}{alias_no} ' \
                     ' ON md5({col})= {geo_table}{alias_no}.id ' \
                     ''.format(geo_table=GEO_CODE_TABLE_NAME,
                               col=col,
                               alias_no=str(self.alias_no))
        condition = ' {geo_table}{alias_no}.id IS NOT NULL ' \
                    ' AND round(2 * asin(sqrt(pow(sin((({lat}*{pi}/180)-({geo_table}{alias_no}.latitude*{pi}/180))' \
                    ' / 2), 2)' \
                    ' + cos(({lat}*{pi}/180)) * ' \
                    'cos(({geo_table}{alias_no}.latitude*{pi}/180)) * ' \
                    'pow(sin(( ({lng}*{pi}/180)  -  ({geo_table}{alias_no}.longitude*{pi}/180)  ) / 2), 2))) * ' \
                    '{radius} * 1000 )<={distance} ' \
                    ''.format(lng=str(center[0]),
                              lat=str(center[1]),
                              pi=str(math.pi),
                              distance=str(distance),
                              geo_table=GEO_CODE_TABLE_NAME,
                              alias_no=str(self.alias_no),
                              radius=str(map_radius))
        self.alias_no += 1
        return condition, join_table

    @staticmethod
    def _parsing_date_expression(expression, indicator_dict):
        """
        解析日期表达式
        :param dict expression:
        :param dict indicator_dict:
        :return:
        """
        indicator_id = expression.get('indicator_id')
        indicator = indicator_dict.get(indicator_id)[0]
        col = indicator.get('odps_table') + '.' + indicator.get('odps_field')  # 条件列
        mode = int(expression.get('mode'))
        value = expression.get('value')
        if mode == 1:
            # 指定日期
            operator = expression.get('operator').lower()
            if operator == 'in' or operator == 'not in':
                value = '(' + ','.join(['\'' + str(v) + '\'' for v in value]) + ')'
            else:
                value = '\'' + str(value[0]) + '\''
            return ' %s %s %s ' % (col, operator, value)
        elif mode == 2:
            # 指定日期范围
            return ' %s >= \'%s\' AND %s <= \'%s\' ' % (col, value[0], col, value[1])
        elif mode == 3:
            # 距今天多少天
            days = int(value[0])
            end_date = datetime.datetime.now()
            start_date = end_date + datetime.timedelta(days=-1 * days)
            return ' %s >= \'%s\' AND %s <= \'%s\'  ' % (col,
                                                         start_date.strftime('%Y-%m-%d 00:00:00'),
                                                         col,
                                                         end_date.strftime('%Y-%m-%d 23:59:59'))
        else:
            return None

    @staticmethod
    def get_expression_content(exp):
        if not exp or len(exp) < 4:
            return False
        flags = ['i', 'd', 'v', 'g', 'm', 't']
        for flag in flags:
            prefix = '{' + flag + ':'
            if exp[0:3] == prefix and exp[-1] == '}':
                return flag, exp[3:-1]
        return False

    def _label_data_cleaning(self):
        LabelDataCleaning(self).cleaning()

    def _label_detail_data_cleaning(self):
        LabelDetailDataCleaning(self).cleaning()

    def _label_check_data_cleaning(self):
        LabelCheckDataCleaning(self).cleaning()

    def _label_statistics_data_cleaning(self):
        LabelStatisticsDataCleaning(self).cleaning()

    def _drop_tmp_table(self):
        self.info('删除临时表：' + self.tmp_label_table_name)
        self.context.get_odps().execute_sql(
            'DROP TABLE IF EXISTS {tmp_label_table_name};'.format(tmp_label_table_name=self.tmp_label_table_name))

    def _get_label_dimensions(self):
        """
        获取标签所选的维度
        :return:
        """
        sql = 'SELECT ' \
              'i.id AS indicator_id,' \
              'i.`name` AS indicator_name,' \
              'i.odps_table,' \
              'i.odps_field ,' \
              'd.id AS  dimension_id,' \
              'd.`name` AS dimension_name ' \
              'FROM dap_bi_label_dimension AS ld ' \
              'INNER JOIN dap_bi_dimension AS d ON ld.dimension_id=d.id ' \
              'INNER JOIN `dap_bi_indicator` AS i ON d.indicator_id=i.id AND i.`type`=\'维度\' ' \
              'WHERE ld.label_id=%(label_id)s AND i.tmpl_id=%(tmpl_id)s'

        with self.context.get_project_db() as db:
            data = db.query(sql, {'label_id': self.label_id, 'tmpl_id': self.indicator_template_id})
        return data

    def _get_label_expression_group(self):
        sql = 'SELECT `id`,`type`,`expression` FROM dap_bi_label_expression_group WHERE label_id=%(label_id)s'
        with self.context.get_project_db() as db:
            data = db.query(sql, {'label_id': self.label_id})
        return data

    def _get_label(self, label_id):
        """
        获取标签信息
        :param label_id
        :return:
        """
        sql = 'SELECT ' \
              '`label_id`,`tmpl_id`,`org_id`,`label_tablename`,`mode`,`logical_expression`,`sync_detail` ' \
              'FROM dap_bi_label ' \
              'WHERE label_id=%(label_id)s'
        with self.context.get_project_db() as db:
            label = db.query_one(sql, {'label_id': label_id})
        if not label:
            raise self.context.UserException('标签不存在:' + label_id)
        elif not label.get('tmpl_id'):
            raise self.context.UserException('标签未选择对应模板')
        elif not label.get('org_id'):
            raise self.context.UserException('标签所属机构为空')
        elif not label.get('label_tablename'):
            raise self.context.UserException('标签表名未生成')
        else:
            return label

    def _get_key_tables(self, template_id):
        key_table_names = ['fact_360', 'fact_master', 'fact_master_organization', 'label_360']
        sql = 'SELECT `name`,`table_name` FROM dap_bi_key_table WHERE tmpl_id=%(tmpl_id)s'
        with self.context.get_project_db() as db:
            data = db.query(sql, {'tmpl_id': template_id})
        if data:
            if sorted([table.get('name') for table in data]) != key_table_names:
                raise self.context.UserException('关键表配置不完整')
            tables = {}
            for table in data:
                tables[table.get('name')] = table.get('table_name')
            return tables
        raise self.context.UserException('标签对应模板缺少关键表配置')

    def _get_all_indicator(self):
        """
        获取所有已配置的指标
        :return:
        """
        sql = 'SELECT id,`name`,odps_table,odps_field,`type` FROM `dap_bi_indicator` ' \
              'WHERE tmpl_id=%(tmpl_id)s ' \
              'AND LENGTH(IFNULL(odps_table,\'\'))>0 AND  LENGTH(IFNULL(odps_field,\'\'))>0'
        with self.context.get_project_db() as db:
            data = db.query(sql, {'tmpl_id': self.indicator_template_id})
        return data

    def _get_all_dimensions(self):
        sql = 'SELECT a.id,a.`name` FROM dap_bi_dimension AS a ' \
              'INNER JOIN dap_bi_indicator AS b ON a.indicator_id=b.id  AND b.tmpl_id=%(tmpl_id)s'
        with self.context.get_project_db() as db:
            data = db.query(sql, {'tmpl_id': self.indicator_template_id})
        return data

    def _get_all_dimension_dict(self):
        dimensions = self._get_all_dimensions()
        if not dimensions:
            return False
        dim_dict = {}
        for dim in dimensions:
            dim_dict[dim.get('id')] = dim.get('name')
        return dim_dict

    def __getstate__(self):
        self_dict = self.__dict__.copy()
        del self_dict['_pool']
        return self_dict
