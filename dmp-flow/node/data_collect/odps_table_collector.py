#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by wangl10 on 2016/9/21.
"""
from odps.core import ODPS
from odps.errors import ODPSError
from odps.models import Column, Partition, Schema
from odps.models.table import TableSchema

from components.data_x import Job, JobContent, JobODPSWriterParameter, JobODPSReaderParameter, JobODPSReader, \
    JobODPSWriter
from dmplib.constants import ODPS_ENDPOINT
from dmplib.data_source.models import ODPSConnStrModel
from dmplib import config
from node.data_collect.collector import Collector, CollectException


class OdpsTableCollector(Collector):
    def __init__(self, collector_node, data_source, table):
        """
        ODPS数据表采集
        :param node.data_collect.collect_node.CollectNode collector_node:
        :param dmplib.data_source.models.DataSourceModel data_source:
        :param dict table:
        """
        super().__init__(collector_node, data_source, table)
        if not isinstance(data_source.conn_str, ODPSConnStrModel):
            raise CollectException('数据源类型不匹配')
        self._odps = ODPS(access_id=self.data_source.conn_str.access_id,
                          secret_access_key=self.data_source.conn_str.access_key,
                          project=self.data_source.conn_str.project_name,
                          endpoint=ODPS_ENDPOINT)
        self.columns = []
        self.partition_columns = []
        self.partition_specs = []
        self._load_columns()

    def _load_columns(self):
        config_columns = self.table['columns']
        table = self._odps.get_table(self.table_name)
        for column in table.schema.columns:
            if isinstance(column, TableSchema.TablePartition):
                self.partition_columns.append(Partition(name=column.name, type=column.type, comment=column.comment))
                continue
            if column.name not in config_columns:
                continue
            self.columns.append(dict(name=column.name, type=column.type, comment=column.comment))
        try:
            for partition in table.partitions:
                p_name = partition.name
                has_sub_partition = False
                for iterate_partition in table.iterate_partitions(spec=p_name):
                    if p_name == iterate_partition.name:
                        continue
                    has_sub_partition = True
                    self.partition_specs.append(p_name + ',' + iterate_partition.name)
                    p_name += ',' + iterate_partition.name
                if not has_sub_partition:
                    self.partition_specs.append(p_name)
        except ODPSError as oe:
            self.collector_node.info(self.table_name + '没有分区：' + str(oe))

    def _get_table_last_timestamp(self):
        return ''

    def _create_odps_table(self):
        """
        创建ODPS数据表
        :return:
        """
        """删除已经存在的数据表"""
        self.context.get_odps().delete_table(self.odps_table_name, if_exists=True)
        """创建新表"""
        self.collector_node.info('创建ODPS数据表：' + self.odps_table_name)
        columns = [Column(**col) for col in self.columns]
        schema = Schema(columns=columns, partitions=self.partition_columns if self.partition_columns else None)
        self.context.get_odps().create_table(self.odps_table_name, schema, if_not_exists=True)
        """创建所有分区"""
        if self.partition_specs:
            table = self.context.get_odps().get_table(self.odps_table_name)
            for partition_spec in self.partition_specs:
                table.create_partition(partition_spec, True)

    def _create_odps_table_partition(self):
        return

    def _update_mysql_table_max_timestamp(self):
        return

    def _write_data(self):
        if not self.partition_specs:
            self.partition_specs.append(None)
        columns = [col.get('name') for col in self.columns]
        for partition_spec in self.partition_specs:
            job = Job()
            job_content = JobContent()

            reader_parameter = JobODPSReaderParameter()
            reader_parameter.set_odps_config(self.data_source.conn_str.get_dict())
            reader_parameter.table = self.table_name
            reader_parameter.column = columns
            if partition_spec:
                reader_parameter.partition.append(partition_spec)
            job_content.reader = JobODPSReader(parameter=reader_parameter)

            writer_parameter = JobODPSWriterParameter()
            writer_parameter.set_odps_config(self.context.get_odps_config())
            writer_parameter.partition = partition_spec
            writer_parameter.table = self.odps_table_name
            writer_parameter.column = columns

            job_content.writer = JobODPSWriter(parameter=writer_parameter)
            job.content.append(job_content)
            self.collector_node.info('%s同步:%s 表分区:%s' % (self.data_source.name, self.table_name, partition_spec))
            self.collector_node.info(job.run(self.collector_node.cur_node.node_instance.instance_id))
