#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by wangl10 on 2016/9/20.
"""
from components.data_x import Job, JobContent, JobMysqlReaderParameter, JobMysqlReader, JobODPSWriterParameter, \
    JobODPSWriter
from dmplib.base.enums import DataCollectMode
from dmplib.base.repository import SimpleMysql
from dmplib.data_source.models import MysqlConnStrModel
from node.data_collect.collector import Collector, CollectException


class MysqlTableCollector(Collector):
    def __init__(self, collector_node, data_source, table):
        """
        Mysql数据表采集
        :param node.data_collect.collect_node.CollectNode collector_node:
        :param dmplib.data_source.models.DataSourceModel data_source:
        :param dict table:
        """
        super().__init__(collector_node, data_source, table)
        if not isinstance(data_source.conn_str, MysqlConnStrModel):
            raise CollectException('数据源类型不匹配')

    def _validate(self):
        if self.collect_mode == DataCollectMode.Incremental.value and self.table.get('timestamp_col_name') is None:
            raise CollectException('数据表：%s 未配置时间戳字段' % (self.table['name']))
        return True

    def _write_data(self):
        if not self._validate():
            return
        if not self.last_timestamp:
            self.last_timestamp = '0000-00-00 00:00:00'
        sql = self._get_read_sql()
        if not sql:
            return

        job = Job()
        job_content = JobContent()
        reader_parameter = JobMysqlReaderParameter()
        reader_parameter.set_connection(self.data_source.conn_str.get_dict(), query_sql=[sql])
        job_content.reader = JobMysqlReader(parameter=reader_parameter)

        writer_parameter = JobODPSWriterParameter()
        writer_parameter.set_odps_config(self.context.get_odps_config())
        writer_parameter.partition = self.odps_table_partition_spec \
            if self.collect_mode == DataCollectMode.Incremental.value else None
        writer_parameter.truncate = self.collect_mode != DataCollectMode.Incremental.value
        writer_parameter.table = self.odps_table_name
        writer_parameter.column = self.table['columns']

        job_content.writer = JobODPSWriter(parameter=writer_parameter)
        job.content.append(job_content)
        self.collector_node.info(job.run(self.collector_node.cur_node.node_instance.instance_id))
        self.collector_node.info('%s.%s同步完成' % (self.data_source.name, self.table_name))

    def _get_read_sql(self):
        if self.collect_mode == DataCollectMode.Incremental.value:
            self.max_timestamp = self._get_mysql_table_max_timestamp(self.last_timestamp)
            if not self.max_timestamp:
                self.collector_node.info('%s.%s没有数据可同步' % (self.data_source.name, self.table_name))
                return None
            return 'SELECT %(columns)s FROM `%(table_name)s` ' \
                   'WHERE `%(timestamp_col_name)s` > \'%(last_timestamp)s\' ' \
                   'AND `%(timestamp_col_name)s` <= \'%(max_timestamp)s\'' \
                   % {'columns': ','.join(self._read_table_columns()),
                      'table_name': self.table_name,
                      'timestamp_col_name': self.table['timestamp_col_name'],
                      'last_timestamp': self.last_timestamp,
                      'max_timestamp': self.max_timestamp
                      }

        else:
            return 'SELECT %(columns)s FROM `%(table_name)s` ' \
                   % {'columns': ','.join(self._read_table_columns()),
                      'table_name': self.table_name}

    def _read_table_columns(self):
        columns = []
        sql = 'SELECT COLUMN_NAME AS `name`,DATA_TYPE AS `type` ,COLUMN_COMMENT AS `comment` ' \
              'FROM information_schema.COLUMNS ' \
              'WHERE TABLE_SCHEMA =DATABASE() AND TABLE_NAME=%(table_name)s ' \
              'ORDER BY ORDINAL_POSITION'
        with SimpleMysql(**self.data_source.conn_str.get_dict()) as db:
            db_columns = db.query(sql, {'table_name': self.table_name})
        db_column_type = {}
        for col in db_columns:
            db_column_type[col['name']] = col['type']
        for col in self.table['columns']:
            col_type = db_column_type[col]
            if not col_type:
                continue
            if col_type == 'binary':
                columns.append('HEX(`' + col + '`) AS ' + '`' + col + '`')
            else:
                columns.append('`' + col + '`')
        return columns

    def _get_mysql_table_max_timestamp(self, last_timestamp):
        """mysql同步获取最大时间戳标识"""
        sql = 'SELECT (CASE WHEN  MAX(`%(timestamp_col_name)s`)>NOW() ' \
              'THEN date_add(NOW(),INTERVAL -1 SECOND) ' \
              'ELSE MAX(`%(timestamp_col_name)s`) END) ' \
              'FROM `%(table_name)s` ' \
              'WHERE `%(timestamp_col_name)s` > %%(last_timestamp)s;' \
              % {'timestamp_col_name': self.table['timestamp_col_name'],
                 'table_name': self.table['name']}

        params = {'last_timestamp': last_timestamp}
        with SimpleMysql(**self.data_source.conn_str.get_dict()) as db:
            max_timestamp = db.query_scalar(sql, params)
        return max_timestamp
