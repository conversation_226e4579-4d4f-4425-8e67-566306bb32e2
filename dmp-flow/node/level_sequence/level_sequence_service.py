#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    Created by wangl<PERSON>@mingyuanyun.com on 2017/12/7.
"""
from flow.flow_context import FlowContext
from node.level_sequence.models import LevelSequenceBaseModel


def generate_level_code(context: FlowContext, model: LevelSequenceBaseModel, separator=None, parent_level_code=None):
    """
    生成层级编码
    :param flow.flow_context.FlowContext context:
    :param node.level_sequence.models.LevelSequenceBaseModel model:
    :param separator: default "-"
    :param parent_level_code: None
    :return:
    """
    model.validate()
    with context.get_project_db() as db:
        sql = (
            "select table_name from dap_bi_level_sequence where table_name=%(table_name)s "
            "and level_id=%(level_id)s and attach_identify=%(attach_identify)s"
        )
        is_exists = db.query_scalar(
            sql, {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
        )
        if not is_exists:
            add_sequence(context, model)
        cur_sequence = str(increase_sequence(context, model)).zfill(model.unit_code_length) + (separator or '-')
        cur_level_code = (get_cur_level_code(context, model) or '') if not parent_level_code else parent_level_code
        # 可能生成的level_code已存在
        if level_code_is_exist(context, cur_level_code + cur_sequence, model):
            return generate_level_code(context, model, separator, parent_level_code)
    return cur_level_code + cur_sequence


def level_code_is_exist(context, level_code, model):
    with context.get_project_db() as db:
        return db.query_scalar(f"select 1 from {model.table_name} where {model.table_level_code_field}=%({model.table_level_code_field})s", {model.table_level_code_field: level_code})


def get_cur_level_code(context: FlowContext, model: LevelSequenceBaseModel):
    """
    获取当前层级编码
    :param flow.flow_context.FlowContext context:
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    sql = 'SELECT `%s` FROM `%s` WHERE `%s`=%%(level_id)s LIMIT 1;' % (
        model.table_level_code_field,
        model.table_name,
        model.table_level_id_field,
    )
    with context.get_project_db() as db:
        return db.query_scalar(sql, {'level_id': model.level_id})


def add_sequence(context: FlowContext, model):
    """
    增加序列
    :param flow.flow_context.FlowContext context:
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    sql = """INSERT INTO `dap_bi_level_sequence`(`table_name`,`level_id`,`attach_identify`,`max_sequence`)
             SELECT `table_name`,`level_id`,`attach_identify`,`max_sequence` FROM  (
                    SELECT %(table_name)s AS `table_name` ,%(level_id)s AS `level_id`
                    ,%(attach_identify)s AS `attach_identify`,0 as `max_sequence`
                  ) AS res
             WHERE NOT EXISTS(SELECT 1 FROM `dap_bi_level_sequence`
                      WHERE `table_name`=%(table_name)s AND `level_id`=%(level_id)s
                      AND `attach_identify`=%(attach_identify)s
                  )"""
    with context.get_project_db() as db:
        return db.execute(
            sql, {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
        )


def increase_sequence(context: FlowContext, model):
    """
    递增序列
    :param flow.flow_context.FlowContext context:
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    # sql = """UPDATE `level_sequence` SET `max_sequence` = (select @max_sequence:=`max_sequence`+1)
    #          WHERE `table_name`=%(table_name)s AND level_id=%(level_id)s AND `attach_identify`=%(attach_identify)s"""
    # condition = {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
    # with context.get_project_db() as db:
    #     db.execute('SET @max_sequence := 0;')
    #     db.execute(sql, condition)
    #     cur_sequence = db.query_scalar('SELECT @max_sequence;')
    #     return cur_sequence
    with context.get_project_db() as conn:
        sequence = conn.query_scalar(
            'select `max_sequence` from `dap_bi_level_sequence` where `table_name`=%(table_name)s and level_id=%(level_id)s and attach_identify=%(attach_identify)s',
            {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
        )

        cur_sequence = sequence + 1

        conn.update('dap_bi_level_sequence', {'max_sequence': cur_sequence}, {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify})

        return cur_sequence


def delete_sequence(context: FlowContext, model):
    """
    清空序列
    :param flow.flow_context.FlowContext context:
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    sql = "delete from dap_bi_level_sequence where table_name = %(table_name)s"
    with context.get_project_db() as db:
        db.execute(sql, {'table_name': model.table_name})
