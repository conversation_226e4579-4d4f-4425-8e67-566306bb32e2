#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by chenc04 on 2019/6/24.
"""
import json
import traceback

from components.fast_logger import FastLogger
from components.redis import RedisCache
from components.enums import AccountMode, AddMode, UserChannel, UserSourceType
from components.wait_lock import WaitLocker
from dmplib.external import dp_server
from dmplib.utils.errors import UserError, SkipError
from dmplib.utils.strings import seq_id
from node.level_sequence.level_sequence_service import generate_level_code
from node.level_sequence.models import UserGroupLevelSequenceModel
from node.node_log_message import NodeMsgType
from node.node_result import NodeResult
from node.organization.organization_execution import OrganizationExecution
from dmplib.sql_adapter import adapter_sql

EXCLUDE_EXTERNAL_USER = "is_disabled"
DMP_LEVEL_CODE_COL_NAME = "dmp_level_code"
ADD_MODE = AddMode.AUTO.value
DEFAULT_USER_GROUP_PARENT_ID = '********-0000-0000-0000-************'


class UserOrgImportNode(OrganizationExecution):
    def __init__(self, context, cur_node):
        """引入数据到user, user_group, user_group_user表"""
        super().__init__(context, cur_node)
        self.user_field_mapping = {}
        self.user_group_field_mapping = {}
        self.user_source_id = ""
        self.user_source_name = ""
        self.target_user_table_name = ""
        self.target_user_group_table_name = ""
        self.account_mode = AccountMode.SYNC.value

        # 是否是主用户渠道
        self.is_erp_user_source = 0

    def execution(self):
        try:
            self.info("启动用户组织引入节点", msg_type=NodeMsgType.STEP.value)

            # 1、加载数据, 校验数据正确性
            self.load_content()

            with WaitLocker(self.user_source_id, 1800) as lock:
                if lock.lock:
                    # 2、引入组织信息到用户组(external_user_group/user_source_group -> user_group)
                    self.import_dmp_user_group()

                    # 3、更新用户组关系(external_user/user_source_user -> user_group_user)
                    self.update_user_group_user()

                    # 4、清空用户缓存
                    dataset_row_permission_userid = '{project_code}:{cache_key}'.format(
                        project_code=self.context.project_code, cache_key='dataset_row_permission_userid:'
                    )
                    RedisCache().del_data(dataset_row_permission_userid)
                    dp_server.refresh_project_user_permission_cache(self.context.project_code)
                else:
                    raise UserError(message='已有流程正在同步，此次同步流程终止')
        except SkipError:
            return NodeResult(True, "跳过用户组织引入")
        except UserError as e:
            self.record_log(e.message, FastLogger.ErrorType.USER_ORG_IMPORT_ERROR, self.user_source_id)
            return NodeResult(False, e.message)
        except BaseException as e:
            self.record_log(str(e), FastLogger.ErrorType.USER_ORG_IMPORT_ERROR, self.user_source_id)
            return NodeResult(False, str(e))
        return NodeResult(True, "用户组织引入成功")

    def load_content(self): # NOSONAR
        try:
            node_content = json.loads(self.cur_node.content)
        except json.JSONDecodeError as e:
            msg = '解析用户组织引入节点配置错误：' + str(e) + json.dumps(self.cur_node)
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise Exception(msg)

        item = node_content.get('item')
        import_ = node_content.get('import')
        if not import_:
            self.info('跳过用户组织引入结点', msg_type=NodeMsgType.DETAIL.value)
            raise SkipError()

        if not item:
            self.error('用户组织引入节点配置缺少item', msg_type=NodeMsgType.DETAIL.value)
            raise Exception('用户组织引入节点配置缺少item')

        try:
            with self.context.get_project_db() as db:
                self.user_source_id = item
                user_source = db.query_one(
                    "select `name`, `data`,`type` from dap_p_user_source where id= %(id)s ",
                    {"id": self.user_source_id}
                )
                if not user_source or not user_source.get('data'):
                    raise Exception('用户渠道不存在')
                self.user_source_name = user_source.get('name')
                self.is_erp_user_source = user_source.get('type') == UserChannel.Erp.value
                self.target_user_table_name = 'dap_bi_external_user' if self.is_erp_user_source else 'dap_p_user_source_user'
                self.target_user_group_table_name = 'dap_bi_external_user_group' if self.is_erp_user_source else 'dap_p_user_source_group'

                user_source_data = json.loads(user_source.get('data'))
                user_source_type = user_source_data.get('source_type', UserSourceType.DATASET.value)

                self.user_field_mapping = {
                    'id': 'id' if self.is_erp_user_source else 'user_id',
                    'name': 'name',
                    'account': 'account',
                    'pwd': 'pwd',
                    'mobile': 'mobile',
                    'email': 'email',
                    'group_id': 'group_id' if self.is_erp_user_source else 'user_source_group_id',
                    'is_disabled': 'is_disabled'
                }
                if user_source_type == UserSourceType.DATASET.value:
                    field_relation = user_source_data.get("user").get("field_relation")
                    # 删除没有引入的字段
                    for k, v in field_relation.items():
                        if not v and self.user_field_mapping.get(k):
                            del self.user_field_mapping[k]
        except BaseException as e:
            msg = '解析用户组织引入节点配置错误：' + str(e)
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise Exception(msg)

    def import_dmp_user_group(self):
        self._pre_check()
        self._insert_dmp_user_group()
        self._delete_dmp_user_group()
        self._update_dmp_user_group()

    @property
    def group_root_id(self):
        return self.user_source_id

    def _check_data_unique(self):
        self.info("检查数据id唯一性", msg_type=NodeMsgType.STEP.value)
        id_field = 'id' if self.is_erp_user_source else 'user_source_group_id'

        # 检查数据中id重复的情况
        query_sql = f'''
                    select `{id_field}`
                    from `{self.target_user_group_table_name}`
                '''
        if not self.is_erp_user_source:
            query_sql += f" where `user_source_id`='{self.user_source_id}' "
        query_sql += f"group by `{id_field}` having count(*) > 1"

        with self.context.get_project_db() as db:
            rows = db.query(query_sql)
        if rows:
            repeated_id = ','.join([str(row.get(id_field)) for row in rows])
            raise Exception(f'数据中存在重复的组织id: {repeated_id}')

    def _check_import_unique(self):
        self.info("检查用户组是否已经引入", msg_type=NodeMsgType.STEP.value)
        id_field = 'id' if self.is_erp_user_source else 'user_source_group_id'

        # 检查是否与已引入的组织id重复
        query_sql = f'''
            select `t1`.`{id_field}`, `t1`.`name` as `source_name`, `t2`.`name` as `dmp_name`
            from `{self.target_user_group_table_name}` `t1`
            left join `dap_p_user_group` `t2`
            on `t1`.`{id_field}`=`t2`.`id` and `t2`.`user_source_id`!='{self.user_source_id}'
            where `t2`.`id` is not null
        '''
        if not self.is_erp_user_source:
            query_sql += f" and `t1`.`user_source_id`='{self.user_source_id}' "
        with self.context.get_project_db() as db:
            rows = db.query(query_sql)
        if rows:
            repeated_id = ','.join([row.get(id_field) for row in rows])
            raise Exception(f'与已引入的组织id重复: {repeated_id}')

    def _check_parent_exists(self):
        self.info("检查数据完整性", msg_type=NodeMsgType.STEP.value)
        id_field = 'id' if self.is_erp_user_source else 'user_source_group_id'

        query_sql = f'''
            select `{id_field}` as `id`, `parent_id`, `name`
            from `{self.target_user_group_table_name}`
        '''
        if not self.is_erp_user_source:
            query_sql += f" where `user_source_id`='{self.user_source_id}' "

        with self.context.get_project_db() as db:
            nodes = db.query(query_sql)

        node_map = {}
        for node in nodes:
            if not node.get('id'):
                raise Exception(f'用户组id不能为空')
            node_map[node.get('id')] = node
        for node in nodes:
            parent_id = node.get('parent_id')
            if not parent_id:
                continue
            if not node_map.get(parent_id):
                self.info(f"异常数据：用户组<{node.get('id')}, {node.get('name')}>的父节点<{parent_id}>不存在,不会同步此条数据", msg_type=NodeMsgType.DETAIL.value)
                continue
            if parent_id == node.get('id'):
                raise Exception(f"用户组<{node.get('id')}, {node.get('name')}>的父节点不能为自己")

    def _pre_check(self):
        self._check_data_unique()
        self._check_import_unique()
        self._check_parent_exists()

    def _auto_insert_root_node(self, root_id, current_list):
        # 根节点不存在时, 自动插入根节点
        if (not current_list) or (root_id not in [node.get('id') for node in current_list]):
            root = {
                'id': root_id,
                'name': self.user_source_name,
                'parent_id': DEFAULT_USER_GROUP_PARENT_ID,
                'code': generate_level_code(self.context, UserGroupLevelSequenceModel(level_id=DEFAULT_USER_GROUP_PARENT_ID))
            }
            with self.context.get_project_db() as db:
                sql = adapter_sql('replace_into_user_group', db.db_type)
                db.exec(sql,
                        {
                            'id': root.get('id'), 'name': root.get('name'),
                            'parent_id': root.get('parent_id'), 'code': root.get('code'),
                            'user_source_id': self.user_source_id, 'account_mode': self.account_mode
                        }
                    )
            current_list.append(root)

    def _build_tree(self, root, nodes):
        all_node_dict = {node.get('id'): node for node in nodes}
        for node in all_node_dict.values():
            if node is root:
                continue

            # 如果节点的parent_id为空, 放置到根节点下
            if not node.get('parent_id'):
                self.warning(f"组织<{node['id']}, {node['name']}>父结点<{node['parent_id']}>不存在, 更新到渠道默认组织下")
                node['parent_id'] = root.get('id')
            elif all_node_dict.get(node.get('parent_id')) is None:
                continue
            parent = all_node_dict.get(node['parent_id'])
            children = parent.setdefault('children', [])
            children.append(node)

    def _insert_tree(self, root):
        # 对树进行宽度优先遍历, 对标记待插入结点进行插入
        queue = [root]
        with self.context.get_project_db() as db:
            while len(queue) > 0:
                node = queue.pop(0)
                if not node.get('exist'):
                    data = {
                        'id': node.get('id'), 'name': node.get('name'), 'parent_id': node.get('parent_id'),
                        'code': generate_level_code(self.context, UserGroupLevelSequenceModel(level_id=node.get('parent_id'))),
                        'user_source_id': self.user_source_id,
                        'account_mode': self.account_mode
                    }
                    sql = adapter_sql('replace_into_user_group', db.db_type)
                    db.exec(sql, data)

                children = node.get('children', [])
                queue.extend(children)


    def _insert_dmp_user_group(self):
        self.info("更新用户组表，自动插入用户组", msg_type=NodeMsgType.STEP.value)

        # 插入新增用户组, 用户组构建为如下结构
        # 根节点为id = user_source_id的结点, parent_id='********-0000-0000-0000-************'
        # 对于父结点id不存在的结点, 默认挂在根节点下
        # 为了避免用户组id冲突, 统一为渠道组织加上渠道id后缀
        root_id = self.group_root_id
        id_field = 'id' if self.is_erp_user_source else 'user_source_group_id'

        query_insert_sql = f"""
            select t1.`{id_field}` as `id`, 
                   t1.`parent_id` as `parent_id`, 
                   t1.`name`
            from {self.target_user_group_table_name} t1 
            left join dap_p_user_group t2 
            on t1.`{id_field}`=t2.`id` and t2.`user_source_id`='{self.user_source_id}' and t2.`account_mode`='{self.account_mode}'
            where t2.`id` is null
        """
        if not self.is_erp_user_source:
            query_insert_sql += f" and t1.`user_source_id`='{self.user_source_id}'"

        query_current_sql = f"select `id`, `parent_id`, `name` from `dap_p_user_group` where `user_source_id`='{self.user_source_id}' "
        with self.context.get_project_db() as db:
            insert_list = db.query(query_insert_sql)
            if not insert_list:
                self.info("跳过引入, 没有需要引入的组织", msg_type=NodeMsgType.DETAIL.value)
                return
            current_list = db.query(query_current_sql) or []

        self._auto_insert_root_node(root_id, current_list)

        # 根据当前节点和待插入节点构造一颗树, 标记出待插入节点
        root = next(node for node in current_list if node.get('id') == root_id)

        for node in current_list:
            node['exist'] = True
        for node in insert_list:
            node['exist'] = False

        self._build_tree(root, [*current_list, *insert_list])
        self._insert_tree(root)
        self.info(f"引入用户组织成功, 引入数量<{len(insert_list)}>", msg_type=NodeMsgType.DETAIL.value)


    def _delete_dmp_user_group(self):
        self.info("更新用户组表，自动删除用户组", msg_type=NodeMsgType.STEP.value)

        # 删除用户组, 同时删除其下子节点, 和对应的关系
        id_field = 'id' if self.is_erp_user_source else 'user_source_group_id'
        query_delete_sql = f"""
            SELECT t1.`id` FROM `dap_p_user_group` t1
            LEFT JOIN {self.target_user_group_table_name} t2 ON t1.`id` = t2.{id_field}
        """
        if not self.is_erp_user_source:
            query_delete_sql += f" AND t2.`user_source_id`='{self.user_source_id}' "
        query_delete_sql += f"""
            WHERE t2.{id_field} IS NULL AND t1.`id` != '{self.group_root_id}' AND t1.`user_source_id`='{self.user_source_id}'
        """

        with self.context.get_project_db() as db:
            rows = db.query(query_delete_sql)
            user_group_ids = [row.get('id') for row in rows]
            self._delete_user_groups(db, user_group_ids)

    def _update_dmp_user_group(self):
        self.info("更新用户组表，自动更新用户组", msg_type=NodeMsgType.STEP.value)

        # 更新用户组
        # 1. 更新字段信息
        id_field = 'id' if self.is_erp_user_source else 'user_source_group_id'
        default_update_fields = ['name']
        update_fields_mapping = {}
        for field in default_update_fields:
            map_field = self.user_field_mapping.get(field)
            if map_field:
                update_fields_mapping[field] = map_field

        if update_fields_mapping:
            update_set = ["user_group.{}={}.{}".format(k, self.target_user_group_table_name, v)
                          for k, v in update_fields_mapping.items() if v]
            set_str = ','.join(update_set)
            update_sql = f"""
                UPDATE `dap_p_user_group` as user_group, `{self.target_user_group_table_name}`
                SET {set_str}
                WHERE user_group.`id` = `{self.target_user_group_table_name}`.`{id_field}` and
                      user_group.`user_source_id`='{self.user_source_id}'
            """
            if not self.is_erp_user_source:
                update_sql += f" and {self.target_user_group_table_name}.user_source_id='{self.user_source_id}'"

            with self.context.get_project_db() as db:
                db.exec(update_sql)

        # 2. 对于parent_id有改动的组织, 需要更新level_code并更新其下所有子节点的level_code
        query_diff_group_sql = f"""
            select t1.`id`, t1.`parent_id` as origin_parent_id, t2.`parent_id` as current_parent_id, t1.`code`
            from dap_p_user_group t1
            inner join {self.target_user_group_table_name} t2
            on t1.`id` = t2.{id_field} and t1.`parent_id` != t2.parent_id
            where t1.user_source_id='{self.user_source_id}'
        """
        if not self.is_erp_user_source:
            query_diff_group_sql += f" and t2.`user_source_id`='{self.user_source_id}'"

        with self.context.get_project_db() as db:
            diff_parent_groups = db.query(query_diff_group_sql)

        ## 两种情况, 一种是的确发生了修改, 另一种是id不存在, 被放在了根节点下
        for group in diff_parent_groups:
            current_parent_id = group.get('current_parent_id')
            origin_parent_id = group.get('origin_parent_id')
            if not current_parent_id:
                current_parent_id = self.user_source_id
            if current_parent_id == origin_parent_id:
                continue
            parent_sql = f"select `id`, `name` from dap_p_user_group where `id`='{current_parent_id}' and `user_source_id`='{self.user_source_id}' "
            with self.context.get_project_db() as db:
                new_parent = db.query_one(parent_sql)

            # 父节点不存在, 不需要进行更新
            if not new_parent:
                continue
            self._move_to_new_parent(group, new_parent)
            self.info(f"节点<{group.get('id'), group.get('name')}>从<{group.get('origin_parent_id')}>到<{new_parent.get('id'), new_parent.get('name')}>")

    def _move_to_new_parent(self, group, parent):
        with self.context.get_project_db() as db:
            code_str = f"{group.get('code')}%"
            # 这里按code排序, 从上往下依次生成level_code
            query_groups_sql = f"select id, name, parent_id, user_source_id, account_mode from dap_p_user_group where code like '{code_str}' order by code "
            groups = db.query(query_groups_sql)
            # move操作时重新生成code
            for item in groups:
                if item.get('id') == group.get('id'):
                    item['parent_id'] = parent.get('id')
                item['code'] = generate_level_code(self.context, UserGroupLevelSequenceModel(level_id=item.get('parent_id')))
                db.exec(f"replace into dap_p_user_group(id, name, parent_id, code, user_source_id, account_mode) values (%(id)s, %(name)s, %(parent_id)s, %(code)s, %(user_source_id)s, %(account_mode)s)",item)

    def _delete_user_groups(self, db, user_group_ids):
        if not user_group_ids:
            return
        for group_id in user_group_ids:
            self._delete_user_group(db, group_id)

    def _delete_user_group(self, db, group_id):
        sql = 'SELECT `code` FROM dap_p_user_group WHERE id=%(id)s LIMIT 1'
        code = db.query_scalar(sql, {'id': group_id})
        if not code:
            return
        sqls = [
            'DELETE FROM dap_p_user_group_func WHERE `group_id` IN ( SELECT `id` FROM `dap_p_user_group` WHERE `code` like %(code)s )',
            'DELETE FROM dap_p_user_group_organ WHERE `group_id` IN ( SELECT `id` FROM `dap_p_user_group` WHERE `code` like %(code)s )',
            'DELETE FROM dap_p_user_group_user WHERE `group_id` IN ( SELECT `id` FROM `dap_p_user_group` WHERE `code` like %(code)s ) ',
            'DELETE FROM dap_p_user_group_role WHERE `group_id` IN ( SELECT `id` FROM `dap_p_user_group` WHERE `code` like %(code)s ) ',
            'DELETE FROM dap_p_user_group_dashboard WHERE `group_id` IN ( SELECT `id` FROM `dap_p_user_group` WHERE `code` like %(code)s ) ',
            'DELETE FROM dap_p_user_group_app WHERE `group_id` IN ( SELECT `id` FROM `dap_p_user_group` WHERE `code` like %(code)s ) ',
            'DELETE FROM dap_p_user_group WHERE `code` like %(code)s',
        ]
        try:
            for sql in sqls:
                db.exec(sql, {'code': code + '%'}, commit=False)
            db.commit()
        except BaseException as e:
            db.rollback()
            raise e

    def update_user_group_user(self):
        self.info("更新用户组用户关系", msg_type=NodeMsgType.STEP.value)
        with self.context.get_project_db() as db:
            # 1. 获取当前用户中心中来源于这个用户源的所有用户id
            rows = db.query('select `id`, `account` from `dap_p_user` where `user_source_id`=%(user_source_id)s', {'user_source_id': self.user_source_id})
            if not rows:
                return
            user_account_map = {
                user.get('account'): user.get('id')
                for user in rows
            }
            user_ids = set(user_account_map.values())
            rows = db.query('select `id` from `dap_p_user_group` where `user_source_id`=%(user_source_id)s', {'user_source_id': self.user_source_id})
            if not rows:
                return
            group_ids = set([group.get('id') for group in rows])

            # 2. 获取当前关系表中该数据源的(user_id, group_id)关系对
            query_sql = "select `user_id`, `group_id` from `dap_p_user_group_user` where `user_source_id`=''"
            manual_relations = db.query(query_sql)
            manual_relations = {
                (row.get('user_id'), row.get('group_id'))
                for row in manual_relations
            }

            query_sql = 'select `user_id`, `group_id` from `dap_p_user_group_user` where `user_source_id`=%(user_source_id)s'
            current_relations = db.query(query_sql, {'user_source_id': self.user_source_id})
            current_relations = {
                (row.get('user_id'), row.get('group_id'))
                for row in current_relations
            }

            # 3. 获取当前关系中间表中该数据源的(account, group_id)关系对
            # (这里需要实际以account+group_id作为唯一标识, user表中的id字段实际对应中间表的id字段, 中间表id字段可能发生变化)
            group_id_field = 'group_id' if self.is_erp_user_source else 'user_source_group_id'
            query_sql = f"select `account`, {group_id_field} as group_id from `{self.target_user_table_name}` "
            if not self.is_erp_user_source:
                query_sql += f"where `user_source_id`='{self.user_source_id}'"
            rows = db.query(query_sql)
            middle_relations = self._convert_to_tuple(rows, 'account', 'group_id')
            new_relations = set()
            for account, group_id in middle_relations:
                user_id = user_account_map.get(account)
                if not user_id:
                    continue
                # 过滤掉没有引入的用户
                if user_id not in user_ids:
                    continue
                # 过滤掉没有引入的用户组
                if group_id not in group_ids:
                    continue
                new_relations.add((user_id, group_id))

            # 4. 对比需要插入和删除的数据
            add_relations = new_relations - current_relations - manual_relations
            add_relations = [{
                'id': seq_id(),
                'user_id': user_id,
                'group_id': group_id,
                'user_source_id': self.user_source_id,
            } for (user_id, group_id) in add_relations]
            if add_relations:
                db.insert_multi_data('dap_p_user_group_user', add_relations, ['id', 'user_id', 'group_id', 'user_source_id'], commit=False)
            self.info(f"更新用户组织关系，添加{len(add_relations)}条关系", msg_type=NodeMsgType.DETAIL.value)

            del_relations = current_relations - new_relations
            if del_relations:
                params = {}
                for i in del_relations:
                    params.setdefault('user_ids', []).append(i[0])
                    params.setdefault('group_ids', []).append(i[1])
                sql = f"""delete from `dap_p_user_group_user` where `user_source_id`='{self.user_source_id}' and `user_id` in %(user_ids)s and `group_id` in %(group_ids)s"""
                db.exec(sql, params, commit=False)
            self.info(f"更新用户组织关系，删除{len(del_relations)}条关系", msg_type=NodeMsgType.DETAIL.value)
            db.commit()

    @staticmethod
    def _convert_to_tuple(rows, key_field, val_field):
        data = set()
        if not rows:
            return data
        for row in rows:
            ids = row.get(val_field, '').split(',')
            for id in ids:
                data.add((row.get(key_field), id))
        return data
