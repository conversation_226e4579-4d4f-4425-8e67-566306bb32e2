#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by chenc04 on 2019/6/24.
"""
import json

from components.fast_logger import FastLogger
from components.redis import RedisCache
from components.enums import AccountMode, AddMode, UserChannel, UserSourceType
from components.wait_lock import WaitLocker
from dmplib.external import dp_server
from dmplib.utils.errors import UserError, SkipError
from dmplib.utils.strings import seq_id
from dmplib.sql_adapter import adapter_db_operation, new_id, escape_str
from dmplib.utils.crypt import encrypt_field
from node.node_log_message import NodeMsgType
from node.node_result import NodeResult
from dmplib import config
from node.organization.organization_execution import OrganizationExecution

EXCLUDE_EXTERNAL_USER = "is_disabled"
DMP_LEVEL_CODE_COL_NAME = "dmp_level_code"
ADD_MODE = AddMode.AUTO.value
DEFAULT_USER_GROUP_PARENT_ID = '********-0000-0000-0000-********0000'
MYSQL_AES_KEY = config.get('Security.mysql_aes_key', 'qwertyuiasdfghjk')


class UpdateUser:

    @staticmethod
    def _update_user_mysql(update_fields_mapping, need_query, db):

        update_list = []
        for item in db.query(need_query):
            values = [new_id(), f"'{escape_str(item.get('account'))}'"]
            for k, v in update_fields_mapping.items():
                item[v] = escape_str(str(item.get(v))) if item.get(v) else ''
                if k == 'mobile':
                    item_v = f"'{encrypt_field(item.get(v))}'"
                elif k == 'is_disabled' and not item[v]:
                    item_v = '0'
                else:
                    item_v = "null" if item.get(v) is None else f"'{item.get(v)}'"
                values.append(item_v)
            value = f"({','.join(values)})"
            update_list.append(value)
        if update_list:
            fields = ['id', 'account'] + list(update_fields_mapping.keys())
            update_sql = f"""
                    insert into `dap_p_user` ({','.join(fields)}) 
                    values {','.join(update_list)} 
                    on duplicate key update {','.join([f'{i}=values({i})' for i in update_fields_mapping.keys()])}
                    """
            db.exec(sql=update_sql)

    @staticmethod
    def _update_user_dm(update_fields_mapping, need_query, db):

        update_list = []
        account_list = []
        for item in db.query(need_query):
            account = item.get('account')
            if account not in account_list:
                account_list.append(account)
            else:
                continue
            values = [f'{new_id()} as `id`', f"'{escape_str(item.get('account'))}' as `account`"]
            for k, v in update_fields_mapping.items():
                item[v] = escape_str(str(item.get(v))) if item.get(v) else ''
                if k == 'mobile':
                    item_v = f"'{encrypt_field(item.get(v))}'"
                elif k == 'is_disabled' and not item[v]:
                    item_v = '0'
                else:
                    item_v = "null" if item.get(v) is None else f"'{item.get(v)}'"
                values.append(f"{item_v} as `{k}`")
            update_list.append(f"""select {','.join(values)} from dual """)
        if update_list:
            fields = list(update_fields_mapping.keys())
            update_sql = """
                    merge into "dap_p_user" as target
                    USING (
                        {using_sql}
                    ) as source
                    on target.account=source.account
                    WHEN MATCHED THEN
                        UPDATE SET {update_sql}
                    WHEN NOT MATCHED THEN
                        INSERT ({insert_fields}) VALUES ({values})
                    """.format(
                using_sql=' union all '.join(update_list),
                update_sql=', '.join([f'target."{i}" = source."{i}"' for i in fields]),
                insert_fields=', '.join([f'"{i}"' for i in ['id', 'account'] + fields]),
                values=', '.join([f'source."{i}"' for i in ['id', 'account'] + fields])
            )
            db.exec(sql=update_sql)

    def update_user(self, update_fields_mapping, need_query, db):
        func = adapter_db_operation(self._update_user_mysql, self._update_user_dm)
        return func(update_fields_mapping, need_query, db)


class UserImportNode(OrganizationExecution):
    def __init__(self, context, cur_node):
        """引入数据到user, user_group, user_group_user表"""
        super().__init__(context, cur_node)
        self.user_field_mapping = {}
        self.user_group_field_mapping = {}
        self.user_source_id = ""
        self.user_source_name = ""
        self.target_user_table_name = ""
        self.target_user_group_table_name = ""
        self.data_limit = 100000
        self.account_mode = ''

        # 是否是主用户渠道
        self.is_erp_user_source = 0

    def execution(self):
        try:
            self.info("启动用户同步节点", msg_type=NodeMsgType.STEP.value)

            # 1、加载数据, 校验数据正确性
            self.load_content()

            with WaitLocker(self.user_source_id, 1800) as lock:
                if lock.lock():
                    # 2、同步用户信息到用户中心(external_user/user_source_user -> user)
                    self.import_dmp_user()

                    # 3、更新用户组关系(external_user/user_source_user -> user_group_user)
                    self.update_user_group_user()

                    # 4、清空用户缓存
                    dataset_row_permission_userid = '{project_code}:{cache_key}'.format(
                        project_code=self.context.project_code, cache_key='dataset_row_permission_userid:'
                    )
                    RedisCache().del_data(dataset_row_permission_userid)
                    dp_server.refresh_project_user_permission_cache(self.context.project_code)
                else:
                    raise UserError(message='已有流程正在同步，此次同步流程终止')
        except UserError as e:
            self.error(e.message, msg_type=NodeMsgType.DETAIL.value)
            self.record_log(e.message, FastLogger.ErrorType.USER_SYNC_ERROR, self.user_source_id)
            return NodeResult(False, e.message)
        except SkipError:
            return NodeResult(True, "跳过用户引入结点")
        except BaseException as e:
            self.error(str(e), msg_type=NodeMsgType.DETAIL.value)
            self.record_log(str(e), FastLogger.ErrorType.USER_SYNC_ERROR, self.user_source_id)
            return NodeResult(False, str(e))
        return NodeResult(True, "用户引入成功")

    def load_content(self):  # NOSONAR
        self.info("加载用户引入结点配置信息", msg_type=NodeMsgType.STEP.value)
        try:
            node_content = json.loads(self.cur_node.content)
        except json.JSONDecodeError as e:
            msg = '解析用户引入节点配置错误：' + str(e) + json.dumps(self.cur_node)
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise Exception(msg)

        item = node_content.get('item')
        import_ = node_content.get('import')
        if not import_:
            self.info('跳过用户引入结点', msg_type=NodeMsgType.DETAIL.value)
            raise SkipError()

        if not item:
            self.error('用户引入节点配置缺少item', msg_type=NodeMsgType.DETAIL.value)
            raise Exception('用户引入节点配置缺少item')

        try:
            with self.context.get_project_db() as db:
                self.user_source_id = item
                user_source = db.query_one(
                    "select `name`, `data`,`type` from `dap_p_user_source` where `id`= %(id)s ",
                    {"id": self.user_source_id}
                )
                if not user_source or not user_source.get('data'):
                    raise Exception('用户渠道不存在')
                self.user_source_name = user_source.get('name')
                self.is_erp_user_source = user_source.get('type') == UserChannel.Erp.value
                self.account_mode = self._get_account_mode(user_source.get('data'))
                self.target_user_table_name = 'dap_bi_external_user' if self.is_erp_user_source else 'dap_p_user_source_user'
                self.target_user_group_table_name = 'dap_bi_external_user_group' if self.is_erp_user_source else 'dap_p_user_source_group'

                user_source_data = json.loads(user_source.get('data'))
                user_source_type = user_source_data.get('source_type', UserSourceType.DATASET.value)

                self.user_field_mapping = {
                    'name': 'name',
                    'account': 'account',
                    'pwd': 'pwd',
                    'mobile': 'mobile',
                    'email': 'email',
                    'group_id': 'group_id' if self.is_erp_user_source else 'user_source_group_id',
                    'is_disabled': 'is_disabled'
                }
                id_mapping = config.get('Node.id_mapping', 0)
                if id_mapping in [1, '1']:
                    self.error("开启用户id保持一致的同步方式")
                    self.user_field_mapping['id'] = 'id' if self.is_erp_user_source else 'user_id'
                if user_source_type == UserSourceType.DATASET.value:
                    field_relation = user_source_data.get("user").get("field_relation")
                    # 删除没有同步的字段
                    for k, v in field_relation.items():
                        if not v and self.user_field_mapping.get(k):
                            del self.user_field_mapping[k]
        except BaseException as e:
            msg = '解析用户引入节点配置错误：' + str(e)
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise Exception(msg)

    def _get_account_mode(self, user_source):
        source_map = {UserSourceType.AD.value: AccountMode.DOMAIN.value}
        account_mode = AccountMode.ERP.value if self.is_erp_user_source else AccountMode.SYNC.value
        try:
            user_source = json.loads(user_source) if user_source else {}
        except BaseException as e:
            msg = 'json序列化失败: {}'.format(str(e))
            self.error(msg, msg_type=NodeMsgType.DETAIL.value)
            raise Exception(msg)
        source_type = user_source.get('source_type')
        return source_map.get(source_type) if source_map.get(source_type) else account_mode

    def _delete_dmp_user(self):
        self.info("更新用户表，同步删除用户", msg_type=NodeMsgType.STEP.value)

        # 查询在用户表但不在中间表的用户id并删除
        query_delete_sql = f"""
        SELECT `user`.`id`, `user`.`account` FROM `dap_p_user` as `user`
        LEFT JOIN `{self.target_user_table_name}` ON `user`.`account` = `{self.target_user_table_name}`.`account`
        """
        if not self.is_erp_user_source:
            query_delete_sql += f" AND `{self.target_user_table_name}`.`user_source_id`='{self.user_source_id}' "
        query_delete_sql += f"""
        WHERE `{self.target_user_table_name}`.`account` IS NULL and `user`.`account_mode`='{self.account_mode}'
        AND `user`.`user_source_id`='{self.user_source_id}'"""

        with self.context.get_project_db() as db:
            rows = db.query(query_delete_sql)
            user_ids = [row.get('id') for row in rows]
            if not user_ids:
                return

            accounts = [row.get('account') for row in rows]
            # 删除相关表数据
            sqls = [
                'DELETE from `dap_p_user` where `id` in %(user_ids)s',
                'DELETE from `dap_p_user_user_role` where `user_id` in %(user_ids)s',
                'DELETE from `dap_p_user_group_user` where `user_id` in %(user_ids)s',
                'DELETE from `dap_p_user_organization` where `user_id` in %(user_ids)s',
                'DELETE from `dap_p_user_2_third_party` where `user_id` in %(user_ids)s',
            ]
            try:
                for sql in sqls:
                    db.exec(sql, {'user_ids': user_ids}, commit=False)
                db.commit()
            except BaseException as e:
                db.rollback()
                raise e

        account_str = ','.join(accounts)
        self.info(f"删除用户成功, 删除数量<{len(accounts)}>, 详情: {account_str}", msg_type=NodeMsgType.DETAIL.value)


    def _get_user_source_all_account(self):
        # 查询该渠道下的所有用户account
        query_all_account_sql = f"select distinct `account` from `{self.target_user_table_name}` "
        if not self.is_erp_user_source:
            query_all_account_sql += f" where `user_source_id`='{self.user_source_id}'"
        with self.context.get_project_db() as db:
            rows = db.query(query_all_account_sql)
            accounts = [row.get('account') for row in rows]
        return accounts

    def import_dmp_user(self):
        # self._update_dmp_user()
        self._update_dmp_user_new()
        self._insert_dmp_user()
        self._delete_dmp_user()

    def _check_account_repeat(self, accounts):
        account_set = set()
        repeated_accounts = []
        for account in accounts:
            if account in account_set:
                repeated_accounts.append(account)
            account_set.add(account)
        if repeated_accounts:
            repeated_account_str = ','.join(list(set(repeated_accounts)))
            raise Exception(f'引入用户失败, 渠道中{repeated_account_str}用户存在不同信息')

    def _do_insert(self, insert_field_str, insert_value_str, import_accounts, accounts):
        insert_sql = f'''
            insert into `dap_p_user`({insert_field_str})
            select distinct {insert_value_str}
            from `{self.target_user_table_name}`
            where `account` in %s 
        '''
        if not self.is_erp_user_source:
            insert_sql += f" and `user_source_id`='{self.user_source_id}'"

        with self.context.get_project_db() as db:
            db.exec(insert_sql, [list(import_accounts)])

        account_str = ','.join(accounts)
        self.info(f"引入用户成功, 引入数量<{len(accounts)}>, 详情: {account_str}", msg_type=NodeMsgType.DETAIL.value)

    def _do_insert_new(self, insert_fields, insert_field_str, insert_value_str, import_accounts, accounts):
        insert_values = []
        for index, v in enumerate(insert_value_str.split(",")):
            insert_values.append(f"{v} as `{insert_fields[index][0]}`")
        insert_values = ",".join(insert_values)
        query_sql = f'''
            select distinct {insert_values}
            from `{self.target_user_table_name}`
            where `account` in %(import_accounts)s 
        '''
        if not self.is_erp_user_source:
            query_sql += f" and `user_source_id`='{self.user_source_id}'"

        def _trans(i, f):
            i[f] = escape_str(str(i.get(f))) if i.get(f) else ''
            if f == "mobile":
                return encrypt_field(i.get(f))
            if f == 'is_disabled' and not i[f]:
                return '0'
            return i.get(f)

        with self.context.get_project_db() as db:
            insert_fields = [i[0] for i in insert_fields]
            values = []
            for item in db.query(query_sql, {'import_accounts': list(import_accounts)}):
                v = [f"'{_trans(item, f)}'" for f in insert_fields]
                value = f"({','.join(v)})"
                values.append(value)
            if values:
                values = ",".join(values)
                insert_sql = f"""
                insert into `dap_p_user` ({insert_field_str})
                values {values}
                """
                db.exec(insert_sql)

        account_str = ','.join(accounts)
        self.info(f"引入用户成功, 引入数量<{len(accounts)}>, 详情: {account_str}", msg_type=NodeMsgType.DETAIL.value)

    def _generate_insert_values(self):
        # 设置待插入字段和默认值
        insert_fields = [('id', new_id()), ('name', None), ('account', None), ('pwd', "''"), ('old_pwd', '1'),
                         ('mobile', "''"), ('email', "''"), ('group_id', "''"), ('account_mode', f"'{self.account_mode}'"),
                         ('add_mode', f"'{ADD_MODE}'"), ('is_disabled', '0'), ('user_source_id', f"'{self.user_source_id}'")]

        # 如果在数据集有同步对应字段, 使用同步的字段, 否则使用默认值, 这里不插入group_id, 后面会用distinct对用户去重
        insert_values = []
        for field, default_value in insert_fields:
            map_field = self.user_field_mapping.get(field)
            if map_field and field != 'group_id':
                # if field == 'mobile':
                #     insert_values.append(f"hex(aes_encrypt(`{map_field}`, '{MYSQL_AES_KEY}'))")
                # else:
                insert_values.append(f'`{map_field}`')
                continue
            insert_values.append(default_value)

        # 检查待插入值不应该是None
        for value in insert_values:
            if value is None:
                raise Exception('用户信息错误, id, name, account信息不能为空')
        return insert_fields, insert_values

    def _insert_dmp_user(self):
        """查询中间表中未引入的用户, 并对用户进行引入"""
        self.info("更新用户表，同步插入用户", msg_type=NodeMsgType.STEP.value)

        if self.is_erp_user_source:
            user_source_accounts = self._get_user_source_all_account()
            if not user_source_accounts:
                self.info(f"跳过引入, 没有需要引入的用户", msg_type=NodeMsgType.DETAIL.value)
                return
            # 查询已经插入的账号, 区分出是否来源于该渠道, 如果不来源于该渠道, 需要进行提示, 并跳过引入
            query_repeated_account_sql = f"select `account`, `user_source_id` from `dap_p_user` where `account` in %(user_source_accounts)s"
            with self.context.get_project_db() as db:
                rows = db.query(query_repeated_account_sql, {'user_source_accounts': user_source_accounts})
                repeated_accounts = [row.get('account') for row in rows if row.get('user_source_id') != self.user_source_id]
                inserted_accounts = [row.get('account') for row in rows if row.get('user_source_id') == self.user_source_id]
            if repeated_accounts:
                repeated_account_str = ','.join(repeated_accounts)
                self.info(f"跳过引入用户{repeated_account_str}, 用户已存在, ", msg_type=NodeMsgType.DETAIL.value)
            import_accounts = set(user_source_accounts) - set(repeated_accounts) - set(inserted_accounts)
        else:
            sql = """select a.`account` from `dap_p_user_source_user` a 
            LEFT JOIN `dap_p_user` b on a.`account` = b.`account` 
            where b.`id` is null and a.`user_source_id` = '{}';""".format(self.user_source_id)
            with self.context.get_project_db() as db:
                import_accounts = db.query_columns(sql)

        if not import_accounts:
            self.info(f"跳过引入, 没有需要引入的用户", msg_type=NodeMsgType.DETAIL.value)
            return

        insert_fields, insert_values = self._generate_insert_values()

        # 生成插入sql, 执行插入操作
        insert_field_str = ','.join([f'`{item[0]}`' for item in insert_fields])
        insert_value_str = ','.join(insert_values)
        check_value_str = ','.join([item for item in insert_values if item[0] != 'id'])

        ## 先对用户数据进行检查, 看是否存在多个account相同的数据
        check_sql = f'''
            select distinct {check_value_str}
            from `{self.target_user_table_name}`
            where `account` in %(import_accounts)s 
        '''
        if not self.is_erp_user_source:
            check_sql += f" and `user_source_id`='{self.user_source_id}'"

        with self.context.get_project_db() as db:
            rows = db.query(check_sql, {'import_accounts': list(import_accounts)})
            accounts = [row.get('account') for row in rows]
            self._check_account_repeat(accounts)

        ## 执行插入操作
        # self._do_insert(insert_field_str, insert_value_str, import_accounts, accounts)
        self._do_insert_new(insert_fields, insert_field_str, insert_value_str, import_accounts, accounts)

    def _update_dmp_user(self):
        """更新用户数据"""
        self.info("更新用户表，同步更新用户", msg_type=NodeMsgType.STEP.value)

        # 如果在数据集有同步对应字段, 使用同步的字段, 否则不进行更新操作
        default_update_fields = ['name', 'pwd', 'mobile', 'email', 'is_disabled']
        update_fields_mapping = {}
        for field in default_update_fields:
            map_field = self.user_field_mapping.get(field)
            if map_field:
                update_fields_mapping[field] = map_field

        if update_fields_mapping:
            items = []
            for k, v in update_fields_mapping.items():
                if k == 'mobile':
                    items.append(f"user.{k}=hex(aes_encrypt({self.target_user_table_name}.{v}, '{MYSQL_AES_KEY}'))")
                else:
                    items.append(f"user.{k}={self.target_user_table_name}.{v}")

            set_str = ','.join(items)
            update_sql = f"""
            UPDATE `dap_p_user` as `user`, `{self.target_user_table_name}`
            SET {set_str}
            WHERE `user`.`account_mode`='{self.account_mode}' AND 
                  `user`.`account`=`{self.target_user_table_name}`.`account` AND
                  `user`.`user_source_id`='{self.user_source_id}'
            """
            if not self.is_erp_user_source:
                update_sql += f" AND `{self.target_user_table_name}`.`user_source_id`='{self.user_source_id}'"

            with self.context.get_project_db() as db:
                db.exec(update_sql)

    def _update_dmp_user_new(self):  # NOSONAR
        """更新用户数据"""
        self.info("更新用户表，同步更新用户", msg_type=NodeMsgType.STEP.value)

        # 如果在数据集有同步对应字段, 使用同步的字段, 否则不进行更新操作
        default_update_fields = ['name', 'pwd', 'mobile', 'email', 'is_disabled']
        update_fields_mapping = {}
        for field in default_update_fields:
            map_field = self.user_field_mapping.get(field)
            if map_field:
                update_fields_mapping[field] = map_field

        if update_fields_mapping:
            items = []
            for k, v in update_fields_mapping.items():
                items.append(f"`{self.target_user_table_name}`.`{v}`")

            query_fields = ','.join(items)

            need_query = f"""
            select `{self.target_user_table_name}`.`account`, {query_fields} from `{self.target_user_table_name}` 
            inner join `dap_p_user` as `user` on `user`.`account` = `{self.target_user_table_name}`.`account`
            WHERE `user`.`account_mode`='{self.account_mode}' AND 
            `user`.`user_source_id`='{self.user_source_id}'
            """

            if not self.is_erp_user_source:
                need_query += f" AND `{self.target_user_table_name}`.`user_source_id`='{self.user_source_id}'"

            with self.context.get_project_db() as db:
                UpdateUser().update_user(update_fields_mapping, need_query, db)

    def update_user_group_user(self):
        self.info("更新用户组用户关系", msg_type=NodeMsgType.STEP.value)
        with self.context.get_project_db() as db:
            # 1. 获取当前用户中心中来源于这个用户源的所有用户id
            rows = db.query('select `id`, `account` from `dap_p_user` where `user_source_id`=%(user_source_id)s', {'user_source_id': self.user_source_id})
            if not rows:
                return
            user_account_map = {
                user.get('account'): user.get('id')
                for user in rows
            }
            user_ids = set(user_account_map.values())
            rows = db.query('select `id` from `dap_p_user_group` where `user_source_id`=%(user_source_id)s', {'user_source_id': self.user_source_id})
            if not rows:
                return
            group_ids = set([group.get('id') for group in rows])

            # 2. 获取当前关系表中该数据源的(user_id, group_id)关系对
            query_sql = 'select `user_id`, `group_id` from `dap_p_user_group_user` where `user_source_id`=%(user_source_id)s'
            current_relations = db.query(query_sql, {'user_source_id': self.user_source_id})
            current_relations = {
                (row.get('user_id'), row.get('group_id'))
                for row in current_relations
            }

            # 3. 获取当前关系中间表中该数据源的(account, group_id)关系对
            # (这里需要实际以account+group_id作为唯一标识, user表中的id字段实际对应中间表的id字段, 中间表id字段可能发生变化)
            group_id_field = 'group_id' if self.is_erp_user_source else 'user_source_group_id'
            query_sql = f"select `account`, {group_id_field} as group_id from `{self.target_user_table_name}` "
            if not self.is_erp_user_source:
                query_sql += f"where `user_source_id`='{self.user_source_id}'"
            rows = db.query(query_sql)
            middle_relations = self._convert_to_tuple(rows, 'account', 'group_id')
            new_relations = set()
            for account, group_id in middle_relations:
                user_id = user_account_map.get(account)
                if not user_id:
                    continue
                # 过滤掉没有引入的用户
                if user_id not in user_ids:
                    continue
                # 过滤掉没有引入的用户组
                if group_id not in group_ids:
                    continue
                new_relations.add((user_id, group_id))

            # 4. 对比需要插入和删除的数据
            add_relations = new_relations - current_relations
            add_relations = [{
                'id': seq_id(),
                'user_id': user_id,
                'group_id': group_id,
                'user_source_id': self.user_source_id,
            } for (user_id, group_id) in add_relations]
            if add_relations:
                db.insert_multi_data('dap_p_user_group_user', add_relations, ['id', 'user_id', 'group_id', 'user_source_id'], commit=False)
            self.info(f"更新用户组织关系，添加{len(add_relations)}条关系", msg_type=NodeMsgType.DETAIL.value)

            del_relations = current_relations - new_relations
            if del_relations:
                params = {}
                for i in del_relations:
                    params.setdefault('user_ids', []).append(i[0])
                    params.setdefault('group_ids', []).append(i[1])
                sql = f"""delete from `dap_p_user_group_user` where `user_source_id`='{self.user_source_id}' and `user_id` in %(user_ids)s and `group_id` in %(group_ids)s""",
                db.exec(sql, params, commit=False)
            self.info(f"更新用户组织关系，删除{len(del_relations)}条关系", msg_type=NodeMsgType.DETAIL.value)
            db.commit()

    @staticmethod
    def _convert_to_tuple(rows, key_field, val_field):
        data = set()
        if not rows:
            return data
        for row in rows:
            ids = row.get(val_field, '').split(',')
            for id in ids:
                data.add((row.get(key_field), id))
        return data
