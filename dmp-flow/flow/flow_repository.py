# -*- coding: UTF-8 -*-
# pylint: skip-file
"""
Created on 2016年8月31日

@author: chenc04
"""

import datetime
import random
import uuid

from node.node import Node, NodeInstance, NodeInstanceStatus
from flow.flow import FlowInstanceStatus


class FlowRepository:
    def __init__(self, context):
        self.context = context

    def update_flow_run_status(self, status):
        with self.context.get_project_db() as db:
            return db.update('dap_bi_flow', {'run_status': status}, {'id': self.context.flow_id})

    def update_flow(self, flow_instance_data):
        # status 变成 run_status
        if flow_instance_data.get("status"):
            flow_instance_data["run_status"] = flow_instance_data.pop("status")
        with self.context.get_project_db() as db:
            return db.update('dap_bi_flow', flow_instance_data, {'id': self.context.flow_id})

    @staticmethod
    def get_unique_str():
        """
        获取唯一字符串
        :return:
        """
        key = ['qwertyuio123456789klzxcvbnm', 'zaq147wsx258edc369rfv456tgb753', '123456789abcdefghijklmnopqrs']
        tmp_str = ''
        i = 0
        while i < 20:
            rd = key[random.randint(0, 99999) % 2]
            tmp_str += rd[random.randint(0, 21)]
            i += 1
        rel = uuid.uuid3(uuid.NAMESPACE_DNS, tmp_str).urn.split(':')
        return rel[2]

    def get_flow(self, flow_id):

        with self.context.get_project_db() as db:
            return db.query_one('SELECT `id`,`name`,`description`,`type`,`schedule`,`status` FROM `dap_bi_flow` WHERE `id` = :flow_id', {'flow_id': flow_id})

    def get_depend_flow(self, flow_id):
        """
        根据flow_id获取依赖流程
        :param flow_id: 流程id
        :return: flow
        """
        with self.context.get_project_db() as db:
            return db.query(
                'SELECT id,name,description,`type`,schedule,`status`,depend_flow_id FROM dap_bi_flow WHERE depend_flow_id = :flow_id',
                {'flow_id': flow_id}
            )

    def get_depend_dataset(self, flow_id):
        """
        根据flow_id获取数据集依赖关系(嵌入数据集业务，后期重构需要剥离)
        :param flow_id: 流程id
        :return: flow
        """
        with self.context.get_project_db() as db:
            sql = (
                'SELECT `id`,name,description,`type`,`schedule`,status,depend_flow_id FROM dap_bi_flow '
                'INNER JOIN dap_bi_dataset_depend on depend_id = id  WHERE source_dataset_id = :flow_id'
            )
            return db.query(sql, {'flow_id': flow_id})

    def get_replacement_dataset(self, replacement_id):
        with self.context.get_project_db() as db:
            sql = 'SELECT `id`,`name`,`type`,`replacement_id` FROM dap_bi_dataset ' ' WHERE replacement_id = :replacement_id'
            return db.query_one(sql, {'replacement_id': replacement_id})

    def create_flow_instance(self, flow_data):
        with self.context.get_project_db() as db:
            flow_instance_id = self.get_unique_str()
            flow_instance_data = {
                'id': flow_instance_id,
                'flow_id': flow_data.get('id'),
                'name': flow_data.get('name'),
                'type': flow_data.get('type'),
                # 'startup_time': self.get_now_time(),
                'status': '已创建',
                'message': '',
            }
            db.insert("dap_bi_instance", flow_instance_data)
            return flow_instance_id

    def get_last_flow_instance(self, flow_id):
        """
        根据flow_id获取最新的流程实例
        :param flow_id: 流程id
        :return: instance
        """
        with self.context.get_project_db() as db:
            return db.query_one(
                'SELECT `id`,`flow_id`,`name`,`type`,startup_time,end_time,status,message FROM dap_bi_instance '
                'WHERE flow_id = :flow_id order by startup_time desc',
                {'flow_id': flow_id}
            )

    def get_runing_flow_instance(self, flow_id, status):
        """
        根据flow_id获取所有流程实例
        :param flow_id: 流程id
        :param status: 流程实例状态
        :return: instance
        """
        with self.context.get_project_db() as db:
            return db.query_one('SELECT id,status FROM dap_bi_instance WHERE flow_id = :flow_id and status = :status ', {'flow_id': flow_id, 'status': status})

    def get_flow_instance_by_flow_id(self, flow_id, status):
        """
        根据flow_id获取所有流程实例
        :param flow_id: 流程id
        :param status: 流程实例状态
        :return: instance
        """
        with self.context.get_project_db() as db:
            return db.query('SELECT id,status FROM dap_bi_instance WHERE flow_id = :flow_id and status = :status ', {'flow_id': flow_id, 'status': status})

    def get_flow_instance_by_id(self, instance_id):
        """
        根据instance_id获取流程实例
        :param instance_id: 流程实例id
        :return: instance
        """
        with self.context.get_project_db() as db:
            return db.query_one(
                'SELECT `id`,`flow_id`,`name`,`type`,startup_time,end_time,status,message FROM dap_bi_instance WHERE id = :instance_id ',
                {'instance_id': instance_id}
            )

    def activity_exist_alert(self, instance_id):
        """
        根据instance_id获取activity表中是否有alert记录
        :param instance_id: 流程实例id
        :return: instance
        """
        with self.context.get_project_db() as db:
            return db.query_one(
                'SELECT id FROM dap_bi_activity WHERE instance_id = :instance_id and status = :status',
                {'instance_id': instance_id, 'status': NodeInstanceStatus.ALERT.value}
            )

    def updata_flow_instance(self, flow_instance_data, flow_instance_id):
        status = flow_instance_data.get('status')
        with self.context.get_project_db() as db:

            if status and status != FlowInstanceStatus.TERMINATION.value:

                db.update('dap_bi_instance', flow_instance_data, dict(id=flow_instance_id))
            # 同步更新操作日志，这里状态做冗余的原因，考虑到后期有可能清理实例
            db.update('dap_bi_dataset_operate_record', {'run_status': status}, {'instance_id': flow_instance_id})
        # 如果是已忽略,不更新.
        if status and status != FlowInstanceStatus.IGNORED.value:
            self.update_flow(flow_instance_data)

    def update_instance(self, instance_data, instance_id):
        """
        更新实例
        """
        status = instance_data.get('status')
        with self.context.get_project_db() as db:
            db.update('dap_bi_instance', instance_data, dict(id=instance_id))
            # 同步更新操作日志，这里状态做冗余的原因，考虑到后期有可能清理实例
            db.update('dap_bi_dataset_operate_record', {'run_status': status}, {'instance_id': instance_id})
        if "message" in instance_data:
            del instance_data['message']
        self.update_flow(instance_data)

    def updata_flow_instance_message(self, flow_instance_id, message):
        with self.context.get_project_db() as db:
            db.update('dap_bi_instance', {"message": message}, dict(id=flow_instance_id))

    def create_node_instance(self, flow_instance_id, node_data):
        node_instance_data = {}
        node_instance_id = self.get_unique_str()

        node_instance_data["id"] = node_instance_id
        node_instance_data["instance_id"] = flow_instance_id
        node_instance_data["node_id"] = node_data.id
        node_instance_data["is_start"] = node_data.is_start
        node_instance_data["is_end"] = node_data.is_end
        node_instance_data["type"] = node_data.type
        node_instance_data["content"] = node_data.content
        node_instance_data["startup_time"] = self.get_now_time()
        node_instance_data["activity_data"] = ""
        node_instance_data["status"] = "已创建"
        node_instance_data["message"] = ""

        with self.context.get_project_db() as db:
            db.insert("dap_bi_activity", node_instance_data)

        return self.node_instance_dict_to_object(node_instance_data)

    def updata_node_instance(self, node_instance_data, node_instance_id):
        with self.context.get_project_db() as db:
            db.update('dap_bi_activity', node_instance_data, dict(id=node_instance_id))

    def updata_activity_by_instance(self, node_instance_data, condition_data):
        with self.context.get_project_db() as db:
            db.update('dap_bi_activity', node_instance_data, condition_data)

    def get_node_instance(self, node_instance_id):
        sql = (
            'SELECT `id`,`instance_id`,`node_id`,`is_start`,`is_end`,`type`,`content`,`startup_time`,`activity_data`'
            ' `status`, `message` FROM `dap_bi_activity` WHERE `id` = %(node_instance_id)s'
        )
        with self.context.get_project_db() as db:
            return db.query_one(sql, {'node_instance_id': node_instance_id})

    def get_root_node(self, flow_id):
        """
        根据flow_id查找根节点

        :param flow_id: 流程ID
        :return: node
        :rtype: dist

        """
        sql = (
            'SELECT `id`,`description`,`name`,`flow_id`,`is_start`,`is_end`,`type`,`content` '
            'FROM `dap_bi_node` '
            'WHERE `flow_id` = %(flow_id)s and `is_start`=1'
        )
        with self.context.get_project_db() as db:
            return db.query_one(sql, {'flow_id': flow_id})

    def get_nodes(self, flow_id):
        """
        根据flow_id获取所有的节点

        :param flow_id: 流程ID
        :return: node
        :rtype: list

        """
        sql = 'SELECT `id`,`description`,`name`,`flow_id`,`is_start`,`is_end`,`type`,`content` FROM `dap_bi_node` WHERE `flow_id` = %(flow_id)s'
        with self.context.get_project_db() as db:
            return db.query(sql, {'flow_id': flow_id})

    def get_node(self, node_id):
        """
        根据node_id获取节点
        :param node_id: 流程ID
        :return: node
        :rtype: list

        """
        sql = 'SELECT `id`,`description`,`name`,`flow_id`,`is_start`,`is_end`,`type`,`content` FROM `dap_bi_node` WHERE `id` = %(node_id)s'
        with self.context.get_project_db() as db:
            return db.query_one(sql, {'node_id': node_id})

    def get_after_node_ids(self, node_id):
        """
        根据节点ID获取所有前置节点的ID

        :param node_id: 节点ID
        :return: ids
        :rtype: list

        """

        with self.context.get_project_db() as db:
            return db.query('select ahead_node_id from dap_bi_line where behind_node_id = :node_id ', {'node_id': node_id})

    def get_lines(self, flow_id):
        """
        根据flow_id获取所有的线

        :param flow_id: 流程ID
        :return: line
        :rtype: list

        """

        with self.context.get_project_db() as db:
            return db.query('SELECT id,flow_id,ahead_node_id,behind_node_id FROM dap_bi_line WHERE flow_id = :flow_id ', {'flow_id': flow_id})

    def get_after_line(self, flow_id, previous_id):
        """
        根据flow_id和上一个节点id获取所有的线

        :param flow_id: 流程ID
        :param previous_id: 前一个节点id
        :return: line
        :rtype: list

        """

        with self.context.get_project_db() as db:
            return db.query(
                'SELECT id,flow_id,ahead_node_id,behind_node_id FROM dap_bi_line WHERE flow_id = :flow_id and ahead_node_id=:previous_id ',
                {'flow_id': flow_id, 'previous_id': previous_id}
            )

    @staticmethod
    def dict_to_node(dict_data):
        node = Node(
            id=dict_data.get('id'),
            name=dict_data.get('name'),
            flow_id=dict_data.get('flow_id'),
            type=dict_data.get('type'),
            is_start=dict_data.get('is_start'),
            is_end=dict_data.get('is_end'),
            content=dict_data.get('content'),
        )
        return node

    def list_dict_to_dict_node(self, list_dict_node):
        flow_nodes = {}
        for d in list_dict_node:
            node = self.dict_to_node(d)
            flow_nodes[node.id] = node
        return flow_nodes

    @staticmethod
    def get_now_time():
        return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    @staticmethod
    def node_instance_dict_to_object(node_instance_data):
        node_instance = NodeInstance(
            id=node_instance_data["id"],
            instance_id=node_instance_data["instance_id"],
            node_id=node_instance_data["node_id"],
            is_start=node_instance_data["is_start"],
            is_end=node_instance_data["is_end"],
            type=node_instance_data["type"],
            content=node_instance_data["content"],
            startup_time=node_instance_data["startup_time"],
            status=node_instance_data["status"],
            message=node_instance_data["message"],
            activity_data=node_instance_data["activity_data"],
        )
        return node_instance

    def update_instance_of_no_execute(self, instance_data, flow_id):
        """
        修改当前列队中已创建的任务状态
        """
        with self.context.get_project_db() as db:
            db.update('dap_bi_instance', instance_data, {"flow_id": flow_id, "status": FlowInstanceStatus.CREATE.value})
