# -*- coding: UTF-8 -*-
# pylint: skip-file
"""
Created on 2016年8月31日

@author: chenc04
"""
import json
import os
import time
import datetime
import traceback

from dmplib import config
from components.message_queue import MessageQueue
from components.my_threading import Thread
from components.redis import RedisCache

from dmplib.utils import auth_util
from dmplib.constants import FLOW_MONITORING_LOGSTORE, APP_RUNTIME
from dmplib.base.enums import DataSourceType
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dmplib.hug import g
from flow.errors import RDSError
from flow.flow import FlowInstanceStatus
from flow.flow_context import FlowContext
from flow.flow_repository import FlowRepository
from node.node_log_message import NodeMessage, NodeMsgType
from flow.flow_launcher import FlowLauncher
from dmplib.utils.loggers import logger


class FlowControl:
    def __init__(self, **kwargs):
        # 当前流程实例id
        self.project_code = kwargs.get('project_code')
        self.flow_id = kwargs.get('flow_id')
        self.flow_instance_id = kwargs.get('flow_instance_id')
        # 定时任务、手动任务标识
        self.schedule_task = kwargs.get('schedule_task', 0)
        self.test_run = int(kwargs.get('test_run', 1)) == 1
        self.context = FlowContext(self.project_code, self.flow_id, self.flow_instance_id)
        self.flow_repository = FlowRepository(self.context)
        self._init_log_handler()
        self._params_validate()
        self.flow_run_thread = None
        self.check_instance_thread = None
        self.current_parent_file_path = os.path.realpath(
            os.path.join(os.path.dirname(os.path.realpath(__file__)), '../')
        )
        self.current_process = None
        self.flow_run_result = (FlowInstanceStatus.FAILED.value, '错误')
        self.flow_start_timestamp = datetime.datetime.utcnow().timestamp()
        # 通道类型：慢通道、快通道
        self.channel_type = "offline" if os.environ.get("QUEUE_NAME_FLOW_OFFLINE") else "online"
        self.redis_key = "flow:run:%s:%s" % (self.project_code, self.flow_id)

        # 日志id
        self.detail_id = 1
        self.end_id = None

    def _init_log_handler(self):
        g.flow_instance_id = self.context.flow_instance_id
        g.code = self.context.project_code
        g.dataset_id = self.flow_id
        # 添加logstore
        # init_logstore_log(self.context.project_code, self.context.flow_instance_id, None)

    def _params_validate(self):
        if not self.project_code:
            raise Exception('缺少项目编码')
        if not self.flow_id:
            raise Exception('缺少流程Id')
        if not self.test_run and not self.flow_instance_id:
            raise Exception('缺少流程实例Id')
        g.code = self.project_code
        flow_data = self.flow_repository.get_flow(self.flow_id)
        if not flow_data:
            raise Exception('流程不存在')
        if APP_RUNTIME.lower() == 'dev' and not self.flow_instance_id:
            self.flow_instance_id = self.flow_repository.create_flow_instance(flow_data)
            self.context.flow_instance_id = self.flow_instance_id
        elif not self.flow_repository.get_flow_instance_by_id(self.flow_instance_id):
            raise Exception('流程实例不存在')
        self.context.flow_start_time = datetime.datetime.now()

    def run(self):

        # 1、启动流程
        node_message = NodeMessage(id=0, type=NodeMsgType.STEP.value, message="启动流程")
        logger.info(node_message)

        msg = "项目code：{project_code}，流程id：{flow_id}，" "流程实例id：{flow_instance_id}".format(
            project_code=self.project_code, flow_id=self.flow_id, flow_instance_id=self.flow_instance_id
        )
        self.start_info(msg)

        # 获取当前实例状态，如果已成功则直接跳过
        cur_instance = self.flow_repository.get_flow_instance_by_id(self.flow_instance_id) or {}
        if cur_instance.get("status") == FlowInstanceStatus.SUCCESS.value:
            self.end_info("当前任务已执行成功，直接跳过清洗")
            return

        try:
            lock = RedisCache().set_nx_data(self.redis_key, "lock", 10)
        except (ConnectionError, TimeoutError) as conn_error:
            self.start_error("Redis无法连接，错误内容：" + str(conn_error))
            self.flow_repository.updata_flow_instance(
                {
                    'status': FlowInstanceStatus.FAILED.value,
                    'startup_time': self.context.flow_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                },
                self.flow_instance_id,
            )
            now = self.get_now_time()
            logger.info("{now}, {flow_id}, {instance_id}, {status}, {code}"
                        .format(now=str(now), flow_id=self.flow_id,
                                instance_id=self.flow_instance_id,
                                status=FlowInstanceStatus.FAILED.value,
                                code=self.project_code))
            raise
        except BaseException:
            self.start_error('获取redis锁异常。')
            self.flow_repository.updata_flow_instance(
                {
                    'status': FlowInstanceStatus.FAILED.value,
                    'startup_time': self.context.flow_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                },
                self.flow_instance_id,
            )
            now = self.get_now_time()
            logger.info("{now}, {flow_id}, {instance_id}, {status}, {code}"
                        .format(now=str(now), flow_id=self.flow_id,
                                instance_id=self.flow_instance_id,
                                status=FlowInstanceStatus.FAILED.value,
                                code=self.project_code))
            raise

        if lock:
            # redis存储流程运行锁，开始运行流程。
            self._start_flow()
        else:
            # 首先获取数据库当前流程的所有实例，只要有再运行中的流程实例，系统自动中止该流程实例。
            # 有且一个实例等于当前消息实例ID，启动流程，更新redis锁的过期时间。
            # 否则认为重复流程中止该流程实例。

            instance = self.flow_repository.get_runing_flow_instance(self.flow_id, FlowInstanceStatus.RUNNING.value)
            if not instance:
                instances = self.flow_repository.get_flow_instance_by_flow_id(
                    self.flow_id, FlowInstanceStatus.CREATE.value
                )
                if (
                        instances
                        and len(instances) == 1
                        and instances[0].get('status') == FlowInstanceStatus.CREATE.value
                        and instances[0].get('id') == self.flow_instance_id
                ):
                    RedisCache().set_expire(self.redis_key, 5)
                    self._start_flow()
            else:
                message = "已经存在运行中的流程，系统自动忽略不运行该流程。"
                self.end_warning(message)
                self.flow_repository.updata_flow_instance(
                    {'status': FlowInstanceStatus.IGNORED.value, 'end_time': self.get_now_time(), 'message': message},
                    self.flow_instance_id,
                )
                now = self.get_now_time()
                logger.info("{now}, {flow_id}, {instance_id}, {status}, {code}"
                            .format(now=str(now), flow_id=self.flow_id,
                                    instance_id=self.flow_instance_id,
                                    status=FlowInstanceStatus.IGNORED.value,
                                    code=self.project_code))
                self.flow_repository.updata_activity_by_instance(
                    {'status': FlowInstanceStatus.IGNORED.value, 'end_time': self.get_now_time()},
                    dict(instance_id=self.flow_instance_id, status=FlowInstanceStatus.RUNNING.value),
                )

    def _start_flow(self):
        try:
            # 更新流程实例
            self.flow_repository.updata_flow_instance(
                {
                    'status': FlowInstanceStatus.RUNNING.value,
                    'startup_time': self.context.flow_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                },
                self.flow_instance_id,
            )
            # now = self.get_now_time()
            # logger.info("{now}, {flow_id}, {instance_id}, {status}, {code}"
            #             .format(now=str(now), flow_id=self.flow_id,
            #                     instance_id=self.flow_instance_id,
            #                     status=FlowInstanceStatus.RUNNING.value,
            #                     code=self.project_code))
        except BaseException:
            # RDS连接失败
            message = '更新流程实例{}为运行中,RDS连接失败。'.format(self.flow_instance_id)
            self.start_error(message)
            raise RDSError(
                message, project_code=self.project_code, flow_id=self.flow_id, instance_id=self.flow_instance_id
            )

        try:
            self.flow_run_thread = Thread(target=self._run_flow)
            self.flow_run_thread.start()
            self.check_instance_thread = Thread(target=self._check_flow_instance_status)
            self.check_instance_thread.start()
            self.flow_run_thread.join()
            self.check_instance_thread.join()

            status, log = self.flow_run_result
            instance_status = self._get_flow_instance_status()
            if instance_status in [FlowInstanceStatus.TERMINATION.value, FlowInstanceStatus.IGNORED.value]:
                status = instance_status
            # 失败的情况下查询下activity表中有无警告数据
            elif status == FlowInstanceStatus.FAILED.value and self._activity_exist_alert():
                status = FlowInstanceStatus.ALERT.value
            # 更新流程实例的状态
            self.flow_repository.updata_flow_instance(
                {'status': status, 'end_time': self.get_now_time()}, self.flow_instance_id
            )
            # 记录天眼日志
            instance = self.flow_repository.get_flow_instance_by_id(self.flow_instance_id)
            FlowLauncher.dataset_fastlogger(instance)

            # now = self.get_now_time()
            # logger.info("{now}, {flow_id}, {instance_id}, {status}, {code}"
            #             .format(now=str(now), flow_id=self.flow_id,
            #                     instance_id=self.flow_instance_id,
            #                     status=status,
            #                     code=self.project_code))
            # 更新节点实例的状态
            self.flow_repository.updata_activity_by_instance(
                {'status': status, 'end_time': self.get_now_time()},
                dict(instance_id=self.flow_instance_id, status=FlowInstanceStatus.RUNNING.value),
            )
            if status == FlowInstanceStatus.SUCCESS.value:
                self.end_info(log)
                # 清洗成功，则把当前队列中已创建的任务改为 已成功
                now = self.get_now_time()
                self.flow_repository.update_instance_of_no_execute(
                    {'status': status, 'end_time': now, 'startup_time': now,
                     'message': '清洗任务已成功，同时间内的相同任务则直接改为已成功'},
                    self.flow_id
                )
            else:
                self.end_error(log)
            self._patch_api_flow_status()
        except Exception:
            # 运行流程中失败
            # redis 连接失败
            self.end_error('运行流程中失败。')
            self.flow_repository.updata_flow_instance(
                {
                    'status': FlowInstanceStatus.FAILED.value,
                    'startup_time': self.context.flow_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                },
                self.flow_instance_id,
            )
            now = self.get_now_time()
            logger.info("{now}, {flow_id}, {instance_id}, {status}, {code}"
                        .format(now=str(now), flow_id=self.flow_id,
                                instance_id=self.flow_instance_id,
                                status=FlowInstanceStatus.FAILED.value,
                                code=self.project_code))
            self._patch_api_flow_status()
            raise

        # 发送依赖流程消息
        self.send_depend_flow_message()

        # 流程实例数据写入流程监控
        self.write_flow_monitor()

    def _patch_api_flow_status(self):
        """
        临时代码，处理api数据集的调度状态，这里是切换到celery执行
        临时将任务状态都切换会运行中，后面有celery进行状态更新
        """
        from dmplib.data_source.services.data_source_service import get_data_source
        from dmplib.base import repository
        try:
            dataset_info = repository.get_data(
                'dap_bi_dataset', conditions={'id': self.flow_id}, multi_row=False, fields=['content']
            ) or {}
            data_source_id = json.loads(dataset_info.get('content', '{}')).get('data_source_id', '')
        except Exception as e:
            return
        model = get_data_source(data_source_id)
        if model and model.type in [DataSourceType.API.value, DataSourceType.MysoftShuXin15.value]:
            with repository.get_db() as db:
                data = {'status': FlowInstanceStatus.RUNNING.value, 'end_time':None}
                db.update('dap_bi_instance', data=data, condition={'id': self.context.flow_instance_id})
                db.update('dap_bi_activity', data=data, condition={'instance_id': self.context.flow_instance_id})
                db.update('dap_bi_flow', data= {'run_status': FlowInstanceStatus.RUNNING.value}, condition={'id': self.flow_id})

    def _run_flow(self):
        """
       启动流程进程函数
       :return: pid 进程id
       """
        try:
            data = {
                "project_code": self.project_code, "flow_id": self.flow_id, "flow_instance_id": self.flow_instance_id,
                "test_run": '1' if self.test_run else '0', 'schedule_task': str(self.schedule_task)
            }
            # msg = "执行run_flow, 参数：{}".format(data)
            # self.start_info(msg)

            sub_g = _AppCtxGlobals()
            _app_ctx_stack.push(sub_g)
            sub_g.dataset_id = self.flow_id

            FlowLauncher(**data).run_flow()

            self.flow_run_result = (FlowInstanceStatus.SUCCESS.value, '流程实例运行成功')
        except BaseException as exception:
            msg = str(exception) + "：" + traceback.format_exc()
            self.flow_run_result = (FlowInstanceStatus.FAILED.value, msg)
        finally:
            _app_ctx_stack.pop()

    def _check_flow_instance_status(self):
        cache = RedisCache()
        while True:
            if not self.flow_run_thread.is_alive():
                # 终止当前线程
                self.check_instance_thread.terminate()
                cache.del_data(self.redis_key)
            instance_status = self._get_flow_instance_status()
            if instance_status != FlowInstanceStatus.RUNNING.value:
                if (
                        instance_status == FlowInstanceStatus.TERMINATION.value
                        or instance_status == FlowInstanceStatus.IGNORED.value
                ):
                    # self.current_process.kill()
                    self.flow_run_thread.is_alive() and self.flow_run_thread.terminate()
                    self.check_instance_thread.terminate()
                else:
                    cache.del_data(self.redis_key)
                    self.flow_run_thread.is_alive() and self.flow_run_thread.terminate()
                    self.check_instance_thread.terminate()
            # 运行中的流程继续维护redis lock
            cache.set_expire(self.redis_key, 5)
            time.sleep(1)

    def _get_flow_instance_status(self):
        """
        获取实例状态
        :return:bool
        """
        try:
            instance = self.flow_repository.get_flow_instance_by_id(self.flow_instance_id)
        except BaseException as e:
            logger.error('获取实例状态失败：' + self.flow_instance_id)
            logger.error(e)
            return False
        if not instance:
            logger.error('实例不存在')
            return False
        return instance.get('status')

    def _activity_exist_alert(self):
        """
        activity表中是否有alert状态数据
        :return:bool
        """
        return self.flow_repository.activity_exist_alert(self.flow_instance_id)

    def send_depend_flow_message(self):
        if self.test_run or self.flow_run_result[0] != FlowInstanceStatus.SUCCESS.value:
            return

        current_flow_data = self.flow_repository.get_flow(self.flow_id)
        source_flow_id = self.flow_id
        if current_flow_data and current_flow_data.get("type") == "数据集":
            replacement_data = self.flow_repository.get_replacement_dataset(self.flow_id)
            if replacement_data and replacement_data.get("id"):
                source_flow_id = replacement_data.get("id")
        depend_flow_list = self.flow_repository.get_depend_flow(source_flow_id)
        depend_dataset_list = self.flow_repository.get_depend_dataset(source_flow_id)

        # 加入数据集依赖
        if depend_flow_list:
            depend_flow_list.extend(depend_dataset_list)
        else:
            depend_flow_list = depend_dataset_list

        if not depend_flow_list:
            return

        self.send_rabbitmq(depend_flow_list)

    def send_rabbitmq(self, depend_flow_list):

        mq = MessageQueue()
        for depend_flow in depend_flow_list:
            flow_data = self.flow_repository.get_flow(depend_flow.get('id'))
            if not flow_data:
                continue

            if depend_flow.get("type") == "数据集":
                queue_name = config.get('RabbitMQ.queue_name_flow', 'dmp_flow')
            else:
                # 去掉offline
                # queue_name = os.environ.get("QUEUE_NAME_FLOW_OFFLINE", "Flow-offline")
                queue_name = config.get('RabbitMQ.queue_name_flow', 'dmp_flow')

            flow_instance_id = self.flow_repository.create_flow_instance(flow_data)
            body = {
                'project_code': self.context.project_code,
                'flow_id': depend_flow.get("id"),
                'flow_instance_id': flow_instance_id,
                'test_run': "0",
                "root_flow_id": self.flow_id,
            }
            mq.send_message(queue_name, json.dumps(body), durable=False)

            msg = "执行依赖流程，队列： {}, 流程id：{}，流程名称：{}".format(queue_name, depend_flow.get('id'),
                                                                        depend_flow.get('name'))
            self.end_info(msg)

    def write_flow_monitor(self):
        """
        将流程实例写入监控流程的阿里云日志中
        :return:
        """
        flow_instance = self.flow_repository.get_flow_instance_by_id(self.flow_instance_id)
        flow_instance['startup_time'] = datetime.datetime.strftime(
            flow_instance.get('startup_time'), '%Y-%m-%d %H:%M:%S'
        )
        flow_instance['end_time'] = (
            datetime.datetime.strftime(flow_instance.get('end_time'), '%Y-%m-%d %H:%M:%S')
            if flow_instance.get('end_time')
            else ""
        )
        flow_instance.pop("message")
        contents = list(flow_instance.items())
        contents.append(("env_code", auth_util.get_env_code()))
        contents.append(('project_code', str(self.project_code)))
        contents.append(('message', flow_instance.get('status')))

        # aliyun_log = AliyunLog(log_store=FLOW_MONITORING_LOGSTORE)
        # aliyun_log.put_logs(contents)

    def start_info(self, msg):
        node_message = NodeMessage(id=self.detail_id, type=NodeMsgType.DETAIL.value, parent_id=0, message=msg)
        self.detail_id += 1
        logger.info(node_message)

    def start_error(self, msg):
        node_message = NodeMessage(id=self.detail_id, type=NodeMsgType.DETAIL.value, parent_id=0, message=msg)
        self.detail_id += 1
        logger.error(node_message)

    def end_step(self):
        if not self.end_id:
            self.end_id = 99999999
            node_message = NodeMessage(id=self.end_id, type=NodeMsgType.STEP.value, message="结束流程")
            logger.info(node_message)

    def end_info(self, msg):
        self.end_step()
        node_message = NodeMessage(id=self.detail_id, type=NodeMsgType.DETAIL.value, parent_id=self.end_id, message=msg)
        self.detail_id += 1
        logger.info(node_message)

    def end_warning(self, msg):
        self.end_step()
        node_message = NodeMessage(id=self.detail_id, type=NodeMsgType.DETAIL.value, parent_id=self.end_id, message=msg)
        self.detail_id += 1
        logger.warning(node_message)

    def end_error(self, msg):
        self.end_step()
        node_message = NodeMessage(id=self.detail_id, type=NodeMsgType.DETAIL.value, parent_id=self.end_id, message=msg)
        self.detail_id += 1
        logger.error(node_message)

    @staticmethod
    def get_now_time():
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
