# -*- coding: UTF-8 -*-
'''
Created on 2016年9月5日

@author: chenc04
'''
import datetime
import time
import logging
import random
import unittest
from collections import namedtuple

from dmplib.hug import g
from hug.types import json, uuid
from flow.flow import Flow
from flow.flow_context import FlowContext
from flow.flow_repository import FlowRepository
from flow.line import Line
from node.node import NodeInstance, Node

logger = logging.getLogger(__name__)


class TestFlowRepository(unittest.TestCase):
    def __init__(self):
        self.project_code = "dev"
        self.flow_id = "d7460ea6-8d1c-11e6-a706-0242c0a80004"
        g.code = self.project_code
        self.context = FlowContext(self.project_code, self.flow_id)
        self.flowRepository = FlowRepository(self.context)

    def test_get_last_flow_instance(self):
        """
        测试 根据flow_id获取最新的流程实例
        :param flow_id: 流程id
        :return: instance
        """
        with self.context.get_project_db() as db:
            flow_instance = db.query_one(
                'SELECT `id`,flow_id,name,`type`,startup_time,end_time,status,message FROM dap_bi_instance WHERE flow_id = %s order by startup_time desc',
                ("a6c59efc-99bc-11e6-bdc4-0242ac110003",))

            start_time = flow_instance["startup_time"]

            current_time = datetime.datetime.strptime(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                                      "%Y-%m-%d %H:%M:%S")

            differ_day = (current_time - start_time).seconds

            if differ_day > 172800:
                self.assertTrue(True)
            else:
                self.assertTrue(False)

    def test_get_root_node(self):
        fr = FlowRepository()
        fr.create_flow_instance(fr.get_flow("41e4fe4f6698f121d9854a882a71be08"))

    def _dict_to_object(self, d):
        class ObjectClass:
            pass

        for _k in d:
            if type(d[_k]) == dict:
                setattr(ObjectClass, _k, NodeInstance(**d[_k]))
            else:
                setattr(ObjectClass, _k, d[_k])
        return ObjectClass

    def dict_to_object(self, dict, type_name):
        return json.loads(dict, object_hook=lambda d: namedtuple(type_name, d.keys())(*d.values()))

    def test_assemble_flow(self):

        flow1 = Flow("test_flow_1", "test_flow_1")

        flow2 = Flow("test_flow_2", "test_flow_2")

        flows = [flow1, flow2]

        return flows

    def test_assemble_node(self):

        #         #nodes=[["node_1","采集节点"],["node_2","odps_sql节点"],["node_3","odps_sql节点"],["node_4","odps_sql节点"]]
        #
        #         n1 =  Node("test_flow_1","node_1","cllect_node",True,False)
        # #         n1.id="node_1"
        # #         n1.type="cllect_node"
        #
        #         n2 =  Node("test_flow_1","node_2","odps_sql_node")
        # #         n2.id="node_2"
        # #         n2.type="odps_sql_node"
        #
        #         n3 =  Node("test_flow_1","node_3","odps_sql_node")
        # #         n3.id="node_3"
        # #         n3.type="odps_sql_node"
        #
        #         n4 =  Node("test_flow_1","node_4","odps_sql_node")
        # #         n4.id="node_4"
        # #         n4.type="odps_sql_node"
        #
        #         n5 =  Node("test_flow_1","node_5","odps_sql_node")
        #
        #         nodes={"node_1":n1,"node_2":n2,"node_3":n3,"node_4":n4,"node_5":n5}



        n1 = Node("test_flow_1", "node_1", "cllect_node", True, False)
        n2 = Node("test_flow_1", "node_2", "odps_sql_node")
        n3 = Node("test_flow_1", "node_3", "odps_sql_node")
        n4 = Node("test_flow_1", "node_4", "odps_sql_node")
        n5 = Node("test_flow_1", "node_5", "odps_sql_node")

        nodes1 = {"node_1": n1, "node_2": n2, "node_3": n3, "node_4": n4, "node_5": n5}

        n11 = Node("test_flow_2", "node_1", "cllect_node", True, False)

        nodes2 = {"node_1": n11}

        flow_nodes = {"test_flow_1": nodes1, "test_flow_2": nodes2}

        return flow_nodes

    def test_assemble_line(self):

        line1 = Line("test_flow_1", "line_1", "node_1", "node_2")

        line2 = Line("test_flow_1", "line_2", "node_1", "node_3")

        line3 = Line("test_flow_1", "line_3", "node_2", "node_4")

        line4 = Line("test_flow_1", "line_4", "node_3", "node_4")

        line5 = Line("test_flow_1", "line_5", "node_2", "node_5")

        lines1 = [line1, line2, line3, line4, line5]

        flow_lines = {"test_flow_1": lines1}

        return flow_lines

    def get_unique_str(self):
        """
        获取唯一字符串
        :return:
        """
        key = ['qwertyuio123456789klzxcvbnm', 'zaq147wsx258edc369rfv456tgb753', '123456789abcdefghijklmnopqrs']
        round = list(range(1, 20))
        str = ''
        for _ in round:
            rd = key[random.randint(0, 99999) % 2]
            str = str + rd[random.randint(0, 21)]

        rel = uuid.uuid3(uuid.NAMESPACE_DNS, str).urn.split(':')
        return rel[2]

    def get_flow(self, flow_id):

        with self.context.get_project_db() as db:
            return db.query_one('SELECT * FROM dap_bi_flow WHERE id = %s', (flow_id,))

    def create_flow_instance(self, flow_data):
        flow_instance_id = None
        print("创建流程实例")
        with self.context.get_project_db() as db:
            flow_instance_id = self.get_unique_str()
            flow_instance_data = {}

            flow_instance_data["id"] = flow_instance_id
            flow_instance_data["flow_id"] = flow_data["id"]
            flow_instance_data["name"] = flow_data["name"]
            flow_instance_data["type"] = flow_data["type"]
            flow_instance_data["startup_time"] = self.get_now_time()
            flow_instance_data["status"] = "已创建"
            flow_instance_data["message"] = ""

            db.insert("dap_bi_instance", flow_instance_data)

        return flow_instance_id

    def updata_flow_instance(self, flow_instance_data, flow_instance_id):
        with self.context.get_project_db() as db:
            db.update('dap_bi_instance', flow_instance_data, dict(id=flow_instance_id))

    def create_node_instance(self, flow_instance_id, node_data):

        print("创建节点实例")

        node_instance_data = {}
        node_instance_id = self.get_unique_str()

        node_instance_data["id"] = node_instance_id
        node_instance_data["instance_id"] = flow_instance_id
        node_instance_data["node_id"] = node_data.id
        node_instance_data["is_start"] = node_data.is_start
        node_instance_data["is_end"] = node_data.is_end

        node_instance_data["type"] = node_data.type
        node_instance_data["content"] = node_data.content
        node_instance_data["startup_time"] = self.get_now_time()

        node_instance_data["activity_data"] = ""
        node_instance_data["status"] = "已创建"
        node_instance_data["message"] = ""

        with self.context.get_project_db() as db:
            db.insert("dap_bi_activity", node_instance_data)

        return self.node_instance_dict_to_object(node_instance_data)

    #         return self._dict_to_object(node_instance_data)
    #         return self.dict_to_object(node_instance_data,"NodeInstance")


    def updata_node_instance(self, node_instance_data, node_instance_id):
        with self.context.get_project_db() as db:
            db.update('dap_bi_activity', node_instance_data, dict(id=node_instance_id))

    def get_root_node(self, flow_id):
        """
        根据flow_id查找根节点

        :param flow_id: 流程ID
        :return: node
        :rtype: dist

        :Example:
        >>>

        """
        with self.context.get_project_db() as db:
            return db.query_one('SELECT * FROM dap_bi_node WHERE flow_id = %s and is_start=1', (flow_id,))

            #         for k,v in  self.test_assemble_node().items():
            #             if k == flow_id:
            #
            #                 for (k,v) in  v.items():
            #                     if  v.start :
            #
            #                         #print(v.id,v.node_type,v.start)
            #
            #                         return v
            #
            #         return  None

    def get_nodes(self, flow_id):
        """
        根据flow_id获取所有的节点

        :param flow_id: 流程ID
        :return: node
        :rtype: list

        :Example:
        >>>

        """

        with self.context.get_project_db() as db:
            return db.query('SELECT * FROM dap_bi_node WHERE flow_id = %s ', (flow_id,))

    def get_lines(self, flow_id):
        """
        根据flow_id获取所有的线

        :param flow_id: 流程ID
        :return: line
        :rtype: list

        :Example:
        >>>

        """

        with self.context.get_project_db() as db:
            return db.query('SELECT * FROM dap_bi_line WHERE flow_id = %s ', (flow_id,))

            #         for k,v in  self.test_assemble_line().items():
            #             if k == flow_id:
            #                 return v
            #         return  []

    def get_after_line(self, flow_id, previous_id):
        """
        根据flow_id和上一个节点id获取所有的线

        :param flow_id: 流程ID
        :param previous_id: 前一个节点id
        :return: line
        :rtype: list

        :Example:
        >>>

        """

        with self.context.get_project_db() as db:
            return db.query('SELECT * FROM dap_bi_line WHERE flow_id = %s and ahead_node_id=%s ', (flow_id, previous_id))


            #         for k,v in  self.test_assemble_line().items():
            #             if k == flow_id:
            #                 result_lines = []
            #
            #                 for line in v:
            #                     if previous_id == line.previous_id:
            #
            #                         result_lines.append(line)
            #
            #                 return result_lines
            #
            #
            #         return []

    def dict_to_node(self, dict):
        node = Node(dict["id"], dict["flow_id"], dict["type"], dict["is_start"], dict["is_end"], dict["content"])
        return node

    def list_dict_to_dict_node(self, list_dict_node):
        flow_nodes = {}

        for dict in list_dict_node:
            node = self.dict_to_node(dict)
            flow_nodes[node.id] = node

        return flow_nodes

    def get_now_time(self):
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def node_instance_dict_to_object(self, node_instance_data):
        node_instance = NodeInstance(node_instance_data)
        return node_instance

    def test_timestamp(self):
        cur_time = time.time()
        print(str(cur_time))

        with self.context.get_project_db() as db:
            data = db.query('SELECT * FROM dap_bi_data_collection WHERE table_name = %s ', ("cb_HkbProductWork",))

            print(data[0]["last_timestamp"])


if __name__ == '__main__':
    unittest.main()
