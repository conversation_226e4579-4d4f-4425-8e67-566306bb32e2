# -*- coding: UTF-8 -*-
'''
Created on 2016年9月5日

@author: chenc04
'''
import logging
import unittest

from dmplib.hug import g
from odps.models import Schema, Record
from flow.flow_context import FlowContext
from flow.flow_repository import FlowRepository
from node.node import Node
logger = logging.getLogger(__name__)


class TestFlowLauncher(unittest.TestCase):
    def __init__(self):
        self.project_code = "cc_test"
        self.flow_id = "d7460ea6-8d1c-11e6-a706-0242c0a80004"
        g.code = self.project_code
        self.context = FlowContext(self.project_code, self.flow_id)
        self.flowRepository = FlowRepository(self.context)

    def test_init(self):
        self.context.get_odps().delete_table("slave_1", if_exists=True)
        self.context.get_odps().delete_table("slave_2", if_exists=True)

        # 创建主表
        schema = Schema.from_lists(['master_id', 'name', 'age', 'ages', 'gender'],
                                   ['string', 'string', 'string', 'string', 'string'])
        data = [['1', '张三', '28', '2', '1'],
                ['2', '李四', '20', '1', '2']]
        self.odps_data('fact_master', schema, data)

        # 创建从表1
        schema = Schema.from_lists(['master_id', 'project_id', 'if_trade', 'trade_price'],
                                   ['string', 'string', 'string', 'string'])
        data = [['00011d51-0c05-4e84-bada-988a9f9a78b5', '项目C', '1', '980000'],
                ['00011d51-0c05-4e84-bada-988a9f9a78b5', '项目D', '1', '580000']]
        self.odps_data('slave_1', schema, data)

        # 创建从表2
        schema = Schema.from_lists(['master_id', 'project_id', 'stage_id', 'intention_price'],
                                   ['string', 'string', 'string', 'string'])
        data = [['00011d51-0c05-4e84-bada-988a9f9a78b5', '项目A', '看房', '100'],
                ['00011d51-0c05-4e84-bada-988a9f9a78b5', '项目A', '成交', '100'],
                ['00009fc8-0bfb-4d58-8c71-e7c175a98ac3', '项目B', '看房', '200']]
        self.odps_data('slave_2', schema, data)

        # 执行指标json节点
        node = Node("id", "flow_id", "type", "is_start", "is_end", "master_table,slave_1,slave_2", "")
        sne = IndicatorPortrait(self.context, node)
        result = sne.execution()
        logger.error(result.result)
        logger.error(result.msg)

    def odps_data(self, table_name, schema, data):
        if not self.context.get_odps().exist_table(table_name):
            # 创建表
            table = self.context.get_odps().create_table(
                table_name,
                schema=schema,
                if_not_exists=True
            )
            records = [Record(schema=schema, values=values) for values in data]
            # 写入数据
            self.context.get_odps().write_table(table, 0, records)

    def test_more_odps_table(self):

        table_names = []
        for num in range(32):
            num = num + 1
            table_name = "slave_" + str(num)
            table_names.append(table_name)

        for table_name in table_names:
            schema = Schema.from_lists(['master_id', 'name'], ['string', 'string'])
            data = [['00011d51-0c05-4e84-bada-988a9f9a78b5', table_name]]
            self.odps_data(table_name, schema, data)

        for table_name in table_names:
            indicator_data = {
                "id": '1',
                "tmpl_id": '1',
                "type_id": '1',
                "name": table_name,
                "odps_table": table_name,
                "odps_field": table_name,
                "is_dimension": '0',
                "rank": 1
            }
            with self.context.get_project_db() as db:
                dict = db.query_one('SELECT id FROM dap_bi_indicator WHERE id = %s', (table_name,))
                logger.info(dict)
                if dict == None:
                    db.insert("dap_bi_indicator", indicator_data)

        # 执行指标json节点
        node = Node("id", "flow_id", "type", "is_start", "is_end", "", "")
        sne = IndicatorPortrait(self.context, node)
        result = sne.execution()
        logger.error(result.result)
        logger.error(result.msg)

        for table_name in table_names:
            with self.context.get_project_db() as db:
                db.delete_by_id('dap_bi_indicator', table_name)

    def test_delete_tables(self):
        table_names = []
        for num in range(32):
            num = num + 1
            table_name = "slave_" + str(num)
            table_names.append(table_name)

        for table_name in table_names:
            with self.context.get_project_db() as db:
                db.delete_by_id('dap_bi_indicator', table_name)

    def test_sync_rds(self):
        node = Node("id", "flow_id", "type", "is_start", "is_end", "", "")
        sne = IndicatorPortrait(self.context, node)
        # 创建RDS表
        column_list = sne.create_rds_table(sne.fact_360)
        # 同步360
        odps_to_rds = OdpsToRds()
        sync_result = odps_to_rds.execution(node.id, sne.fact_360, column_list)
        self.assertTrue(sync_result.result)


if __name__ == '__main__':
    unittest.main()
