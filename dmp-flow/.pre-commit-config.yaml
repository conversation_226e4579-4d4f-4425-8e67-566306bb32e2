exclude: ^(blib2to3/|profiling/|tests/data/)

repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v1.2.3
    hooks:
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
    -   id: name-tests-test

-   repo: https://github.com/ambv/black
    rev: 18.9b0
    hooks:
    - id: black
      name: black
      language: system
      types: [python]
      entry: black
      args:
      - --verbose
      - --skip-string-normalization
      - --line-length=120

-   repo: https://github.com/pre-commit/mirrors-pylint
    rev: v2.1.1
    python_version: python3.6
    hooks:
    - id: pylint
      files: \.py$
      language: system
      entry: pipenv run pylint
