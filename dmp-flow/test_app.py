# -*- coding: UTF-8 -*-
"""
Created on 2016年8月31日

@author: chenc04
"""
import pymysql
from sshtunnel import BaseSSHTunnelForwarderError, SSHTunnelForwarder
from dmplib.base.repository import SimpleMysql

def connect(self):
    """Connect to the mysql server"""

    try:
        if self.use_ssh:
            try:
                self.ssh = SSHTunnelForwarder(
                    (self.ssh_host, int(self.ssh_port)),
                    ssh_username=self.ssh_user,
                    ssh_password=self.ssh_password,
                    remote_bind_address=(self.host, int(self.port)),
                )
                self.ssh.start()
            except BaseSSHTunnelForwarderError:
                logging.exception('ssh connection failed')
                self.ssh = None
        if self.host == 'rm-bp10jpberdfgm88e5.mysql.rds.aliyuncs.com':
            self.host = '*********'
            self.port = 5590
            self.user = 'mic_test'
            self.passwd = 'Mic@2017'
        if self.host == 'rm-bp1f9pc4yqu7v84x2.mysql.rds.aliyuncs.com':
            self.host = '*********'
            self.port = 5589
            self.user = 'mic_test'
            self.passwd = 'Mic@95938'
        if self.host == '127.0.0.1':
            self.host = '*********'

        self.conn = pymysql.connect(
            db=self.database,
            host=self.host,
            port=int(self.port) if not self.ssh else int(self.ssh.local_bind_port),
            user=self.user,
            passwd=self.password,
            charset=self.charset,
            connect_timeout=self.connect_timeout,
            ssl_disabled=True
        )
        self.cur = self.conn.cursor()
        self.conn.autocommit(self.autocommit)
    except BaseException as e:
        if self.retry > 0:
            self.retry -= 1
            logging.exception(
                "MySQL connection failed and retry %s, host: %s", (self.retry_max - self.retry), self.host
            )
            self.connect()
        else:
            raise e

SimpleMysql.connect = connect

import logging
from flow.flow_launcher import FlowLauncher

logger = logging.getLogger(__name__)

def test_sync_flow():
    data = {
        'project_code': 'luyy',
        'flow_id': '3a024219-8ce6-329f-505a-4b603d716281',
        'flow_instance_id': '163bb85e-9620-11ec-8709-7cd30ae45d22'
    }
    FlowLauncher(**data).run_flow()


def test_import_flow():
    data = {
        'project_code': 'test',
        'flow_id': '3a01ea54-b386-40ee-c50e-8fb9b11fd89c',
        'flow_instance_id': '3a01ea54-b386-40ee-c50e-8fb9b11fd89c'
    }
    FlowLauncher(**data).run_flow()

if __name__ == '__main__':
    test_sync_flow()
