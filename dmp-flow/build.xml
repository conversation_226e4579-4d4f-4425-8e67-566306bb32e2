<?xml version="1.0" encoding="UTF-8"?>
<project name="dmp-flow" default="build" xmlns:sonar="antlib:org.sonar.ant">
    <!-- By default, we assume all tools to be on the $PATH -->
    <property name="toolsdir" value=""/>

    <property name="sonar.host.url" value="http://sonar.mypaas.com.cn"/>
    <property name="sonar.login" value="****************************************"/>

    <!-- Define the SonarQube project properties -->
    <property name="sonar.projectKey" value="cn.com.mypaas:dmp-flow"/>
    <property name="sonar.projectName" value="DMP.Flow"/>
    <property name="sonar.projectVersion" value="1.0"/>
    <property name="sonar.sources" value="flow,node"/>
    <!--<property name="sonar.tests" value="tests"/>-->
    <property name="sonar.exclusions" value=""/>
    <property name="sonar.language" value="py"/>
    <property name="sonar.sourceEncoding" value="UTF-8"/>
    <!--<property name="sonar.python.xunit.reportPath" value="tests/nosetests.xml" />-->
    <!--<property name="sonar.python.coverage.reportPath" value="tests/coverage.xml" />-->

    <!-- Define the SonarQube target -->
    <target name="sonar">
        <!-- Execute the SonarQube analysis -->
        <sonar:sonar/>
    </target>

    <!--<target name="nose"-->
            <!--depends="prepare"-->
            <!--description="nose extends unittest to make testing easier">-->
        <!--<exec executable="${toolsdir}nosetests" failonerror="true">-->
            <!--<arg value="-w"/>-->
            <!--<arg value="tests/"/>-->
            <!--<arg value="&#45;&#45;with-xunit"/>-->
            <!--<arg value="&#45;&#45;xunit-file=tests/nosetests.xml"/>-->
            <!--<arg value="&#45;&#45;with-coverage"/>-->
            <!--<arg value="&#45;&#45;cover-erase"/>-->
            <!--<arg value="&#45;&#45;cover-xml"/>-->
        <!--</exec>-->
    <!--</target>-->

    <target name="build"
            depends="sonar"
            description="默认构建任务"/>

    <target name="tools-parallel" description="Run tools in parallel">
        <parallel threadCount="2">
            <sequential>
                <!--antcall target="pdepend"/-->
                <antcall target="phpmd-ci"/>
            </sequential>
            <antcall target="phpcpd-ci"/>
            <antcall target="phpcs-ci"/>
            <antcall target="phploc-ci"/>
        </parallel>
    </target>

    <target name="clean"
            unless="clean.done"
            description="Cleanup build artifacts">
        <delete dir="${basedir}/build/api"/>
        <delete dir="${basedir}/build/coverage"/>
        <delete dir="${basedir}/build/logs"/>
        <delete dir="${basedir}/build/pdepend"/>
        <delete dir="${basedir}/build/phpdox"/>
        <property name="clean.done" value="true"/>
    </target>

    <target name="prepare"
            unless="prepare.done"
            depends="clean"
            description="Prepare for build">
        <mkdir dir="${basedir}/build/api"/>
        <mkdir dir="${basedir}/build/coverage"/>
        <mkdir dir="${basedir}/build/logs"/>
        <mkdir dir="${basedir}/build/pdepend"/>
        <mkdir dir="${basedir}/build/phpdox"/>
        <property name="prepare.done" value="true"/>
    </target>

    <target name="pdepend"
            depends="prepare"
            description="Calculate software metrics using PHP_Depend and log result in XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${toolsdir}pdepend">
            <arg value="--jdepend-xml=${basedir}/build/logs/jdepend.xml"/>
            <arg value="--jdepend-chart=${basedir}/build/pdepend/dependencies.svg"/>
            <arg value="--overview-pyramid=${basedir}/build/pdepend/overview-pyramid.svg"/>
            <arg path="${basedir}"/>
        </exec>
    </target>

</project>

