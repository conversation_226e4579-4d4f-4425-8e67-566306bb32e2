#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

import json
import random
from datetime import datetime

from base import repository
from base.dmp_constant import EXTERNAL_SNAP_H, EXTERNAL_SNAP_M
from base.enums import DashboardSnapshot, FlowType, FlowStatus, FlowNodeType
from components.snapshot_service import DropSnapShotTables
from dmplib.utils.errors import UserError
from dmplib import config
from dmplib.hug import g
from dashboard_snapshot.models import SnapshotEditModel
from dashboard_snapshot.repositories import dashboard_snapshot_repository
from dashboard_chart.services.released_dashboard_service import get_all_dashboard_and_dataset_of_need_snapshot
from dashboard_chart.services import metadata_service
from flow.services.flow_service import add_flow, update_flow_schedule, delete_flow_schedule
from flow.models import FlowModel, FlowNodeModel
from dashboard_snapshot.services import external_service


def edit_dashboard_snapshot(model: SnapshotEditModel) -> None:
    """
    1、编辑dashboard表
    2、计算调度时间
    3、调加rundeck调度任务
    """
    # 校验
    dashboard = dashboard_snapshot_repository.get_dashboard(model.dashboard_id)
    if not dashboard:
        raise UserError(message="报告不存在")

    model.schedule_config = model.schedule_config if model.schedule_config else {}

    # 编辑dashboard表
    update_status = repository.update(
        "dap_bi_dashboard", {"auto_snap": model.auto_snap, "schedule_config": json.dumps(model.schedule_config)},
        conditions={"id": model.dashboard_id}
    )
    if update_status:
        # 删除报告元数据缓存，重新加载元数据
        metadata_service.delete_release_metadata(model.dashboard_id)
        metadata_service.delete_release_metadata_cache(model.dashboard_id)

    # 获取调度任务
    origin_flow = dashboard_snapshot_repository.get_flow({"id": model.dashboard_id}, ["status"])

    # 开启自动拍照
    status = FlowStatus.Enable.value
    if model.auto_snap == DashboardSnapshot.ON.value:
        # 计算调度时间
        schedule = _calculate_schedule(model.schedule_config)

        # 添加flow记录
        if not origin_flow:
            add_dashboard_flow(model.dashboard_id, dashboard.get('name'), FlowStatus.Enable.value, schedule)
        else:
            repository.update_data("dap_bi_flow", {"schedule": schedule, "status": status},
                                   {"id": model.dashboard_id})

        # 新增、更新调度任务
        update_flow_schedule(model.dashboard_id, command=get_command(model.dashboard_id, queue_name='celery-slow'))
    else:  # 关闭自动拍照
        schedule = None
        # 删除调度任务
        if origin_flow and origin_flow.get("status") == FlowStatus.Enable.value:
            status = FlowStatus.Disable.value
            delete_flow_schedule(model.dashboard_id)

        repository.update_data("dap_bi_flow", {"schedule": schedule, "status": status},
                                   {"id": model.dashboard_id})


def add_dashboard_flow(flow_id, name, status, schedule):
    flow = FlowModel()
    flow.id = flow_id
    flow.name = name
    flow.type = FlowType.DashboardSnap.value
    flow.status = status
    flow.schedule = schedule
    flow.nodes = [FlowNodeModel(name=name, type=FlowNodeType.DashboardSnap.value)]
    add_flow(flow)
    return flow


def get_command(flow_id, queue_name='celery'):
    """
    获取rundeck执行celery的command命令
    :param flow_id:
    :param queue_name:
    :return:
    """
    celery_task_name = "app_celery.dashboard_snapshot"
    cmd_template_snap = config.get(
        "Rundeck.cmd_template_celery",
        "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
    )
    command = '%s %s %s %s %s' % (cmd_template_snap, g.code, celery_task_name, flow_id, queue_name)
    return command


def _calculate_schedule(schedule_config: dict) -> str:
    """
    schedule_config": {
           "type": 1,       # 1 周期调度  2  一次任务
           "schedule_type": "month",  # month, quarter, week
           "month": 1,
           "date": 1
    }
    """
    if not schedule_config:
        raise UserError(message="schedule_config字段结构错误")

    s_type = schedule_config.get("type")
    schedule_type = schedule_config.get("schedule_type")
    mon = schedule_config.get("month")
    date = schedule_config.get("date")
    try:
        hour = EXTERNAL_SNAP_H
        minute = EXTERNAL_SNAP_M
    except:
        hour = 23
        minute = random.randint(1, 30)

    def _int(n):
        try:
            return int(n)
        except Exception as e:
            raise UserError(message="参数错误") from e

    def quarter():   # NOSONAR
        if _int(mon) not in [1, 2, 3]:
            raise UserError(message="month参数错")
        if not (1 <= _int(date) <= 31):
            raise UserError(message="date参数错误")
        m = ','.join(list(map(lambda x: str(x + _int(mon) - 1), [1, 4, 7, 10])))
        return f"0 {minute} {hour} {date} {m} ? *"

    def month():   # NOSONAR
        if not (1 <= _int(date) <= 31):
            raise UserError(message="date参数错误")
        d = _int(date)
        if _int(date) == 31:
            d = "L"
        return f"0 {minute} {hour} {d} * ? *"

    def week():    # NOSONAR
        if _int(date) not in [1, 2, 3, 4, 5, 6, 7]:
            raise UserError(message="date参数错误")
        w = _int(date) + 1 if _int(date) < 7 else 1
        return f"0 {minute} {hour} ? * {w} *"

    if s_type == 1:
        return locals().get(schedule_type)()
    raise NotImplementedError


def just_execute_dashboard_snap(dashboard_id):
    """
    立即执行拍照
    """
    # 校验
    dashboard = dashboard_snapshot_repository.get_dashboard(dashboard_id)
    if not dashboard:
        raise UserError(message="报告不存在")

    # 是否发布校验
    extra = dashboard_snapshot_repository.get_dashboard_extra_data(dashboard_id)
    if not extra or not extra.get("released_on"):
        raise UserError(message="报告未发布")

    flow = dashboard_snapshot_repository.get_flow({"id": dashboard_id}, ["status"])

    if not flow:
        # 添加flow记录
        add_dashboard_flow(dashboard_id, dashboard.get('name'), FlowStatus.Enable.value, None)
    else:
        repository.update_data("dap_bi_flow", {"schedule": None, "status": FlowStatus.Enable.value},
                               {"id": dashboard_id})

    # 执行拍照任务
    snap_id = external_service.execute_dashboard_snap(dashboard_id)
    return snap_id


def edit_dashboard_snapshot_record(record_id, title):
    """
    编辑标题
    """
    # 校验title是否重复
    if repository.get_data('dap_bi_dashboard_snapshot_record', {"title": title, "id": record_id}, fields=["title"]):
        raise UserError(message="名称已存在")

    repository.update_data("dap_bi_dashboard_snapshot_record", {"title": title}, {"id": record_id})


def del_dashboard_snapshot_record(record_id):
    """
    删除拍照
    """
    # 获取record
    record = repository.get_data('dap_bi_dashboard_snapshot_record', {"id": record_id}, ["dashboard_id", "snap_id"])

    if not record:
        return True

    # 获取拍照的dashboard_ids, dataset_ids
    dashboard_ids, dataset_ids = get_all_dashboard_and_dataset_of_need_snapshot(
        [record.get("dashboard_id")], [], [record.get("dashboard_id")]
    )

    # 删除拍照数据
    DropSnapShotTables(dashboard_ids, dataset_ids, [record.get("snap_id")]).do()


def get_dashboard_snapshot_record_list(dashboard_id):
    return repository.get_list("dap_bi_dashboard_snapshot_record", {"dashboard_id": dashboard_id}, order_by="modified_on desc")


def get_dashboard_all_snapshot_list(dashboard_id):
    """
    获取某个报告所有的拍照列表，报告+简讯的快照列表
    :param dashboard_id:
    :return:
    """
    sql = """
    select sd.increment_id as id, s.snap_id,sd.id as dashboard_id,sd.name as title,s.snap_type, s.created_on,
    s.created_by,s.modified_on,s.modified_by from `dap_bi_snapshot_dashboard_released_snapshot_dashboard` sd 
    INNER JOIN `dap_bi_snapshot` s on sd.snap_id = s.snap_id
    where sd.id  = %(dashboard_id)s and sd.data_type = 0
    order by s.modified_on desc
    """
    snapshot_list = repository.get_data_by_sql(sql, {"dashboard_id": dashboard_id})
    if snapshot_list:
        for item in snapshot_list:
            title = item.get("title") + item.get("modified_on").strftime('%Y%m%d%H%M%S')
            if item.get("snap_type") in ['', '简讯']:
                item["snap_type"] = '简讯'
                title += '_简讯'
            item["title"] = title
    return snapshot_list


def format_schedule_config(schedule_config: dict):
    schedule_type = schedule_config.get('schedule_type')
    if schedule_type == 'month':
        return f'按月，每月{schedule_config.get("date")}号'
    if schedule_type == 'quarter':
        return f'按季度，{schedule_config.get("month")}季度，第{schedule_config.get("date")}天'
    if schedule_type == 'week':
        return f'按周，第{schedule_config.get("date")}天'
    return '未知'
