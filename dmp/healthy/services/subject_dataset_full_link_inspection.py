import json
import time
from datetime import datetime
import os
import logging

from data_source.services import data_source_service
from dataset.repositories import dataset_inspection_repository
from dataset.services import dataset_subject_service
from dmplib.components import auth_util
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from dmplib.hug import g
from base.enums import (
    SubjectDatasetInspectionCheckItemResult,
    DatasetVersionStatus,
    SubjectDatasetInspectionNodeType,
    FlowInstanceStatus,
    SubjectDatasetInspectionStatus,
    SubjectDataSourceType,
    InspectTeam,
    InspectNodeNames,
)
from dataset.repositories import dataset_subject_repository
from dashboard_chart.repositories import external_dashboard_repository
from flow.repositories import flow_instance_repository
from dataset.repositories import dataset_version_repository
from healthy.services import dashboard_healthy_service
from dmplib import config
from dataset.services.dataset_subject_service import get_dataset_subject_inspection_by_id
from healthy.services.healthy_node_recorder import NodeCheckerRecorder

logger = logging.getLogger(__name__)


def now_date():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def async_run_subject_full_inspection_storage(subject_id: str, data_source_type: str):
    """
    DMP数据存储全链路巡检节点
    :param subject_id:
    :return:
    """
    inspection_result = async_run_dataset_subject_full_inspection_storage(subject_id, data_source_type)
    # 更新数据库
    result = dataset_subject_service.generate_db_subject_inspect_result([item for _, item in inspection_result.items()])
    for node_name, item in result.items():
        data = {'end_time': item['check_time'], 'status': item["status"], 'inspect_result': json.dumps(item)}
        dataset_subject_repository.upset_dataset_subject_inspection_node(subject_id, node_name, data)


def async_run_subject_full_inspection_dashboard_node(subject_id: str, data_source_type: str):
    """
    DMP数据报告全链路巡检节点
    :param subject_id:
    :return:
    """
    inspection_result = get_dashboard_node_inspection_result(subject_id, data_source_type)
    # 更新数据库
    result = dataset_subject_service.generate_db_subject_inspect_result([item for _, item in inspection_result.items()])
    for node_name, item in result.items():
        data = {'end_time': item['check_time'], 'status': item["status"], 'inspect_result': json.dumps(item)}
        dataset_subject_repository.upset_dataset_subject_inspection_node(subject_id, node_name, data)


def async_run_dataset_subject_full_inspection_storage(subject_id: str, data_source_type: str):
    empty_normally_result = generate_inspection_result(SubjectDatasetInspectionNodeType.DMPDataStorage.value)
    if data_source_type == SubjectDataSourceType.Api.value:
        return _get_api_inspect_result(subject_id, empty_normally_result)
    if data_source_type == SubjectDataSourceType.Subject.value:
        return _get_subject_inspect_result(subject_id, empty_normally_result)
    return empty_normally_result


def _get_api_inspect_result(subject_id: str, empty_normally_result: dict) -> dict:
    if not data_source_service.is_api_datasource(subject_id):
        return empty_normally_result
    # 获取所有数据集id
    dataset_ids = data_source_service.get_reference_datasets(subject_id) or []
    check_item = {
        'name': "检测数据源使用",
        'result': SubjectDatasetInspectionCheckItemResult.Normally.value,
        'msg': "该数据源正在使用中",
        'owner': "",
        'url': "",
    }
    if not dataset_ids:
        check_item.result = SubjectDatasetInspectionCheckItemResult.Warning.value
        check_item.msg = "无任何数据集使用该数据源"
        check_item.owner = InspectTeam.DmpTeam.value
    check_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    return generate_inspection_result(SubjectDatasetInspectionNodeType.DMPDataStorage.value, [check_item], check_time)


def _get_subject_inspect_result(subject_id: str, empty_normally_result: dict) -> dict:
    subject = dataset_subject_repository.get_dataset_subject_by_id(subject_id)
    if not subject:
        return empty_normally_result
    dataset_folder_id = subject.get("dataset_folder_id")
    if not dataset_folder_id:
        return empty_normally_result
    flow_instance = flow_instance_repository.get_last_flow_end_time(dataset_folder_id)
    if not flow_instance:
        return empty_normally_result
    flow_status = flow_instance.get("status")
    last_instance_time = flow_instance.get("end_time")
    if isinstance(last_instance_time, datetime):
        last_instance_time = last_instance_time.strftime("%Y-%m-%d %H:%M:%S")
    # 获取所有数据集id
    dataset_ids = dataset_subject_repository.get_dataset_ids_by_subject_id(subject_id) or []
    if not dataset_ids:
        return empty_normally_result
    check_items = get_dataset_inspection_node_result(dataset_ids, flow_status, subject)
    inspection_result = generate_inspection_result(
        SubjectDatasetInspectionNodeType.DMPDataStorage.value, check_items, check_time=last_instance_time
    )
    return inspection_result


def _check_highdata_push_timeout(check_items: list, subject: dict):
    """
    检测hd是否超时未推送数据
    :param check_items:
    :param subject:
    :return:
    """
    subject_inspection = dataset_subject_service.get_dataset_subject_inspection_by_id(subject["id"])
    if not subject_inspection:
        return
    if not subject["version"] or not subject_inspection["inspect_version"]:
        return
    inspection_node = dataset_subject_repository.get_dataset_subject_inspection_node(
        subject["id"], InspectNodeNames.DmpSendDataRequest.value
    )
    if not inspection_node:
        return
    dataset_subject = dataset_subject_repository.get_dataset_subject_by_id(subject["id"])
    if dataset_subject and (int(subject["version"][1:15])) <= int(dataset_subject["version"][1:15]):
        return
    start_time_stamp = int(time.mktime(inspection_node["start_time"].timetuple()))
    spend_time = int(time.time()) - start_time_stamp
    timeout = 60 * 60
    if int(subject["version"][1:15]) <= int(subject_inspection["inspect_version"][1:15]) and spend_time > timeout:
        check_items.append(
            {
                'name': subject['name'],
                'result': SubjectDatasetInspectionCheckItemResult.Failed.value,
                'msg': f"主题包 {subject['name']} 超时未推送数据, 当前该主题包无法使用最新版本数据",
                'owner': InspectTeam.HdTeam.value,
                'url': "",
            }
        )


def get_dataset_inspection_node_result(dataset_ids: list, flow_status: str, subject: dict):
    check_items = []
    # 获取数据集预发布版本状态
    dataset_versions = dataset_version_repository.get_dataset_version_by_ids(dataset_ids)
    if not dataset_versions:
        return check_items
    result_map = {
        DatasetVersionStatus.Normal.value: SubjectDatasetInspectionCheckItemResult.Normally.value,
        DatasetVersionStatus.Failure.value: SubjectDatasetInspectionCheckItemResult.Failed.value,
        DatasetVersionStatus.CheckFailure.value: SubjectDatasetInspectionCheckItemResult.Failed.value,
    }
    # 检测是否手动指定版本
    _check_datasets_fixed_version(check_items, dataset_ids)
    # 检测HighData数据推送是否超时
    _check_highdata_push_timeout(check_items, subject)
    for dataset_version in dataset_versions:
        # 如果主题流程都失败，所有下面的流程都置为失败
        if flow_status == FlowInstanceStatus.Failed.value:
            item_result = {
                'name': dataset_version['name'],
                'result': SubjectDatasetInspectionCheckItemResult.Failed.value,
                'msg': "同步失败",
                'owner': InspectTeam.YxTeam.value,
                'url': "/flow/ops/ops_list/{}/{}".format(subject["dataset_folder_id"], subject["name"]),
            }
            check_items.append(item_result)
            continue
        # 巡检结果不正常，需要有message和url信息
        url = ""
        if dataset_version.get("status") != DatasetVersionStatus.Normal.value:
            inspection_id = dataset_version.get("inspection_id")
            # 查询对应dataset_inspection
            subject_inspection_id = dataset_inspection_repository.get_inspection_parent_id(inspection_id)
            # 通过inspection_id查询巡检结果
            inspection_result = dataset_inspection_repository.get_inspection_details(inspection_id)
            inspection_message = []
            for inspection in inspection_result:
                try:
                    rules = json.loads(inspection["inspection_rules"])
                except ValueError:
                    continue
                _message = "{col_name}{operator}{col_value}".format(
                    col_name=rules.get("col_name"), operator=rules.get("operator"), col_value=rules.get("col_value")
                )
                inspection_message.append(_message)
            # 结果拼接成message
            message = "数据巡检失败，以下规则不通过：{}".format('、'.join(inspection_message))
            url = "/flow/inspection_result/%s" % subject_inspection_id
            owner = InspectTeam.YxTeam.value
            # 拼接url信息
        else:
            message = "巡检通过"
            owner = ""
        item_result = {
            'name': dataset_version['name'],
            'result': result_map[dataset_version['status']],
            'msg': message,
            'url': url,
            'owner': owner,
        }
        check_items.append(item_result)
    return check_items


def _check_datasets_fixed_version(check_items, dataset_ids):
    fixed_datasets_version_map = {
        fixed_version["dataset_id"]: fixed_version
        for fixed_version in dataset_version_repository.get_datasets_fixed_version(dataset_ids)
    }
    for dataset_id in dataset_ids:
        if dataset_id in fixed_datasets_version_map:
            fixed_dataset_version = fixed_datasets_version_map[dataset_id]
            check_items.append(
                {
                    'name': fixed_dataset_version['name'],
                    'result': SubjectDatasetInspectionCheckItemResult.Warning.value,
                    'msg': f"数据集 {fixed_dataset_version['name']} 当前使用了指定版本"
                    f" {fixed_dataset_version['version_name']}, 数据将不会主动更新",
                    'owner': InspectTeam.YxTeam.value,
                    'url': "",
                }
            )


def generate_inspection_result(node_type: str, check_items: list = None, check_time: str = None):
    node_type_name_map = {
        SubjectDatasetInspectionNodeType.DMPDataDashboard.value: "DMP报告巡检",
        SubjectDatasetInspectionNodeType.DMPDataStorage.value: "DMP数据集同步",
    }
    if node_type not in node_type_name_map:
        raise ValueError("全链路巡检节点类型不存在")

    inspection_result = {
        node_type: {
            "node_type": node_type,
            "node_name": node_type_name_map[node_type],
            "check_time": check_time or now_date(),
            "check_items": check_items or [],
        }
    }
    return inspection_result


def get_dashboard_node_inspection_result(subject_id: str, data_source_type: str):
    empty_normally_result = generate_inspection_result(SubjectDatasetInspectionNodeType.DMPDataDashboard.value)
    dataset_ids = []
    # 区分不同种类的巡检
    if data_source_type == SubjectDataSourceType.Subject.value:
        dataset_ids = dataset_subject_repository.get_dataset_ids_by_subject_id(subject_id) or []
    if data_source_type == SubjectDataSourceType.Api.value:
        dataset_ids = data_source_service.get_reference_datasets(subject_id)
    if not dataset_ids:
        return empty_normally_result

    dashboard_ids = external_dashboard_repository.get_released_dashboard_ids_by_dataset_ids(dataset_ids) or []
    if not dashboard_ids:
        return empty_normally_result

    recorder = NodeCheckerRecorder()
    for dashboard_id in dashboard_ids:
        dashboard_healthy_service.start_dashboard_healthy(g.code, dashboard_id, recorder=recorder)

    records = external_dashboard_repository.get_dashboard_inspection_result(dashboard_ids) or []
    check_items = []

    status_map = {-1: "待检查", 0: "正常", 1: "警告", 2: "错误"}
    result_map = {
        -1: SubjectDatasetInspectionCheckItemResult.Warning.value,
        0: SubjectDatasetInspectionCheckItemResult.Normally.value,
        1: SubjectDatasetInspectionCheckItemResult.Warning.value,
        2: SubjectDatasetInspectionCheckItemResult.Failed.value,
    }

    for r in records:
        result = result_map.get(r['status'], SubjectDatasetInspectionCheckItemResult.Warning.value)
        owner = ""
        if r['status'] != 0:
            owner = InspectTeam.YxTeam.value
        item_result = {
            'name': r['dashboard_name'],
            'result': result,
            'msg': "报告流程巡检结果 ：%s" % status_map.get(r['status']),
            'url': "/healthy/report/%s" % r['dashboard_id'],
            'owner': owner,
        }
        check_items.append(item_result)

    inspection_result = generate_inspection_result(SubjectDatasetInspectionNodeType.DMPDataDashboard.value, check_items)
    return inspection_result


def send_subject_inspection_result(subject_id: str):
    if not subject_id:
        return False
    inspection_result = get_dataset_subject_inspection_by_id(subject_id)
    if not inspection_result:
        return False
    schedule_status = inspection_result.get('schedule_status')
    subject_name = inspection_result.get('subject_name')
    if schedule_status in [
        SubjectDatasetInspectionStatus.Normally.value,
        SubjectDatasetInspectionStatus.Inspecting.value,
    ]:
        return False
    inspect_result = []
    inspect_nodes = dataset_subject_repository.get_dataset_subject_inspection_all_nodes([subject_id])
    error_num, warning_num = 0, 0
    for inspect_node in inspect_nodes:
        try:
            node_result = json.loads(inspect_node.get("inspect_result"))
            inspect_result.append(node_result)
        except Exception:
            continue
    for subject_item in inspect_result:
        error_num += int(subject_item.get('error_num', 0))
        warning_num += int(subject_item.get('warning_num', 0))

    if error_num or warning_num:
        current_domain = AppHosts.get(SkylineApps.DP, False)
        subject_result_url = f'{current_domain}bi-visual/pro_layout/full_link_inspection/{subject_id}'

        contents = list()
        contents.append(("env_code", auth_util.get_env_code()))
        contents.append(('project_code', getattr(g, 'code', '')))
        contents.append(('subject_id', subject_id))
        contents.append(('subject_name', subject_name))
        contents.append(('error_count', str(error_num)))
        contents.append(('warning_count', str(warning_num)))
        contents.append(('url', subject_result_url))
        contents.append(('msg', f'【{subject_name}】存在{error_num}条失败项, {warning_num}条警告项, 详情{subject_result_url}。'))

    return True
