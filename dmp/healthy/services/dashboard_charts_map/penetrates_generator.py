#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
用于组装报告单图关系图中的penetrates穿透数据，同时会将巡检任务检查出来的异常错误组装到对应单图id下。
组装后返回数据范例如下：
    "penetrates":[
            {
                "chart_id":"39ec6831-9291-666c-8a63-7cdd206b40db",
                "parent_id":"39ec682f-b167-c288-7bee-47cacea7ce7c",
                "relations":[
                    [
                        {
                        "field_id": "39e72e31-3386-217d-f2db-db6fc2a41d99",
                        "field_name": "区域",
                        "field_type": "字符串",
                        },
                        {
                        "field_id": "39e9b13a-3526-d342-0f77-140e4baac7bb",
                        "field_name": "区域",
                        "field_type": "字符串",
                        },
                    ]
                ],
                "errors":[],
                "chart_code":"table",
                "chart_name":"分页表格-2",
                "dataset":{
                    "is_same_dataset":0,
                    "dataset_name":"营销-公司签约回款目标含操盘并表",
                    "dataset_id":"39e9b137-e9fa-874b-87d9-0d36dec66dfe",
                    "type":"UNION"
                }
            }
        ],
"""

# ---------------- 标准模块 ----------------
import logging
from collections import defaultdict

# ---------------- 业务模块 ----------------
from healthy.services.dashboard_charts_map.map_generator import MapGenerator
from healthy.services.dashboard_charts_map.charts_map_models import ChartsMapChartModel, ChartsMapPenetrateModel


logger = logging.getLogger(__name__)


class PenetratesGenerator(MapGenerator):
    """
    单图穿透关系
    """

    def __init__(self, metadata_storage):
        self.extra_field_data_dict = defaultdict(dict)
        self.penetrate_model_list = list()
        super().__init__(metadata_storage)

    def _collect_fields_info(self, penetrates_data):
        """
        收集数据集字段信息
        :param penetrates_data:
        :return:
        """
        field_id_list = list()
        for single_penetrate in penetrates_data:
            relation = single_penetrate.get('relation', [])
            if not relation:
                continue
            for single_relation in relation:
                if single_relation.get('child_chart_field_id', ''):
                    field_id_list.append(single_relation.get('child_chart_field_id', ''))
                if single_relation.get('parent_chart_field_id', ''):
                    field_id_list.append(single_relation.get('parent_chart_field_id', ''))

        self._generate_field_dict(field_id_list)

    def _generate_field_dict(self, field_id_list):
        """

        :param field_id_list:
        :return:
        """
        if len(field_id_list):
            extra_field_data = self._metadata_storage.batch_get_field_data_by_ids(list(set(field_id_list)))
            for single_data in extra_field_data:
                if single_data.get('id'):
                    self.extra_field_data_dict[single_data.get('id')] = single_data

    def _assign_relation(self, orig_relation):
        """
        组装字段关系
        :param orig_relation:
        :return:
        """
        new_relation = list()
        for relation in orig_relation:
            parent_chart_field_id = relation.get('parent_chart_field_id', '')
            child_chart_field_id = relation.get('child_chart_field_id', '')
            parent_chart_field_info = self.extra_field_data_dict.get(parent_chart_field_id, {})
            child_chart_field_info = self.extra_field_data_dict.get(child_chart_field_id)
            new_relation.append(self.get_single_field_relation(parent_chart_field_info, child_chart_field_info))
        return new_relation

    def _get_recursion_penetrates_data(self, chart_id, penetrates_data, chart_map_chart_model):
        """
        递归获取穿透关系
        :return:
        """
        for single_penetrate in penetrates_data:
            son_chart_id = single_penetrate.get('chart_id')
            parent_id = single_penetrate.get('parent_id')
            if parent_id != chart_id:
                continue
            son_chart_data = self._metadata_storage.get_chart_data_by_chart_id(son_chart_id)
            son_chart_id_source = self._metadata_storage.get_chart_source_by_chart_id(son_chart_id)
            parent_chart_id_source = self._metadata_storage.get_chart_source_by_chart_id(parent_id)
            model = ChartsMapPenetrateModel()
            model.chart_id = son_chart_id
            model.parent_id = parent_id
            model.chart_name = son_chart_data.get('name', '')
            model.chart_code = son_chart_data.get("chart_component_code")
            model.dataset = self.get_dataset_model(son_chart_id_source, parent_chart_id_source)
            chart_map_chart_model.errors = self.append_addition_msg(chart_map_chart_model.errors, model.chart_id)

            relation = single_penetrate.get('relation')
            # 关联字段是同一数据集的情况下，不显示关联的字段信息
            if model.dataset and model.dataset.is_same_dataset != 1 and relation:
                model.relations = self._assign_relation(relation)
            self.penetrate_model_list.append(model)
            if model.chart_id:
                self._get_recursion_penetrates_data(model.chart_id, penetrates_data, chart_map_chart_model)
        return chart_map_chart_model

    def generate(self, chart_map_chart_model: ChartsMapChartModel):
        """

        :param chart_map_chart_model: 传入的model对象
        :return:
        """
        # 清空generator类属性
        self.extra_field_data_dict = defaultdict(dict)
        self.penetrate_model_list = list()

        # 初始数据
        penetrates_data = self._metadata_storage.get_penetrate_data()
        initiator_chart_id = chart_map_chart_model.chart_id

        # 预先收集所有数据集字段信息
        self._collect_fields_info(penetrates_data)

        # 通过递归获取完整的单图穿透关系链
        chart_map_chart_model = self._get_recursion_penetrates_data(
            initiator_chart_id, penetrates_data, chart_map_chart_model
        )

        chart_map_chart_model.penetrates = self.penetrate_model_list
        return chart_map_chart_model
