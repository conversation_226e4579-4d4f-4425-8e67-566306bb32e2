#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
跳转关系
"""

# ---------------- 标准模块 ----------------
import logging
from collections import defaultdict

# ---------------- 业务模块 ----------------
from .checker import Checker
from base.enums import DashboardJumpConfigStatus, DashboardJumpType
from base.enums import HealthyCheckResultStatus as ResultStatus


logger = logging.getLogger(__name__)


class RedirectsChecker(Checker):
    def __init__(self, metadata_storage):
        self._check_result_dict = defaultdict(dict)
        super().__init__(metadata_storage)

    @staticmethod
    def get_responder_ids(dashboard_filter_id, redirect_extend_data):
        """

        :param dashboard_filter_id:
        :param redirect_extend_data:
        :return:
        """
        if not redirect_extend_data:
            return "", ""
        dashboard_filter_extend_data = redirect_extend_data.get(dashboard_filter_id)
        responder_dataset_id = dashboard_filter_extend_data.get("dataset_id") if dashboard_filter_extend_data else ""
        responder_field_id = (
            dashboard_filter_extend_data.get("main_dataset_field_id") if dashboard_filter_extend_data else ""
        )
        return responder_dataset_id, responder_field_id

    @staticmethod
    def get_main_dataset_field_name(dashboard_filter_id, redirect_extend_data):
        if not redirect_extend_data:
            return "", ""
        extend_data = redirect_extend_data.get(dashboard_filter_id)
        if not extend_data:
            return ""
        return extend_data.get("alias_name") or extend_data.get("col_name") or extend_data.get("origin_col_name")

    def check(self):
        redirects_data = self._metadata_storage.get_redirect_data()

        if not redirects_data:
            return self._check_result_dict

        for single_redirect in redirects_data:
            initiator_chart_id = single_redirect.get("chart_id")
            chart_redirect_data = single_redirect.get("chart_redirect", [])
            for redirect_data in chart_redirect_data:
                status = redirect_data.get("status")
                if status in [DashboardJumpConfigStatus.Invalid.value]:
                    continue
                if redirect_data.get("target_type") == DashboardJumpType.Url.value:
                    continue
                self._check_redirect_relations(redirect_data, initiator_chart_id)

        # 返回结果
        return self._check_result_dict

    def _check_redirect_relations(self, redirect_data, initiator_chart_id):
        """
        巡检relations
        :param redirect_data:
        :param initiator_chart_id:
        :return:
        """
        first_report = self._metadata_storage.get_first_report()
        extend_redirect_data = first_report.get("redirect_extend_data", {})
        for single_relation in redirect_data.get("relations", []):
            initiator_field_id = single_relation.get("field_initiator_id")
            dashboard_filter_id = single_relation.get("dashboard_filter_id")
            if not dashboard_filter_id:
                continue
            initiator_name = self._metadata_storage.get_chart_name_from_metadata(initiator_chart_id)
            if not extend_redirect_data.get(dashboard_filter_id):
                self.add_check_msg(
                    self._check_result_dict,
                    initiator_chart_id,
                    initiator_name,
                    1010,
                    ResultStatus.Warning.value,
                    initiator_name,
                )
                continue

            if not self.is_dataset_field_exists(initiator_field_id):
                self.add_check_msg(
                    self._check_result_dict,
                    initiator_chart_id,
                    initiator_name,
                    1007,
                    ResultStatus.UnHealthy.value,
                    *[initiator_name, "跳转", self._metadata_storage.get_field_name_from_metadata(initiator_field_id)],
                )

            _, responder_field_id = self.get_responder_ids(dashboard_filter_id, extend_redirect_data)
            if not self.is_dataset_field_exists(responder_field_id):
                self.add_check_msg(
                    self._check_result_dict,
                    initiator_chart_id,
                    initiator_name,
                    1009,
                    ResultStatus.UnHealthy.value,
                    *[initiator_name, self.get_main_dataset_field_name(dashboard_filter_id, extend_redirect_data)],
                )
