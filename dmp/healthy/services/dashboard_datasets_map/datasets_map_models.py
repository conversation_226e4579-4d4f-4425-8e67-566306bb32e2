#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/1/14 10:43
# <AUTHOR> caoxl
# @File     : charts_map.py
from base.models import BaseModel


class BaseDatasetsMapModel(BaseModel):
    # pylint: disable=useless-super-delegation
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class DatasetsMapModel(BaseDatasetsMapModel):
    __slots__ = ["dashboard", "datasets"]

    def __init__(self, **kwargs):
        self.dashboard = None
        self.datasets = []
        super().__init__(**kwargs)


class DatasetsMapDashboardModel(BaseDatasetsMapModel):
    __slots__ = ["dashboard_id", "dashboard_name", "cover", "status", "errors"]

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.dashboard_name = None
        self.cover = None
        self.status = 1  # 0->未发布 1->已发布
        self.errors = []
        super().__init__(**kwargs)


class DatasetsMapDatasetModel(BaseDatasetsMapModel):
    __slots__ = [
        "dataset_id",
        "dataset_name",
        "type",
        "parent_id",
        "status",
        "errors",
        "penetrates",
        "linkages",
        "filters",
        "redirects",
        "new_filters",
        "new_linkages",
        "childs",
    ]

    def __init__(self, **kwargs):
        self.dataset_id = None
        self.dataset_name = None
        self.parent_id = None
        self.type = None
        self.status = None
        self.errors = []
        self.dataset = None
        self.penetrates = []
        self.linkages = []
        self.filters = []
        self.redirects = []
        self.new_filters = []
        self.new_linkages = []
        self.childs = []
        super().__init__(**kwargs)


class DatasetsMapPenetrateModel(BaseDatasetsMapModel):
    __slots__ = ["dataset_id", "dataset_name", "errors", "relations", "is_same_dataset", "type"]

    def __init__(self, **kwargs):
        self.dataset_id = None
        self.type = None
        self.dataset_name = None
        self.is_same_dataset = 0
        self.errors = []
        self.relations = []
        self.childs = []
        super().__init__(**kwargs)


class DatasetsMapLinkageModel(BaseDatasetsMapModel):
    __slots__ = ["dataset_id", "dataset_name", "errors", "relations", "is_same_dataset", "parent_id", "type"]

    def __init__(self, **kwargs):
        self.dataset_id = None
        self.parent_id = None
        self.dataset_name = None
        self.type = None
        self.is_same_dataset = 0
        self.errors = []
        self.relations = []
        self.childs = []
        super().__init__(**kwargs)


class DatasetsMapFilterModel(BaseDatasetsMapModel):
    __slots__ = ["dataset_id", "dataset_name", "errors", "relations", "is_same_dataset", "parent_id", "type"]

    def __init__(self, **kwargs):
        self.dataset_id = None
        self.parent_id = None
        self.type = None
        self.dataset_name = None
        self.is_same_dataset = 0
        self.errors = []
        self.relations = []
        self.childs = []
        super().__init__(**kwargs)


class DatasetsMapRedirectModel(BaseDatasetsMapModel):
    __slots__ = [
        "dataset_id",
        "dataset_name",
        "errors",
        "relations",
        "is_same_dataset",
        "parent_id",
        "type",
        "target",
        "target_type",
        "redirect_status",
    ]

    def __init__(self, **kwargs):
        self.dataset_id = None
        self.parent_id = None
        self.type = None
        self.dataset_name = None
        self.target = None
        self.target_type = None
        self.redirect_status = None
        self.is_same_dataset = 0
        self.errors = []
        self.relations = []
        self.create_type = 0
        self.childs = []
        super().__init__(**kwargs)


class DatasetsNewFilterModel(BaseDatasetsMapModel):
    __slots__ = ["dataset_id", "dataset_name", "errors", "relations", "is_same_dataset", "parent_id", "type"]

    def __init__(self, **kwargs):
        self.dataset_id = None
        self.parent_id = None
        self.type = None
        self.dataset_name = None
        self.is_same_dataset = 0
        self.errors = []
        self.relations = []
        self.childs = []
        super().__init__(**kwargs)


class DatasetsNewLinkageModel(BaseDatasetsMapModel):
    __slots__ = ["dataset_id", "dataset_name", "errors", "relations", "is_same_dataset", "parent_id", "type"]

    def __init__(self, **kwargs):
        self.dataset_id = None
        self.parent_id = None
        self.type = None
        self.dataset_name = None
        self.is_same_dataset = 0
        self.errors = []
        self.relations = []
        self.childs = []
        super().__init__(**kwargs)
