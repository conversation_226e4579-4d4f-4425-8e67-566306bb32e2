#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
# @Time     : 2019/1/14 18:38
# <AUTHOR> caoxl
# @File     : test_dashbaord_chart_map.py

from dmplib.hug import g
from tests.base import BaseTest
import unittest
from healthy.services import dashboard_healthy_service


class TestDashboardHealthy(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code="dev", account="test")

    def test_charts_map(self):
        g.userid = "22b11db4-e907-4f1f-8835-b9daab6e1f23"
        dashboard_metadata = {
            "dashboard": {
                "id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                "biz_code": "73dff128e27e47688627455bb2f57581",
                "create_type": 1,
                "parent_id": "39eb2ed5-5bd2-d1d9-c6d3-bf3267dbad5e",
                "description": "",
                "scale_mode": 0,
                "new_layout_type": "grid",
                "publish": {"released_on": "", "share_secret_key": "", "status": 1, "type_access_released": 4},
                "styles": {
                    "attrs": {},
                    "background": {"show": True, "color": "#EBEDF2", "size": "stretch", "image": ""},
                    "grid_padding": {
                        "chart_margin": [10, 10],
                        "container_padding": [10, 10],
                        "chart_padding": [15, 15, 15, 15],
                        "chart_background": "#FFFFFF",
                    },
                    "theme": "colorful_white",
                },
                "cover": "",
                "name": "\u76ee\u6807\u62a5\u544a-\u4e0d\u540c\u6570\u636e\u96c6",
                "level_code": "1441-0005-",
                "rank": None,
                "layout": {
                    "mode": "grid",
                    "toolbar": "show",
                    "ratio": "16:9",
                    "height": 1080,
                    "platform": "pc",
                    "lattice": 10,
                    "width": 1920,
                },
            },
            "installed_component": [],
            "first_report": {
                "dashboard_filters": [
                    {
                        "id": "b04bece1-10bd-11e9-a905-1742e27d71a7",
                        "expression": None,
                        "alias_name": "\u4e8b\u4e1a\u90e8",
                        "main_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "filter_relations": [],
                        "type": "\u666e\u901a",
                        "format": None,
                        "data_type": "\u5b57\u7b26\u4e32",
                        "col_name": "col1",
                        "col_value": None,
                        "operator": None,
                        "operators": [
                            {
                                "select_all_flag": 0,
                                "id": "b11cd175-10bd-11e9-a905-1742e27d71a7",
                                "operator": "!=",
                                "col_value": "\u7b2c\u56db\u4e8b\u4e1a\u90e8",
                            }
                        ],
                        "dashboard_id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                        "select_all_flag": "0",
                        "field_group": "\u7ef4\u5ea6",
                        "main_dataset_field_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                    },
                    {
                        "id": "9c459c8a-10ba-11e9-a905-1742e27d71a7",
                        "expression": None,
                        "alias_name": "\u7701\u4efd",
                        "main_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "filter_relations": [],
                        "type": "\u666e\u901a",
                        "format": None,
                        "data_type": "\u5b57\u7b26\u4e32",
                        "col_name": "col2",
                        "col_value": None,
                        "operator": None,
                        "operators": [
                            {
                                "select_all_flag": 0,
                                "id": "9cfe3e24-10ba-11e9-a905-1742e27d71a7",
                                "operator": "in",
                                "col_value": '["\u4e0a\u6d77\u5e02","\u56db\u5ddd\u7701","\u5929\u6d25\u5e02","\u5b89\u5fbd\u7701","\u5c71\u897f\u7701","\u5e7f\u4e1c\u7701","\u6c5f\u82cf\u7701","\u6cb3\u5357\u7701","\u6d59\u6c5f\u7701","\u7518\u8083\u7701","\u798f\u5efa\u7701","\u91cd\u5e86\u5e02"]',
                            }
                        ],
                        "dashboard_id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                        "select_all_flag": "0",
                        "field_group": "\u7ef4\u5ea6",
                        "main_dataset_field_id": "39e7702f-6d4a-7b0a-dd26-8254f8fcc297",
                    },
                    {
                        "id": "96a7c59e-10c5-11e9-a906-1742e27d71a7",
                        "expression": None,
                        "alias_name": "\u57ce\u5e02",
                        "main_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "filter_relations": [],
                        "type": "\u666e\u901a",
                        "format": None,
                        "data_type": "\u5b57\u7b26\u4e32",
                        "col_name": "col4",
                        "col_value": None,
                        "operator": None,
                        "operators": [
                            {
                                "select_all_flag": 0,
                                "id": "978be40f-10c5-11e9-a906-1742e27d71a7",
                                "operator": "!=",
                                "col_value": "\u4e0a\u6d77",
                            }
                        ],
                        "dashboard_id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                        "select_all_flag": "0",
                        "field_group": "\u7ef4\u5ea6",
                        "main_dataset_field_id": "39e7702f-6d4d-3364-bd2d-855adaf150b9",
                    },
                    {
                        "id": "a7733ea9-10ba-11e9-a905-1742e27d71a7",
                        "expression": None,
                        "alias_name": "\u9879\u76ee",
                        "main_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "filter_relations": [],
                        "type": "\u666e\u901a",
                        "format": None,
                        "data_type": "\u5b57\u7b26\u4e32",
                        "col_name": "col5",
                        "col_value": None,
                        "operator": None,
                        "operators": [
                            {
                                "select_all_flag": 0,
                                "id": "a82f89c5-10ba-11e9-a905-1742e27d71a7",
                                "operator": "is not None",
                                "col_value": "",
                            }
                        ],
                        "dashboard_id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                        "select_all_flag": "0",
                        "field_group": "\u7ef4\u5ea6",
                        "main_dataset_field_id": "39e7702f-6d4e-8ce3-6671-0ba8133b7cd6",
                    },
                    {
                        "id": "0c9d0b0c-10be-11e9-a905-1742e27d71a7",
                        "expression": None,
                        "alias_name": "\u6458\u724c\u65e5\u671f",
                        "main_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "filter_relations": [],
                        "type": "\u666e\u901a",
                        "format": None,
                        "data_type": "\u65e5\u671f",
                        "col_name": "col9",
                        "col_value": None,
                        "operator": None,
                        "operators": [
                            {
                                "select_all_flag": 0,
                                "id": "0ddc68d0-10be-11e9-a905-1742e27d71a7",
                                "operator": "=",
                                "col_value": "*",
                            }
                        ],
                        "dashboard_id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                        "select_all_flag": "0",
                        "field_group": "\u7ef4\u5ea6",
                        "main_dataset_field_id": "39e7702f-6d54-f55e-52b6-eda187bb89e1",
                    },
                    {
                        "id": "c3ac2428-10be-11e9-a905-1742e27d71a7",
                        "expression": None,
                        "alias_name": "\u603b\u53ef\u552e\u8d27\u503c(/\u4e07\u5143)",
                        "main_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "filter_relations": [],
                        "type": "\u666e\u901a",
                        "format": None,
                        "data_type": "\u6570\u503c",
                        "col_name": "col22",
                        "col_value": None,
                        "operator": None,
                        "operators": [
                            {
                                "select_all_flag": 0,
                                "id": "c469a7c4-10be-11e9-a905-1742e27d71a7",
                                "operator": ">",
                                "col_value": "1000",
                            }
                        ],
                        "dashboard_id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                        "select_all_flag": "0",
                        "field_group": "\u5ea6\u91cf",
                        "main_dataset_field_id": "39e7702f-6d67-8711-3ee6-d6ba9a106902",
                    },
                ],
                "id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                "biz_code": "73dff128e27e47688627455bb2f57581",
                "create_type": 1,
                "layout": {
                    "lattice": 10,
                    "mode": "grid",
                    "height": 1080,
                    "ratio": "16:9",
                    "toolbar": "show",
                    "platform": "pc",
                    "width": 1920,
                },
                "parent_id": "39eb2ed5-5bd2-d1d9-c6d3-bf3267dbad5e",
                "description": "",
                "scale_mode": 0,
                "new_layout_type": "grid",
                "publish": {"released_on": "", "share_secret_key": "", "status": 1, "type_access_released": 4},
                "styles": {
                    "attrs": {},
                    "background": {"color": "#EBEDF2", "show": True, "image": "", "size": "stretch"},
                    "grid_padding": {
                        "chart_margin": [10, 10],
                        "container_padding": [10, 10],
                        "chart_padding": [15, 15, 15, 15],
                        "chart_background": "#FFFFFF",
                    },
                    "theme": "colorful_white",
                },
                "charts": [
                    {
                        "column_order": None,
                        "config": "",
                        "id": "18502fdb-10c9-11e9-9042-976337d1e7ca",
                        "percentage": None,
                        "name": "\u65f6\u95f4\u533a\u95f4\u7b5b\u9009",
                        "chart_component_code": "time_interval_filter",
                        "funcSetup": {"display_item": "", "refresh_rate": ""},
                        "data_modified_on": None,
                        "chart_type": "filter",
                        "data": {
                            "default_value": "",
                            "chart_default_value": [],
                            "datasource": {"type": "", "id": ""},
                            "data_type": {"logic_type": "interval"},
                            "indicator": {
                                "dims": [],
                                "filters": [],
                                "comparisons": [],
                                "nums": [],
                                "chart_params": [],
                                "zaxis": [],
                                "desires": [],
                                "marklines": [],
                            },
                        },
                        "position": {
                            "row": 18,
                            "size_y": 6,
                            "size_x": 6,
                            "i": "18502fdb-10c9-11e9-9042-976337d1e7ca",
                            "col": 6,
                        },
                        "page_size": 0,
                        "sort_method": None,
                        "level_code": "",
                    },
                    {
                        "column_order": None,
                        "config": "",
                        "id": "3f443d02-10c1-11e9-a906-1742e27d71a7",
                        "percentage": None,
                        "name": "\u65f6\u95f4\u8f74",
                        "chart_component_code": "timeline",
                        "funcSetup": {"display_item": "", "refresh_rate": ""},
                        "data_modified_on": None,
                        "chart_type": "filter",
                        "data": {
                            "default_value": "",
                            "chart_default_value": [],
                            "datasource": {"type": "EXCEL", "id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"},
                            "data_type": {"logic_type": "assist"},
                            "indicator": {
                                "dims": [
                                    {
                                        "note": None,
                                        "id": "43968028-10c1-11e9-a906-1742e27d71a7",
                                        "expression": None,
                                        "dim": "39e7702f-6d54-f55e-52b6-eda187bb89e1",
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "field_group": "\u7ef4\u5ea6",
                                        "dataset_field_id": "39e7702f-6d54-f55e-52b6-eda187bb89e1",
                                        "sort": "",
                                        "alias": "\u6458\u724c\u65e5\u671f",
                                        "alias_name": "\u6458\u724c\u65e5\u671f",
                                        "col_name": "col9",
                                        "content": None,
                                        "formula_mode": "day",
                                        "dashboard_chart_id": "3f443d02-10c1-11e9-a906-1742e27d71a7",
                                        "type": "\u666e\u901a",
                                        "data_type": "\u65e5\u671f",
                                        "visible": 1,
                                        "rank": 0,
                                    }
                                ],
                                "filters": [],
                                "comparisons": [],
                                "nums": [],
                                "chart_params": [],
                                "zaxis": [],
                                "desires": [],
                                "marklines": [],
                            },
                        },
                        "position": {
                            "col": 0,
                            "size_x": 6,
                            "z": 1501,
                            "size_y": 6,
                            "row": 18,
                            "i": "3f443d02-10c1-11e9-a906-1742e27d71a7",
                        },
                        "page_size": 0,
                        "sort_method": None,
                        "level_code": "",
                    },
                    {
                        "column_order": None,
                        "config": "",
                        "id": "4e196789-10ba-11e9-a905-1742e27d71a7",
                        "percentage": None,
                        "name": "\u6761\u5f62\u56fe",
                        "chart_component_code": "horizon_bar",
                        "funcSetup": {"display_item": "", "refresh_rate": ""},
                        "data_modified_on": None,
                        "chart_type": "chart",
                        "data": {
                            "default_value": "",
                            "chart_default_value": [],
                            "datasource": {"type": "EXCEL", "id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"},
                            "data_type": {"logic_type": "default"},
                            "indicator": {
                                "dims": [
                                    {
                                        "note": None,
                                        "id": "6de13cf5-10ba-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dim": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "field_group": "\u7ef4\u5ea6",
                                        "dataset_field_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                                        "sort": "",
                                        "alias": "\u4e8b\u4e1a\u90e8",
                                        "alias_name": "\u4e8b\u4e1a\u90e8",
                                        "col_name": "col1",
                                        "content": None,
                                        "formula_mode": "",
                                        "dashboard_chart_id": "4e196789-10ba-11e9-a905-1742e27d71a7",
                                        "type": "\u666e\u901a",
                                        "data_type": "\u5b57\u7b26\u4e32",
                                        "visible": 1,
                                        "rank": 0,
                                    }
                                ],
                                "filters": [],
                                "comparisons": [],
                                "nums": [
                                    {
                                        "note": None,
                                        "id": "7033b872-10ba-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "dataset_field_id": "39e84452-0987-9198-79e1-c1285cfa0a48",
                                        "alias_name": "\u5408\u4f5c\u9879\u76ee\u6570",
                                        "visible": 1,
                                        "axis_type": 0,
                                        "field_group": "\u5ea6\u91cf",
                                        "chart_code": "",
                                        "type": "\u666e\u901a",
                                        "calc_None": 1,
                                        "display_format": {
                                            "fixed_decimal_places": 0,
                                            "thousand_point_separator": 1,
                                            "unit": "\u65e0",
                                            "column_unit_name": "",
                                            "display_mode": "num",
                                        },
                                        "alias": "\u5408\u4f5c\u9879\u76ee\u6570",
                                        "formula_mode": "sum",
                                        "col_name": "col16",
                                        "sort": "",
                                        "dashboard_chart_id": "4e196789-10ba-11e9-a905-1742e27d71a7",
                                        "num": "39e84452-0987-9198-79e1-c1285cfa0a48",
                                        "data_type": "\u6570\u503c",
                                        "rank": 0,
                                    }
                                ],
                                "chart_params": [],
                                "zaxis": [],
                                "desires": [],
                                "marklines": [],
                            },
                        },
                        "position": {
                            "col": 0,
                            "size_x": 6,
                            "z": "auto",
                            "size_y": 6,
                            "row": 0,
                            "i": "4e196789-10ba-11e9-a905-1742e27d71a7",
                        },
                        "page_size": 0,
                        "sort_method": None,
                        "level_code": "",
                    },
                    {
                        "column_order": None,
                        "config": "",
                        "id": "6942f55a-10bd-11e9-a905-1742e27d71a7",
                        "percentage": None,
                        "name": "\u5217\u8868\u7b5b\u9009",
                        "chart_component_code": "checkbox_filter",
                        "funcSetup": {"display_item": "", "refresh_rate": ""},
                        "data_modified_on": None,
                        "chart_type": "filter",
                        "data": {
                            "default_value": "",
                            "chart_default_value": [],
                            "datasource": {"type": "EXCEL", "id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"},
                            "data_type": {"logic_type": "assist"},
                            "indicator": {
                                "dims": [
                                    {
                                        "note": None,
                                        "id": "7105a714-10bd-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dim": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "field_group": "\u7ef4\u5ea6",
                                        "dataset_field_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                                        "sort": "",
                                        "alias": "\u4e8b\u4e1a\u90e8",
                                        "alias_name": "\u4e8b\u4e1a\u90e8",
                                        "col_name": "col1",
                                        "content": None,
                                        "formula_mode": "",
                                        "dashboard_chart_id": "6942f55a-10bd-11e9-a905-1742e27d71a7",
                                        "type": "\u666e\u901a",
                                        "data_type": "\u5b57\u7b26\u4e32",
                                        "visible": 1,
                                        "rank": 0,
                                    }
                                ],
                                "filters": [],
                                "comparisons": [],
                                "nums": [],
                                "chart_params": [],
                                "zaxis": [],
                                "desires": [],
                                "marklines": [],
                            },
                        },
                        "position": {
                            "col": 6,
                            "size_x": 6,
                            "z": None,
                            "size_y": 6,
                            "row": 6,
                            "i": "6942f55a-10bd-11e9-a905-1742e27d71a7",
                        },
                        "page_size": 0,
                        "sort_method": None,
                        "level_code": "",
                    },
                    {
                        "column_order": None,
                        "config": "",
                        "id": "749bf492-10ba-11e9-a905-1742e27d71a7",
                        "percentage": None,
                        "name": "\u666e\u901a\u997c\u56fe",
                        "chart_component_code": "pie",
                        "funcSetup": {"display_item": "", "refresh_rate": ""},
                        "data_modified_on": None,
                        "chart_type": "chart",
                        "data": {
                            "default_value": "",
                            "chart_default_value": [],
                            "datasource": {"type": "EXCEL", "id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"},
                            "data_type": {"logic_type": "default"},
                            "indicator": {
                                "dims": [
                                    {
                                        "note": None,
                                        "id": "770f8cab-10ba-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dim": "39e7702f-6d4a-7b0a-dd26-8254f8fcc297",
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "field_group": "\u7ef4\u5ea6",
                                        "dataset_field_id": "39e7702f-6d4a-7b0a-dd26-8254f8fcc297",
                                        "sort": "",
                                        "alias": "\u7701\u4efd",
                                        "alias_name": "\u7701\u4efd",
                                        "col_name": "col2",
                                        "content": None,
                                        "formula_mode": "",
                                        "dashboard_chart_id": "749bf492-10ba-11e9-a905-1742e27d71a7",
                                        "type": "\u666e\u901a",
                                        "data_type": "\u5b57\u7b26\u4e32",
                                        "visible": 1,
                                        "rank": 0,
                                    }
                                ],
                                "filters": [],
                                "comparisons": [],
                                "nums": [
                                    {
                                        "note": None,
                                        "id": "7af35685-10ba-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "dataset_field_id": "39e84452-098c-8f2d-93bb-83224e5cc64c",
                                        "alias_name": "\u5269\u4f59\u53ef\u552e\u8d27\u503c(/\u4e07\u5143)",
                                        "visible": 1,
                                        "axis_type": 0,
                                        "field_group": "\u5ea6\u91cf",
                                        "chart_code": "",
                                        "type": "\u666e\u901a",
                                        "calc_None": 1,
                                        "display_format": {
                                            "fixed_decimal_places": 0,
                                            "thousand_point_separator": 1,
                                            "unit": "\u65e0",
                                            "column_unit_name": "",
                                            "display_mode": "num",
                                        },
                                        "alias": "\u5269\u4f59\u53ef\u552e\u8d27\u503c(/\u4e07\u5143)",
                                        "formula_mode": "sum",
                                        "col_name": "col20",
                                        "sort": "",
                                        "dashboard_chart_id": "749bf492-10ba-11e9-a905-1742e27d71a7",
                                        "num": "39e84452-098c-8f2d-93bb-83224e5cc64c",
                                        "data_type": "\u6570\u503c",
                                        "rank": 0,
                                    }
                                ],
                                "chart_params": [],
                                "zaxis": [],
                                "desires": [],
                                "marklines": [],
                            },
                        },
                        "position": {
                            "col": 6,
                            "size_x": 6,
                            "z": None,
                            "size_y": 6,
                            "row": 0,
                            "i": "749bf492-10ba-11e9-a905-1742e27d71a7",
                        },
                        "page_size": 0,
                        "sort_method": None,
                        "level_code": "",
                    },
                    {
                        "column_order": None,
                        "config": "",
                        "id": "83f7b00d-10ba-11e9-a905-1742e27d71a7",
                        "percentage": None,
                        "name": "\u5806\u53e0\u9762\u79ef\u56fe",
                        "chart_component_code": "stack_area",
                        "funcSetup": {"display_item": "", "refresh_rate": ""},
                        "data_modified_on": None,
                        "chart_type": "chart",
                        "data": {
                            "default_value": "",
                            "chart_default_value": [],
                            "datasource": {"type": "EXCEL", "id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"},
                            "data_type": {"logic_type": "default"},
                            "indicator": {
                                "dims": [
                                    {
                                        "note": None,
                                        "id": "86c4d9c7-10ba-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dim": "39e7702f-6d4a-7b0a-dd26-8254f8fcc297",
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "field_group": "\u7ef4\u5ea6",
                                        "dataset_field_id": "39e7702f-6d4a-7b0a-dd26-8254f8fcc297",
                                        "sort": "",
                                        "alias": "\u7701\u4efd",
                                        "alias_name": "\u7701\u4efd",
                                        "col_name": "col2",
                                        "content": None,
                                        "formula_mode": "",
                                        "dashboard_chart_id": "83f7b00d-10ba-11e9-a905-1742e27d71a7",
                                        "type": "\u666e\u901a",
                                        "data_type": "\u5b57\u7b26\u4e32",
                                        "visible": 1,
                                        "rank": 0,
                                    }
                                ],
                                "filters": [],
                                "comparisons": [],
                                "nums": [
                                    {
                                        "note": None,
                                        "id": "88737427-10ba-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "dataset_field_id": "39e84452-0987-9198-79e1-c1285cfa0a48",
                                        "alias_name": "\u5408\u4f5c\u9879\u76ee\u6570",
                                        "visible": 1,
                                        "axis_type": 0,
                                        "field_group": "\u5ea6\u91cf",
                                        "chart_code": "",
                                        "type": "\u666e\u901a",
                                        "calc_None": 1,
                                        "display_format": {
                                            "fixed_decimal_places": 0,
                                            "thousand_point_separator": 1,
                                            "unit": "\u65e0",
                                            "column_unit_name": "",
                                            "display_mode": "num",
                                        },
                                        "alias": "\u5408\u4f5c\u9879\u76ee\u6570",
                                        "formula_mode": "sum",
                                        "col_name": "col16",
                                        "sort": "",
                                        "dashboard_chart_id": "83f7b00d-10ba-11e9-a905-1742e27d71a7",
                                        "num": "39e84452-0987-9198-79e1-c1285cfa0a48",
                                        "data_type": "\u6570\u503c",
                                        "rank": 0,
                                    },
                                    {
                                        "note": None,
                                        "id": "8a790829-10ba-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "dataset_field_id": "39e84452-0989-0e89-6245-4dc02722c1d9",
                                        "alias_name": "\u672a\u552e\u5957\u6570(/\u5957)",
                                        "visible": 1,
                                        "axis_type": 0,
                                        "field_group": "\u5ea6\u91cf",
                                        "chart_code": "",
                                        "type": "\u666e\u901a",
                                        "calc_None": 1,
                                        "display_format": {
                                            "fixed_decimal_places": 0,
                                            "thousand_point_separator": 1,
                                            "unit": "\u65e0",
                                            "column_unit_name": "",
                                            "display_mode": "num",
                                        },
                                        "alias": "\u672a\u552e\u5957\u6570(/\u5957)",
                                        "formula_mode": "sum",
                                        "col_name": "col18",
                                        "sort": "",
                                        "dashboard_chart_id": "83f7b00d-10ba-11e9-a905-1742e27d71a7",
                                        "num": "39e84452-0989-0e89-6245-4dc02722c1d9",
                                        "data_type": "\u6570\u503c",
                                        "rank": 1,
                                    },
                                ],
                                "chart_params": [],
                                "zaxis": [],
                                "desires": [],
                                "marklines": [],
                            },
                        },
                        "position": {
                            "col": 0,
                            "size_x": 6,
                            "z": None,
                            "size_y": 6,
                            "row": 6,
                            "i": "83f7b00d-10ba-11e9-a905-1742e27d71a7",
                        },
                        "page_size": 0,
                        "sort_method": None,
                        "level_code": "",
                    },
                    {
                        "column_order": None,
                        "config": "",
                        "id": "b5ddad85-10c0-11e9-a905-1742e27d71a7",
                        "percentage": None,
                        "name": "\u65f6\u95f4\u533a\u95f4\u7b5b\u9009",
                        "chart_component_code": "time_interval_filter",
                        "funcSetup": {"display_item": "", "refresh_rate": ""},
                        "data_modified_on": None,
                        "chart_type": "filter",
                        "data": {
                            "default_value": "",
                            "chart_default_value": [],
                            "datasource": {"type": "EXCEL", "id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"},
                            "data_type": {"logic_type": "interval"},
                            "indicator": {
                                "dims": [
                                    {
                                        "note": None,
                                        "id": "bb2d3c6a-10c0-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dim": "39e7702f-6d54-f55e-52b6-eda187bb89e1",
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "field_group": "\u7ef4\u5ea6",
                                        "dataset_field_id": "39e7702f-6d54-f55e-52b6-eda187bb89e1",
                                        "sort": "",
                                        "alias": "\u6458\u724c\u65e5\u671f",
                                        "alias_name": "\u6458\u724c\u65e5\u671f",
                                        "col_name": "col9",
                                        "content": None,
                                        "formula_mode": "day",
                                        "dashboard_chart_id": "b5ddad85-10c0-11e9-a905-1742e27d71a7",
                                        "type": "\u666e\u901a",
                                        "data_type": "\u65e5\u671f",
                                        "visible": 1,
                                        "rank": 0,
                                    }
                                ],
                                "filters": [],
                                "comparisons": [],
                                "nums": [],
                                "chart_params": [],
                                "zaxis": [],
                                "desires": [],
                                "marklines": [],
                            },
                        },
                        "position": {
                            "col": 6,
                            "size_x": 6,
                            "z": None,
                            "size_y": 6,
                            "row": 12,
                            "i": "b5ddad85-10c0-11e9-a905-1742e27d71a7",
                        },
                        "page_size": 0,
                        "sort_method": None,
                        "level_code": "",
                    },
                    {
                        "column_order": None,
                        "config": "",
                        "id": "f93aa984-10bf-11e9-a905-1742e27d71a7",
                        "percentage": None,
                        "name": "\u6570\u503c\u533a\u95f4\u7b5b\u9009",
                        "chart_component_code": "number_filter",
                        "funcSetup": {"display_item": "", "refresh_rate": ""},
                        "data_modified_on": None,
                        "chart_type": "filter",
                        "data": {
                            "default_value": "",
                            "chart_default_value": [],
                            "datasource": {"type": "EXCEL", "id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"},
                            "data_type": {"logic_type": "interval"},
                            "indicator": {
                                "dims": [
                                    {
                                        "note": None,
                                        "id": "14d9e3e7-10c0-11e9-a905-1742e27d71a7",
                                        "expression": None,
                                        "dim": "39e7702f-6d67-8711-3ee6-d6ba9a106902",
                                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                        "field_group": "\u5ea6\u91cf",
                                        "dataset_field_id": "39e7702f-6d67-8711-3ee6-d6ba9a106902",
                                        "sort": "",
                                        "alias": "\u603b\u53ef\u552e\u8d27\u503c(/\u4e07\u5143)",
                                        "alias_name": "\u603b\u53ef\u552e\u8d27\u503c(/\u4e07\u5143)",
                                        "col_name": "col22",
                                        "content": None,
                                        "formula_mode": "",
                                        "dashboard_chart_id": "f93aa984-10bf-11e9-a905-1742e27d71a7",
                                        "type": "\u666e\u901a",
                                        "data_type": "\u6570\u503c",
                                        "visible": 1,
                                        "rank": 0,
                                    }
                                ],
                                "filters": [],
                                "comparisons": [],
                                "nums": [],
                                "chart_params": [],
                                "zaxis": [],
                                "desires": [],
                                "marklines": [],
                            },
                        },
                        "position": {
                            "col": 0,
                            "size_x": 6,
                            "z": None,
                            "size_y": 6,
                            "row": 12,
                            "i": "f93aa984-10bf-11e9-a905-1742e27d71a7",
                        },
                        "page_size": 0,
                        "sort_method": None,
                        "level_code": "",
                    },
                ],
                "cover": "",
                "chart_relations": {
                    "filters": [],
                    "linkages": [],
                    "redirects": [],
                    "chart_linkages": [],
                    "penetrates": [],
                    "chart_filters": [
                        {
                            "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                            "chart_initiator_id": "f93aa984-10bf-11e9-a905-1742e27d71a7",
                            "id": "3313512c-10c0-11e9-a905-1742e27d71a7",
                            "related_list": [
                                {
                                    "field_responder_id": "",
                                    "id": "3037f69a-10c0-11e9-a905-1742e27d71a7",
                                    "is_same_dataset": 1,
                                    "chart_responder_id": "4e196789-10ba-11e9-a905-1742e27d71a7",
                                    "related_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                },
                                {
                                    "field_responder_id": "",
                                    "id": "30b19367-10c0-11e9-a905-1742e27d71a7",
                                    "is_same_dataset": 1,
                                    "chart_responder_id": "749bf492-10ba-11e9-a905-1742e27d71a7",
                                    "related_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                },
                                {
                                    "field_responder_id": "",
                                    "id": "30e96c40-10c0-11e9-a905-1742e27d71a7",
                                    "is_same_dataset": 1,
                                    "chart_responder_id": "83f7b00d-10ba-11e9-a905-1742e27d71a7",
                                    "related_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                },
                            ],
                            "field_initiator_id": "39e7702f-6d67-8711-3ee6-d6ba9a106902",
                        },
                        {
                            "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                            "chart_initiator_id": "b5ddad85-10c0-11e9-a905-1742e27d71a7",
                            "id": "0595d9bd-10c1-11e9-a906-1742e27d71a7",
                            "related_list": [
                                {
                                    "field_responder_id": "",
                                    "id": "04b4ef98-10c1-11e9-a905-1742e27d71a7",
                                    "is_same_dataset": 1,
                                    "chart_responder_id": "4e196789-10ba-11e9-a905-1742e27d71a7",
                                    "related_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                },
                                {
                                    "field_responder_id": "",
                                    "id": "04b4ef98-10c1-11e9-a906-1742e27d71a7",
                                    "is_same_dataset": 1,
                                    "chart_responder_id": "749bf492-10ba-11e9-a905-1742e27d71a7",
                                    "related_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                },
                                {
                                    "field_responder_id": "",
                                    "id": "04b4efa5-10c1-11e9-a906-1742e27d71a7",
                                    "is_same_dataset": 1,
                                    "chart_responder_id": "83f7b00d-10ba-11e9-a905-1742e27d71a7",
                                    "related_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                                },
                            ],
                            "field_initiator_id": "39e7702f-6d54-f55e-52b6-eda187bb89e1",
                        },
                    ],
                },
                "level_code": "1441-0005-",
                "rank": None,
                "name": "\u76ee\u6807\u62a5\u544a-\u4e0d\u540c\u6570\u636e\u96c6",
            },
            "screens": [
                {
                    "dashboard_id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                    "snapshot_id": "",
                    "screen_id": "39eb2f0e-b874-4014-da10-d480fe4c416f",
                }
            ],
        }
        charts_map = dashboard_healthy_service.get_dashboard_charts_map(dashboard_metadata)
        print(charts_map)


if __name__ == "__main__":
    unittest.main()
