from abc import ABC, abstractmethod
import time
from copy import copy
from datetime import datetime

from dmplib.components.enums import DBType
from dmplib import config
from dmplib.utils.errors import UserError
from manual_filling.repositories import filling_template_repositories
from manual_filling.common.constant import SHARE_TABLE, TMP_TABLE
from dmplib.saas.project import get_db, get_data_db
from dmplib.hug import g
from manual_filling.common.enums import ViewAction
from manual_filling.repositories.filling_data_repositories import (
    get_user_name, check_column_value, trans_value, _update_insert_to_table
)


class DatabaseTableGenerator(ABC):
    """
    抽象基类定义了创建数据库表的基本方法。
    """

    @abstractmethod
    def generate_table(self, template_id, filling_template, version) -> str:
        pass


class DatabaseDataToTable(ABC):
    """
    抽象基类定义了数据库表update and insert的基本方法。
    """

    @abstractmethod
    def data_2_table(self, data, table_name, batch, column_map: dict = None, fill_type=None, column_check: dict = None, unique_columns=None):
        pass


class MySQLTableGenerator(DatabaseTableGenerator):
    """
    实现MySQL数据库表生成器。
    """

    def generate_table(self, template_id, filling_template, version) -> str:
        template_columns = filling_template_repositories.get_filling_template_column(template_id)
        if not template_columns:
            raise UserError("列为空，请先编辑模板列后再发布")

        result_table = filling_template.get('table_name')
        share_table = SHARE_TABLE.format(result_table)
        tmp_table = TMP_TABLE.format(result_table)
        unique = filling_template.get("unique_columns", "")
        if not unique:
            raise UserError(message="缺少唯一性校验字段")
        unique = ",".join([f"`{i}`" for i in unique.split(",")])

        had_batch = filling_template_repositories.check_had_batch(template_id)
        recreate = False
        if version != 0 and not had_batch:
            with get_data_db() as db:
                table_sql = """drop table IF EXISTS {};""".format(result_table)
                share_sql = """drop table IF EXISTS {};""".format(share_table)
                tmp_sql = """drop table IF EXISTS {};""".format(tmp_table)
                db.exec_sql(table_sql)
                db.exec_sql(share_sql)
                db.exec_sql(tmp_sql)
            recreate = True

        if version == 0 or recreate:
            # 新增模板
            template_columns.insert(1, {'column_name': 'batch_id', 'data_type': 'char(36)'})
            template_columns.insert(2, {'column_name': 'batch_name', 'data_type': 'varchar(64)'})
            template_columns.insert(3, {'column_name': 'batch_update_time', 'data_type': 'datetime'})
            template_columns.insert(4, {'column_name': 'cur_fill_user_id', 'data_type': 'char(36)'})
            template_columns.insert(5, {'column_name': 'cur_fill_user_name', 'data_type': 'varchar(128)'})
            template_columns.insert(6, {'column_name': 'status', 'data_type': 'char(36)'})
            template_columns.insert(7, {'column_name': 'remark', 'data_type': 'text'})
            template_columns.insert(8, {'column_name': 'reviewer', 'data_type': 'varchar(256)'})
            template_columns.insert(9, {'column_name': 'review_time', 'data_type': 'datetime'})
            primary_key = f"id_{int(time.time())} INT PRIMARY KEY AUTO_INCREMENT"
            unique_key = f"UNIQUE KEY `unique_verify` (`batch_id`,{unique})"
            field_sql = ",".join(
                [f"`{column.get('column_name')}`" + " " + column.get('data_type') + " null" for column in
                 template_columns])
            create_share_table_sql = f"""
            create table {share_table}(
            {primary_key},
            {field_sql},
            {unique_key}
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
            """
            create_result_table_sql = f"""
            create table {result_table}(
            {primary_key},
            {field_sql},
            {unique_key}
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
            """
            create_temp_table_sql = f"""
            create table {tmp_table}(
            {primary_key},
            {field_sql},
            {unique_key}
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
            """
            with get_data_db() as db:
                db.exec_sql(create_temp_table_sql, commit=False)
                db.exec_sql(create_share_table_sql, commit=False)
                db.exec_sql(create_result_table_sql, commit=False)
                db.commit()
        else:
            # 修改模板，添加新字段
            add_columns = [column for column in template_columns if column.get('version') == version]
            if add_columns:
                with get_data_db() as db:
                    for column in add_columns:
                        add_share_table_column_sql = f"alter table {share_table} add column `{column.get('column_name')}` {column.get('data_type')} null;"
                        add_result_table_column_sql = f"alter table {result_table} add column `{column.get('column_name')}` {column.get('data_type')} null;"
                        add_tmp_table_column_sql = f"alter table {tmp_table} add column `{column.get('column_name')}` {column.get('data_type')} null;"
                        db.exec_sql(add_share_table_column_sql, commit=False)
                        db.exec_sql(add_result_table_column_sql, commit=False)
                        db.exec_sql(add_tmp_table_column_sql, commit=False)
                    db.commit()
        if filling_template.get('data_source_type') == 1 and filling_template.get('data_source_id'):
            from app_celery import sync_filling_data
            result = {'project_code': g.code, 'table_name': result_table}
            sync_filling_data.apply_async(kwargs=result)


class DMTableGenerator(DatabaseTableGenerator):
    """
    实现达梦数据库表生成器。
    """

    def generate_table(self, template_id, filling_template, version) -> str:
        template_columns = filling_template_repositories.get_filling_template_column(template_id)
        if not template_columns:
            raise UserError("列为空，请先编辑模板列后再发布")

        result_table = filling_template.get('table_name')
        share_table = SHARE_TABLE.format(result_table)
        tmp_table = TMP_TABLE.format(result_table)
        unique = filling_template.get("unique_columns", "")
        if not unique:
            raise UserError(message="缺少唯一性校验字段")
        unique = ",".join([f'"{i}"' for i in unique.split(",")])

        had_batch = filling_template_repositories.check_had_batch(template_id)
        recreate = False
        if version != 0 and not had_batch:
            with get_data_db() as db:
                table_sql = """drop table IF EXISTS {};""".format(result_table)
                share_sql = """drop table IF EXISTS {};""".format(share_table)
                tmp_sql = """drop table IF EXISTS {};""".format(tmp_table)
                db.exec_sql(table_sql)
                db.exec_sql(share_sql)
                db.exec_sql(tmp_sql)
            recreate = True

        build_in_columns = [
            {'column_name': 'batch_id', 'data_type': 'char(36)'},
            {'column_name': 'batch_name', 'data_type': 'varchar(64 char)'},
            {'column_name': 'batch_update_time', 'data_type': 'datetime'},
            {'column_name': 'cur_fill_user_id', 'data_type': 'char(36)'},
            {'column_name': 'cur_fill_user_name', 'data_type': 'varchar(128 char)'},
            {'column_name': 'status', 'data_type': 'char(36)'},
            {'column_name': 'remark', 'data_type': 'text'},
            {'column_name': 'reviewer', 'data_type': 'varchar(256 char)'},
            {'column_name': 'review_time', 'data_type': 'datetime'}
        ]

        if version == 0 or recreate:
            # 新增模板
            template_columns = build_in_columns + template_columns
            primary_key = f"id_{int(time.time())} INT PRIMARY KEY AUTO_INCREMENT"
            unique_key = f'UNIQUE ("batch_id",{unique})'
            field_sql = ",".join(
                [f'"{column.get("column_name")}"' + " " + column.get('data_type') + " null" for column in
                 template_columns])

            create_sql = """
                create table {result_table}(
                {primary_key},
                {field_sql},
                {unique_key}
                ) ;
            """

            create_share_table_sql = copy(create_sql).format(
                result_table=share_table,
                primary_key=primary_key,
                field_sql=field_sql,
                unique_key=unique_key
            )

            create_result_table_sql = copy(create_sql).format(
                result_table=result_table,
                primary_key=primary_key,
                field_sql=field_sql,
                unique_key=unique_key
            )
            create_temp_table_sql = copy(create_sql).format(
                result_table=tmp_table,
                primary_key=primary_key,
                field_sql=field_sql,
                unique_key=unique_key
            )
            with get_data_db() as db:
                db.exec_sql(create_temp_table_sql, commit=False)
                db.exec_sql(create_share_table_sql, commit=False)
                db.exec_sql(create_result_table_sql, commit=False)
                db.commit()
        else:
            # 修改模板，添加新字段
            add_columns = [column for column in template_columns if column.get('version') == version]
            if add_columns:
                with get_data_db() as db:
                    for column in add_columns:
                        column_name = column.get("column_name")
                        data_type = column.get("data_type")
                        add_share_table_column_sql = f'alter table {share_table} add column "{column_name}" {data_type} null;'
                        add_result_table_column_sql = f'alter table {result_table} add column "{column_name}" {data_type} null;'
                        add_tmp_table_column_sql = f'alter table {tmp_table} add column "{column_name}" {data_type} null;'
                        db.exec_sql(add_share_table_column_sql, commit=False)
                        db.exec_sql(add_result_table_column_sql, commit=False)
                        db.exec_sql(add_tmp_table_column_sql, commit=False)
                    db.commit()
        if filling_template.get('data_source_type') == 1 and filling_template.get('data_source_id'):
            from app_celery import sync_filling_data
            result = {'project_code': g.code, 'table_name': result_table}
            sync_filling_data.apply_async(kwargs=result)


class MysqlDataToTable(DatabaseDataToTable):
    """
    实现MySQL数据库表插入器。
    """
    def data_2_table(
            self, data, table_name, batch, column_map: dict = None,
            fill_type=None, column_check: dict = None, unique_columns=None
    ):

        if not data:
            return
        col_name_list = list(data[0].keys())
        col_name = list(column_map.values()) + ["batch_update_time", "cur_fill_user_name", "status", "remark",
                                                "reviewer", "review_time"]
        for col in col_name_list:
            if col not in col_name:
                raise UserError(message=f"字段【{col}】不存在")
        col_name_list = ["batch_id", "batch_name"] + col_name_list
        values = []
        reversed_map = {v: k for k, v in column_map.items()}
        now = f"{datetime.now():%Y-%m-%d %H:%M:%S}"
        user_name = get_user_name()
        not_review = fill_type not in [ViewAction.Review.value, ViewAction.ReviewReject.value,
                                       ViewAction.ReviewStaging.value]
        if not_review:
            col_name_list.append("cur_fill_user_id")
        update_values = ", ".join([f"`{i}`=values(`{i}`)" for i in col_name_list])

        for item in data:
            if fill_type != ViewAction.Staging.value:
                check_column_value(item, column_check, reversed_map)
            if fill_type == ViewAction.Review.value:
                item["status"] = "通过"
            item["batch_id"] = batch.get("id")
            item["batch_name"] = batch.get("batch_name")
            if not_review:
                item["batch_update_time"] = now
                item["cur_fill_user_id"] = g.userid
                item["cur_fill_user_name"] = user_name
            else:
                item["review_time"] = now
                item["reviewer"] = user_name
            values.append(f"({','.join([trans_value(item.get(i)) for i in col_name_list])})")
            if len(values) >= 5000:
                _update_insert_to_table(table_name, col_name_list, values, update_values)
                values = []
        if values:
            _update_insert_to_table(table_name, col_name_list, values, update_values)


class DMDataToTable(DatabaseDataToTable):
    """
    实现DM数据库表插入器。
    """

    @staticmethod
    def _merge_into(table_name, values, unique_columns, col_name_list):
        with get_data_db() as db:
            merge_sql = """
                    merge into "{table_name}" as target
                    USING (
                        {using_sql}
                    ) as source
                    on {on_sql}
                    WHEN MATCHED THEN
                        UPDATE SET {update_sql}
                    WHEN NOT MATCHED THEN
                        INSERT ({insert_fields}) VALUES ({values})
                    """.format(
                table_name=table_name,
                using_sql=' union all '.join(values),
                on_sql=' AND '.join([f'target."{i}" = source."{i}"' for i in unique_columns]),
                update_sql=', '.join([f'target."{i}" = source."{i}"' for i in col_name_list if i not in unique_columns]),
                insert_fields=', '.join([f'"{i}"' for i in col_name_list]),
                values=', '.join([f'source."{i}"' for i in col_name_list])
            )
            try:
                db.exec_sql(merge_sql)
            except Exception as e:
                raise UserError(message=f"数据写入错误：{e}")

    def data_2_table(
            self, data, table_name, batch, column_map: dict = None,
            fill_type=None, column_check: dict = None, unique_columns=None
    ):
        if not data:
            return

        if isinstance(unique_columns, str):
            unique_columns = ['batch_id'] + unique_columns.split(',')

        col_name_list = list(data[0].keys())
        col_name = list(column_map.values()) + ["batch_update_time", "cur_fill_user_name", "status", "remark",
                                                "reviewer", "review_time"]
        for col in col_name_list:
            if col not in col_name:
                raise UserError(message=f"字段【{col}】不存在")
        col_name_list = ["batch_id", "batch_name"] + col_name_list
        values = []
        reversed_map = {v: k for k, v in column_map.items()}
        now = f"{datetime.now():%Y-%m-%d %H:%M:%S}"
        user_name = get_user_name()
        not_review = fill_type not in [ViewAction.Review.value, ViewAction.ReviewReject.value,
                                       ViewAction.ReviewStaging.value]
        if not_review:
            col_name_list.append("cur_fill_user_id")

        for item in data:
            if fill_type != ViewAction.Staging.value:
                check_column_value(item, column_check, reversed_map)
            if fill_type == ViewAction.Review.value:
                item["status"] = "通过"
            item["batch_id"] = batch.get("id")
            item["batch_name"] = batch.get("batch_name")
            if not_review:
                item["batch_update_time"] = now
                item["cur_fill_user_id"] = g.userid
                item["cur_fill_user_name"] = user_name
            else:
                item["review_time"] = now
                item["reviewer"] = user_name
            values.append(f"""select {','.join([f'{trans_value(item.get(i))} as "{i}"' for i in col_name_list])} from dual """)
            if len(values) >= 1000:
                self._merge_into(table_name, values, unique_columns, col_name_list)
                values = []
        if values:
            self._merge_into(table_name, values, unique_columns, col_name_list)


def get_table_generator() -> DatabaseTableGenerator:
    """
    工厂方法返回正确的数据库表生成器实例。
    """
    if config.get('DB.db_type') == DBType.DM.value:
        return DMTableGenerator()
    else:
        return MySQLTableGenerator()


def get_data_2_table_generator():
    if config.get('DB.db_type') == DBType.DM.value:
        return DMDataToTable()
    else:
        return MysqlDataToTable()
