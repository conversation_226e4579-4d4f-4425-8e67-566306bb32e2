#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401

"""
    <NAME_EMAIL> 2019/3/6
"""
import tempfile

from PIL import Image, ImageEnhance
from PIL import ImageFont, ImageDraw
from math import ceil

DEFAULT_FONT_SIZE = 14
DEFAULT_FONT_COLOR = '#5c6985'
DEFAULT_ALPHA = 0.3
# font_size为14时可容纳39个字符
DEFAULT_WIDTH = 320
DEFAULT_WIDTH_OFFSET = 100
DEFAULT_HEIGHT = 110
DEFAULT_HEIGHT_OFFSET = 40
DEFAULT_ROTATE = 15
DEFAULT_BACKGROUND = 'transparency'


class DMark:
    """
    example: filename = DMark('<EMAIL>', {'font': 'ttf/yahei.ttf', 'color': (92, 105, 133, 20)}).png()
    options (Object): optional, the options of watermark, with keys below:
        width (Number): default is 250.
        height (Number): default is 80.
        color (String): the text color, default is #ebebeb.
        alpha (Float): the text alpha(0 ~ 1), default is 0.8.
        font (String): the text font style, default is MicroSoft YaHei.
        font_size (Number): the text font size, default is 16px.
        rotate (Number): the rotation of text, default is 15 rotation.
        background (String): the background of picture, include dark、white、transparency。default is transparency.
    """

    __slots__ = (
        "text", "alpha", "font", "font_size", "color",
        "width_offset", "height_offset", "width", "height", "rotate", "background"
    )

    def __init__(self, text, options=None):
        self.text = text if text else 'no data'
        if options:
            default = {
                "alpha": DEFAULT_ALPHA,
                "font": None,
                "font_size": DEFAULT_FONT_SIZE,
                "color": DEFAULT_FONT_COLOR,
                "width_offset": DEFAULT_WIDTH_OFFSET,
                "height_offset": DEFAULT_HEIGHT_OFFSET,
                # 函数调用取值不能在这里执行，因为函数内使用到了其它设置的值还没有设置
                "width": self.get_width,
                "height": self.get_height,
                "rotate": DEFAULT_ROTATE,
                "background": DEFAULT_BACKGROUND,
            }
            for k, v in default.items():
                v = options.get(k) or v
                if callable(v):
                    v = v()
                setattr(self, k, v)

    def png(self):
        """
        生成含有指定文字水印的png图片
        :return:
        """
        # 默认使用透明背景图片底色
        img = self.get_background_image()
        font = self.get_font()

        watermark_img = Image.new('RGBA', size=img.size, color=(0, 0, 0, 0))

        draw = ImageDraw.Draw(watermark_img)
        draw.text((0, 0), self.text, fill=self.color, font=font)
        watermark_img = watermark_img.rotate(15, Image.BICUBIC, expand=True)

        alpha = watermark_img.split()[3]
        alpha = ImageEnhance.Brightness(alpha).enhance(self.alpha)
        watermark_img.putalpha(alpha)
        img = Image.composite(watermark_img, img, watermark_img)

        filename = tempfile.NamedTemporaryFile(suffix='.png').name
        img.save(filename, quality=100)
        return filename

    def get_background_image(self):
        """
        生成图片的底层背景图片色，没指定background参数默认使用透明背景作为底色
        :return:
        """
        background_dict = {'transparency': (0, 0, 0, 0), 'dark': (15, 24, 47, 255), 'white': (255, 255, 255, 255)}
        background_color = (
            background_dict.get(self.background) if background_dict.get(self.background) else (0, 0, 0, 0)
        )
        return Image.new('RGBA', (self.width, self.height), background_color)

    def get_font(self):
        """
        获取字体和字体大小对象，没有指定该参数使用系统默认字体和字体大小
        :return:
        """
        font_size = self.font_size if self.font_size else DEFAULT_FONT_SIZE
        if self.font:
            return ImageFont.truetype(self.font, font_size)
        return ImageFont.load_default()

    def get_width(self):
        """
        根据文字字体和字体大小，rotate角度，以及文字个数来动态确定图片长度。因算法非常复杂，目前只考虑字体个数一个因素来定长
        :return:
        """
        width = 8 * len(self.text)
        # 取当前宽度整数值
        if len(str(width)) < 2:
            width = 15
        else:
            i = 1 if len(str(width)) < 3 else 2
            mod = 10 * (len(str(width)) - i)
            width = ceil(width / mod) * mod + 5
        return width + self.width_offset

    def get_height(self):
        """
        根据文字字体和字体大小，rotate角度，以及文字个数来动态确定图片宽度。因算法非常复杂，目前只考虑字体个数一个因素来定宽
        :return:
        """
        height = 25
        mod = 3 * len(self.text)
        height += mod
        return height + self.height_offset


# DMark(
#     # '<EMAIL>.cn00000000000000000',
#     'erert45345@m000000000000000',
#     # '123456',
#     {'font_size': 14, 'font': 'ttf/yahei.ttf', 'background': 'dark', 'color': (92, 105, 133, 255)},
# ).png()
