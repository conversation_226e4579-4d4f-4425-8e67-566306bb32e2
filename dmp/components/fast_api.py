from datetime import datetime, <PERSON>elta
import os
import redis

from base.dmp_constant import APP_NAME
from dmplib import redis, config
from dmplib.redis import RedisCache
from mylog import MyLog, FieldType, Period
from loguru import logger

from base.enums import FastLogType
from dmplib.components import auth_util
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps

enable = False
fast_log = None
LOG_FOLDER = "/data/logs/"
STRUCTURE_LOCK_KEY = 'fast_log_structure_lock:'
SCHEDULE_LOG_DEFINE = {
    'tenant_code': {'type': FieldType.STRING},
    'flow_instance_id': {'type': FieldType.STRING},
    'node_id':  {'type': FieldType.STRING},
    'level_name':  {'type': FieldType.STRING},
    'level_no': {'type': FieldType.INTEGER},
    'created': {'type': FieldType.DATETIME},
    'file_name':  {'type': FieldType.STRING},
    'module':  {'type': FieldType.STRING},
    'func_name':  {'type': FieldType.STRING},
    'path_name':  {'type': FieldType.STRING},
    'line_no': {'type': FieldType.INTEGER},
    'process': {'type': FieldType.INTEGER},
    'process_name':  {'type': FieldType.STRING},
    'thread_name':  {'type': FieldType.STRING},
    'thread': {'type': FieldType.INTEGER},
    'exc_text':  {'type': FieldType.STRING, 'length': 65535},
    'message': {'type': FieldType.STRING, 'length': 65535},
    'created_on': {'type': FieldType.DATETIME}
}
USER_BEHAVIOR_LOG_DEFINE = {
    "tenant_code": {'type': FieldType.STRING},
    "account": {'type': FieldType.STRING},
    "module": {'type': FieldType.STRING},
    "action": {'type': FieldType.STRING},
    "content": {'type': FieldType.STRING},
    "ip": {'type': FieldType.STRING},
    "domain": {'type': FieldType.STRING},
    "path": {'type': FieldType.STRING},
    "url": {'type': FieldType.STRING},
    "do_object": {'type': FieldType.STRING},
    "brower": {'type': FieldType.STRING},
    "created_on": {'type': FieldType.DATETIME},
    "created_by": {'type': FieldType.STRING},
    "extra": {'type': FieldType.STRING, 'length': 65535}
}

def get_logserver_url():
    return AppHosts.get(SkylineApps.LOG)

if auth_util.is_env_enable_skyline_auth():
    url = get_logserver_url()
    if url:
        logger.info(f'当前环境已部署日志服务[{url}]，后续日志通过日志服务写入')
        fast_log = MyLog(get_logserver_url, auth_util.gen_auth_token, meta_data_file=LOG_FOLDER+'metadata')
        enable = True

def insert_log(log_type:FastLogType, params:dict):
    if not enable:
        return
    define = {}
    if FastLogType.SCHEDULE == log_type:
        define = SCHEDULE_LOG_DEFINE
    elif FastLogType.USER_BEHAVIOR == log_type:
        define = USER_BEHAVIOR_LOG_DEFINE
    extra_keys = set(params.keys()) - set(define.keys())
    for key in extra_keys:
        del params[key]
    replace_none(params, define)
    if not params.get('tenant_code'):
        params['tenant_code'] = 'sys'
    params['created_on'] = datetime.now()
    fast_log.insert(log_type.value, params)


FAST_LOG_MAP = {}


def get_spec_queue_log(queue_name):
    obj = FAST_LOG_MAP.get(queue_name, None)
    if not obj:
        log = MyLog(get_logserver_url, auth_util.gen_auth_token, meta_data_file=LOG_FOLDER + 'metadata',
                         work_id=queue_name, log_file_max_size=5 * 1024 * 1024)
        overwrite_metadata = False
        log.synchronize(
            {
                 # 日志名称
                "log_name": FastLogType.SCHEDULE.value,
                # 产品代码
                "product_code": "dmp",
                # 日志文件路径
                "log_files": LOG_FOLDER + "schedule/*.log",
                # 定义字段, 必须包含 tenant_code 这个字段
                "fields": [
                    {'name': k, **v} for k, v in SCHEDULE_LOG_DEFINE.items()
                ],
                # 分表的循环周期
                "period": Period.DATE,
                # 单位: 天. 设置清理时间. Default: 7天
                "ttl": 30,
            },{
                "log_name": FastLogType.USER_BEHAVIOR.value,
                "product_code": "dmp",
                "log_files": LOG_FOLDER + "user_behavior/*.log",
                "fields": [
                    {'name': k, **v} for k, v in USER_BEHAVIOR_LOG_DEFINE.items()
                ],
                "period": Period.DATE,
                "ttl": 30,
            }, write_meta_data=overwrite_metadata
        )
        FAST_LOG_MAP[queue_name] = log
    return obj


def insert_dmp_flow_log(fast_log_obj, log_type:FastLogType, params:dict):
    if not enable:
        return
    define = {}
    if FastLogType.SCHEDULE == log_type:
        define = SCHEDULE_LOG_DEFINE
    extra_keys = set(params.keys()) - set(define.keys())
    for key in extra_keys:
        del params[key]
    replace_none(params, define)
    if not params.get('tenant_code'):
        params['tenant_code'] = 'sys'
    params['created_on'] = datetime.now()
    fast_log_obj.insert(log_type.value, params)


def query_flow_log(tenant_code, flow_instance_id,page_num=1, page_size=100):
    define = SCHEDULE_LOG_DEFINE
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    one_month_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')
    filters = [{"expr":"__created_time__ > ?", "val": one_month_ago},{"expr":"__created_time__ <= ?", "val": now}, {'expr': 'tenant_code = ?', 'val': tenant_code}, {'expr': 'flow_instance_id = ?', 'val': flow_instance_id}]
    datares = fast_log.query_list(product_code='dmp', log_name=FastLogType.SCHEDULE.value,
                                     cols=','.join(define.keys()), filters=filters,
                                     orders='created_on', limit=page_size, offset=(page_num - 1) * page_size, )
    countres = fast_log.query_count(product_code='dmp', log_name=FastLogType.SCHEDULE.value, filters=filters)
    data = datares.get('list', [])
    return {'items': data, 'total': countres}


def query_user_behavior(conditions:dict , page_num=1, page_size=100):
    define = USER_BEHAVIOR_LOG_DEFINE
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    one_month_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')
    # 将时间转换为字符串格式
    filters = [{"expr":"__created_time__ > ?", "val": one_month_ago}, {"expr":"__created_time__ <= ?", "val": now}]
    if conditions:
        for k, v in conditions.items():
            filters.append({'expr': k, 'val': v})
    datares = fast_log.query_list(
        product_code='dmp',
        log_name=FastLogType.USER_BEHAVIOR.value,
        cols=','.join(define.keys()),
        filters=filters,
        orders='created_on desc',
        limit=page_size,
        offset=(page_num - 1) * page_size,
    )
    countres = fast_log.query_count(
        product_code='dmp',
        log_name=FastLogType.USER_BEHAVIOR.value,
        filters=filters
    )
    data = datares.get('list', [])
    return {'items': data, 'page_size': page_size, 'skip': page_num, 'total': countres}


def _format_module_title(item, module_title):
    if item.get('moudel') == 'large_screen':
        return '大屏'
    else:
        return module_title

def init_fast_log_structure():
    if not enable:
        return
    conn = RedisCache()
    key = STRUCTURE_LOCK_KEY + APP_NAME
    overwrite_metadata = False
    if conn.setnx(key, '1'):
        conn.expire(key, 30)
        if not os.path.exists(LOG_FOLDER + 'metadata'):
            overwrite_metadata = True
    logger.info(f'初始化天眼日志结构 overwrite={overwrite_metadata}')
    fast_log.synchronize(
        {
             # 日志名称
            "log_name": FastLogType.SCHEDULE.value,
            # 产品代码
            "product_code": "dmp",
            # 日志文件路径
            "log_files": LOG_FOLDER + "schedule/*.log",
            # 定义字段, 必须包含 tenant_code 这个字段
            "fields": [
                {'name': k, **v} for k, v in SCHEDULE_LOG_DEFINE.items()
            ],
            # 分表的循环周期
            "period": Period.DATE,
            # 单位: 天. 设置清理时间. Default: 7天
            "ttl": 30,
        },{
            "log_name": FastLogType.USER_BEHAVIOR.value,
            "product_code": "dmp",
            "log_files": LOG_FOLDER + "user_behavior/*.log",
            "fields": [
                {'name': k, **v} for k, v in USER_BEHAVIOR_LOG_DEFINE.items()
            ],
            "period": Period.DATE,
            "ttl": 30,
        }, write_meta_data=overwrite_metadata
    )



def replace_none(params, define):
    for k, v in params.items():
        if not v and (define.get(k)).get('type') == FieldType.STRING:
            params[k] = ''
        if k in ['created_on'] and not v:
            params[k] = datetime.now()
        if k in ['created'] and v and isinstance(v, str):
            params[k] = datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
