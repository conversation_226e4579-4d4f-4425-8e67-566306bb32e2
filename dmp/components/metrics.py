"""
定义和初始化监控的对象, 按规范命名
"""
import datetime
from prometheus_client import Histogram
from dmplib.metrics import definition
from dmplib import config


from typing import List, Optional


class PromDurationBase:
    def __init__(self, name: str, document: str, labelnames: List[str]) -> None:
        self._name = name
        self._document = document
        self._labelnames = labelnames
        self._enabled = definition.is_enabled()

        # 默认全部需要加上env_code标签
        if not labelnames:
            self._labelnames = [definition.ENV_CODE_LABEL]

        if definition.ENV_CODE_LABEL not in self._labelnames:
            self._labelnames.insert(0, definition.ENV_CODE_LABEL)
        self._prom = Histogram(name, document, self._labelnames)

        self._start_time = None
        self._env_code = config.get_env_code()

    def start(self) -> None:
        if not self._enabled:
            return
        self._start_time = datetime.datetime.now()

    def _end_with_labels(self, labelvalues: List[str]) -> None:
        if not self._enabled:
            return
        if not labelvalues:
            labelvalues = []
        labelvalues.insert(0, self._env_code)
        self._prom.labels(*labelvalues).observe((datetime.datetime.now() - self._start_time).total_seconds())


class PromDatasetQueryDuration(PromDurationBase):
    """
    数据集查询时长
    """

    def __init__(self) -> None:
        super().__init__("dataset_query_duration", "Request duration for query dataset", ['name'])

    def end(self, dataset_name: Optional[str]) -> None:
        if not dataset_name:
            return
        self._end_with_labels([dataset_name])


prom_dataset_query_duration = PromDatasetQueryDuration()
