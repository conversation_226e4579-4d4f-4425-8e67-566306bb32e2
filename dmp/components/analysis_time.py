# !/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : __init__.py.py
# @Author: guq
# @Date  : 2022/1/24
# @Desc  :
import os
import time
import json
import traceback
from enum import Enum, unique
from contextlib import contextmanager
from typing import Union

import curlify
from loguru import logger
from base.enums import DataSourceType
from base.models import BaseModel, BaseModelEncoder
from dmplib.hug import g
from dmplib.utils.strings import seq_id
from base.dmp_constant import __ANALYSIS_DOCTOR_DISPLAY_SQL_KEYWORD__, __ANALYSIS_DOCTOR_KEYWORD__


class NodeModel(BaseModel):
    __slots__ = ['id', 'name', 'level_code', 'parent_id']

    def __init__(self, level_code, name, id, parent_id, **kwargs):
        self.id = id
        self.name = name
        self.level_code = level_code
        self.parent_id = parent_id
        self.data = {}
        super().__init__(**kwargs)

    def __str__(self):
        value = json.dumps(self.get_dict(), cls=BaseModelEncoder, ensure_ascii=False)
        return f'{__class__}-{hex(id(self))} <{value}>'


@unique
class TypeEnum(Enum):
    ready_query = NodeModel('0001-', '数据准备时间', 1, None)
    indirect_query = NodeModel('0001-0001-', '变量间接取数', 2, 1)
    main_query_generate = NodeModel('0001-0002-', '主SQL生成时间', 3, 1)
    sql_query = NodeModel('0002-', 'SQL查询时间', 4, None)
    main_sql_query = NodeModel('0002-0001-', '主SQL查询时间', 5, 4)
    super_calc_query = NodeModel('0002-0002-', '高级计算', 6, 4)
    percent_query = NodeModel('0002-0002-0001-', '百分比计算', 7, 6)
    same_ring_query = NodeModel('0002-0002-0002-', '同环比计算', 8, 6)
    other_calc_query = NodeModel('0002-0002-0003-', '其他', 9, 6)
    summary_subtotal_query = NodeModel('0002-0003-', '总小计', 10, 4)
    cate_query = NodeModel('0002-0003-0001-', '分类汇总', 11, 10)
    col_subtotal_query = NodeModel('0002-0003-0002-', '列总计', 12, 10)
    row_subtotal_query = NodeModel('0002-0003-0003-', '行小计', 13, 10)
    return_data_deal = NodeModel('0003-', '数据返回处理', 14, None)
    # unknown = NodeModel('0004-', '数据返回处理', 14, None)


# 运行时详细的debug结构信息
@unique
class ReleaseTypeEnum(Enum):
    ready_query = NodeModel('0001-', '数据准备时间', 1, None)
    indirect_query = NodeModel('0001-0001-', '变量间接取数', 2, 1)
    main_query_generate = NodeModel('0001-0002-', '主SQL生成时间', 3, 1)

    # 主SQL的处理
    # sql_query = NodeModel('0002-', 'SQL查询时间', 4, None)
    total_mian_query_process = NodeModel('0002-', '主SQL取数过程', 100, None)
    # main_sql_pre_query = NodeModel('0002-0001-', '主SQL取数过程-前置过程',11, 10)
    main_sql_query = NodeModel('0002-0002-', '主SQL取数过程-查询SQL', 101, 100)
    # main_sql_query = NodeModel('0002-0002-', '主SQL取数过程-数据处理', 5, 4)

    # 高级计算处理
    super_total_calc_query = NodeModel('0003-', '高级计算取数过程', 200, None)
    # super_calc_query = NodeModel('0002-0002-', '高级计算', 6, 4)
    percent_query = NodeModel('0003-0001-', '百分比计算-SQL查询', 210, 200)
    same_ring_query = NodeModel('0003-0002-', '同环比计算-SQL查询', 220, 200)
    other_calc_query = NodeModel('0003-0003-', '其他-SQL查询', 230, 200)

    # 行、列总小计
    summary_subtotal_query = NodeModel('0004-', '行、列总小计取数全过程', 300, None)
    # 列总、小计
    subtotal_col_process = NodeModel('0004-0001-', '列的总、小计处理过程', 310, 300)
    single_subtotal_col_process = NodeModel('0004-0001-0001-', '列小计的单个分类小计处理过程', 320, 310)
    single_col_build = NodeModel('0004-0001-0001-0002-', '分类小计-查询全过程', 330, 320)
    cate_query = NodeModel('0004-0001-0001-0002-0001-', '分类小计-SQL计时', 340, 330)
    single_col_compare_process = NodeModel('0004-0001-0001-0003-', '列小计对比维度下的补充计算', 350, 320)
    col_compare_query = NodeModel('0004-0001-0001-0003-', '列小计对比维度下-SQL计时', 351, 350)
    subtotal_summary_process = NodeModel('0004-0001-0002-', '列总计处理过程', 360, 310)
    col_subtotal_query = NodeModel('0004-0001-0002-0001-', '列总计-SQL计时', 370, 360)
    summary_compare_process = NodeModel('0004-0001-0001-0003-', '列总计对比维度下的补充计算', 380, 360)
    col_summary_compare_query = NodeModel('0004-0001-0001-0003-0002-', '列总计对比维度下-SQL计时', 381, 380)
    # 行小计
    subtotal_row_process = NodeModel('0004-0002-', '行的小计处理过程', 400, 300)
    row_subtotal_query = NodeModel('0004-0002-0002-', '行小计-SQL查询', 410, 400)

    # 最终的返回数据处理
    return_data_deal = NodeModel('0005-', '数据返回处理', 1000, None)


class TypeEnumClassProxy:
    def __getattribute__(self, item):
        enum_cls = AnalysisTimeUtils.get_type_enum()
        # 兼容另一种场景下没有这个类型的枚举值
        # 没有的话返回一个mock的enum
        # 然后在具体的记录时刻直接忽略掉
        if not hasattr(enum_cls, item):
            enum_obj = MockTypeEnum()
        else:
            enum_obj = getattr(enum_cls, item)
        return enum_obj


class MockTypeEnum:
    class MockKey:
        mock_value = '__mock_type_enum_value__'
        value = mock_value

    def __getattr__(self, item):
        if item not in self.__dict__:
            return self.MockKey.value
        return getattr(self, item)


ALL_NODES = {val for val in TypeEnum.__members__.values()}
ALL_RELEASED_NODES = {val for val in ReleaseTypeEnum.__members__.values()}


def set_sql_debug(req):
    from func_timing.middlewares import FuncTimingMiddleware
    field = __ANALYSIS_DOCTOR_DISPLAY_SQL_KEYWORD__
    result = FuncTimingMiddleware().rule_check(field, req)
    if result:
        setattr(g, DataQueryAnalyzer.__g_sql_debug__, True)


def append_stack(func, record: dict, root_path='') -> dict:
    from func_timing.timing import append_stack as real_append_stack
    return real_append_stack(func, record, root_path)


class TimeDataTreeStruct:
    @staticmethod
    def build_record_tree(records):
        # 抄过来的，从报表层级生成
        data_mapping = {r.get('id'): r for r in records}
        result = []
        for r in records:
            parent_id = r.get('parent_id')
            if len(r.get('level_code', '')) == 5 or not parent_id or r.get('id') == parent_id:
                result.append(r)
            else:
                parent_r = data_mapping.get(parent_id)
                if not parent_r:
                    continue
                if not parent_r.get('sub'):
                    parent_r['sub'] = []
                parent_r['sub'].append(r)
        return result

    @staticmethod
    def trans_analysis_data(datas: list):
        full_data = []
        for data in datas:
            model = data.pop('type', None)
            if not model:
                continue
            node_data = model.get_dict() or {}
            node_data['data'] = data.copy()
            full_data.append(node_data)

        # 补齐没有的节点数据, 重建所有结构
        ids = {i.get('id') for i in full_data}
        all_nodes = ALL_RELEASED_NODES if AnalysisTimeUtils.get_curr_is_released_analysis_mode() else ALL_NODES
        for node in all_nodes:
            model = node.value
            if model.id not in ids:
                full_data.append(model.get_dict())
        full_data = sorted(full_data, key=lambda x: x.get('id'))

        result = []
        for node in full_data:
            # 1. 补全data为空的节点（父节点）
            if not node.get('data'):
                node['data'] = TimeDataTreeStruct.calc_parent_node_time(
                    parent_level_code=node.get('level_code'),
                    datas=full_data
                )
            # 2. 剔除为空的子节点
            if node.get('data'):
                result.append(node)
        return TimeDataTreeStruct.build_record_tree(result)

    @staticmethod
    def get_time_node(child_nodes, key='start_time'):
        return [
            n.get('data', {}).get(key)
            for n in child_nodes if n.get('data', {}).get(key)
        ]

    @staticmethod
    def calc_parent_node_time(parent_level_code, datas):
        # 给父节点计算过程时间
        child_nodes = [
            n for n in datas
            if n.get('level_code', '').startswith(parent_level_code) and n.get('level_code', '') != parent_level_code
        ]
        # 没有子节点直接返回空
        if not child_nodes:
            return {}

        start_time_list = TimeDataTreeStruct.get_time_node(child_nodes, 'start_time')
        end_time_list = TimeDataTreeStruct.get_time_node(child_nodes, 'end_time')
        if not start_time_list or not end_time_list:
            return {}

        min_start_time = min(start_time_list)
        max_end_time = max(end_time_list)
        return {
            "start_time": min_start_time,
            "end_time": max_end_time,
            "exec_time": max_end_time - min_start_time,
            "is_clac": True
        }


# 使用该上下文管理器可以对代码块添加计时
@contextmanager
def record_code_timing(step, sql, db_type, extra, need_type_inference):
    start_time = AnalysisTimeUtils.now()
    yield
    AnalysisTimeUtils.record(
        step=step, sql=sql,
        db_type=db_type, start_time=start_time,
        extra=extra, need_type_inference=need_type_inference
    )


@contextmanager
def just_timing(flag):
    start_time = AnalysisTimeUtils.now()
    yield
    end_time = AnalysisTimeUtils.now()
    duration = round((end_time - start_time) * 1000, 3)
    logger.error(f'{flag} cost: {duration}ms')


# 直接记录所有节点的时间
class TimeNode:
    __flag__ = 'time_node_stamp_data'

    @staticmethod
    def recode_time_node(node_name, extra={}):
        # logger.debug(f'current node_name: {node_name}')
        __flag__ = TimeNode.__flag__
        data = getattr(g, __flag__, [])
        cell = {
            "name": node_name,
            "curr": round(time.time() * 1000, 2)
        }
        if extra:
            cell['extra'] = extra
        data.append(cell)
        try:
            setattr(g, __flag__, data)
        except:
            pass

    @staticmethod
    def get_node_time_data():
        __flag__ = TimeNode.__flag__
        data = getattr(g, __flag__, [])
        for idx, cell in enumerate(data):
            if idx > 0:
                prev = cell.get('curr', 0) - data[idx - 1].get('curr', 0)
                cell['from_prev'] = round(prev, 2)
        return data


class AnalysisTimeUtils:
    cached_map = {}
    cached_analyzer_map = {}
    db_type = DataSourceType
    step_type = TypeEnumClassProxy()
    record_code = record_code_timing
    just_timing = just_timing
    recode_time_node = TimeNode.recode_time_node

    @staticmethod
    def get_curr_analyzer():
        flag_id = AnalysisTimeUtils.get_curr_flag_id()
        # analyzer = getattr(g, DataQueryAnalyzer.__g_property_name__)
        analyzer = AnalysisTimeUtils.cached_analyzer_map.get(flag_id)
        if analyzer is None:
            analyzer = DataQueryAnalyzer(flag_id, data=AnalysisTimeUtils.cached_map)
            analyzer.with_debug = AnalysisTimeUtils.get_sql_debug()
            # setattr(g, DataQueryAnalyzer.__g_property_name__, analyzer)
            AnalysisTimeUtils.cached_analyzer_map[flag_id] = analyzer
        return analyzer

    @staticmethod
    def get_curr_flag_id():
        return getattr(g, DataQueryAnalyzer.__g_flag_id__, '')

    @staticmethod
    def get_sql_debug():
        return getattr(g, DataQueryAnalyzer.__g_sql_debug__, False)

    @staticmethod
    def enter(chart_id):
        setattr(g, DataQueryAnalyzer.__g_start_time__, time.time())
        flag_id = f'{chart_id};{seq_id()}'
        setattr(g, DataQueryAnalyzer.__g_flag_id__, flag_id)
        # 留着以后备用
        setattr(g, DataQueryAnalyzer.__g_chart_id__, chart_id)
        AnalysisTimeUtils.cached_map.setdefault(flag_id, [])

    @staticmethod
    def exit():
        flag_id = AnalysisTimeUtils.get_curr_flag_id()
        # 移除缓存的数据
        AnalysisTimeUtils.cached_map.pop(flag_id, None)
        AnalysisTimeUtils.cached_analyzer_map.pop(flag_id, None)

    @staticmethod
    def now():
        return time.time()

    @staticmethod
    def record(
            step=None, db_type=None, sql=None,
            start_time: Union[int, float] = 0, extra={},
            need_type_inference=False
    ):
        # 判断是celery runtime, 则不设置记录
        if os.environ.get('CELERY_APP', '0') == '1':
            return
        # 来源于不属于当前的统计格式
        if step == MockTypeEnum.MockKey.mock_value:
            return
        try:
            analyzer = AnalysisTimeUtils.get_curr_analyzer()
            analyzer.append_step(
                step=step, sql=sql, db_type=db_type,
                start_time=start_time, extra=extra, need_type_inference=need_type_inference
            )
        except:
            logger.error(f'添加时间记录失败：{traceback.format_exc()}')

    @staticmethod
    def append_analysis_data(result: dict):
        if not isinstance(result, dict):
            return result
        try:
            analyzer = AnalysisTimeUtils.get_curr_analyzer()
            analysis_data = analyzer.get_analysis_data()
            analysis_data = TimeDataTreeStruct.trans_analysis_data(analysis_data)
            analysis_total_time = int((time.time() - getattr(g, DataQueryAnalyzer.__g_start_time__, 0)) * 1000)
            result['analysis_data'] = analysis_data
            result['analyzer_node_time'] = TimeNode.get_node_time_data()
            result['analysis_total_time'] = f'{analysis_total_time}ms'
        except:
            logger.error(f'添加时间记录到返回结果失败：{traceback.format_exc()}')
        return result

    # 临时方案：目前运行时性能数据与设计时的数据不一样，更加详细
    # 现在是同时兼容两种不同的计时，以及格式返回
    # 后续建议将设计时的更换成运行时, 只保留一种
    @staticmethod
    def get_curr_is_released_analysis_mode():
        request_params = getattr(g, 'request_data', {}).get('params', {})
        return __ANALYSIS_DOCTOR_KEYWORD__ in request_params

    @staticmethod
    def get_type_enum():
        if AnalysisTimeUtils.get_curr_is_released_analysis_mode():
            return ReleaseTypeEnum
        else:
            return TypeEnum


# 类型推导
class TypeInference:
    mapping = {
        TypeEnum.indirect_query.value: ['assign_indirect_values', 'indirect_query.py'],  # 间接查询
        # 分类汇总
        TypeEnum.cate_query.value: ['widget/subtotal/subtotal_col_query.py:_single_query_subtotal_col_data'],
        # 列总计
        TypeEnum.col_subtotal_query.value: ['widget/subtotal/subtotal_col_query.py:_query_subtotal_col_summary'],
        TypeEnum.row_subtotal_query.value: ['widget/subtotal/subtotal_row_query.py'],  # 对比维度的行小计
        TypeEnum.percent_query.value: ['widget/advanced_compute/ratio_compute.py'],  # 百分比计算
        TypeEnum.same_ring_query.value: ['widget/advanced_compute/same_ring_ratio_compute.py'],  # 同环比计算
        TypeEnum.main_sql_query.value: [
            'agent/query_agent.py:query',
            'data_query/charts/chart_query.py:query_data'
        ],  # 主报表取数(上面都不是，算是主报表取数)
        TypeEnum.other_calc_query.value: [
            'dashboard_chart/data_query/widget/count_query.py',  # 总数查询
            'dashboard_chart/data_query/widget/dataset_query.py',  # select数据集额外查询
            'dashboard_chart/data_query/widget/subtotal_query.py',  # StatisticTable小计/总计查询
        ]
    }

    released_mapping = {
        ReleaseTypeEnum.indirect_query.value: ['assign_indirect_values', 'indirect_query.py'],  # 间接查询
        # 分类汇总
        ReleaseTypeEnum.col_compare_query.value: [
            'widget/subtotal/subtotal_col_query.py:_single_query_subtotal_col_data',
            '_get_subtotal_row_data'
        ],  # 对比维度下的SQL列小计取数
        ReleaseTypeEnum.cate_query.value: ['widget/subtotal/subtotal_col_query.py:_single_query_subtotal_col_data'],
        # 列总计
        ReleaseTypeEnum.col_summary_compare_query.value: [
            'widget/subtotal/subtotal_col_query.py:_query_subtotal_col_summary',
            '_get_subtotal_row_summary_data'
        ],  # 对比维度下的SQL列总计取数
        ReleaseTypeEnum.col_subtotal_query.value: ['widget/subtotal/subtotal_col_query.py:_query_subtotal_col_summary'],
        ReleaseTypeEnum.row_subtotal_query.value: ['widget/subtotal/subtotal_row_query.py'],  # 对比维度的行小计
        ReleaseTypeEnum.percent_query.value: ['widget/advanced_compute/ratio_compute.py'],  # 百分比计算
        ReleaseTypeEnum.same_ring_query.value: ['widget/advanced_compute/same_ring_ratio_compute.py'],  # 同环比计算
        ReleaseTypeEnum.main_sql_query.value: [
            'agent/query_agent.py:query',
            'data_query/charts/chart_query.py:query_data'
        ],  # 主报表取数(上面都不是，算是主报表取数)
        ReleaseTypeEnum.other_calc_query.value: [
            'dashboard_chart/data_query/widget/count_query.py',  # 总数查询
            'dashboard_chart/data_query/widget/dataset_query.py',  # select数据集额外查询
            'dashboard_chart/data_query/widget/subtotal_query.py',  # StatisticTable小计/总计查询
        ]
    }

    def do(self):
        stack = append_stack(func=None, record={}, root_path=os.environ['DMP_ROOT_PATH']).pop('stack_source', '')
        mapping = __class__.released_mapping if AnalysisTimeUtils.get_curr_is_released_analysis_mode() else __class__.mapping  # noqa
        for type_str, condition in mapping.items():
            if self._contain_keyword(condition, stack):
                return type_str, stack
        return '未知', stack

    def _contain_keyword(self, words, text):
        for word in words:
            if word not in text:
                return False
        return True


class DataQueryAnalyzer:
    __g_property_name__ = 'data_interface_time'
    __g_flag_id__ = 'data_interface_time_flag_id'
    __g_chart_id__ = 'data_interface_time_chart_id'
    __g_sql_debug__ = 'data_interface_time_sql_debug'
    __g_start_time__ = 'data_interface_time_start_time'
    with_debug = False

    def __init__(self, flag_id, data: dict):
        self.flag_id = flag_id
        self.data = data

    def _init_data(self):
        if self.flag_id not in self.data:
            self.data[self.flag_id] = []

    def _append_data(self, data: dict):
        self._init_data()
        self.data[self.flag_id].append(data)

    def get_analysis_data(self):
        return self.data.get(self.flag_id, [])

    def append_step(self, step, sql, db_type='mysql', start_time=0, extra={}, need_type_inference=False):
        # 对于SQL的时间记录需要根据栈调动反推业务场景类型
        # 其他场景直接指定场景类型
        start_time = int(start_time * 1000)
        end_time = int(time.time() * 1000)
        exec_time = end_time - start_time
        _error_info = (
            f'step: {step}, sql：{sql}, db_type: {db_type}, start_time: {start_time}, '
            f'end_time: {end_time}, '
            f'extra: {extra}, '
            f'need_type_inference: {need_type_inference}'
        )
        record_func_mapping = {
            DataSourceType.API.value: self._deal_api_db_data,
            DataSourceType.Mysql.value: self._deal_common_db_data,
            DataSourceType.MSSQL.value: self._deal_common_db_data,
            DataSourceType.PostgreSQL.value: self._deal_common_db_data,
            DataSourceType.MysoftNewERP.value: self._deal_common_db_data,
            DataSourceType.MysoftShuXin.value: self._deal_common_db_data,
            DataSourceType.MysoftShuXin15.value: self._deal_common_db_data,
        }
        if need_type_inference:
            # 依赖类型自动推断
            if db_type not in record_func_mapping:
                logger.debug(f'时间记录接受了错误的数据源类型：{_error_info}')
                return
            record = record_func_mapping[db_type](sql, extra)
        else:
            # 手动指定类型
            record = {'type': step}

        record['start_time'] = start_time
        record['end_time'] = end_time
        record['exec_time'] = exec_time
        self._append_data(record)

    def _deal_api_db_data(self, sql, extra):
        response = extra.get('response')
        if response:
            try:
                result = response.json()
                data = {}
                sql_type, stack = TypeInference().do()
                if self.with_debug:
                    sql = result.get('sql', '')
                    data['curl'] = curlify.to_curl(response.request, compressed=True)
                    data['sql'] = sql
                    data['stack'] = stack
                data['type'] = sql_type
                return data
            except:
                logger.error(f'处理api数据集的SQL计时错误: {traceback.format_exc()}')
        return {}

    # def _deal_mysql_db_data(self, sql, extra):
    #     try:
    #         data = {}
    #         sql_type, stack = TypeInference().do()
    #         if self.with_debug:
    #             data['sql'] = sql
    #             data['stack'] = stack
    #         data['type'] = sql_type
    #         return data
    #     except:
    #         logger.error(f'处理mysql数据集的SQL计时错误: {traceback.format_exc()}')
    #     return {}

    def _deal_common_db_data(self, sql, extra):
        try:
            data = {}
            sql_type, stack = TypeInference().do()
            if self.with_debug:
                data['sql'] = sql
                data['stack'] = stack
            data['type'] = sql_type
            return data
        except:
            logger.error(f'处理数据集的SQL计时错误: {traceback.format_exc()}')
        return {}
