#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dmplib.db import mysql_wrapper
from dmplib import config
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
from loguru import logger


def load_register_dynamic_config_keys():
    """
    获取注册的动态配置key
    """
    # keys = config.get("App.dynamics_config_keys") or []
    keys = 'ShuXin.is_new_subtotal_col,ShuXin.is_open_indicator_detail,App.is_open_authority_control,IngratePlatform.mdc_mip_enable_auth,App.application_snapshot_status,IngratePlatform.white_tenants,Rundeck.java_plugin_tenant'
    if keys:
        if isinstance(keys, list):
            return keys
        else:
            return keys.split(',')
    return []


class DynamicConfig:
    __instance = None

    def __init__(self):
        self.cache_prefix = "dynamic_config:"
        self.cache_key = "dynamic_config"
        self.cache = RedisCache(self.cache_prefix)
        self.dynamic_config_key_list = load_register_dynamic_config_keys() or []

    @staticmethod
    def get_instance():
        if DynamicConfig.__instance:
            return DynamicConfig.__instance
        DynamicConfig.__instance = DynamicConfig()
        return DynamicConfig.__instance

    def get(self, key, default=None):
        """
        获取配置
        :param str key: 格式 [section].[key] 如：app.name
        :param Any default: 默认值
        :return:
        """
        try:
            if key in self.dynamic_config_key_list:
                return self.get_by_cache(key, default)
            else:
                return config.get(key, default)
        except Exception as e:
            logger.error(f"get {key} config error: {e}")
            return default

    def get_by_cache(self, name, default):
        value = self.cache.hget(self.cache_key, name)
        if not value:
            sql = "select `config_value` from `dap_bi_dynamics_config` where `config_key` = %(config_key)s limit 1"
            value = mysql_wrapper.get_db().query_scalar(sql, {"config_key": name})
            if not value:
                value = config.get(name, default)
            self.cache.hset(self.cache_key, name, value)
        return value

    def update_value(self, name, value):
        if not name or not value:
            raise UserError(message="config_key和config_value不能为空")
        if name in self.dynamic_config_key_list:
            try:
                with mysql_wrapper.get_db() as db:
                    db.exec_sql("""delete from `dap_bi_dynamics_config` where `config_key` = '{key}'""", {"key": name})
                    db.exec_sql("insert into `dap_bi_dynamics_config`(`config_key`, `config_value`, `modified_on`) VALUES ('{key}','{value}',now());", {"key": name, "value": value})
                self.cache.hset(self.cache_key, name, value)
            except Exception as e:
                logger.error(f"get {name} config error: {e}")
                raise UserError(message='参数变更失败：%s' % str(e))
            return self.get_by_cache(name, None)
        else:
            raise UserError(message='%s请把参数注册成动态参数' % name)

    def get_all(self):
        return self.cache.hgetall(self.cache_key)

    def delete_cache(self):
        self.cache.delete(self.cache_key)

def get(name, default=None):
    """
    获取配置
    :param str key: 格式 [section].[key] 如：app.name
    :param Any default: 默认值
    :return:
    """
    return DynamicConfig.get_instance().get(name, default)


def update_value(name, value):
    return DynamicConfig.get_instance().update_value(name, value)


def get_all():
    """
    获取所有动态配置
    """
    return DynamicConfig.get_instance().get_all()
