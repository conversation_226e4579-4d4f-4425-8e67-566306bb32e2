#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    Author:<EMAIL>
    Created: 2018年02月02日
"""
import logging
from dmplib.hug import debugger

Debug = debugger.Debug


def log_setting(request, response, rules):
    '''
    通用日志埋点
    :param self:
    :param input:
    :param output:
    :param rules:
    :return:
    '''

    log_data = {}

    if rules.get('request'):
        for prop in rules.get('request'):
            log_data[prop] = request.get(prop)

    if rules.get('response'):
        for prop in rules.get('response'):
            log_data[prop] = response.get(prop)

    logging.log(60, log_data)

    return log_data
