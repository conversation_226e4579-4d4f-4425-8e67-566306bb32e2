#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/9/25.
"""
from dmplib.utils import strings


def generate_new_col_name(dataset_id, col_name, table_name=None):
    """
    生成新的字段名（中文会生成以首字母的拼音）
    :param col_name:
    :return:
    """
    if table_name:
        return "{}_{}".format(
            strings.get_first_pinyin_hanzi(col_name), strings.fletcher32(table_name + dataset_id + ":" + col_name)
        )
    else:
        return "{}_{}".format(strings.get_first_pinyin_hanzi(col_name), strings.fletcher32(dataset_id + ":" + col_name))
