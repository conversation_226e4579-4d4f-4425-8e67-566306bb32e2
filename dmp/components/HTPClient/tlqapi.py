from ctypes import *
import os
import platform

osPlat=platform.system()
TLQHomeDir = os.getenv("HTPCLIHOMEDIR") or os.path.dirname(os.path.abspath(__file__))
if TLQHomeDir == None:
    exception = Exception("Cannot Find HTPCLIHOMEDIR")
    raise exception
if(osPlat=='Windows'):
    from msilib import Win64
    os.environ['path']+=';'+TLQHomeDir+'\\bin'
    TLQPIPATH=TLQHomeDir+'\\bin\\libhtpclient.dll'
    TLQCharCode='gbk'
else:
    TLQPIPATH = TLQHomeDir+'/lib/libhtpclient.so'
    TLQCharCode='utf-8'
    
TLQ = cdll.LoadLibrary(TLQPIPATH)
# 宏定义
TLQ_ERR_STR_LEN = 1024 * 2
TL_SYS= 0
TL_SYS_ERR   =TL_SYS+ 1	   # HTP系统级别错误 
TL_SYS_FILE_CREATE   =TL_SYS+ 2	   # 文件创建出错 
TL_SYS_FILE_DELETE   =TL_SYS+ 3	   # 文件删除出错 
TL_SYS_FILE_OPEN   =TL_SYS+ 4   # 文件打开出错 
TL_SYS_FILE_CLOSE   =TL_SYS+ 5   # 关闭文件出错 
TL_SYS_FILE_READ   =TL_SYS+ 6   # 读文件出错 
TL_SYS_FILE_WRITE   =TL_SYS+ 7   # 写文件出错 
TL_SYS_FILE_SEEK   =TL_SYS+ 8   # 移动文件指针出错 
TL_SYS_FILE_NOTEXIST   =TL_SYS+ 9	   # 文件不存在 
TL_SYS_FILE_EXIST   =TL_SYS+ 10	   # 文件已经存在 
TL_SYS_DIR_NOTEXIST   =TL_SYS+ 11	   # 文件路径不存在 
TL_SYS_DIR_EXIST   =TL_SYS+ 12   # 文件路径已经存在 
TL_SYS_FILE_STAT   =TL_SYS+ 13   # 获取文件状态失败 
TL_SYS_FILE_GETLOCK   =TL_SYS+ 14	   # 获取文件锁失败 
TL_SYS_MUTEX_LOCK   =TL_SYS+ 15	   # 多线程互斥加锁失败 
TL_SYS_MUTEX_UNLOCK   =TL_SYS+ 16	   # 多线程互斥解锁失败 
TL_SYS_COND_DESTROY   =TL_SYS+ 17	   # 线程条件变量销毁失败 
TL_SYS_THREAD_SIGMASK   =TL_SYS+ 18   # 线程掩码设置失败 
TL_SYS_THREAD_CREATE   =TL_SYS+ 19	   # 创建线程失败 
TL_SYS_THREAD_DESTROY   =TL_SYS+ 20   # 线程释放失败 
TL_SYS_MALLOC   =TL_SYS+ 21   # malloc函数失败 
TL_SYS_SOCKET_CREATE   =TL_SYS+ 22	   # 创建Socket失败 
TL_SYS_SOCKET_INIT   =TL_SYS+ 23	   # 初始化Socket失败 
TL_SYS_SOCKET_CONNECT   =TL_SYS+ 24   # Socket连接过程失败 
TL_SYS_SOCKET_BIND   =TL_SYS+ 25	   # 绑定Socket失败 
TL_SYS_SOCKET_ACCEPT   =TL_SYS+ 26	   # Socket接受失败 
TL_SYS_SOCKET_LISTEN   =TL_SYS+ 27	   # Socket端口侦听失败 
TL_SYS_SOCKET_SEND   =TL_SYS+ 28	   # Socket发送失败 
TL_SYS_SOCKET_RECV   =TL_SYS+ 29	   # Socket接收失败 
TL_SYS_SOCKET_SELECT   =TL_SYS+ 30	   # Socket检查链路 
TL_SYS_SOCKET_CLOSE   =TL_SYS+ 31	   # 关闭Socket失败 
TL_SYS_SOCKET_CLEAR   =TL_SYS+ 32	   # Socket注销失败 
TL_SYS_SOCKET_SETOPT   =TL_SYS+ 33	   # 设置Socket相关项失败 
TL_SYS_SOCKET_GETOPT   =TL_SYS+ 34	   # 获取Socket相关项失败 
TL_SYS_SOCKET_SETASYNC   =TL_SYS+ 35   # 设置Socket异步失败 
TL_SYS_SOCKET_SENDTIMEOUT   =TL_SYS+ 36	   # Socket发送超时失败 
TL_SYS_SOCKET_RECVTIMEOUT   =TL_SYS+ 37	   # Socket接收超时失败 
TL_SYS_SOCKET_CONNTIMEOUT   =TL_SYS+ 38	   # Socket连接超时失败 
TL_SYS_END   = 999	   #sys error最大值
# 应用级别错误 1000-1999
TL_ERR_UNKOUNM   =TL_SYS_END + 1	   # 未知错误 
TL_ERR_PARA_INVALID   =TL_SYS_END + 2   # 参数无效 
TL_ERR_MSG_INVALID   =TL_SYS_END + 3   # 消息无效 
TL_ERR_CHKSUM   =TL_SYS_END + 4   # 校验和出错 
TL_ERR_CHKENV   =TL_SYS_END + 5   # 检查环境变量出错 
TL_ERR_CHKHASH   =TL_SYS_END + 6	   # 检查HASH值不正确 
TL_ERR_CHK_STATE   =TL_SYS_END + 7	   # 检查运行状态不正确 
TL_ERR_SET_CONFIG_MODE   =TL_SYS_END + 8	   # 设置客户端配置模式错误 
TL_ERR_NOT_REGISTER   =TL_SYS_END + 9   # 实例未注册 
TL_ERR_STATUS_CODE   =TL_SYS_END + 10   # 和工作节点或管理节点交互协议响应的状态码有错误0表示没有错误 
TL_ERR_FUN_CALL   =TL_SYS_END + 11	   # 内部函数回调错误 
TL_ERR_BROKER_NOT_FOUND   =TL_SYS_END + 12	   # 无可用工作节点 
TL_ERR_EMPTY_LIST   =TL_SYS_END + 13   # 空链表 
TL_ERR_PACK_MESSAGE   =TL_SYS_END + 14   # 协议消息序列化失败 
TL_ERR_UNPACK_MESSAGE   =TL_SYS_END + 15	   # 协议消息反序列化失败 
TL_ERR_INVALID_TOPICNAME   =TL_SYS_END + 16	   # 主题名或队列名无效 
TL_ERR_INVALID_MSGLEN   =TL_SYS_END + 17	   # 消息长度无效 
TL_ERR_INVALID_PRIOR   =TL_SYS_END + 18   # 优先级无效 
TL_ERR_INVALID_EXPIRE   =TL_SYS_END + 19	   # 生命周期无效 
TL_ERR_INVALID_MSGTYPE   =TL_SYS_END + 20	   # 消息类型错误 
TL_ERR_INVALID_MSGATTR   =TL_SYS_END + 21	   # 消息关联属性无效 
TL_ERR_INVALID_OPTION   =TL_SYS_END + 22	   # 选项无效 
TL_ERR_INVALID_CLIENTID   =TL_SYS_END + 23	   # 客户端ID无效 
TL_ERR_INVALID_CONNID   =TL_SYS_END + 24	   # 连接ID无效 
TL_ERR_INVALID_MSGID   =TL_SYS_END + 25   # 消息ID无效 
TL_ERR_INVALID_MSGDATA   =TL_SYS_END + 26	   # 消息数据无效 
TL_ERR_INVALID_INSTANCE   =TL_SYS_END + 27	   # 找不到的实例 
TL_ERR_INVALID_INSTANCETYPE   =TL_SYS_END + 28 # 实例类型不正确 
TL_ERR_DOMAIN_NOT_FOUND   =TL_SYS_END + 29	   # 域未找到没 
TL_ERR_TOPIC_NOT_EXIST   =TL_SYS_END + 30	   # 主题不存在 
TL_ERR_MESSAGE_NOT_FOUND   =TL_SYS_END + 31	   # 消息未找到 
TL_ERR_END   = 1999	   #api error最大值
# 客户端与工作节点返回错误 2000-2999
TL_ERR_CB_FAIL   =TL_ERR_END + 1 # 操作失败 
TL_ERR_CB_NO_ENOUGH_MEMORY   =TL_ERR_END + 2	 # 内存空间不足 
TL_ERR_CB_REQUEST_TIMEOUT   =TL_ERR_END + 3 # 请求超时 
TL_ERR_CB_OUTOF_DISK_SPACE   =TL_ERR_END + 4	 # 磁盘空间不足 
TL_ERR_CB_INVALID_PARAMETER   =TL_ERR_END + 5	 # 包含无效参数 
TL_ERR_CB_TOPIC_NOT_EXIST   =TL_ERR_END + 6 # 主题不存在 
TL_ERR_CB_TOPIC_ALREADY_EXIST   =TL_ERR_END + 7	 # 主题已经存在 
TL_ERR_CB_DOMAIN_NOT_EXIST   =TL_ERR_END + 8	 # 域名不存在      
TL_ERR_CB_DOMAIN_ALREADY_EXIST   =TL_ERR_END + 9 # 域名已存在 
TL_ERR_CB_DOMAIN_COUNT_EXCEED_LICENSE   =TL_ERR_END + 10 # 域名数量超过license最大值（创建主题时超过最大数时可返回该错误码）
TL_ERR_CB_SCVNAME_NOT_EXIST   =TL_ERR_END + 11	 # 服务名不存在 
TL_ERR_CB_SVCNAME_ADD_ERROR   =TL_ERR_END + 12	 # 服务名添加失败 
TL_ERR_CB_SVCNAME_HAS_ADVERTISED   =TL_ERR_END + 13 # 服务名已发布 
TL_ERR_CB_SVCNAME_NOT_ADVERTISE   =TL_ERR_END + 14 # 服务名未发布 
TL_ERR_CB_CONSUME_HAS_REGISTER   =TL_ERR_END + 15 # 消费者已经注册（consumerid相同，域名、主题不同，不会替换原先注册的域名、主题等信息）
TL_ERR_CB_CONSUMER_NOT_REGISTER   =TL_ERR_END + 16 # 消费者未注册，所有拉取请求都会丢弃，不能消费到消息（需要重新注册，才可以正常消费） 
TL_ERR_CB_PRODUCER_HAS_REGISTER   =TL_ERR_END + 17 # 生产者已经注册（producerid相同，域名、主题不同，不会替换原先注册的域名、主题等信息）
TL_ERR_CB_PRODUCER_NOT_REGISTER   =TL_ERR_END + 18 # 生产者未注册，所有的发送消息请求都会丢弃（需要重新注册，才可以正常发送消息）
TL_ERR_CB_COMMITLOG_ERROR   =TL_ERR_END + 19	 # 消息存盘时发生错误（消息没有正确存盘，消息可能丢失，生产者需要重发消息）
TL_ERR_CB_MESSAGE_NOT_FOUND   =TL_ERR_END + 20	 # 工作节点有此主题，但是当前没有消息，一般用于消费消息时使用 
TL_ERR_CB_MSG_DELETE   =TL_ERR_END + 21	 # 消息被删除，拉取消息时指定的offset小于主题最小位移 
TL_ERR_CB_MSG_OFFSET_OVERFLOW   =TL_ERR_END + 22 #  拉取消息时指定offset大于主题最大位移 
TL_ERR_CB_RESOURCE_BUSY   =TL_ERR_END + 23 # 资源繁忙，需要重试（比如，发送大文件消息时，需要处理此错） 
TL_ERR_CB_CONN_REG_MULTIPLE   =TL_ERR_END + 24	 # 连接重复注册 
TL_ERR_CB_CONN_REG_FORBID   =TL_ERR_END + 25	 # 连接被禁止注册 
TL_ERR_CB_SESSION_NOT_REGISTER   =TL_ERR_END + 26 # 连接未注册 
TL_ERR_CB_CLIENT_COUNT_EXCEED_LICENSE   =TL_ERR_END + 27 # 客户端数量超过license限制 
TL_ERR_CB_RAFT_NOT_LEADER   =TL_ERR_END + 28	 # 消息请求节点并非Leader节点 
TL_ERR_CB_RAFT_LEADERSHIP_LOST   =TL_ERR_END + 29 # raft leader节点状态切换     
TL_ERR_CB_RAFT_BROKER_STOP   =TL_ERR_END + 30	 # raft 节点停止 
TL_ERR_CB_PUSH_MSG_RSP_NO_MSG   =TL_ERR_END + 31 # 推送服务响应-没消息 
TL_ERR_CB_PUSH_MSG_RSP_HAS_MSG   =TL_ERR_END + 32 # 推送服务响应-有消息 
TL_ERR_CB_PUSH_MSG_RSP_TIMEOUT   =TL_ERR_END + 33 # 推送服务响应-任务超时 
TL_ERR_CB_GROUP_IN_CONSUMING   =TL_ERR_END + 34	 # 消费者组中有消费者正在消费 
TL_ERR_CB_CONSUMEQUEUE_INFO_NOT_EXIST   =TL_ERR_END + 35 # 消费索引信息不存在 
TL_ERR_CB_ACCOUNT_ALREADY_EXIST   =TL_ERR_END + 36 # 账户已存在 
TL_ERR_CB_ACCOUNT_NOT_EXIST   =TL_ERR_END + 37	 # 账户不存在 
TL_ERR_CB_WADDR_ALREADY_INCLUDED   =TL_ERR_END + 38 # 地地址已被添加到白名单中 
TL_ERR_CB_WADDR_NOT_EXIST   =TL_ERR_END + 39	 # 地址未被添加到在白名单中 
TL_ERR_CB_AUTH_FAILED   =TL_ERR_END + 40 # 验证失败 
TL_ERR_CB_REQUESTOR_HAS_REGISTER   =TL_ERR_END + 41 #请求者已经注册（请求应答） 
TL_ERR_CB_REQUESTOR_NOT_REGISTER   =TL_ERR_END + 42 # 请求者未注册（请求应答），需要注册才可以执行接下来的操作 
TL_ERR_CB_REPLIER_HAS_REGISTER   =TL_ERR_END + 43 # 应答者已经注册（请求应答）
TL_ERR_CB_REPLIER_NOT_REGISTER   =TL_ERR_END + 44 # 应答者未注册（请求应答），需要注册才可以执行接下来的操作 
TL_ERR_CB_REQRPL_SERVICE_CLOSE   =TL_ERR_END + 45 #请求应答服务未开启或已关闭 
TL_ERR_CB_REPLIER_ADD_ERROR   =TL_ERR_END + 46	 #应答者添加失败 
TL_ERR_CB_REPLIER_TID_INVALID   =TL_ERR_END + 47 #应答者tid无效tid错误或tid已超时
TL_ERR_CB_REQUEST_FWD_ERROR   =TL_ERR_END + 48	 #请求转发失败请求转发过程中应答者失效
TL_ERR_CB_REQUEST_QUEUE_BUSY   =TL_ERR_END + 49	 # 请求队列已满
TL_ERR_CB_REQUEST_REPLIER_NOT_EXIST   =TL_ERR_END + 50	 # 请求的对应服务没有应答者
TL_ERR_CB_MSG_REACHED_TOPIC_END   =TL_ERR_END + 51 # 主题消息已经消费到末尾
TL_ERR_CB_MSG_WAITACK = TL_ERR_END + 52  # 消费者已经分配消息，还有消息未回ACK
TL_ERR_CB_GROUP_NO_SPACE = TL_ERR_END + 53		      # 消费组空间已满
TL_ERR_CB_END   = 2999	 #工作节点broker error最大值
# 客户端与管理节点返回错误 3000-3999
TL_ERR_CN_UNMATCHED_CONDITIONS   =TL_ERR_CB_END + 1	   # 管理节点匹配失败 
TL_ERR_CN_NO_ENOUGH_MEMORY   =TL_ERR_CB_END + 2   # 内存空间不足 
TL_ERR_CN_REQUEST_TIMEOUT   =TL_ERR_CB_END + 3   # 请求超时 
TL_ERR_CN_ENCODE_ERR   =TL_ERR_CB_END + 4	   # 序列化失败 
TL_ERR_CN_DECODE_ERR   =TL_ERR_CB_END + 5	   # 反序列化失败 
TL_ERR_CN_TOPIC_NOT_EXIST   =TL_ERR_CB_END + 6   # 主题不存在 
TL_ERR_CN_QUEUE_NOT_EXIST   =TL_ERR_CB_END + 7   #  队列不存在 
TL_ERR_CN_MESSAGE_SIZE_INCORRECT   =TL_ERR_CB_END + 8  # 消息大小不正确 
TL_ERR_CN_INVALID_PARAMETER   =TL_ERR_CB_END + 9	   # 管理节点检查参数错误 
TL_ERR_CN_QUERY_ERROR   =TL_ERR_CB_END + 10	   # 管理节点查询失败 
TL_ERR_CN_UNKNOWN_ERR   =TL_ERR_CB_END + 11	   # 管理节点未知的错误 
TL_ERR_CN_BUILD_RELEVANCY_FAIL   =TL_ERR_CB_END + 12   # 管理节点建立关联表失败 
TL_ERR_CN_CLIENT_NOT_EXIST   =TL_ERR_CB_END + 13	   # 管理节点检查到客户端不存在 
TL_ERR_CN_ADD_TOPIC_FAILED   =TL_ERR_CB_END + 14	   # 管理节点添加主题失败 
TL_ERR_CN_ZONE_NOT_EXIST   =TL_ERR_CB_END + 15   # 管理节点检查到域名不存在 
TL_ERR_CN_SAVE_TO_XML_FAILED   =TL_ERR_CB_END + 16	   # 管理节点保存配置到xml失败 
TL_ERR_CN_RELATED_ZONE_NOT_EXIST   =TL_ERR_CB_END + 17 # 管理节点检查到关联的域名不存在 
TL_ERR_CN_ADD_ZONE_FAILED   =TL_ERR_CB_END + 18   # 管理节点添加域名失败 
TL_ERR_CN_DEL_ZONE_FAILED   =TL_ERR_CB_END + 19   # 管理节点删除域名失败 
TL_ERR_CN_DEL_TOPIC_FAILED   =TL_ERR_CB_END + 20	   # 管理节点删除主题失败 
TL_ERR_CN_TOPIC_MODIFY_FAILED   =TL_ERR_CB_END + 21	   # 管理节点修改主题失败 
TL_ERR_CN_TOPIC_TYPE_INVALID   =TL_ERR_CB_END + 22	   # 管理节点检查到主题类型无效 
TL_ERR_CN_TOPIC_INACCESSIBLE   =TL_ERR_CB_END + 23	   # 主题不可访问 
TL_ERR_CN_SERVICE_INACCESSIBLE   =TL_ERR_CB_END + 24   # 服务不能访问 
TL_ERR_CN_TOPIC_ALREADY_EXIST   =TL_ERR_CB_END + 25	   # 主题已经存在 
TL_ERR_CN_PARTIALLY_SUCCESS   =TL_ERR_CB_END + 26	   # 操作部分成功 
TL_ERR_CN_ACCOUNT_ROLE_INVALID   =TL_ERR_CB_END + 27   # 账户角色无效 
TL_ERR_CN_ADD_SERVICE_FAILED   =TL_ERR_CB_END + 28	   # 添加服务失败 
TL_ERR_CN_SERVICE_NOT_EXIST   =TL_ERR_CB_END + 29	   # 服务不存在 
TL_ERR_CN_CLUSTER_NOT_EXIST   =TL_ERR_CB_END + 30	   # 账号不存在 
TL_ERR_CN_END   = 3999
TL_ERR_BN_USER_PASSWD_ERR = TL_ERR_CN_END + 1		                # 用户密码错误 
TL_ERR_BN_RAFT_URL_ID_MISMATCH = TL_ERR_CN_END + 2              # 当brokerid与raft url list不一致时，返回此错误码 
TL_ERR_BN_BROKER_COUNT_EXCEED_LICENSE = TL_ERR_CN_END + 3		    # broker向namesvr注册时，可能返回该错误码 
TL_ERR_BN_DOMAIN_NUM_EXCEEDED = TL_ERR_CN_END + 4               # broker域数量超过限制 
TL_ERR_BN_TOPIC_NUM_EXCEEDED = TL_ERR_CN_END + 5                # broker主题数量超过限制 
TL_ERR_BN_END = 5999
#*****TLQ ERRORCODE END*****
#
TLQ_MSG_TYPE_BUFF = 0
#TLQSockType
TLQ_SOCK_DEFAULT = 0
TLQ_SOCK_REP= TLQ_SOCK_DEFAULT + 0
TLQ_SOCK_REQ= TLQ_SOCK_DEFAULT + 1
TLQ_SOCK_PULL= TLQ_SOCK_DEFAULT + 2
TLQ_SOCK_PUSH= TLQ_SOCK_DEFAULT + 3
TLQ_SOCK_PUB= TLQ_SOCK_DEFAULT + 4
TLQ_SOCK_SUB= TLQ_SOCK_DEFAULT + 5
TLQ_SOCK_PAIR= TLQ_SOCK_DEFAULT + 6
#TLQConfigOption
TLQ_CONFIG_MESSAGE_MAX_SIZE= 0
TLQ_CONFIG_MESSAGE_PUT_TIME_OUT= 1
TLQ_CONFIG_MESSAGE_PULL_TIME_OUT= 2
TLQ_CONFIG_MESSAGE_ROLL_TIMES= 3
TLQ_CONFIG_MESSAGE_SEND_CACHE_MAX= 4
TLQ_CONFIG_MESSAGE_RECV_CACHE_MAX= 5
TLQ_CONFIG_MESSAGE_WAIT_DELETE_CACHE_MAX= 6
TLQ_CONFIG_MESSAGE_ATTR_MAX_COUNT= 7
TLQ_CONFIG_MESSAGE_ATTR_MAX_LENGTH= 8
TLQ_CONFIG_GROUP_MESSAGE_MAX_ELEMENT_SIZE= 9
TLQ_CONFIG_EVENT_MESSAGE_MAX_ELEMENT_SIZE= 10
TLQ_CONFIG_BUSINESS_MESSAGE_MAX_ELEMENT_SIZE = 11
TLQ_CONFIG_FILE_MESSAGE_MAX_SIZE= 12
TLQ_CONFIG_BROKER_RECONNET_COUNT= 13
TLQ_CONFIG_MANAGER_RECONNET_COUNT= 14
TLQ_CONFIG_MANAGER_UPDATE_BROKER_TIME = 15
TLQ_CONFIG_LOG_LEVEL = 16
TLQ_CONFIG_MESSAGE_IS_PTOP= 17
TLQ_CONFIG_MESSAGE_CONFIG_MODE= 18
TLQ_CONFIG_MESSAGE_MSG_TYPE= 19
TLQ_CONFIG_MESSAGE_IS_SYNC_SEND= 20
TLQ_CONFIG_MESSAGE_MSG_ARRANGE_MODE= 21
TLQ_CONFIG_MESSAGE_EXPIRY= 22
TLQ_CONFIG_MESSAGE_DELAY_TIME= 23
TLQ_CONFIG_MESSAGE_ROLL_TIME= 24
TLQ_CONFIG_MESSAGE_TRANSFER_FLAG= 25
TLQ_CONFIG_MESSAGE_PRIORITY= 26
TLQ_CONFIG_MESSAGE_PERSISTENCE= 27
TLQ_CONFIG_MESSAGE_ATTR_COUNT= 28
TLQ_CONFIG_MESSAGE_ATTR_DATA_SIZE= 29
TLQ_CONFIG_MESSAGE_SSL_PROTOCOL_VERSION = 30
TLQ_CONFIG_MESSAGE_SSL_NEGOFLAG= 31
TLQ_CONFIG_MESSAGE_SSL_SESSION_TIME_OUT = 32


class TLQError(Structure):
    _fields_ = [
        ("tlq_errno", c_int),
        ("sys_errno", c_int),
        ("errstr", c_char * (TLQ_ERR_STR_LEN+1)),
    ]

class TLQConfig(Structure):
    _fields_ = [("pData", c_void_p),
                ]
class TLQContext(Structure):
    _fields_ = [("pData", c_void_p)]

class TLQManager(Structure):
    _fields_ = [("pData", c_void_p)]
# Producer
class TLQLightProducer (Structure):
    _fields_ = [("pData", c_void_p)]
# Consumer
class TLQLightPullConsumer(Structure):
    _fields_ = [("pData", c_void_p)]
# Message
class TLQMessage(Structure):
    _fields_ = [("pData", c_void_p)]
    
class TLQBatchMessage(Structure):
    _fields_ = [("pData", c_void_p)]

class TLQMultiMessage(Structure):
    _fields_ = [("domain", c_char*65),
                ("topic", c_char*129),
                ("group", c_char*65),
                ("brokerId", c_longlong),
                ("minOffset", c_longlong),
                ("maxOffset", c_longlong),
                ("messageCount", c_int),
                ("messageArray", POINTER(TLQMessage)),
                ]
# TLQConfig.h 
def TLQConfigInit(outConfig: TLQConfig, err: TLQError):
    return TLQ.TLQConfigInit(byref(outConfig), byref(err))

def TLQConfigSetManager(outConfig: TLQConfig, addr: str, err: TLQError):
    return TLQ.TLQConfigSetManager(outConfig, addr.encode(), byref(err))

def TLQConfigSetClientID(outConfig: TLQConfig, clientid: str, err: TLQError):
    return TLQ.TLQConfigSetClientID(outConfig, clientid.encode(), byref(err))

def TLQConfigSetUsernamePassword(config:TLQConfig,username:str,password:str,tlqErr: TLQError):
    return TLQ.TLQConfigSetUsernamePassword(config,username.encode(),password.encode(),byref(tlqErr))

def TLQConfigDestroy(outConfig: TLQConfig, err: TLQError):
    return TLQ.TLQConfigDestroy( byref(outConfig), byref(err))

def TLQConfigSetLogLevel(outConfig: TLQConfig, level: int, err: TLQError):
    return TLQ.TLQConfigSetLogLevel(outConfig, level, byref(err))

# TLQMessage
def TLQMessageInit(msg: TLQMessage, err: TLQError):
    return TLQ.TLQMessageInit(byref(msg), byref(err))

def TLQMessageSetData(msg: TLQMessage, inData: c_void_p, inDataSize: int, err: TLQError):
    return TLQ.TLQMessageSetData(msg, pointer(inData), inDataSize, byref(err))

def TLQMessageAck(msg: TLQMessage, err: TLQError):
    return TLQ.TLQMessageAck(msg, byref(err))

def TLQMessageGetData(msg: TLQMessage, outData:c_void_p, outLen: c_int, err: TLQError):
    return TLQ.TLQMessageGetData(msg, pointer(outData), pointer(outLen), byref(err))

def TLQMessageDestroy(msg: TLQMessage, err: TLQError):
    return TLQ.TLQMessageDestroy(byref(msg), byref(err))

def TLQMessageSetTopic(msg:TLQMessage, topic:str, err: TLQError):
    return TLQ.TLQMessageSetTopic(msg,topic.encode(),byref(err))

def TLQMessageAttrSetString(msg: TLQMessage, attrName: str, val: str, err: TLQError):
    return TLQ.TLQMessageAttrSetString(msg, attrName.encode(), val.encode(), byref(err))

def TLQMessageAttrGetString(msg: TLQMessage, attrName: str, err: TLQError):
    TLQ.TLQMessageAttrGetString.restype = c_char_p
    return TLQ.TLQMessageAttrGetString(msg, attrName.encode(), byref(err))

def TLQMessageAttrSetStringOriginal(msg:TLQMessage,val:str,tlqErr:TLQError):
    return TLQ.TLQMessageAttrSetStringOriginal(msg,val.encode(),byref(tlqErr))

def TLQMessageAttrGetStringOriginal(msg: TLQMessage,attr:c_void_p,len:c_int,tlqErr:TLQError):
    return TLQ.TLQMessageAttrGetStringOriginal(msg,pointer(attr),byref(len),byref(tlqErr))

# TLQBatchMessage
def TLQBatchMessageInit(batchMessage: TLQBatchMessage, err: TLQError):
    return TLQ.TLQBatchMessageInit(byref(batchMessage), byref(err))

def TLQBatchMessageAdd(batchMessage: TLQBatchMessage, err: TLQError,message: TLQMessage, *messages):
    return TLQ.TLQBatchMessageAdd(batchMessage, byref(err),byref(message), *[byref(msg) for msg in messages], None)

def TLQBatchMessageDestroy(batchMessage: TLQBatchMessage, err: TLQError):
    return TLQ.TLQBatchMessageDestroy(byref(batchMessage), byref(err))

# TLQLightProducer
def TLQLightProducerStart(producer: TLQLightProducer, err: TLQError):
    return TLQ.TLQLightProducerStart(producer, byref(err))

def TLQLightProducerInit(config: TLQConfig,  outProducer: TLQLightProducer, err: TLQError):
    return TLQ.TLQLightProducerInit(config,  byref(outProducer), byref(err))

def TLQLightProducerSetDomain(producer: TLQLightProducer, domain: str, err: TLQError):
    return TLQ.TLQLightProducerSetDomain(producer, c_char_p(domain.encode(TLQCharCode)), byref(err))

def TLQLightProducerSetAutoTopic(producer: TLQLightProducer,flag:int,tlqErr: TLQError):
    return TLQ.TLQLightProducerSetAutoTopic(producer,flag,byref(tlqErr))

def TLQLightProducerSetClusterName(producer: TLQLightProducer,clusterName:str,addr:str,err: TLQError):
    return TLQ.TLQLightProducerSetClusterName(producer, c_char_p(clusterName.encode(TLQCharCode)), c_char_p(addr.encode(TLQCharCode)),byref(err))

def TLQLightProducerSetTopic(producer: TLQLightProducer, topic: str, err: TLQError):
    return TLQ.TLQLightProducerSetTopic(producer, c_char_p(topic.encode(TLQCharCode)), byref(err))

def TLQLightProducerSetGroup(producer: TLQLightProducer, group: str, err: TLQError):
    return TLQ.TLQLightProducerSetGroup(producer, c_char_p(group.encode(TLQCharCode)), byref(err))

def TLQLightPutMessage(producer: TLQLightProducer, message: TLQMessage, err: TLQError):
    return TLQ.TLQLightPutMessage(producer, message, byref(err))

def TLQLightPutBatchMessage(producer: TLQLightProducer, batchMessage: TLQBatchMessage, err: TLQError):
    return TLQ.TLQLightPutBatchMessage(producer, batchMessage, byref(err))

def TLQLightProducerDestroy(producer: TLQLightProducer, err: TLQError):
    return TLQ.TLQLightProducerDestroy(byref(producer), byref(err))

# TLQLightPullConsumer
def TLQLightPullConsumerInit(config: TLQConfig, outConsumer: TLQLightPullConsumer, err: TLQError):
    return TLQ.TLQLightPullConsumerInit(config, byref(outConsumer), byref(err))

def TLQLightPullConsumerSetTopic(consumer: TLQLightPullConsumer, topic: str, err: TLQError):
    return TLQ.TLQLightPullConsumerSetTopic(consumer, c_char_p(topic.encode(TLQCharCode)), byref(err))

def TLQLightPullConsumerSetDomain(consumer: TLQLightPullConsumer, domain: str, err: TLQError):
    return TLQ.TLQLightPullConsumerSetDomain(consumer, c_char_p(domain.encode(TLQCharCode)), byref(err))

def TLQLightPullConsumerSetGroup(consumer: TLQLightPullConsumer, group: str, err: TLQError):
    return TLQ.TLQLightPullConsumerSetGroup(consumer, c_char_p(group.encode(TLQCharCode)), byref(err))

def TLQLightPullConsumerSetClusterName(consumer: TLQLightPullConsumer, clusterName:str,addr:str,err: TLQError):
    return TLQ.TLQLightPullConsumerSetClusterName(consumer, c_char_p(clusterName.encode(TLQCharCode)), c_char_p(addr.encode(TLQCharCode)),byref(err))

def TLQLightPullConsumerSetConsumeFlag(consumer:TLQLightPullConsumer, consumeFlag:int, err: TLQError):
    return TLQ.TLQLightPullConsumerSetConsumeFlag(consumer, consumeFlag, byref(err))

def TLQLightPullConsumerSetTimerInterval(consumer:TLQLightPullConsumer, timerInterval:int, err: TLQError):
    return TLQ.TLQLightPullConsumerSetTimerInterval(consumer, timerInterval, byref(err))

def TLQLightPullConsumerSetAckMode(consumer: TLQLightPullConsumer, manualAck: int, err: TLQError):
    return TLQ.TLQLightPullConsumerSetAckMode(consumer, manualAck, byref(err))

def TLQLightPullConsumerStart(consumer: TLQLightPullConsumer, err: TLQError):
    return TLQ.TLQLightPullConsumerStart(consumer, byref(err))

def TLQLightPullMessage(consumer: TLQLightPullConsumer, message: TLQMessage, err: TLQError):
    return TLQ.TLQLightPullMessage(consumer, byref(message), byref(err))

def TLQLightPullMultiMessage(consumer: TLQLightPullConsumer, pullNum: int, multiMessage: TLQMultiMessage, err: TLQError):
    return TLQ.TLQLightPullMultiMessage(consumer, pullNum, byref(multiMessage), byref(err))

def TLQLightPullMultiRollbackMessage(consumer: TLQLightPullConsumer, offset: int, pullMaxNum: int, multiMessage: TLQMultiMessage, err: TLQError):
    return TLQ.TLQLightPullMultiRollbackMessage(consumer, c_longlong(offset), c_int(pullMaxNum), byref(multiMessage), byref(err))

def TLQLightPullConsumerAckMessage(consumer: TLQLightPullConsumer, err: TLQError):
    return TLQ.TLQLightPullConsumerAckMessage(consumer, byref(err))

def TLQLightPullConsumerDestroy(consumer: TLQLightPullConsumer, err: TLQError):
    return TLQ.TLQLightPullConsumerDestroy(byref(consumer), byref(err))

# TLQManager
def TLQManagerInit(config: TLQConfig, outManager: TLQManager, err: TLQError):
    return TLQ.TLQManagerInit(config, byref(outManager), byref(err))

def TLQManagerStart(manager: TLQManager, err: TLQError):
    return TLQ.TLQManagerStart(manager, byref(err))

def TLQManagerCreateDomain(manager: TLQManager, domainName: str, err: TLQError):
    return TLQ.TLQManagerCreateDomain(manager, c_char_p(domainName.encode(TLQCharCode)), byref(err))

def TLQManagerSetClusterName(manager: TLQManager,clusterName:str,addr:str,tlqErr: TLQError):
    return TLQ.TLQManagerSetClusterName(manager,c_char_p(clusterName.encode(TLQCharCode)),c_char_p(addr.encode(TLQCharCode)),byref(tlqErr))

def TLQManagerQueryDomain(manager: TLQManager, domainName: str, updateTime: c_int, err: TLQError):
    return TLQ.TLQManagerQueryDomain(manager, c_char_p(domainName.encode(TLQCharCode)), byref(updateTime), byref(err))

def TLQManagerCreateTopic(manager: TLQManager, topicName: str, domainName: str, topicType: int, err: TLQError):
    return TLQ.TLQManagerCreateTopic(manager, c_char_p(topicName.encode(TLQCharCode)), c_char_p(domainName.encode(TLQCharCode)), topicType, byref(err))

def TLQManagerQueryTopic(manager: TLQManager, topicName: str, domainName: str, err: TLQError):
    return TLQ.TLQManagerQueryTopic(manager, c_char_p(topicName.encode(TLQCharCode)), c_char_p(domainName.encode(TLQCharCode)), byref(err))

def TLQManagerDeleteDomain(manager: TLQManager, domainName: str, err: TLQError):
    return TLQ.TLQManagerDeleteDomain(manager, c_char_p(domainName.encode(TLQCharCode)), byref(err))

def TLQManagerDeleteTopic(manager: TLQManager, topicName: str, domainName: str, err: TLQError):
    return TLQ.TLQManagerDeleteTopic(manager, c_char_p(topicName.encode(TLQCharCode)), c_char_p(domainName.encode(TLQCharCode)), byref(err))


def TLQManagerDestroy(manager: TLQManager, err: TLQError):
    return TLQ.TLQManagerDestroy(byref(manager), byref(err))

def TLQMessageGetOffset(msg:TLQMessage , offset:c_longlong,tlqErr:TLQError):
    return TLQ.TLQMessageGetOffset(msg,byref(offset),byref(tlqErr))

def TLQMessageGetID(msg:TLQMessage,outId:c_void_p,outLen:c_int,tlqErr:TLQError):
    return TLQ.TLQMessageGetID(msg,pointer(outId),pointer(outLen),byref(tlqErr))

def TLQMessageSetMsgId(msg:TLQMessage, value:str, length:int, tlqErr:TLQError):
    return TLQ.TLQMessageSetMsgId(msg,c_char_p(value.encode(TLQCharCode)),length,byref(tlqErr))

def TLQMultiMessageDestroy(multiMesssage: TLQMultiMessage, err: TLQError):
    return TLQ.TLQMultiMessageDestroy(byref(multiMesssage), byref(err))

def TLQGetNetStatus(type:int,instance):
    return TLQ.TLQGetNetStatus(type,byref(instance))