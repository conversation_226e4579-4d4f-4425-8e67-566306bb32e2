import os
from dmplib.tests.base_test import BaseTest
from ..mail import _format_email_address, MailContact

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), 'app.unit.config')


class TestMail(BaseTest):
    def test__format_email_address(self):  # pylint: disable=no-self-use
        m1 = MailContact(name='中(', mail='<EMAIL>')
        receiver = _format_email_address(m1)
        assert receiver

        m2 = MailContact(name='中(明）', mail='<EMAIL>')
        receiver = _format_email_address(m2)
        assert receiver
