# pylint: skip-file
import unittest
from components.remove_comment import remove_comment


class TestQueryEncoder(unittest.TestCase):
    def test_sql_remove_comment(self):
        sqltext = """
# 单注释
# 2222单注释
-- 头注释带特殊
/*

符号*/
--（~！@#￥%……&*（）——++\、|'";:,./<>?
select * from a -- for test
where age = ";;" and age = "1231231" and name = '/* ok;;; string */' ;/* sfsdfds */order by age -- "afdsfds/*"
-- sasfsdfds
/*段注释带特殊符号（~！@#￥%……&*（）——++\、|;:'",./<>?）*/
-- 换行带特殊符号（~！@#￥%……&*（）——++\、|;:'",./<>?）
-- 尾带特殊符号（~！@#￥%……&*（）——++\、|;:'",./<>?）

"""
        real_sql = """

select * from a where age = ";;" and age = "1231231" and name = '/* ok string */' order by age

"""
        sql = remove_comment(sqltext)
        self.assertEqual(sql, real_sql)

    def test_sql_remove_comment2(self):
        sqltext = """-- 头注释带特殊符号（~！@#￥%……&*（）——++\、|;:'",./<>?）
select * from /*段注释带特殊符号（~！@#￥%……&*（）——++\、|;:'",./<>?）*/ chart_result_b2e8aff36a6b1582; -- 行末带特殊符号（~！@#￥%……&*（）——++\、|;:'",./<>?）
-- 尾带特殊符号（~！@#￥%……&*（）——++\、|;:'",./<>?）

-- 换行带特殊符号（~！@#￥%……&*（）——++\、|;:'",./<>?）

/*段注释带特殊符号（~！@#￥%……&*（）——++\、|;:'",./<>?）*/"""
        real_sql = """select * from  chart_result_b2e8aff36a6b1582;

"""
        sql = remove_comment(sqltext)
        self.assertEqual(sql, real_sql)

    def test_single_line_comments(self):
        sqltext = """--aaaa"""
        real_sql = ""
        sql = remove_comment(sqltext)
        self.assertEqual(sql, real_sql)
