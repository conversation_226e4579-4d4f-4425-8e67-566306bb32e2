#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file


from tests.base import BaseTest
from download.services.oss_attach_move_service import MoveAttach

import unittest


class TestOssAttachMoveService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='uitest', account='admin')

    def test_move_attach(self):

        MoveAttach('uitest', 'oss-cn-shenzhen.aliyuncs.com', 'dmp-test', 'oss').\
            move_attach()

    def test_assign_move_attach(self):

        MoveAttach('uitest', 'oss-cn-shenzhen.aliyuncs.com', 'dmp-test', 'oss', 'application').\
            move_attach()

    def test_error_move_attach(self):

        MoveAttach('uitest', 'oss-cn-shenzhen.aliyuncs.com', 'dmp-test', 'oss', 'zzzzzzzzzzz').\
            move_attach()

    def test_update_sql(self):
        table = 'message2'
        fields = ['url', 'title']
        ma = MoveAttach('uitest', 'oss-cn-shenzhen.aliyuncs.com', 'dmp-test', 'oss')
        old_oss_url = ma.get_origin_env_host()
        new_oss_url = ma.get_current_env_host()
        ma._update_sql(table, fields, old_oss_url, new_oss_url)

    def test_match_attach(self):
        data_list = [
            {"cover": "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/39fc9ad8-c99e-f8e9-5817-18efd1e59c8a.jpg",
             "background": '{"show":true,"color":"#EBEDF2","image":"","size":"stretch","user_image":""}',
             "id": "39fc9ad8-9a82-ad39-dc75-776c067e7453"},
            {"cover": "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/39e3eb34-0ab9-2e6f-8895-4a04abd3d70f.jpg",
             "background": '{"show":true,"color":"transparent","image":"http://dmp-test.oss-cn-shenzhen.aliyuncs.com/39e3a943-352e-9b89-ecc6-a68af78e1e94.jpg","size":"stretch","user_image":""}',
             "id": "39fe3088-9c09-4bab-b74a-ac214fdb7637"},
            {"cover": "",
             "background": '{"show":true,"color":"RGBA(15,24,47,1)","image":"https://erp-dmp.oss-cn-shenzhen.aliyuncs.com/3a0039fa-8bde-aa19-b39f-8cd56315d716.png","size":"stretch","user_image":""}',
             "id": "3a0033c0-e39d-eb8c-7f1f-c9f97f9ae8d6"},
        ]
        MoveAttach('uitest', 'oss-cn-shenzhen.aliyuncs.com', 'dmp-test', 'oss')._match_attach(data_list,
                                                                                              ["cover", "background"])

    def test_download_origin_attach(self):
        url = 'https://minio-api-dmp-saas-hw.mypaas.com/dmp/3a07fdde-a86c-6957-1e31-4b6f1b494530.jpg'
        MoveAttach('uitest', 'minio-api-dmp-saas-hw.mypaas.com', 'dmp')._download_origin_attach(url)


if __name__ == '__main__':
    unittest.main()
