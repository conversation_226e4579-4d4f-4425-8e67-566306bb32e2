#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    <NAME_EMAIL> on 2018/4/13.
"""
from tests.base import BaseTest

import unittest

from download.services import download_oss_service


class TestDownloadOssService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev', account='admin')

    def test_download_oss_file(self):
        url = 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/**************.zip'
        sign_url = download_oss_service.download_oss_file(url)
        self.assertIsNotNone(sign_url)


if __name__ == '__main__':
    unittest.main()
