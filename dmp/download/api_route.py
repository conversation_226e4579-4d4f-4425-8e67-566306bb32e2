#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    API Route
    Created by chenc04 2017/3/16.
"""

import hug

from dmplib.hug import APIWrapper
from download.models import DownloadJobFlowModel, DownloadJobQueryModel, DownloadHistoryQueryModel
from download.services import download_job_service, download_oss_service

api = APIWrapper(__name__)


@api.admin_route.get('/label_detail/export_file')
def export_file(**kwargs):
    """
    导出标签明细
    :param kwargs: id=标签id，name=流程名称，indicators=指标id（逗号分隔），total_amount=标签覆盖数，
                    format=文件类型（txt、csv），is_compressed=是否压缩（1、0），is_mutifile=是否分文件（1、0）
    :return:
    """
    model = DownloadJobFlowModel(**kwargs)
    return True, None, download_job_service.export_label_detail(model)


@api.admin_route.get('/download_file')
def download_file(download_job_id: hug.types.text):
    """
    下载文件
    :param download_job_id: 下载任务id
    :return:
    """
    data = download_job_service.download_file(download_job_id)
    return data['file_path']


@api.admin_route.get('/download_list')
def download_list(**kwargs):
    """
    获取下载任务
    :param kwargs: page=当前页数，page_size=每页显示条数
    :return:

    """
    return True, None, download_job_service.get_download_job(DownloadJobQueryModel(**kwargs))


@api.admin_route.get('/download_history')
def download_history_list(**kwargs):
    """
    获取下载历史记录
    :param kwargs: page=当前页数，page_size=每页显示条数
    :return:
    """
    return (
        True,
        None,
        download_job_service.get_download_history_by_download_id(DownloadHistoryQueryModel(**kwargs)).get_result_dict(),
    )


@api.route.get('/oss')
def download_oss_file(**kwargs):
    """
    下载oss文件
    :param kwargs: url=http://dmp-prod.oss.aliyun.com/file1.xls
    :return:
    """
    return download_oss_service.download_oss_file(kwargs.get("url"))
