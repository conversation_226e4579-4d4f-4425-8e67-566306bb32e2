"""
oauth 集成
"""
from base import repository


def get_oauth_config(platform, app_id):
    return repository.get_data(
        "dap_bi_project_app_oauth", {"app_id": app_id, "platform": platform}, ["corpid", "app_secret"], from_config_db=True
    )

def get_oauth_config_by_app_id(app_id):
    return repository.get_data(
        "dap_bi_project_app_oauth", {"app_id": app_id}, ['platform', "corpid", "app_secret"], from_config_db=True
    )

def get_oauth_mobile_config(config_id):
    return repository.get_data("dap_bi_mobile_subscribe_config", {"id": config_id}, ["corp_id", "corp_secret"])


def get_app(app_id):
    return repository.get_data("dap_bi_project_app", {"id": app_id}, ["name", "default_page_url", "code"], from_config_db=True)


def get_third_party_app_config(config_id):
    """
    获取新的应用信息
    :param config_id:
    :return:
    """
    sql = """
    select a.app_secret, b.corp_id
    from dap_bi_third_party_app a left join dap_bi_third_party b on a.third_party_id = b.id 
    where a.id = %(config_id)s limit 1
    """
    data_list = repository.get_data_by_sql(sql, {"config_id": config_id})
    return data_list[0] if data_list and isinstance(data_list, list) else {}
