import random

from dmplib.redis import conn as conn_redis

__all__ = [
    'is_account_locked',
    'lock_account',
    'unlock_account',

    'is_ip_locked',
    'lock_ip',
    'inc_ip_login_count',
    'clear_ip_login_count',

    'generate_email_code',
    'check_email_code',
    'inc_email_failed_times',
    'clear_email_failed_times',

    'inc_auth_failed_times',
    'clear_auth_failed_times',
]

### 用户锁定
def lock_key(user_id):
    return f'user:lock:{user_id}'

def is_account_locked(user_id):
    conn = conn_redis()
    rv = conn.get(lock_key(user_id))
    return rv is not None

def lock_account(user_id, expire):
    conn = conn_redis()
    conn.set(lock_key(user_id), value=1, time=expire)

def unlock_account(user_id):
    conn = conn_redis()
    conn.delete(lock_key(user_id))

### ip锁定
IP_LOGIN_COUNT_EXPIRE = 60
IP_LOGIN_LIMIT = 10
IP_LOCK_EXPIRE = 60 * 60

def ip_lock_key(ip):
    return f'ip:lock:{ip}'

def ip_login_count_key(ip):
    return f'ip:login_count:{ip}'

def is_ip_locked(ip):
    conn = conn_redis()
    rv = conn.get(ip_lock_key(ip))
    return rv is not None

def lock_ip(ip, expire):
    conn = conn_redis()
    conn.set(ip_lock_key(ip), value=1, time=expire)

def unlock_ip(ip):
    conn = conn_redis()
    conn.delete(ip_lock_key(ip))

def inc_ip_login_count(ip):
    conn = conn_redis()
    count = conn.incr(ip_login_count_key(ip))
    conn.expire(ip_login_count_key(ip), time=IP_LOGIN_COUNT_EXPIRE)
    if count >= IP_LOGIN_LIMIT:
        lock_ip(ip, expire=IP_LOCK_EXPIRE)
    return count

def clear_ip_login_count(ip):
    conn = conn_redis()
    conn.delete(ip_login_count_key(ip))

### 邮箱验证
EMAIL_LOCK_EXPIRE = 60 * 60
EMAIL_FAILED_LIMIT = 10
EMAIL_CODE_EXPIRE = 3 * 60
EMAIL_FAILED_COUNT_EXPIRE = 5 * 60

def email_failed_count_key(user_id):
    return f'email:code_failed_times:{user_id}'

def email_code_key(user_id):
    return f'email:code:{user_id}'

def generate_email_code(user_id):
    conn = conn_redis()
    code = ''.join([random.choice('**********') for _ in range(0, 6)])
    conn.set(email_code_key(user_id), code, time=EMAIL_CODE_EXPIRE)
    return code

def inc_email_failed_times(user_id):
    conn = conn_redis()
    count = conn.incr(email_failed_count_key(user_id))
    conn.expire(email_failed_count_key(user_id), time=EMAIL_FAILED_COUNT_EXPIRE)
    if count >= EMAIL_FAILED_LIMIT:
        lock_account(user_id, expire=EMAIL_LOCK_EXPIRE)

def clear_email_failed_times(user_id):
    conn = conn_redis()
    conn.delete(email_failed_count_key(user_id))

def check_email_code(user_id, code):
    conn = conn_redis()
    val = conn.get(email_code_key(user_id))
    if val and val.decode() == code:
        conn.delete(email_code_key(user_id))
        return True
    return False

### 登录验证
AUTH_FAILED_LIMIT = 10
AUTH_FAILED_COUNT_EXPIRE = 5 * 60
AUTH_LOCK_EXPIRE = 10 * 60

def auth_failed_count_key(user_id):
    return f'auth:failed_times:{user_id}'

def inc_auth_failed_times(user_id):
    conn = conn_redis()
    count = conn.incr(auth_failed_count_key(user_id))
    conn.expire(auth_failed_count_key(user_id), time=AUTH_FAILED_COUNT_EXPIRE)
    if count >= AUTH_FAILED_LIMIT:
        lock_account(user_id, expire=AUTH_LOCK_EXPIRE)

def clear_auth_failed_times(user_id):
    conn = conn_redis()
    conn.delete(auth_failed_count_key(user_id))

