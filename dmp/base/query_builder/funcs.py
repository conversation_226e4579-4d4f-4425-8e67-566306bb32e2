#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/9/18 15:17
# <AUTHOR> caoxl
# @File     : func.py
# pylint: disable=W0622,W0105


class Func:
    """
    函数父类
    """

    def __init__(self, name, *params):
        self._name = name
        self._params = params

    @property
    def name(self):
        return self._name

    @property
    def params(self):
        return self._params


"""
=========================功能函数===========================
"""


class If(Func):
    def __init__(self, *params):
        super().__init__("if", *params)


class IfNull(Func):
    def __init__(self, *params):
        super().__init__("ifnull", *params)


class IsNull(Func):
    def __init__(self, *params):
        super().__init__("isnull", *params)


class Distinct(Func):
    def __init__(self, *params):
        super().__init__("distinct", *params)


"""
=========================字符串函数===========================
"""


class Concat(Func):
    def __init__(self, *params):
        super().__init__("concat", *params)


class Length(Func):
    def __init__(self, *params):
        super().__init__("length", *params)


class Left(Func):
    def __init__(self, *params):
        super().__init__("left", *params)


class Right(Func):
    def __init__(self, *params):
        super().__init__("right", *params)


class Lower(Func):
    def __init__(self, *params):
        super().__init__("lower", *params)


class Upper(Func):
    def __init__(self, *params):
        super().__init__("upper", *params)


class Repeat(Func):
    def __init__(self, *params):
        super().__init__("repeat", *params)


class Curdate(Func):
    def __init__(self, *params):
        super().__init__("CURDATE", *params)


class Timestampdiff(Func):
    def __init__(self, *params):
        super().__init__("TIMESTAMPDIFF", *params)


class Replace(Func):
    def __init__(self, *params):
        super().__init__("replace", *params)


class Substring(Func):
    def __init__(self, *params):
        super().__init__("substring", *params)


class Reverse(Func):
    def __init__(self, *params):
        super().__init__("reverse", *params)


class Position(Func):
    def __init__(self, *params):
        super().__init__("position", *params)


class Lpad(Func):
    def __init__(self, *params):
        super().__init__("lpad", *params)


class Ltrim(Func):
    def __init__(self, *params):
        super().__init__("ltrim", *params)


class Rtrim(Func):
    def __init__(self, *params):
        super().__init__("rtrim", *params)


class Trim(Func):
    def __init__(self, *params):
        super().__init__("trim", *params)


class FindInSet(Func):
    def __init__(self, *params):
        super().__init__("find_in_set", *params)


"""
=========================数学函数===========================
"""

class Avg(Func):
    def __init__(self, *params):
        super().__init__("avg", *params)


class Min(Func):
    def __init__(self, *params):
        super().__init__("min", *params)


class Max(Func):
    def __init__(self, *params):
        super().__init__("max", *params)


class Count(Func):
    def __init__(self, *params):
        super().__init__("count", *params)


class Sum(Func):
    def __init__(self, *params):
        super().__init__("sum", *params)


class Abs(Func):
    def __init__(self, *params):
        super().__init__("abs", *params)


class Mod(Func):
    def __init__(self, *params):
        super().__init__("mod", *params)


class Floor(Func):
    def __init__(self, *params):
        super().__init__("floor", *params)


class Ceil(Func):
    def __init__(self, *params):
        super().__init__("ceil", *params)


class Round(Func):
    def __init__(self, *params):
        super().__init__("round", *params)


class Pow(Func):
    def __init__(self, *params):
        super().__init__("pow", *params)


class Sqrt(Func):
    def __init__(self, *params):
        super().__init__("sqrt", *params)


class Pi(Func):
    def __init__(self, *params):
        super().__init__("pi", *params)


class Truncate(Func):
    def __init__(self, *params):
        super().__init__("truncate", *params)


class Least(Func):
    def __init__(self, *params):
        super().__init__("least", *params)


class Greatest(Func):
    def __init__(self, *params):
        super().__init__("greatest", *params)


class Random(Func):
    def __init__(self, *params):
        super().__init__("random", *params)


class Rank(Func):
    def __init__(self, *params):
        super().__init__("rank", *params)


class Exp(Func):
    def __init__(self, *params):
        super().__init__("exp", *params)


"""
=========================日期函数===========================
"""


class DayOfWeek(Func):
    def __init__(self, *params):
        super().__init__("dayofweek", *params)


class Weekday(Func):
    def __init__(self, *params):
        super().__init__("weekday", *params)


class DayOfMonth(Func):
    def __init__(self, *params):
        super().__init__("dayofmonth", *params)


class DayOfYear(Func):
    def __init__(self, *params):
        super().__init__("dayofyear", *params)


class Month(Func):
    def __init__(self, *params):
        super().__init__("month", *params)


class Quarter(Func):
    def __init__(self, *params):
        super().__init__("quarter", *params)


class Week(Func):
    def __init__(self, *params):
        super().__init__("week", *params)


class Year(Func):
    def __init__(self, *params):
        super().__init__("year", *params)


class Hour(Func):
    def __init__(self, *params):
        super().__init__("hour", *params)


class Minute(Func):
    def __init__(self, *params):
        super().__init__("minute", *params)


class Second(Func):
    def __init__(self, *params):
        super().__init__("second", *params)


class DateFormat(Func):
    def __init__(self, *params):
        super().__init__("date_format", *params)


class Now(Func):
    def __init__(self, *params):
        super().__init__("now", *params)


class CurrentDate(Func):
    def __init__(self, *params):
        super().__init__("current_date", *params)


class CurrentTimestamp(Func):
    def __init__(self, *params):
        super().__init__("current_timestamp", *params)


class LastDay(Func):
    def __init__(self, *params):
        super().__init__("last_day", *params)


class AddDate(Func):
    def __init__(self, *params):
        super().__init__("add_date", *params)


class SubDate(Func):
    def __init__(self, *params):
        super().__init__("sub_date", *params)


class Nvl(Func):
    def __init__(self, *params):
        super().__init__("nvl", *params)


class GroupConcat(Func):
    def __init__(self, *params):
        super().__init__("group_concat", *params)


class Limit1(Func):
    def __init__(self, *params):
        super().__init__("limit1", *params)

"""
+++++++++++++++++++++++++++++++++关系映射+++++++++++++++++++++++++++
"""


if_ = If
if_null = IfNull
is_null = IsNull
distinct = Distinct

concat = Concat
length = Length
left = Left
right = Right
lower = Lower
upper = Upper
repeat = Repeat
curdate = Curdate
timestampdiff = Timestampdiff
replace = Replace
substring = Substring
reverse = Reverse
position = Position
lpad = Lpad
ltrim = Ltrim
rtrim = Rtrim
trim = Trim
find_in_set = FindInSet
avg = Avg
min = Min
max = Max
count = Count
sum = Sum
abs = Abs
mod = Mod
floor = Floor
ceil = Ceil
round = Round
pow = Pow
sqrt = Sqrt
pi = Pi
truncate = Truncate
least = Least
greatest = Greatest
random = Random
rank = Rank
exp = Exp

dayofweek = DayOfWeek
weekday = Weekday
dayofmonth = DayOfMonth
dayofyear = DayOfYear
month = Month
quarter = Quarter
week = Week
year = Year
hour = Hour
minute = Minute
second = Second
date_format = DateFormat
now = Now
current_date = CurrentDate
current_timestamp = CurrentTimestamp
last_day = LastDay
add_date = AddDate
sub_date = SubDate
nvl = Nvl
group_concat = GroupConcat
limit1 = Limit1
