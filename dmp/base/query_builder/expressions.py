#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/9/18 15:23
# <AUTHOR> caoxl
# @File     : expression.py
# pylint: disable=W0622,W0105,R0201
from .exceptions import InvalidArgumentExcption, InvalidCallExcption


class Expression:
    def __init__(self, name, operator):
        self._name = name
        self._operator = operator

    def operator(self):
        return self._operator

    def name(self):
        return self._name


"""
++++++++++++++++逻辑关系表达式+++++++++++++++++
"""


class LogicalRelationExpression(Expression):
    """
    逻辑关系表达式
    """


class And(LogicalRelationExpression):
    """
    AND
    """

    def __init__(self, *clauses):
        _operator = "AND"
        super().__init__(name="and", operator=_operator)
        self._clauses = clauses
        self._operator = _operator

    def get_clauses(self):
        return self._clauses


class Or(LogicalRelationExpression):
    """
    OR
    """

    def __init__(self, *clauses):
        _operator = "OR"
        super().__init__(name="or", operator=_operator)
        self._clauses = clauses
        self._operator = _operator

    def get_clauses(self):
        return self._clauses


class Xor(LogicalRelationExpression):
    """
    XOR
    """

    def __init__(self, *clauses):
        _operator = "XOR"
        super().__init__(name="xor", operator=_operator)
        self._clauses = clauses
        self._operator = _operator

    def get_clauses(self):
        return self._clauses


"""
++++++++++++++++二元表达式+++++++++++++++++
"""


class BinaryExpression(Expression):
    """
    二元表达式
    """

    def get_left(self):
        raise InvalidCallExcption(message="请在子类中实现该方法!")

    def get_right(self):
        raise InvalidCallExcption(message="请在子类中实现该方法!")


class BinaryExpressionFactory:
    """
    二元表达式工厂类
    """

    @staticmethod
    def create_expression(left, operator, right):
        if operator not in BINARY_OPERATOR_TO_EXPRESSION.keys():
            raise InvalidArgumentExcption(message="操作符无法识别！")
        expression_class = BINARY_OPERATOR_TO_EXPRESSION.get(operator)
        return expression_class(left=left, right=right)


class Add(BinaryExpression):
    """
    + (加)
    """

    def __init__(self, left, right):
        _operator = "+"
        super().__init__(name="add", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Mul(BinaryExpression):
    """
    * (乘)
    """

    def __init__(self, left, right):
        _operator = "*"
        super().__init__(name="mul", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Sub(BinaryExpression):
    """
    - (减)
    """

    def __init__(self, left, right):
        _operator = "-"
        super().__init__(name="sub", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Div(BinaryExpression):
    """
    / (除)
    """

    def __init__(self, left, right):
        _operator = "/"
        super().__init__(name="div", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Mod(BinaryExpression):
    """
    % (求模)
    """

    def __init__(self, left, right):
        _operator = "%"
        super().__init__(name="mod", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Lt(BinaryExpression):
    """
    <  (小于)
    """

    def __init__(self, left, right):
        _operator = "<"
        super().__init__(name="lt", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Le(BinaryExpression):
    """
    <=  (小于等于)
    """

    def __init__(self, left, right):
        _operator = "<="
        super().__init__(name="le", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Ne(BinaryExpression):
    """
    !=  (不等于)
    """

    def __init__(self, left, right):
        _operator = "!="
        super().__init__(name="ne", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Gt(BinaryExpression):
    """
    >  (大于)
    """

    def __init__(self, left, right):
        _operator = ">"
        super().__init__(name="gt", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Ge(BinaryExpression):
    """
    >= (大于等于)
    """

    def __init__(self, left, right):
        _operator = ">="
        super().__init__(name="ge", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Eq(BinaryExpression):
    """
    = (等于)
    """

    def __init__(self, left, right):
        _operator = "="
        super().__init__(name="eq", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class In(BinaryExpression):
    """
    IN
    """

    def __init__(self, left, right):
        _operator = "IN"
        super().__init__(name="in", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Notin(BinaryExpression):
    """
    NOT IN
    """

    def __init__(self, left, right):
        _operator = "NOT IN"
        super().__init__(name="notin", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class As(BinaryExpression):
    """
    AS
    """

    def __init__(self, left, right):
        _operator = "NOT IN"
        super().__init__(name="as", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Is(BinaryExpression):
    """
    IS
    """

    def __init__(self, left, right):
        _operator = "IS"
        super().__init__(name="is", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Isnot(BinaryExpression):
    """
    IS NOT
    """

    def __init__(self, left, right):
        _operator = "IS NOT"
        super().__init__(name="isnot", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Like(BinaryExpression):
    """
    Like
    """

    def __init__(self, left, right):
        _operator = "LIKE"
        super().__init__(name="like", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Notlike(BinaryExpression):
    """
    NOT LIKE
    """

    def __init__(self, left, right):
        _operator = "NOT LIKE"
        super().__init__(name="notlike", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Between(BinaryExpression):
    """
    BETWEEN
    """

    def __init__(self, left, right: list):
        _operator = "BETWEEN"
        super().__init__(name="between", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


class Notbetween(BinaryExpression):
    """
    NOT BETWEEN
    """

    def __init__(self, left, right: list):
        _operator = "NOT BETWEEN"
        super().__init__(name="notbetween", operator=_operator)
        self._left = left
        self._right = right
        self._operator = _operator

    def get_left(self):
        return self._left

    def get_right(self):
        return self._right


"""
++++++++++++++++一元表达式+++++++++++++++++
"""


class UnaryExpression(Expression):
    """
    一元表达式
    """


class Inv(UnaryExpression):
    """
    NOT
    """

    def __init__(self, clause):
        _operator = "NOT"
        super().__init__(name="not", operator=_operator)
        self._clause = clause
        self._operator = _operator

    def get_clause(self):
        return self._clause


class All(UnaryExpression):
    """
    ALL  all(select * from table)
    """

    def __init__(self, clause):
        _operator = "ALL"
        super().__init__(name="all", operator=_operator)
        self._clause = clause
        self._operator = _operator

    def get_clause(self):
        return self._clause


class Any(UnaryExpression):
    """
    Any  any(select * from table)
    """

    def __init__(self, clause):
        _operator = "ANY"
        super().__init__(name="any", operator=_operator)
        self._clause = clause
        self._operator = _operator

    def get_clause(self):
        return self._clause


class Some(UnaryExpression):
    """
    Some  some(select * from table)
    """

    def __init__(self, clause):
        _operator = "SOME"
        super().__init__(name="some", operator=_operator)
        self._clause = clause
        self._operator = _operator

    def get_clause(self):
        return self._clause


class Exists(UnaryExpression):
    """
    EXISTS
    """

    def __init__(self, clause):
        _operator = "EXISTS"
        super().__init__(name="exists", operator=_operator)
        self._clause = clause
        self._operator = _operator

    def get_clause(self):
        return self._clause


class Asc(UnaryExpression):
    """
    ASC
    """

    def __init__(self, clause):
        _operator = "ASC"
        super().__init__(name="asc", operator=_operator)
        self._clause = clause
        self._operator = _operator

    def get_clause(self):
        return self._clause


class Desc(UnaryExpression):
    """
    DESC
    """

    def __init__(self, clause):
        _operator = "DESC"
        super().__init__(name="desc", operator=_operator)
        self._clause = clause
        self._operator = _operator

    def get_clause(self):
        return self._clause


class Neg(UnaryExpression):
    """
    -  (负数)
    """

    def __init__(self, clause):
        _operator = "-"
        super().__init__(name="neg", operator=_operator)
        self._clause = clause
        self._operator = _operator

    def get_clause(self):
        return self._clause


"""
++++++++++++++++复杂语句表达式+++++++++++++++++
"""


class ComplexExpression(Expression):
    pass


class Case(ComplexExpression):
    """
    CASE WHEN ELSE
    """

    def __init__(self, whens: list, else_=None):
        _operator = ""
        super().__init__(name="case", operator=_operator)
        self._whens = whens
        self._else = else_
        self._operator = _operator

    def get_whens(self):
        return self._whens

    def get_else(self):
        return self._else


"""
+++++++++++++++++++++++++++++++++关系映射+++++++++++++++++++++++++++
"""

and_ = And
or_ = Or
xor = Xor

add = Add
mul = Mul
sub = Sub
div = Div
mod = Mod
lt = Lt
le = Le
ne = Ne
gt = Gt
ge = Ge
eq = Eq
in_ = In
notin = Notin
as_ = As
is_ = Is
isnot = Isnot
like = Like
notlike = Notlike
between = Between
notbetween = Notbetween

inv = Inv
all_ = All
any_ = Any
some = Some
exists = Exists
asc = Asc
desc = Desc
neg = Neg

case = Case


"""
+++++++++++++++++++++++++++++++++二元操作符+++++++++++++++++++++++++++
"""
BINARY_OPERATOR_TO_EXPRESSION = {
    # binary
    "XOR": xor,
    "AND": and_,
    "OR": or_,
    "+": add,
    "*": mul,
    "-": sub,
    "/ ": div,
    "%": mod,
    "<": lt,
    "<=": le,
    "!=": ne,
    ">": gt,
    ">=": ge,
    "=": eq,
    "IN": in_,
    "NOT IN": notin,
    "AS": as_,
    "IS": is_,
    "IS NOT": isnot,
    "BETWEEN": between,
    "NOT BETWEEN": notbetween,
}
