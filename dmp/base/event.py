import json
from collections import defaultdict
from loguru import logger

from dmplib.components.app_hosts import AppHosts
from dmplib.redis import RedisCache
from open_data.openapi_schema import superportal_330_api_info
from dmplib.components.enums import IngrateAction, SkylineApps

_event = None
AddDataSourceEvent = "AddDataSource"
UpdateDataSourceEvent = "UpdateDataSource"
DelDataSourceEvent = "DelDataSourceEvent"


class Event:
    def __init__(self):
        self.fns = defaultdict(list)

    def register(self, event_name: str, fn):
        self.fns[event_name].append(fn)

    def trigger(self, event_name: str, data):
        for fn in self.fns[event_name]:
            fn(event_name, data)


def get_instance():
    global _event
    if not _event:
        _event = Event()
    return _event


def register(event_name: str, fn):
    get_instance().register(event_name, fn)


def trigger(event_name: str, data):
    get_instance().trigger(event_name, data)


def _register_api_2_ingrate_platform(api_info, code=None, cache_key_suffix=''):
    from dmplib.components.ingrate_platform import IngratePlatformApi
    cache = RedisCache(key_prefix="register_api")
    key = '_register_api_2_ingrate_platform' + cache_key_suffix
    if cache.set_nx_ex(key, 1, ex=3600, nx=True):
        mip_api = IngratePlatformApi(code=code, timeout=10, action=IngrateAction.Register.value)
        result = mip_api.register_interface(
            name='数见',
            connector_code='Dmp',
            api_info=api_info
        )
        cache.expire(key, 7 * 24 * 60 * 60)
        return result
    return None


REGISTER_RESULT_CACHE_KEY = 'register_result_'
def register_api(code=None):
    # 如果配置中心没有地址不注册接口
    if not AppHosts.get(SkylineApps.IPAAS):
        return
    from open_data.openapi_schema import superportal_api_info
    redis = RedisCache(key_prefix='mip')
    try:
        # 注册菜单api到集成平台
        result = _register_api_2_ingrate_platform(superportal_api_info, code)
        if result:
            redis.set(f'{REGISTER_RESULT_CACHE_KEY}1', json.dumps(result, ensure_ascii=False), 30 * 24 * 60 * 60)
    except Exception as e:
        logger.info(f"register_api error: {e}")
        result = {'msg': str(e)}
        redis.set(f'{REGISTER_RESULT_CACHE_KEY}1', json.dumps(result, ensure_ascii=False), 30 * 24 * 60 * 60)
    try:
        logger.info('注册330接口到集成平台')
        # 注册330菜单api到集成平台
        result = _register_api_2_ingrate_platform(superportal_330_api_info, code, '_330')
        if result:
            redis.set(f'{REGISTER_RESULT_CACHE_KEY}2', json.dumps(result, ensure_ascii=False), 30 * 24 * 60 * 60)
    except Exception as e:
        logger.info(f"register_api error: {e}")
        result = {'msg': str(e)}
        redis.set(f'{REGISTER_RESULT_CACHE_KEY}2', json.dumps(result, ensure_ascii=False), 30 * 24 * 60 * 60)

