#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/17.
"""
from base import repository, service
from label.models import LabelDimensionModel
from label.services import label_dimension_service


def add_label_indicator(model):
    """
    添加标签指标
    :param label.models.LabelIndicatorModel model:
    :return:
    """
    model.validate()
    fields = ['label_id', 'indicator_id', 'indicator_name']
    repository.add_model('dap_bi_label_indicator', model, fields)
    result = model.dimensions_to_model()
    for dimension in model.dimensions:
        if not isinstance(dimension, LabelDimensionModel):
            continue
        label_dimension_service.add_label_dimension(dimension)
    return result


def delete_label_indicator_by_label_id(label_id):
    """
    删除标签下所有已选指标
    :param str label_id:
    :return int:
    """
    record = repository.delete_data('dap_bi_label_indicator', {'label_id': label_id})
    record += label_dimension_service.delete_label_dimension_by_label_id(label_id)
    return record


def get_label_indicators_by_label_id(label_id):
    """
    获取标签下已选指标
    :param label_id:
    :return:
    """
    fields = ['indicator_id', 'indicator_name']
    indicators = repository.get_data('dap_bi_label_indicator', {'label_id': label_id}, fields, True)
    if indicators:
        dimensions = label_dimension_service.get_label_dimensions_by_label_id(label_id)
        dimensions = service.list_dict_group_by(dimensions, 'indicator_id')
        for indicator in indicators:
            indicator['dimensions'] = dimensions.get(indicator.get('indicator_id'))
    return indicators
