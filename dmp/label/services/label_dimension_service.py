#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/17.
"""
from base import repository


def add_label_dimension(model):
    """
    添加标签指标维度
    :param label.models.LabelDimensionModel model:
    :return:
    """
    model.validate()
    fields = ['label_id', 'indicator_id', 'dimension_id', 'dimension_name']
    repository.add_model('dap_bi_label_dimension', model, fields)


def delete_label_dimension_by_label_id(label_id):
    """
    删除标签指标维度
    :param label_id:
    :return:
    """
    return repository.delete_data('dap_bi_label_dimension', {'label_id': label_id})


def get_label_dimensions_by_label_id(label_id):
    """
    获取标签指标维度
    :param label_id:
    :return:
    """
    fields = ['indicator_id', 'dimension_id', 'dimension_name']
    return repository.get_data('dap_bi_label_dimension', {'label_id': label_id}, fields, True)
