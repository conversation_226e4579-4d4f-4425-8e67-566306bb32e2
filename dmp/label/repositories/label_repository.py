#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/24.
"""
from dmplib.constants import (
    LABEL_CHECK_TABLE_NAME_SUFFIX,
    LABEL_STATISTICS_TABLE_NAME_SUFFIX,
    DATA_CLEAN_PRIMARY_KEY_NAME,
)
from dmplib.saas.project import get_data_db, get_db
from base import repository


def get_label_list(query_model):
    """
    获取标签列表
    :param label.models.LabelFlowQueryModel query_model:
    :return:
    """
    sql = 'SELECT f.id,f.`name`,f.description,f.type,f.build_in,f.`schedule`,f.`status`,' 'f.run_status,f.depend_flow_id,df.`name` as depend_flow_name,l.cover_count,l.mode ' 'FROM dap_bi_flow as f ' 'INNER JOIN dap_bi_label as l on f.id=l.label_id ' 'LEFT JOIN dap_bi_flow as df on f.depend_flow_id=df.id '
    params = {}
    wheres = []
    if query_model.keyword:
        wheres.append('( f.`name` LIKE %(keyword)s  OR  f.`description` LIKE %(keyword)s)')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.type:
        wheres.append('f.`type` = %(type)s')
        params['type'] = query_model.type
    if query_model.build_in:
        wheres.append('f.`build_in` = %(build_in)s')
        params['build_in'] = query_model.build_in
    if query_model.org_id:
        wheres.append('l.`org_id` = %(org_id)s')
        params['org_id'] = query_model.org_id
    if query_model.tmpl_id:
        wheres.append('l.`tmpl_id` = %(tmpl_id)s')
        params['tmpl_id'] = query_model.tmpl_id
    if query_model.mode:
        wheres.append('l.`mode` = %(mode)s')
        params['mode'] = query_model.mode
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            if sort.id == 'name':
                sorts.append('convert(f.`name` USING gbk) COLLATE gbk_chinese_ci ' + sort.method)
            if sort.id == 'cover_count':
                sorts.append('cover_count ' + sort.method)
    sql += ' ORDER BY ' + (','.join(sorts) if sorts else 'f.`created_on` DESC')
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_label_data_analysis(model):
    """
    获取标签指标数据分析
    :param label.models.LabelAnalysisModel model:
    :return:
    """
    params = {}
    indicator_param_names = []
    for i in range(len(model.indicator_id_list)):
        param_name = 'indicator_id' + str(i)
        indicator_param_names.append('%(' + param_name + ')s')
        params[param_name] = model.indicator_id_list[i]
    sql = []
    for table in model.tables:
        table_name = table.get('table_name') + '_' + LABEL_CHECK_TABLE_NAME_SUFFIX
        params[table_name + '_label_id'] = table.get('label_id')
        tmp_sql = (
            'SELECT %({table_name}_label_id)s AS label_id ,indicator_id,null_count,not_null_count '
            'FROM `{table_name}` '.format(table_name=table_name)
        )
        if indicator_param_names:
            tmp_sql += 'WHERE indicator_id IN (' + ','.join(indicator_param_names) + ')'
        sql.append(tmp_sql)
    sql = ' UNION ALL '.join(sql)
    with get_data_db() as db:
        return db.query(sql, params)


def get_label_dim_analysis(model):
    """
    获取标签指标维度分析
    :param label.models.LabelAnalysisModel model:
    :return:
    """
    params = {}
    indicator_param_names = []
    for i in range(len(model.indicator_id_list)):
        param_name = 'indicator_id' + str(i)
        indicator_param_names.append('%(' + param_name + ')s')
        params[param_name] = model.indicator_id_list[i]
    sql = []
    for table in model.tables:
        table_name = table.get('table_name') + '_' + LABEL_STATISTICS_TABLE_NAME_SUFFIX
        params[table_name + '_label_id'] = table.get('label_id')
        tmp_sql = (
            'SELECT %({table_name}_label_id)s AS label_id ,indicator_id,dimension_name,`rank`,`count` '
            'FROM `{table_name}` '.format(table_name=table_name)
        )
        if indicator_param_names:
            tmp_sql += 'WHERE indicator_id IN (' + ','.join(indicator_param_names) + ') '
        sql.append(tmp_sql)
    sql = 'SELECT * FROM (' + ' UNION ALL '.join(sql) + ') res ORDER BY indicator_id,`rank`'
    with get_data_db() as db:
        return db.query(sql, params)


def get_label_detail_data(query_model):
    """
    获取标签明细数据
    :param label.models.LabelDetailDataQueryModel query_model:
    :return label.models.LabelDetailDataQueryModel:
    """
    sql = 'SELECT {master_id},{cols} ' 'FROM {table_name}'.format(
        master_id=DATA_CLEAN_PRIMARY_KEY_NAME,
        cols='`' + '`,`'.join(query_model.list_col) + '`',
        table_name='`' + query_model.label_table_name + '`',
    )
    params = {}
    if query_model.keyword:
        params['keyword'] = '%' + query_model.keyword_escape + '%'
        sql += ' WHERE (' + ' OR '.join(['`' + col + '` LIKE %(keyword)s' for col in query_model.list_col]) + ')'
    if query_model.sorts:
        sql += ' ORDER BY ' + ','.join(
            ['convert(`%s` USING gbk) COLLATE gbk_chinese_ci %s' % (sc.id, sc.method) for sc in query_model.sorts]
        )
    with get_data_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model
