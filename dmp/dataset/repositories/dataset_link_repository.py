# !/usr/bin/env python3
# -*- coding: utf-8 -*-


from dmplib.saas.project import get_db


def get_link_datas(dataset_id):
    """
    获取数据集图像模式的关联表数据
    :param dataset_id:
    :return:
    """
    sql = """
            select `id`,`dataset_id`,`from_table_name`,`from_alias_name`,`from_id`,`to_table_name`,`to_alias_name`,
            `to_id`, `join_type`,`join_fields` from dap_bi_dataset_tables_collection WHERE dataset_id  = %(dataset_id)s
          """.format(
        dataset_id=dataset_id
    )
    with get_db() as db:
        return db.query(sql, {"dataset_id": dataset_id})
