# !/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    <NAME_EMAIL> on 2018/1/9.
"""
import re
import datetime
from datetime import datetime, timedelta
from decimal import Decimal
import pymysql
from base.enums import DatasetType, DataSourceType
from base.sql_adapter import adapter_sql
from data_source.repositories import mysql_data_source_repository
from dmplib.saas.project import get_db, get_data_db
from dmplib.utils.errors import UserError
from message.models import MessageModel
from message.services import message_service

from typing import List, Optional


def get_same_data_source_and_use_cache_dataset(data_source_id):
    sql = f'''SELECT id, content, cache_flow_id from dap_bi_dataset where content like '%"data_source_id": "{data_source_id}"%' and use_cache=1'''
    with get_db() as db:
        return db.query(sql)

def get_dataset_by_source(data_source_id):
    sql = f'''SELECT * from dap_bi_dataset where content like '%"data_source_id": "{data_source_id}"%' '''
    with get_db() as db:
        return db.query(sql)
def get_dataset_map_by_ids(dataset_ids):
    """
    根据ids获取数据
    :param dataset_ids:
    :return:
    """
    if isinstance(dataset_ids, list):
        dataset_ids = '("{}")'.format('","'.join(list(dataset_ids))) if dataset_ids else '("")'
    sql = 'SELECT `id`, `name` FROM dap_bi_dataset WHERE id in ' + dataset_ids
    with get_db() as db:
        return db.query(sql)


def get_dataset_by_ids(dataset_ids):
    """
    根据ids获取数据
    :param dataset_ids:
    :return:
    """
    if isinstance(dataset_ids, list):
        dataset_ids = "('{}')".format("','".join(list(dataset_ids))) if dataset_ids else "('')"
    sql = 'SELECT * FROM dap_bi_dataset WHERE id in ' + dataset_ids
    with get_db() as db:
        return db.query(sql)


def get_dataset_by_names(dataset_names: list):
    """
    根据ids获取数据
    :param dataset_names:
    :return:
    """
    if not dataset_names:
        return []
    sql = 'SELECT name FROM dap_bi_dataset WHERE name in %(dataset_names)s'
    with get_db() as db:
        return db.query_columns(sql, {"dataset_names": dataset_names}) or []


def get_dataset_id_map_by_names(dataset_names: list):
    """
    根据ids获取数据
    :param dataset_names:
    :return:
    """
    if not dataset_names:
        return []
    sql = 'SELECT id,name FROM dap_bi_dataset WHERE name in %(dataset_names)s'
    with get_db() as db:
        result = db.query(sql, {"dataset_names": dataset_names}) or []
        return {item['name']: item['id'] for item in result}


def get_dataset_level_code(dataset_id):
    """
    获取数据集level code
    :param dataset_id:
    :return:
    """
    sql = 'SELECT `level_code` FROM dap_bi_dataset WHERE id=%(id)s LIMIT 1'
    with get_db() as db:
        return db.query_scalar(sql, {'id': dataset_id})

def get_dataset_level_codes(dataset_ids):
    """
    获取数据集level code
    :param dataset_id:
    :return:
    """
    if isinstance(dataset_ids, list):
        dataset_ids = '("{}")'.format('","'.join(list(dataset_ids))) if dataset_ids else '("")'
    sql = 'SELECT `level_code` FROM dap_bi_dataset WHERE id in ' + dataset_ids
    with get_db() as db:
        return db.query(sql)


def get_dataset_list_by_group_id(
        parent_id: None = '', exclude_types: Optional[List[str]] = None, start_time: None = None, end_time: None = None,
        filter_vars=False, without_content=False
):
    """
    根据数据集编码获取所有数据集
    :param end_time:
    :param start_time:
    :param exclude_types:
    :param str parent_id:
    :param filter_vars:
    :param without_content:
    :return:
    """
    # pylint: disable=line-too-long
    content_column_sql = "ds.`content`,"
    if without_content:
        content_column_sql = ""
    sql = f'''
    SELECT ds.`id`,ds.`name`,ds.`type`,ds.`level_code`,ds.`parent_id`,ds.`connect_type`,
    { content_column_sql }ds.`created_on`, ds.`modified_on`, ds.`created_by`, ds.`is_lock`, ds.`external_type`, ds.`edit_mode` , ds.`is_import_table` , ds.`import_table_name` , ds.`import_table_type` 
    FROM dap_bi_dataset AS ds WHERE 1=1
    '''

    params = {}
    if parent_id:

        # 只获取根节点下所有数据集对象列表
        if parent_id == 'root':
            parent_id = ''

        sql += ' and ds.`parent_id`=%(parent_id)s '
        params['parent_id'] = parent_id
    if start_time:
        sql += ' and ds.modified_on >= %(start_time)s '
        params['start_time'] = start_time
    if end_time:
        sql += ' and ds.modified_on <= %(end_time)s'
        if start_time == end_time:  # 2018-02-01 -> 2018-02-02
            end_time = datetime.strptime(end_time, '%Y-%m-%d')
            end_time = end_time + timedelta(days=1)
            end_time = end_time.strftime('%Y-%m-%d')

        params['end_time'] = end_time

    if exclude_types:
        # 直连模式
        if 'directly_connected' in exclude_types:
            # 加上 connect_type is null 的原因是 connect_type <> '直连 会把不等于直连和connect_type为null的都排除
            sql += " and (ds.connect_type <> '直连' or ds.connect_type is null) "
            exclude_types.remove('directly_connected')
        sql += ' and ds.`type` not in %(exclude_types)s '
        params['exclude_types'] = exclude_types

    if filter_vars:
        sql += ' and ds.id not in (select DISTINCT dataset_id from dap_bi_dataset_vars)'

    sql += ' ORDER BY ds.level_code '

    with get_db() as db:
        return db.query(sql, params)


def get_dataset_list(connect_type: str = '1'):
    """
    根据数据集编码获取所有数据集
    :param str connect_type:
    :return: []
    """
    # pylint: disable=line-too-long
    sql = '''
    SELECT ds.`id`,ds.`name`,ds.`type`,ds.`level_code`,ds.`parent_id`,ds.`connect_type`,
    ds.`created_on`, ds.`modified_on`, ds.`created_by`
    FROM dap_bi_dataset AS ds WHERE 1=1
    '''

    params = {}
    if connect_type == '1':
        sql += " and (ds.connect_type = '直连') "
    elif connect_type == '2':
        sql += " and (ds.connect_type <> '直连' or ds.connect_type is null) "

    sql += ' ORDER BY ds.level_code '

    with get_db() as db:
        return db.query(sql, params)


def get_datasets(is_lock: str = '1',
                 exclude_types: Optional[List[str]] = None):
    """
    根据数据集编码获取所有数据集
    :param str connect_type:
    :return: []
    """
    # pylint: disable=line-too-long
    sql = '''
    SELECT ds.`id`,ds.`name`,ds.`type`,ds.`level_code`,ds.`parent_id`,ds.`connect_type`
    FROM dap_bi_dataset AS ds WHERE 1=1
    '''

    params = {}
    if is_lock:
        sql += " and `is_lock` =%(is_lock)s"
        params['is_lock'] = is_lock

    if exclude_types:
        # 直连模式
        if 'directly_connected' in exclude_types:
            # 加上 connect_type is null 的原因是 connect_type <> '直连 会把不等于直连和connect_type为null的都排除
            sql += " and (ds.connect_type <> '直连' or ds.connect_type is null) "
            exclude_types.remove('directly_connected')
        sql += ' and ds.`type` not in %(exclude_types)s '
        params['exclude_types'] = exclude_types

    sql += ' ORDER BY ds.level_code '

    with get_db() as db:
        return db.query(sql, params)


def get_tmp_folder():
    sql = '''SELECT a.`id`,a.`name`
            FROM dap_bi_dataset as a
            inner join dap_bi_dataset as b on a.parent_id=b.id 
            LEFT JOIN dap_bi_dataset as c on b.parent_id=c.id
            LEFT JOIN dap_bi_dataset as d on c.parent_id=d.id and d.id!=d.parent_id
            WHERE a.type = 'FOLDER' and ( b.`name`='系统分发数据集文件夹' or c.`name`='系统分发数据集文件夹' or d.`name`='系统分发数据集文件夹' )  and  a.id NOT IN (
                SELECT DISTINCT parent_id
                FROM dap_bi_dataset
             )'''
    with get_db() as db:
        return db.query(sql)


def get_dataset_connect_type_by_source(data_source_id, connect_type: str = '1'):
    sql = '''
       SELECT ds.`id`,ds.`name`,ds.`type`,ds.`level_code`,ds.`parent_id`,ds.`connect_type`,
       ds.`created_on`, ds.`modified_on`, ds.`created_by`
       FROM dap_bi_dataset AS ds WHERE 1=1
       '''
    if connect_type == '1':
        sql += " and (ds.connect_type = '直连') "
    elif connect_type == '2':
        sql += " and (ds.connect_type <> '直连' or ds.connect_type is null) "

    if data_source_id:
        sql += f''' and (content like '%"{data_source_id}"%') '''

    sql += ' ORDER BY ds.level_code '

    with get_db() as db:
        return db.query(sql)


def get_dataset_amount(table_name, where_str=None):
    """
    获取数据集总数
    :param str table_name:
    :param str where_str:
    :return:
    """

    sql = "select count(*) from %s  %s " % (table_name, where_str if where_str else '')

    with get_data_db() as db:
        return db.query_scalar(sql)


# pylint: disable=unused-argument
def get_dataset_amount_direct(table_name, where_str=None, direct_sql=None, data_source_model=None):
    """
    获取数据集总数
    :param str table_name:
    :param str where_str:
    :return:
    """
    sql = "select count(*) from ({direct_sql}) a".format(direct_sql=direct_sql.rstrip(';'))

    try:
        with mysql_data_source_repository.get_mysql_db(data_source_model) as db:
            result_data = db.query_scalar(sql)
        # 为单图缓存数据，出错之后还能显示原来的数据，避免图标错误
        # key = '{prefix}_data_{table_name}'.format(prefix=DIRECT_PREFIX, table_name=table_name)
        # redis.conn().set_data(key, result_data)
        return result_data
    except pymysql.Error as e:
        raise UserError(message='获取数据集数据错误：' + str(e))


def delete_dataset(dataset):
    """
    删除数据集文件夹
    :param dataset:
    :return:
    """

    # 删除数据集，如果是文件夹的话，需要判断下面有没有子目录或者数据集，若存在数据集则不能删除
    # 只允许删除空目录或者单个数据集

    if dataset.get("type") == DatasetType.Folder.value:
        sql = 'SELECT `id`, `level_code` FROM dap_bi_dataset WHERE `level_code` like %(dataset_level_code)s '
        with get_db() as db:
            child_items = db.query(sql, {'dataset_level_code': dataset.get("level_code") + '%'})
            if child_items and len(child_items) > 1:
                is_exist_child = False
                for child_item in child_items:
                    if len(child_item.get("level_code")) == len(dataset.get("level_code")) + 5:
                        is_exist_child = True
                        break
                if is_exist_child:
                    raise UserError(message='该文件夹下存在数据集或子目录，无法删除')

    sql = 'DELETE FROM dap_bi_dataset WHERE `id` = %(dataset_id)s limit 1'
    with get_db() as db:
        return db.exec_sql(sql, {'dataset_id': dataset.get("id")})


def insert_multi_data(table, list_data, fields):
    """
    添加多行数据
    :param str table:
    :param list list_data:
    :param list fields:
    :return:
    """
    params = {}
    values = []
    tmp = 0
    for data in list_data:
        if not isinstance(data, dict):
            continue
        tmp_val = []
        for c in fields:
            if not isinstance(c, str):
                continue
            p_name = c + '_' + str(tmp)
            tmp_val.append('%(' + p_name + ')s')
            params[p_name] = data.get(c)
        values.append('(' + ','.join(tmp_val) + ')')
        tmp += 1

    if not values:
        return 0
    sql = 'INSERT INTO {table}({cols}) VALUES {values};'.format(
        table=table, cols=','.join(['`' + c + '`' for c in fields]), values=','.join(values)
    )
    with get_data_db() as db:
        db.exec_sql('set sql_mode="NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION"')
        return db.exec_sql(sql, params)


def drop_dataset_table(table_name):
    """
    删除数据集表
    :param table_name:
    :return:
    """
    sql = 'DROP TABLE IF EXISTS ' + table_name
    with get_data_db() as db:
        return db.exec_sql(sql)


def update_dataset_excel_index(table_name, add_index_sqls):
    """
    修改excel索引
    :param table_name:
     :param add_index_sqls:
    :return:
    """
    from dmplib.hug import g

    with get_data_db() as db:

        sql = adapter_sql('excel_index', db.db_type).format(owner=db.db)

        index_names = db.query(sql, {'table_name': table_name}) or []
        drop_index_sqls = []
        drop_index_sqls = get_drop_index_sql(index_names, DataSourceType.Mysql.value, table_name)

        if drop_index_sqls:
            for drop_index_sql in drop_index_sqls:
                db.exec_sql(drop_index_sql)
        if add_index_sqls:
            for add_index_sql in add_index_sqls:
                db.exec_sql(add_index_sql)
        return len(drop_index_sqls)


def get_drop_index_sql(index_names, db_type, table_name):
    """
    修改excel索引
    :param index_names:
    :param db_type:
    :param table_name:
    :return:
    """
    drop_index_sqls = []
    for index_name in index_names:
        drop_index_sql = 'drop index [{index_name}] on [{table_name}];'.format(index_name=index_name.get("INDEX_NAME"),
                                                                               table_name=table_name)
        if db_type.lower() == DataSourceType.Mysql.value.lower():
            drop_index_sql = 'drop index `{index_name}` on `{table_name}`;'.format(
                index_name=index_name.get("INDEX_NAME"), table_name=table_name)
        elif db_type.lower() == DataSourceType.DM.value.lower():
            drop_index_sql = 'drop index "{index_name}";'.format(
                index_name=index_name.get("INDEX_NAME"), table_name=table_name)
        drop_index_sqls.append(drop_index_sql)

    return drop_index_sqls


def dm_get_execute_sqls(sqls, db_type=DataSourceType.DM.value):

    if sqls and len(sqls) and db_type.lower() == DataSourceType.DM.value.lower():
        start_sql = "begin"
        end_sql = "end;"
        new_sqls = []
        for sql in sqls:
            new_sql = "  execute IMMEDIATE '" + sql + "';"
            new_sqls.append(new_sql)
        return start_sql + "\r\n" + '\r\n'.join(new_sqls) + "\r\n" + end_sql
    else:
        return sqls


def get_query_index_sql(db_type, table_name):
    """
    修改excel索引
    :param db_type:
    :param table_name:
    :return:
    """
    sql = "SELECT [name] as index_name  FROM sys.indexes WHERE object_id=OBJECT_ID('{table_name}', N'U') and is_primary_key!=1".format(
        table_name=table_name)
    if db_type.lower() == DataSourceType.Mysql.value.lower():
        sql = "select DISTINCT(index_name) from information_schema.STATISTICS where TABLE_SCHEMA = (SELECT schema()) and INDEX_NAME!='PRIMARY' and table_name='{table_name}'".format(
            table_name=table_name)
    elif db_type.lower() == DataSourceType.DM.value.lower():
        sql = "SELECT DISTINCT(INDEX_NAME) as index_name from DBA_IND_COLUMNS WHERE TABLE_NAME='{table_name}'".format(
            table_name=table_name)
    return sql


def delete_dataset_excel_index(dataset, delete_indexs, addd_index_sql=None):
    """
    修改excel索引
    :param dataset:
    :param delete_indexs:
    :param addd_index_sql:
    :return:
    """
    delete_index_sqls = []
    for delete_index in delete_indexs:
        index_name = delete_index.get("index_name")
        table_name = dataset.get("table_name")
        delete_index_sqls.append('drop index `{index_name}` on `{table_name}`'.format(index_name=index_name,
                                                                                      table_name=table_name))
    with get_data_db() as db:
        if delete_index_sqls and len(delete_index_sqls) > 0:
            for delete_index_sql in delete_index_sqls:
                try:
                    db.exec_sql(delete_index_sql)
                except Exception as ex:
                    continue
        if addd_index_sql:
            db.exec_sql(addd_index_sql)


def create_dataset_table(table_name, fields, field_type):
    """
    创建数据集表
    :param str table_name:
    :param dict fields:
    :param dict field_type :
    :return:
    """
    sql = 'CREATE TABLE `' + table_name + '` ('
    col_str = ''
    defalut_val = {
        "日期": "DEFAULT \'0000-00-00 00:00:00\'",
        "字符串": "DEFAULT NULL",
        "数值": "DEFAULT NULL",
        "地址": "DEFAULT NULL",
    }
    for col in fields:
        col_name = "`" + col["col_name"] + "`"
        if not col_str:
            col_str = col_name + " " + field_type[col["data_type"]] + " " + defalut_val[col["data_type"]]
        else:
            col_str = ',' + col_name + " " + field_type[col["data_type"]] + " " + defalut_val[col["data_type"]]
        sql += col_str
    sql += ') ENGINE=InnoDB DEFAULT CHARSET=utf8;'
    with get_data_db() as db:
        return db.exec_sql(sql)


def create_dataset_table_sql(table_name, fields, field_type):
    """
    获取创建数据集表sql语句
    :param str table_name:
    :param dict fields:
    :param dict field_type :
    :return:
    """
    sql = 'CREATE TABLE `' + table_name + '` ('
    col_str = ''
    defalut_val = {"日期": "DEFAULT CURRENT_TIMESTAMP", "字符串": "DEFAULT NULL", "数值": "DEFAULT NULL",
                   "地址": "DEFAULT NULL"}
    for col in fields:
        col_name = "`" + col["col_name"] + "`"
        if not col_str:
            col_str = col_name + " " + field_type[col["data_type"]] + " " + defalut_val[col["data_type"]]
        else:
            col_str = ',' + col_name + " " + field_type[col["data_type"]] + " " + defalut_val[col["data_type"]]
        sql += col_str
    sql += ') ENGINE=InnoDB DEFAULT CHARSET=utf8;'
    return sql


def insert_multi_data_sql(table, list_data, fields):
    """
    添加多行数据
    :param str table:
    :param list list_data:
    :param list fields:
    :return:
    """

    values = []
    for data in list_data:
        if not isinstance(data, dict):
            continue
        tmp_val = []
        for c in fields:
            if not isinstance(c, str):
                continue
            tmp_val.append(data.get(c))
        if tmp_val:
            tmp_val_list = []
            for w in tmp_val:
                if w is None:
                    continue
                tmp_val_list.append('"%s"' % w.replace('"', '\\"'))
            values.append("(" + ','.join(tmp_val_list) + ")")

    if not values:
        return 0
    sql = 'INSERT INTO {table}({cols}) VALUES {values};'.format(
        table=table, cols=','.join(['`' + c + '`' for c in fields]), values=','.join(values)
    )
    return sql


def get_dataset_by_name(names, query_folder=False):
    """
    获取所有数据集（排除文件夹）
    :param list names:
    :param str query_folder:
    :return:
    """
    if not query_folder:
        sql = 'SELECT `id`,`name`,`table_name`,`user_group_id`,`type`,`connect_type` FROM dap_bi_dataset where `type` <> {} '.format(
            "'" + DatasetType.Folder.value + "'"
        )
    else:
        sql = 'SELECT `id`,`name`,`table_name`,`user_group_id`,`type`,`connect_type` FROM dap_bi_dataset `type` = {} '.format(
            "'" + DatasetType.Folder.value + "'"
        )

    if names:
        sql += ' and  `name` in ({value})'.format(value="'" + "','".join(names) + "'")

    with get_db() as db:
        return db.query(sql)


def get_sql_mode_direct_dataset_content(connect_type):
    """
    获取sql编辑模式直连类型的数据集content
    :return:
    """
    sql = '''
    select `id`, `content` from dap_bi_dataset
    where (`type`='SQL' and (`edit_mode`='sql' or `edit_mode` is null) and `connect_type`=%(connect_type)s) or
          (`type`='API' and (`edit_mode`='sql' or `edit_mode` is null))
    '''
    with get_db() as db:
        return db.query(sql, {'connect_type': connect_type})


def get_relation_mode_table_collection_count(connect_type):
    """
    获取relation编辑模式直连类型的数据集关联的表的个数
    :return:
    """
    sql = '''
    select dataset_id as id, count(1) as cnt from dap_bi_dataset_tables_collection
    where dataset_id in (
        select id
        from dap_bi_dataset
        where ((type='SQL' and edit_mode='relation' and connect_type=%(connect_type)s) or
              (type='API' and edit_mode='relation')) and to_id is not null
    )
    group by dataset_id
    '''
    with get_db() as db:
        return db.query(sql, {'connect_type': connect_type})


def get_dataset_folder_by_name(name, parent_id):
    """
    获取数据集文件夹
    :param str name:
    :param str parent_id:
    :return:
    """
    # pylint: disable=line-too-long
    sql = (
        'SELECT `id`,`name`,`level_code`,`user_group_id` FROM dap_bi_dataset  '
        'where `type` = %(type_name)s and `name` = %(name)s  '
        'and `parent_id` = %(parent_id)s '
    )
    params = {'type_name': DatasetType.Folder.value, 'name': name, 'parent_id': parent_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_subject_folder_by_name(name):
    """
    获取数据集文件夹
    :param str name:
    :param str parent_id:
    :return:
    """
    # pylint: disable=line-too-long
    sql = '''
    SELECT d.`id`, d.`name`, d.`level_code`, d.`user_group_id` FROM dap_bi_dataset as d
    inner join dap_bi_dataset_subject as ds on ds.dataset_folder_id = d.id
    where d.`type` = %(type_name)s and d.`name` = %(name)s
    '''
    params = {'type_name': DatasetType.Folder.value, 'name': name}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset(dataset_id):
    """
    获取单个数据集
    :param str dataset_id:
    :return:
    """
    sql = 'SELECT * FROM dap_bi_dataset where `id` = %(id)s'
    params = {'id': dataset_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dataset_name(dataset_id):
    """
    获取单个数据集
    :param str dataset_id:
    :return:
    """
    sql = 'SELECT name FROM dap_bi_dataset where `id` = %(id)s'
    params = {'id': dataset_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dataset_field(dataset_field_id):
    """
    获取单个数据集
    :param str dataset_id:
    :return:
    """
    sql = """SELECT id, dataset_id, alias_name, note, col_name, origin_col_name,
              data_type, visible, field_group, `rank`, `type`, `expression`,
              inspection_wheres, inspection_rules,created_on, created_by,
              format FROM dap_bi_dataset_field where `id` = %(id)s"""
    params = {'id': dataset_field_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dataset_type(dataset_id):
    """
    获取单个数据集类型
    :param str dataset_id:
    :return:
    """
    sql = 'SELECT `type` FROM dap_bi_dataset where `id` = %(id)s'
    params = {'id': dataset_id}
    with get_db() as db:
        return db.query_scalar(sql, params)


def get_dataset_by_col_name(dataset_id, col_names):
    """
    获取数据集字段集合
    :param dataset_id: 数据集ID
    :param col_names: 数据集col_name列表
    :return
    """
    sql = (
        "SELECT `id`, `dataset_id`, `col_name`, `origin_table_name`, `origin_col_name` "
        "FROM dap_bi_dataset_field "
        "WHERE dataset_id = %(dataset_id)s AND col_name in (%(col_names)s);"
    )
    params = {'dataset_id': dataset_id, 'col_names': "','".join(col_names)}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_connect_type(dataset_id):
    """
    获取单个数据集连接类型
    :param str dataset_id:
    :return:
    """
    sql = 'SELECT `connect_type` FROM dap_bi_dataset where `id` = %(id)s'
    params = {'id': dataset_id}
    with get_db() as db:
        return db.query_scalar(sql, params)


def get_dataset_result_data(table_name, fields, where_str=None, limit=None):
    """
    获取数据集结果
    :param str table_name:
    :param list fields:
    :param int limit:
    :return:
    """
    if limit is None:
        limit = 100
    if where_str:
        sql = 'SELECT {cols} FROM {table_name} {where_str} limit {limit}'.format(
            cols='`' + '`,`'.join(fields) + '`',
            table_name=table_name,
            where_str=where_str.replace('%Y-%m-%d', '%%Y-%%m-%%d'),
            limit=limit
        )
    else:
        sql = 'SELECT {cols} FROM {table_name}  limit {limit}'.format(
            cols='`' + '`,`'.join(fields) + '`', table_name=table_name, limit=limit
        )
    with get_data_db() as db:
        return db.query(sql)


# pylint: disable=unused-argument
def get_dataset_result_data_direct(
        table_name, fields, where_str=None, limit=None, direct_sql=None, data_source_model=None, not_limit=False
):
    """
    获取数据集结果
    :param str table_name:
    :param list fields:
    :param int limit:
    :return:
    """
    # 匹配原来sql语句中的limit
    sql_limit = re.findall(r"limit (\d+)", direct_sql, flags=re.IGNORECASE)
    if limit is None:
        limit = 100
    # 比较大小
    if sql_limit and int(sql_limit[0]):
        last_limit = limit if int(sql_limit[0]) > limit else int(sql_limit[0])
    else:
        last_limit = limit
    if where_str:
        sql = '{direct_sql} {where_str} '.format(
            direct_sql=direct_sql.rstrip(';'), where_str=where_str.replace('%Y-%m-%d', '%%Y-%%m-%%d')
        )
    else:
        sql = '{direct_sql} '.format(direct_sql=direct_sql.rstrip(';'))

    if not not_limit:
        sql = get_limit_sql(sql, last_limit)
    result_data = []
    if data_source_model:
        try:
            with mysql_data_source_repository.get_mysql_db(data_source_model) as db:
                tmp_result_data = db.query(sql)
                for item in tmp_result_data:
                    tmp_items = {}
                    for item_key, item_value in item.items():
                        item_value = (
                            item_value.normalize()
                            if isinstance(item_value, Decimal) and str(item_value) == '0E-10'
                            else item_value
                        )
                        # Decimal 0 不处理的话前端显示None
                        if item_value in [Decimal(0)]:
                            item_value = 0
                        tmp_items[item_key] = str(item_value) if item_value else item_value
                    result_data.append(tmp_items)
        except pymysql.Error as e:
            # 一小时内只保存一条消息
            _message = {
                'source_id': data_source_model.name + datetime.now().strftime("%Y-%m-%d %H"),
                'source': '数据源',
                'type': '系统消息',
                'title': '{}数据源连接失败'.format(data_source_model.name),
                'url': '/datawork/data-integration/edit/mysql/{}'.format(data_source_model.id),
            }
            message_service.message_add(MessageModel(**_message))
            raise UserError(message='获取数据集数据错误：' + str(e))
        # 为单图缓存数据，出错之后还能显示原来的数据，避免图标错误
        # key = '{prefix}_data_{table_name}'.format(prefix=DIRECT_PREFIX, table_name=table_name)
        # redis.conn().set_data(key, result_data)
    return result_data


def move_dataset_tree(level_code, new_level_code):
    """
    :更新level_code树
    :date 2017/6/19
    :param :
    :return :
    """
    # pylint: disable=line-too-long
    sql = (
        'UPDATE dap_bi_dataset set `level_code`=replace(`level_code`,%(level_code)s,%(new_level_code)s )'
        'WHERE `level_code` like %(level_code_path)s'
    )

    params = {'level_code_path': level_code + '%', 'level_code': level_code, 'new_level_code': new_level_code}
    with get_db() as db:
        return db.exec_sql(sql, params)


def copy_dataset_data(source_table_name, new_table_name, fields):
    """
    拷贝数据集数据表
    :param str source_table_name : 原表名
    :param str new_table_name : 新表名
    :param list fields : 字段名
    :return :
    """

    sql = 'CREATE TABLE {new_table_name} ' 'select {cols} from {source_table_name}'.format(
        new_table_name=new_table_name, cols='`' + '`,`'.join(fields) + '`', source_table_name=source_table_name
    )
    with get_data_db() as db:
        return db.exec_sql(sql)


def get_dataset_relate_dashboard(dataset_id):
    """
    根据数据集id获取关联单图数据
    :param dataset_id:
    :return:
    """
    # pylint: disable=line-too-long
    sql = (
        'select a.dashboard_id , c.`name` as  dashboard_name , c.`type` as dashboard_type , '
        'c.level_code as dashboard_level_code  , c.parent_id , a.id as dashboard_chart_id , '
        'a.`name` as dashboard_chart_name from  dap_bi_dashboard_chart a '
        'INNER JOIN dap_bi_dataset b on a.source = b.id '
        'LEFT JOIN dap_bi_dashboard c on c.id = a.dashboard_id '
        'where b.id = %(dataset_id)s '
    )
    params = {'dataset_id': dataset_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_relate_dashboard_by_fields(dataset_id, field_ids):
    """
    根据数据集id获取关联单图数据
    :param dataset_id:
    :return:
    """
    # pylint: disable=line-too-long
    sql = (
        'select a.dashboard_id, d.`alias` as col_name, c.`name` as  dashboard_name , '
        'd.dim as dataset_field_id, a.`name` as dashboard_chart_name from  dap_bi_dashboard_chart a '
        'INNER JOIN dap_bi_dataset b on a.source = b.id '
        'LEFT JOIN dap_bi_dashboard_chart_dim d on a.id = d.dashboard_chart_id '
        'LEFT JOIN dap_bi_dashboard c on c.id = a.dashboard_id '
        'where b.id = %(dataset_id)s'
    )

    # pylint: disable=line-too-long
    sql2 = (
        'select a.dashboard_id, d.`alias` as col_name, c.`name` as  dashboard_name , '
        'd.num as dataset_field_id, a.`name` as dashboard_chart_name from  dap_bi_dashboard_chart a '
        'INNER JOIN dap_bi_dataset b on a.source = b.id '
        'LEFT JOIN dap_bi_dashboard_chart_num d on a.id = d.dashboard_chart_id '
        'LEFT JOIN dap_bi_dashboard c on c.id = a.dashboard_id '
        'where b.id = %(dataset_id)s'
    )

    if field_ids and len(field_ids) == 1:
        sql += ' and d.dim = %(field_ids)s'
        sql2 += ' and d.num = %(field_ids)s'
    elif field_ids and len(field_ids) > 1:
        sql += ' and d.dim in {field_ids}'.format(field_ids=tuple(field_ids))
        sql2 += ' and d.num in {field_ids}'.format(field_ids=tuple(field_ids))

    params = {'dataset_id': dataset_id, 'field_ids': field_ids}
    result = []
    with get_db() as db:
        # 维度
        dim_result = db.query(sql, params)
        # 度量
        num_result = db.query(sql2, params)
        result += dim_result if dim_result else []
        result += num_result if num_result else []
        return result


def get_move_list_by_level_code(level_code, dataset_type=None):
    """
    根据level_code获取所有包含自己的子级数据
    :param str level_code:
    :param str dataset_type:
    :return:
    """

    sql = (
        'SELECT id,`name`,`type`,`level_code`,`parent_id` FROM dap_bi_dataset '
        'WHERE level_code like %(level_code)s'
    )
    params = {'level_code': level_code + '%'}
    if dataset_type:
        sql += " and `type` =%(dashboard_type)s"
        params['dashboard_type'] = dataset_type
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_relate_dataset(dataset_id):
    """
    根据数据集id获取关联组合数据集
    :param dataset_id:
    :return:
    """
    # pylint: disable=line-too-long
    sql = (
        'select a.`id` , a.`name` , a.`type` , a.replacement_id,  '
        'a.level_code , a.parent_id, a.content, a.modified_on '
        ' from  dap_bi_dataset a '
        'INNER JOIN dap_bi_dataset_depend b on a.id = b.depend_id '
        'where b.source_dataset_id = %(dataset_id)s '
    )
    params = {'dataset_id': dataset_id}
    with get_db() as db:
        return db.query(sql, params)


def get_open_data_relate_dataset(dataset_id):
    sql = """ select id, name, modified_on as last_time
          from dap_bi_open_data where dataset_id like %(dataset_id)s and status=1"""
    params = {
        "dataset_id": '%' + dataset_id + '%'
    }
    with get_db() as db:
        return db.query(sql, params)


def get_depend_datasets_relate_dataset(dataset_id):
    sql = """ select a.id, a.name, a.modified_on from dap_bi_flow a LEFT JOIN dap_bi_flow b on a.depend_flow_id = b.id
 WHERE b.id = %(dataset_id)s """

    params = {'dataset_id': dataset_id}
    with get_db() as db:
        return db.query(sql, params)


def get_mobile_subscribe_relate_dataset(dataset_id):
    sql = """ select b.id, b.subject_email as name, b.modified_on as last_time
          from dap_bi_dashboard_email_subscribe b where b.dataset_ids like %(dataset_id)s """
    params = {
        "dataset_id": '%' + dataset_id + '%'
    }
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_by_level_code(query_level_codes):
    """
    根据层级编码获取所有数据集名称
    :param set query_level_codes:
    :return:
    """

    level_code_params = {}
    level_code_sql_list = []
    for query_level_code in query_level_codes:
        level_key = 'level_' + str(query_level_code).replace('-', '')
        level_code_sql_list.append('level_code like %(' + level_key + ')s')
        level_code_params[level_key] = str(query_level_code) + '%'
    level_code_sql = ' or '.join(level_code_sql_list)
    level_code_sql = '(' + level_code_sql + ')'
    sql = 'select `id`,`name`,level_code from dap_bi_dataset ' 'where  `type` = %(dataset_type)s and '
    new_sql = sql + level_code_sql
    params = {'dataset_type': DatasetType.Folder.value}
    new_params = dict(params, **level_code_params)

    with get_db() as db:
        return db.query(new_sql, new_params)


def get_datasets_by_level_code(query_level_codes):
    """
    根据层级编码获取所有数据集名称
    :param set query_level_codes:
    :return:
    """

    level_code_sql_list = []
    for query_level_code in query_level_codes:
        level_code_sql_list.append('`level_code` like \'' + query_level_code['level_code'] + '%\'')
    level_code_sql = ' or '.join(level_code_sql_list)
    level_code_sql = '(' + level_code_sql + ')'
    sql = 'select `id`,`name`,level_code from dap_bi_dataset ' 'where '
    new_sql = sql + level_code_sql

    with get_db() as db:
        return db.query(new_sql)


def dataset_table_exists(table_name):
    """
    数据表是否存在
    :param table_name:
    :return:
    """
    with get_data_db() as db:
        sql = adapter_sql('cmc.table_is_exist', db.db_type)
        return db.query_scalar(sql, {'table_name': table_name})


def get_limit_sql(sql, data_limit):
    pattern = r';|limit.*'
    limit_str = re.findall(r'limit.*', sql, flags=re.I)
    if limit_str:
        limit_num = int(re.subn(r'limit', ' ', limit_str[0], flags=re.I)[0].strip())
        if limit_num > data_limit:
            sql = re.subn(pattern, ' ', sql, flags=re.I)[0].strip() + ' LIMIT ' + str(data_limit)
    else:
        sql = re.subn(pattern, ' ', sql, flags=re.I)[0].strip() + ' LIMIT ' + str(data_limit)
    return sql


def update_dataset_graph_config(dataset_id, node_data, link_data):
    with get_db() as db:
        db.delete("dap_bi_dataset_graph_table", {'dataset_id': dataset_id}, commit=False)
        if node_data:
            db.insert_multi_data("dap_bi_dataset_graph_table", node_data, list(node_data[0].keys()), commit=False)

        db.delete("dap_bi_dataset_graph_table_relation", {'dataset_id': dataset_id}, commit=False)
        if link_data:
            db.insert_multi_data("dap_bi_dataset_graph_table_relation", link_data, list(link_data[0].keys()), commit=False)
        db.commit()


def update_dataset_tables_collection(dataset_id, list_data):
    """
    更新 dataset_tables_collection
    :param dataset_id:
    :param datas:
    :return:
    """
    fields = [
        'id',
        'dataset_id',
        'from_alias_name',
        'from_table_name',
        'from_id',
        'to_table_name',
        'to_id',
        'to_alias_name',
        'join_type',
        'join_fields',
        'from_table_type',
        'to_table_type',
    ]
    with get_db() as db:
        db.delete('dap_bi_dataset_tables_collection', {'dataset_id': dataset_id})
        return db.replace_multi_data('dap_bi_dataset_tables_collection', list_data, fields, condition_field=['id']) > 0


def update_dataset_filter(dataset_id, list_data):
    """
    更新 dataset_filter
    :param dataset_id:
    :param list_data:
    :return:
    """
    fields = [
        'id',
        'dataset_id',
        'logical_relation',
        'table_id',
        'table_name',
        'col_name',
        'col_type',
        'operator',
        'col_value',
        'filter_type',
        'json_value'
    ]
    with get_db() as db:
        db.delete('dap_bi_dataset_filter', {'dataset_id': dataset_id})
        return db.replace_multi_data('dap_bi_dataset_filter', list_data, fields, condition_field=['id']) > 0


def get_dataset_folders(level_code):
    """
    根据level_code获取上级文件夹
    :param level_code:
    :return:
    """
    sql = 'SELECT * FROM dap_bi_dataset WHERE `type` = %(dataset_type)s AND `level_code` like %(level_code)s '
    with get_db() as db:
        return db.query(sql, {"dataset_type": DatasetType.Folder.value, "level_code": level_code + "%"})


def get_dataset_by_type(dataset_type) -> list:
    sql = "SELECT * FROM dap_bi_dataset WHERE `type` = %(dataset_type)s"
    with get_db() as db:
        return db.query(sql, {"dataset_type": dataset_type})


def get_check_clean_status(table_names):
    """
    获取清洗表
    :param table_name:
    :return:
    """
    if len(table_names) == 1:
        sql = """ select TableName from dap_bi_dmp_data_clean_status where  lower(TableName) = '{table_name}'""".format(
            table_name=table_names[0]
        )
    else:
        table_name_str = ','.join("'{}'".format(table_name) for table_name in table_names)
        sql = """ select TableName from dap_bi_dmp_data_clean_status where lower(TableName) in ({table_name_str}) """.format(
            table_name_str=table_name_str
        )
    return sql


def get_feed_relate_dataset_id(dataset_id):
    sql = ''' select subject_email, type, modified_on from dap_bi_dashboard_email_subscribe
            WHERE dataset_ids LIKE %(dataset_id)s '''
    with get_db() as db:
        return db.query(sql, {'dataset_id': "%" + dataset_id + "%"})


def get_ground_dataset(dataset_ids):
    sql = '''select id, table_name from dap_bi_dataset WHERE (connect_type <> '直连' or connect_type is null) and
            type <> 'api' and id in %(dataset_ids)s'''
    with get_db() as db:
        return db.query(sql, {"dataset_ids": dataset_ids})


def get_field_type(table_name):
    sql = '''desc {table_name};'''.format(table_name=table_name)
    with get_data_db() as db:
        fields = db.query(sql)
        return {field.get("Field"): field.get("Type") for field in fields}


def get_exist_by_table_name(table_name):
    sql = '''show index from {table_name};'''.format(table_name=table_name)
    with get_data_db() as db:
        indexs = db.query(sql)
        return [index.get("Key_name") for index in indexs] or []


def get_user_dataset_permission_field_value(user_code, table_name, associated_field_name, user_code_field):
    with get_data_db() as db:
        sql = f"select `{associated_field_name}` as org_name from `{table_name}` where `{user_code_field}` = %(user_code)s "
        return db.query(sql, {'user_code': user_code})


def get_dataset_permission_list():
    with get_db() as db:
        sql = '''select p.id,p.name,p.dataset_id,ds.name as dataset_name,ds.table_name as dataset_table_name,p.`type`,
               p.user_code_field_id,dsf_user_code_field.origin_col_name as user_code_field_name,
               p.permission_associated_field_id,dsf_associated_field.origin_col_name as permission_associated_field_name,
               p.created_on,p.created_by,p.modified_on,p.modified_by
               from dap_bi_dataset_permission p
               join dap_bi_dataset ds on p.dataset_id=ds.id
               join dap_bi_dataset_field dsf_user_code_field on dsf_user_code_field.id=p.user_code_field_id
               join dap_bi_dataset_field dsf_associated_field on dsf_associated_field.id=p.permission_associated_field_id
               order by p.created_on desc '''
        return db.query(sql)


def get_not_directly_id_list():
    with get_db() as db:
        sql = "select id from dap_bi_dataset where type = 'SQL' and (connect_type != '直连' or connect_type is null)"
        return db.query(sql)


def indicator_search(key):
    sql = '''
            select id,dataset_id,alias_name from dap_bi_dataset_field df 
            where dataset_id in (select id from dap_bi_dataset where external_type = 'pulsar_indicator') 
            and field_group ='度量' 
            and visible = 1
            and alias_name like %(key)s
        '''
    with get_db() as db:
        return db.query(sql, {"key": "%" + key + "%"})


def delete_dataset_schedule_fields(dataset_id):
    sql = ''' delete from  dap_bi_dataset_schedule_page_field where dataset_id = %(dataset_id)s'''
    with get_db() as db:
        return db.exec_sql(sql, {'dataset_id': dataset_id})


def add_dataset_schedule_fields(data):
    if not data:
        return
    with get_db() as db:
        return db.insert_multi_data('dap_bi_dataset_schedule_page_field', data, list(data[0].keys()))


def get_dataset_schedule_fields(dataset_id):
    sql = '''select * from  dap_bi_dataset_schedule_page_field where dataset_id = %(dataset_id)s'''
    with get_db() as db:
        return db.query(sql, {'dataset_id': dataset_id}) or []


def get_excel_dataset_fields(dataset_id):
    with get_db() as db:
        sql = """
            select `col_name`,`data_type`,`format`,`type`,`alias_name` from dap_bi_dataset_field
            where `dataset_id` = %(dataset_id)s  and type=\'普通\' ORDER BY `rank`
            """
        return db.query(sql, {'dataset_id': dataset_id}) or []

