#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/03/11 10:43
# <AUTHOR> caoxl
# @File     : query_structure_builder_v3.py
# pylint: disable=R0201
from base.enums import DBEngine
from dmplib.utils.errors import UserError
from . import external_subject_utils
from self_service import external_service as external_self_service


class QueryStructureRebuilderV3:
    def __init__(self, original_query_structure, external_subject_ids, dataset_data, dataset_fields, table_name, external_subject):
        self.original_query_structure = original_query_structure
        self.external_subject_ids = external_subject_ids
        self.dataset_data = dataset_data
        self.dataset_fields = dataset_fields
        self.table_name = table_name
        self.external_subject = external_subject

        if not self.table_name:
            if len(self.external_subject_ids) > 1:
                raise UserError(message='当前不支持多主题合并查询')
            if len(self.external_subject_ids) == 0:
                raise UserError(message='服务器内部错误，主题不存在')
        self.external_subject_id = self.external_subject_ids[0]

    def build_structure(self):
        """"""
        from .simple_subject_structure_rebuilder import SimpleSubjectStructureRebuilder

        rebuilder = SimpleSubjectStructureRebuilder(
            self.original_query_structure,
            self.external_subject_id,
            self.dataset_data,
            self.dataset_fields,
            self.table_name,
        )
        return rebuilder.build_structure()
