#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from base.dmp_constant import EXTERNAL_CUBE_SUBJECT_DETAIL_ALIAS
from dataset.query.external_subject_query.multi_dim_agg_rebuilder import MultiDimAggRebuilder
from dataset.query.external_subject_query.multi_dim_detail_rebuilder import MultiDimDetailRebuilder

class QueryStructureRebuilder:
    def __init__(self, original_query_structure, external_subject_id, dataset_data, dataset_fields, raw_external_subject):
        self.original_query_structure = original_query_structure
        self.external_subject_id = external_subject_id
        self.dataset_data = dataset_data
        self.dataset_fields = dataset_fields
        self.raw_external_subject = raw_external_subject
        self.detail_mode = False

    def generate_structure(self):
        """区分明细模式"""
        self._preprocess_flag()
        if self.detail_mode:
            rebuilder = MultiDimDetailRebuilder
        else:
            rebuilder = MultiDimAggRebuilder
        return rebuilder(
            self.original_query_structure,
            self.external_subject_id,
            self.dataset_data,
            self.dataset_fields,
            self.raw_external_subject,
        ).generate_structure()

    def _remove_field_from_query_structure(self, field_idx):
        self.original_query_structure.select = [*self.original_query_structure.select[:field_idx], *self.original_query_structure.select[field_idx+1:]]

    def _preprocess_flag(self):
        # 明细模式
        field_idx, self.detail_mode = self._check_detail_mode()
        if self.detail_mode:
            # 区分明细模式
            self._remove_field_from_query_structure(field_idx)

    def _check_detail_mode(self):
        """检查是否是明细模式"""
        for idx, slice in enumerate(self.original_query_structure.select):
            if slice.alias == EXTERNAL_CUBE_SUBJECT_DETAIL_ALIAS:
                return idx, True
        return 0, False
