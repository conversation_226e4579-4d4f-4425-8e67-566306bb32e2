from typing import List, Tuple, Dict
from base.enums import DatasetFieldType
from dataset.query.external_subject_query.external_subject_utils import BaseFieldInfo
from dataset.query.external_subject_query.models import (
    ESTable,
    ESField,
    ESRelation,
    ESTableCategory,
    ESFieldType,
)
from dataset.query.result_data import MultiDimDataException


class ExternalSubject:
    def __init__(self):
        self.table_map: Dict[str, ESTable] = None
        self.field_map: Dict[BaseFieldInfo, ESField] = None
        self.relations: List[ESRelation] = None
        self.relation_map: Dict[Tuple[str, str], Tuple[str, str]] = None

    def build_data(self, raw, dataset_field_map):
        """加载数据, 构建基础查询字典"""
        if not raw:
            raise MultiDimDataException(msg='数据错误: 外部主题不存在！')

        # load tables
        self.table_map = {raw_table.get('table_name'): ESTable(raw_table) for raw_table in raw.get('tables')}
        # load relations
        self.relations = [ESRelation(r) for r in raw.get('relations')]
        self.relation_map = {}
        for item in self.relations:
            self.relation_map[(item.from_table, item.to_table)] = (item.from_field, item.to_field)
            self.relation_map[(item.to_table, item.from_table)] = (item.to_field, item.from_field)

        # load fields
        self.field_map = {}
        for item in raw.get('table_fields'):
            table_name = item.get('table_name')
            table: ESTable = self.table_map.get(table_name)
            if not table:
                raise MultiDimDataException(msg=f'数据错误: 表{table_name}不存在')
            for f in item.get('field_list'):
                field = ESField(table_name, f)
                if field.type == ESFieldType.Description:
                    field.type = ESFieldType.Dimension
                table.fields.append(field)
                dataset_field = dataset_field_map.get(field.dataset_field_id)
                if not dataset_field:
                    raise MultiDimDataException(msg=f'数据错误: 字段<{field.table}.{field.field}>数据集字段不存在')
                field.dataset_field_type = DatasetFieldType(dataset_field['type'])
                field.dataset_field_col_name = dataset_field['col_name']
                if (
                    field.dataset_field_type == DatasetFieldType.CalculateIndicator
                    and table.category not in [ESTableCategory.Ads, ESTableCategory.Dws, ESTableCategory.Dwd]
                ):
                    continue
                self.field_map[field] = field

        self.build_table_blood_relations()
        self.build_field_blood_relations()

    def build_table_blood_relations(self):
        """维度表的直接/间接父子关系"""
        # 构建维度表的直接父子关系
        for relation in self.relations:
            if not relation.parent:
                continue
            parent: ESTable = self.table_map.get(relation.parent)
            if not parent:
                raise MultiDimDataException(msg=f'数据错误: 构建维度表父子关系失败, 表<{relation.parent}>不存在')
            child: ESTable = self.table_map.get(relation.child)
            if not child:
                raise MultiDimDataException(msg=f'数据错误: 构建维度表父子关系失败, 表<{relation.child}>不存在')
            if not (parent.category == ESTableCategory.Dim and child.category == ESTableCategory.Dim):
                continue
            parent.child_tables.add(child.table_name)
            child.parent_tables.add(parent.table_name)

        # 构建维度表的间接父子关系
        for table in self.table_map.values():
            if table.category != ESTableCategory.Dim:
                continue
            self.build_table_indirect_blood_relations(table)

    def build_table_indirect_blood_relations(self, table: ESTable):
        # bfs搜索表的父子表
        queue = [*table.parent_tables]
        while queue:
            parent_table_name = queue.pop(0)
            # 如果已经遍历过, 跳过
            if parent_table_name in table.indirect_parent_tables:
                continue
            # 将表加入到间接父表中
            table.indirect_parent_tables.add(parent_table_name)
            # 入队所有该表的直接父节点
            parent_table = self.table_map.get(parent_table_name)
            queue.extend(parent_table.parent_tables)

        # 以同样的方式搜索子表
        queue = [*table.child_tables]
        while queue:
            child_table_name = queue.pop(0)
            # 如果已经遍历过, 跳过
            if child_table_name in table.indirect_child_tables:
                continue
            # 将表加入到间接子表中
            table.indirect_child_tables.add(child_table_name)
            # 入队所有该表的直接子表
            child_table = self.table_map.get(child_table_name)
            queue.extend(child_table.child_tables)

        if table.indirect_parent_tables & table.indirect_child_tables:
            raise MultiDimDataException(
                msg=f'数据错误: 表<{table.table_name}>的父子关系存在交集'
                f'<{table.indirect_child_tables}>, <{table.indirect_parent_tables}>'
            )

    def build_field_blood_relations(self):
        """构建维度字段的直接父子关系和间接父子关系"""
        # 构建字段的父子关系
        for field in self.field_map.values():
            if field.type != ESFieldType.Dimension:
                continue
            if not field.raw_parent_field:
                continue
            parent_field = self.field_map.get(field.raw_parent_field)
            if (not parent_field) or parent_field.type != ESFieldType.Dimension:
                continue
            field.parent_dim = BaseFieldInfo(table=parent_field.table, field=parent_field.field)
            parent_field.child_dims.add(BaseFieldInfo(table=field.table, field=field.field))

        # 构建字段间接父子关系
        for field in self.field_map.values():
            if field.type != ESFieldType.Dimension:
                continue
            self.build_field_indirect_blood_relations(field)

    def build_field_indirect_blood_relations(self, field: ESField):
        cur_field = field
        # 向上获取字段的间接父字段
        while cur_field.parent_dim:
            if cur_field.parent_dim in field.indirect_parent_dims:
                break
            field.indirect_parent_dims.add(cur_field.parent_dim)

        # bfs搜索字段的间接子字段
        queue = [*field.child_dims]
        while queue:
            base_child_field: BaseFieldInfo = queue.pop(0)
            # 如果子字段在间接子字段中, 跳过
            if base_child_field in field.indirect_child_dims:
                continue
            child_field = self.field_map.get(base_child_field)
            # 将字段加入到间接子字段中
            field.indirect_child_dims.add(base_child_field)
            # 入队所有该字段的直接子字段
            queue.extend(child_field.child_dims)
