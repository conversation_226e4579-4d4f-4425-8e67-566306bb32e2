#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/2/5.
"""
from base.models import BaseModel


class LinkData(BaseModel):
    __slots__ = [
        "from_id",
        "from_table_name",
        "from_alias_name",
        "to_alias_name",
        "to_id",
        "to_table_name",
        "join_type",
        "join_fields",
    ]

    def __init__(self, **kwargs):
        self.from_id = None
        self.from_table_name = None
        self.from_alias_name = None
        self.to_id = None
        self.to_table_name = None
        self.to_alias_name = None
        self.join_type = None

        self.from_version_name = None
        self.to_version_name = None
        # 数据格式：[Jo<PERSON><PERSON><PERSON>("left":"a","operator":"=","right":"b","logical_relation":"AND")]
        self.join_fields = []
        super().__init__(**kwargs)


class Join<PERSON>ield(BaseModel):
    __slots__ = ["left", "operator", "right", "logical_relation", "conditions"]

    def __init__(self, **kwargs):
        self.left = None
        self.operator = None
        self.right = None
        self.logical_relation = None
        self.conditions = []
        super().__init__(**kwargs)
