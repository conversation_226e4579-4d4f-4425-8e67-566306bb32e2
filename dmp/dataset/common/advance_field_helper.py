# -*- coding:utf-8 -*-
import re
import json

from base.enums import DatasetType, DatasetConnectType, DatasetEditMode
from dataset.cache import dataset_meta_cache
from dataset.services import dataset_var_service, dataset_var_parser
from dmplib.utils.errors import UserError
from components.deal_dataset_transition import get_db_engine, format_col


def get_dataset_field_real_name(dataset_id):
    """
    获取数据集字段集合，同时获取数据集字段名:
    若origin_table_name存在，则
    real_col_name=origin_table_name.origin_col_name
    否则
    real_col_name=col_name
    :param dataset_id: 数据集ID
    :return:
    """
    dataset_data = dataset_meta_cache.get_dataset_cache(dataset_id)
    db_engine = get_db_engine(dataset_data)
    dataset_fields = dataset_meta_cache.get_dataset_field_cache(dataset_id)
    link_datas = dataset_meta_cache.get_dataset_link_data_cache(dataset_id)
    for dfield in dataset_fields:
        origin_col_name = dfield.get('origin_col_name', None)
        if origin_col_name and (
            dataset_data.get("type") in [DatasetType.ExternalSubject.value]
            or dataset_data.get('connect_type') == DatasetConnectType.Directly.value
        ):
            origin_table_name = dfield.get('origin_table_name', None)
            if origin_table_name:
                if dataset_data.get('edit_mode') == DatasetEditMode.Graph.value:
                    origin_table_name = get_table_name_for_graph(dfield, origin_table_name)
                else:
                    origin_table_name = get_table_name(link_datas, dfield, origin_table_name)
                dfield['real_col_name'] = format_col(origin_table_name, db_engine) + '.' + format_col(origin_col_name, db_engine)
            else:
                # dfield['real_col_name'] = (origin_table_name + '.' if origin_table_name else '') + origin_col_name
                dfield['real_col_name'] = format_col(origin_col_name, db_engine)
        else:
            dfield['real_col_name'] = format_col(dfield['col_name'], db_engine)
    return dataset_fields


def get_table_name(link_datas, dataset_field, origin_table_name):

    for link_data in link_datas:
        if (
            link_data.from_table_name
            and link_data.from_table_name == dataset_field.get("origin_table_name")
            and link_data.from_id == dataset_field.get("origin_table_id")
        ):
            origin_table_name = "`{}`".format(link_data.from_alias_name)
            break

        if (
            link_data.to_table_name
            and link_data.to_table_name == dataset_field.get("origin_table_name")
            and link_data.to_id == dataset_field.get("origin_table_id")
        ):
            origin_table_name = "`{}`".format(link_data.to_alias_name)
            break

    return origin_table_name if origin_table_name.startswith("`") else f'`{origin_table_name}`'


def get_table_name_for_graph(dataset_field, origin_table_name):
    origin_table_alias_name = dataset_field.get('origin_table_alias_name', None)
    if origin_table_alias_name:
        origin_table_name = '`{}`'.format(origin_table_alias_name)
    return origin_table_name if origin_table_name.startswith("`") else f'`{origin_table_name}`'


def cube_indicator_expression_real_name(expression, is_custm_op_cvt=True):
    table_and_cols = re.findall(r'{(.*?)}\.\[(.*?)\]', expression)
    for table_name, col_name in table_and_cols:
        expression = re.sub(r'{%s}\.\[%s\]' % (table_name, col_name), f'{table_name}.{col_name}', expression)
    table_and_cols = re.findall(r'{(.*?)}\.\[(.*?)\]', expression)
    if len(table_and_cols) > 0:
        raise UserError(message=f'字段不存在: {table_and_cols}')

    cols = re.findall(r'\[(.*?)\]', expression)
    for col_name in cols:
        expression = re.sub(r'\[%s\]' % (col_name), f'{col_name}', expression)
    cols = re.findall(r'\[(.*?)\]', expression)
    if len(cols) > 0:
        raise UserError(message=f'字段不存在: {cols}')

    return custom_operator_convertor(expression) if is_custm_op_cvt else expression

def snow_indicator_expression_real_name(expression, dataset_field, is_custm_op_cvt=True):
    """
    若origin_table_name存在，则替换[col_name]为origin_table_name.origin_col_name
    否则，替换[col_name]为col_name:
    IF([col3],1,0) => IF(col3,1,0)
    IF([col3],1,0) => IF(origin_table_name.origin_col_name,1,0)
    当is_custm_op_cvt=True: TODAY() => CURDATE(), DAY_DIFF([col1], [col2]) => TIMESTAMPDIFF(DAY, col1, col2)
    不及格 => '不及格', 是+否 => '是+否'
    1+1 => 1+1
    :param expression:
    :param dataset_id:
    :param is_custm_op_cvt: 是否执行定制化函数转换
    :return:
    """
    col_names = re.findall(r'\[(.*?)\]', expression)
    for col_name in col_names:
        real_name = f'`{dataset_field.get("origin_table_name")}`.{col_name}'
        expression = re.sub(f'\[%s\]' % col_name, real_name, expression)

    col_names = re.findall(r'\[(.*?)\]', expression)
    if len(col_names) > 0:
        raise UserError(message='字段不存在: %s' % ','.join(col_names))

    return custom_operator_convertor(expression) if is_custm_op_cvt else expression

from components.analysis_time import AnalysisTimeUtils
from dmplib.hug import g

def expression_real_name(expression, dataset_id, is_custm_op_cvt=True):
    """
    若origin_table_name存在，则替换[col_name]为origin_table_name.origin_col_name
    否则，替换[col_name]为col_name:
    IF([col3],1,0) => IF(col3,1,0)
    IF([col3],1,0) => IF(origin_table_name.origin_col_name,1,0)
    当is_custm_op_cvt=True: TODAY() => CURDATE(), DAY_DIFF([col1], [col2]) => TIMESTAMPDIFF(DAY, col1, col2)
    不及格 => '不及格', 是+否 => '是+否'
    1+1 => 1+1
    :param expression:
    :param dataset_id:
    :param is_custm_op_cvt: 是否执行定制化函数转换
    :return:
    """
    col_names = re.findall(r'\[(.*?)\]', expression)
    AnalysisTimeUtils.recode_time_node('疑似代码位置-5')

    # if str(getattr(g, 'request_data', {}).get('params', {}).get('test_mode')) == '1':
    #     real_names = dataset_meta_cache.get_advance_filed_dataset_real_name_cache(dataset_id)
    #     if real_names is None:
    #         real_names = get_dataset_field_real_name(dataset_id)
    #         dataset_meta_cache.set_advance_filed_dataset_real_name_cache(dataset_id, real_names)
    # else:
    #     real_names = get_dataset_field_real_name(dataset_id)
    real_names = get_dataset_field_real_name(dataset_id)

    AnalysisTimeUtils.recode_time_node('疑似代码位置-6')
    real_names_dict = {rn['col_name']: rn['real_col_name'] for rn in real_names}
    for col_name in col_names:
        real_name = real_names_dict.get(col_name, None)
        if not real_name:
            continue
        # # 添加占位符， 处理mysql,oracle, sqlserver语法的特殊字段
        # if real_name.find("`") == -1:
        #     expression = re.sub(r'\[%s\]' % col_name, f'`{real_name}`', expression)
        # else:
        #     expression = re.sub(r'\[%s\]' % col_name, f'{real_name}', expression)
        expression = re.sub(r'\[%s\]' % col_name, f'{real_name}', expression)

    # col_names = re.findall(r'\[(.*?)\]', expression)
    # if len(col_names) > 0:
    #     raise UserError(message='字段不存在: %s' % ','.join(col_names))

    return custom_operator_convertor(expression) if is_custm_op_cvt else expression


def expression_dict2str(expression_dict, brackets=True):
    """
    旧式expression转字符串:
    [
        {"op":"SUM"},{"op":"("},{"id":"39e6a242-997d-ebfa-de11-2197f58a82f4","title":"土地成交总价"},
        {"op":")"},{"op":"/"},{"op":"input","value":"10000"}
    ]
    转:
    SUM([id])/10000
    或, brackets=False时:
    SUM(id)/10000
    :param expression_dict:
    :param brackets: 是否使用[]包裹col_name
    :return:
    """
    exps = []
    dataset_field_ids = set()
    for itm in expression_dict:
        if 'op' in itm:
            if itm.get('op') == 'input':
                value = get_item_value(itm.get('value'))
                exps.append(value)
            else:
                exps.append(itm.get('op'))
        if 'id' in itm:
            if brackets:
                exps.append('[{' + itm.get('id') + '}]')
            else:
                exps.append('{' + itm.get('id') + '}')
            dataset_field_ids.add(itm.get('id'))
    return ''.join(exps), list(dataset_field_ids)


def get_item_value(item_value):
    try:
        float(item_value)
        return item_value
    except ValueError:
        return '"{value}"'.format(value=item_value)


def custom_operator_convertor(expression):
    """
    定制化表达式函数转换(MYSQL)：
    DAY_DIFF(日期字段1,日期字段2)       =>      TIMESTAMPDIFF(DAY,日期字段1,日期字段2)
    HOUR_DIFF(日期字段1，日期字段2)     =>      TIMESTAMPDIFF(HOUR,日期字段1，日期字段2)
    MINUTE_DIFF(日期字段1，日期字段2)   =>      TIMESTAMPDIFF(MINUTE,日期字段1，日期字段2)
    TODAY()                            =>      CURDATE()
    :param expression: 表达式
    :return:
    """
    # MYSQL操作符
    operators_mysql = {
        'DAY_DIFF(': 'TIMESTAMPDIFF(',
        'HOUR_DIFF(': 'TIMESTAMPDIFF(',
        'MINUTE_DIFF(': 'TIMESTAMPDIFF(',
        'TODAY': 'CURDATE',
    }

    ptn = re.compile(r'(\w+)_DIFF\(|TODAY')
    mobjs = ptn.finditer(expression)

    operator_symbols = [mobj.group() for mobj in mobjs]
    if len(operator_symbols) == 0:
        return expression

    operator_symbol_values = ptn.findall(expression)

    operator_symbols_tuple = zip(operator_symbols, operator_symbol_values)
    for symbol, value in operator_symbols_tuple:
        target = '%s%s%s' % (operators_mysql[symbol], value, ',' if value else '')
        expression = expression.replace(symbol, target)

    return expression


def expression_convertor(expression, brackets=True, is_custm_op_cvt=True):
    """
    旧式expression转字符串:
    [
        {"op":"SUM"},{"op":"("},{"id":"39e6a242-997d-ebfa-de11-2197f58a82f4","title":"土地成交总价"},
        {"op":")"},{"op":"/"},{"op":"input","value":"10000"}
    ]
    转:
    SUM([col_name])/10000
    或, brackets=False时:
    SUM(col_name)/10000
    :param expr_str:
    :param brackets: 是否使用[]包裹col_name
    :param is_custm_op_cvt: 是否执行定制化函数转换
    :return:
    """
    try:
        if isinstance(expression, str):
            expression = json.loads(expression)
        exps, dataset_field_ids = expression_dict2str(expression, brackets)
    except json.decoder.JSONDecodeError:
        msg = '字段表达式json格式错误! 表达式: {}'.format(json.dumps(expression))
        raise UserError(message=msg)
    except:
        msg = '字段旧式表达式解析错误！表达式：{}'.format(json.dumps(expression))
        raise UserError(message=msg)

    if len(dataset_field_ids) == 0:
        return custom_operator_convertor(exps) if is_custm_op_cvt else exps

    dataset_fields = dataset_meta_cache.get_multi_dataset_field_cache(dataset_field_ids)

    # 查找引用数据集ID是否存在
    not_find_dataset_fields = []
    for dataset_field_id in dataset_field_ids:
        if not exists_dataset_fields(dataset_field_id, dataset_fields):
            not_find_dataset_fields.append(dataset_field_id)
    if not_find_dataset_fields and len(not_find_dataset_fields) > 0:
        msg = "高级字段表达式：{exps}，引用的字段ID：{dataset_field_ids}已不存在".format(
            exps=exps, dataset_field_ids=",".join(not_find_dataset_fields)
        )
        raise UserError(message=msg)

    dataset_field_colnames = dict()
    for dfield in dataset_fields:
        dataset_field_colnames[dfield['id']] = dfield['col_name']

    expression_converted = ''.join(exps).format(**dataset_field_colnames)
    return custom_operator_convertor(expression_converted) if is_custm_op_cvt else expression_converted


def exists_dataset_fields(dataset_field_id, dataset_fields):
    """
    判断数据集字段ID是否存在dataset_fields中
    :param dataset_field_id:
    :param dataset_fields:
    :return:
    """
    is_exists = False
    for dataset_field in dataset_fields:
        if dataset_field.get("id") == dataset_field_id:
            is_exists = True
    return is_exists


def transform_var(expression_advance: str, dataset_var_models: None, test_mode=None):
    """
    转换替换高字段表达式中的变量
    例如：[col_name_1]-{:var_1}  -->  [col_name_1]-变量值
    :param str expression_advance:
    :param [] components.query_models.Var dataset_var_models:
    :param bool test_mode:
    :return:
    """
    var_params = dataset_var_parser.DatasetVarParser.get_var_names_from_sql_str(expression_advance)
    dataset_var_models = dataset_var_models or []
    not_exist_params = []
    for param in var_params:
        is_exist = False
        for dataset_var_model in dataset_var_models:
            if dataset_var_model.var_id == param:
                is_exist = True
                var_value = dataset_var_service.parse_dataset_var(dataset_var_model, test_mode)
                expression_advance = (
                    dataset_var_parser.DatasetVarParser.replace_sql_vars_by_var_value(
                        expression_advance, param, var_value
                    )
                    if var_value is not None
                    else expression_advance
                )
        if not is_exist:
            not_exist_params.append(param)
    if not_exist_params:
        msg = "变量【{}】不存在。".format(','.join(not_exist_params))
        raise UserError(message=msg)
    return expression_advance
