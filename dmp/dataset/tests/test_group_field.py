# -*- coding: utf-8 -*-
# pylint: skip-file
"""
Created on 2017年6月16日

@author: chenc04
"""
import json
import logging
import unittest
import os

from base.enums import DatasetType
from components import query_sql_encoder
from components.deal_dataset_transition import deal_exexpression_to_sql
from components.query_models import ModelEncoder
from data_source.services import data_source_service
from dataset import external_query_service
from dataset.common import advance_field_helper
from dataset.query import query_data
from dataset.services.dataset_api_service import DatasetAPIService
from dmplib.utils.errors import UserError

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)

from tests.base import BaseTest
from dataset.models import DatasetGroupFieldModel, DatasetApiContentModel
from dataset.services import dataset_group_field


class TestDatasetMysqlService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='fangzhiadmin_test', account='fangzhiadmin_test')

    def test_get_group_field(self):
        kwargs = {"dataset_field_id": "39ea47c9-6f0e-e17d-8cb8-e5ac5892dd36"}
        r = dataset_group_field.get_group_field(kwargs.get("dataset_field_id"))
        print(r)

    def test_update_group_field(self):
        kwargs = {
            "id": "39ea47c9-6f0e-e17d-8cb8-e5ac5892dd36",
            "dataset_id": "39e42e0f-c1f2-93f4-a5cc-8d1e18318654",
            "origin_dataset_field_id": "39e42e0f-c257-2f9b-e8b1-8dfd6dd77d06",
            "alias_name": "新字段名称2",
            "group_type": "condition",
            "expression": {
                "default_name": "未分组",
                "groups": [
                    {
                        "name": "分组1",
                        "logic": "and",
                        "conditions": [
                            {"operator": "=", "col_value": "21"},
                            {"operator": "!=", "col_value": "23"},
                            {"operator": "like", "col_value": "%a%"},
                            {"operator": "not like", "col_value": "%b%"},
                        ],
                    },
                    [
                        {
                            "name": "分组2",
                            "logic": "or",
                            "conditions": [
                                {"operator": "=", "col_value": "21"},
                                {"operator": "!=", "col_value": "23"},
                                {"operator": "like", "col_value": "%a%"},
                                {"operator": "not like", "col_value": "%b%"},
                            ],
                        }
                    ],
                ],
            },
        }
        model = DatasetGroupFieldModel(**kwargs)
        r = dataset_group_field.update_group_field(model)
        print(r)

    def test_delete_group_field(self):
        kwargs = {"dataset_field_id": "39ea47c9-6f0e-e17d-8cb8-e5ac5892dd36"}
        r = dataset_group_field.delete_group_field(kwargs.get("dataset_field_id"))
        print(r)

    def test_test_group_field_to_sql_1(self):
        query_structure_json = """{
            "where": [],
            "dataset": "",
            "having": [],
            "object": [{"name": "dataset_b5925fe7e4926407", "alias": "dataset_b5925fe7e4926407"}],
            "select": [
                {
                    "specifier": "",
                    "props": [],
                    "func": "",
                    "alias": "QY_7721104685",
                    "operator": "",
                    "obj_name": "dataset_b5925fe7e4926407",
                    "prop_name": "FZQY_15973276423",
                    "prop_ref": "39ea6a8c-53aa-0a5d-23ac-64c2e301fe3e",
                    "value": ""
                },
                {
                    "specifier": "",
                    "props": [],
                    "func": "",
                    "alias": "SCZT_12480925146",
                    "operator": "",
                    "obj_name": "dataset_b5925fe7e4926407",
                    "prop_name": "SCZT_12480925146",
                    "value": ""
                },
                {
                    "specifier": "",
                    "props": [
                        {
                            "specifier": "",
                            "props": [
                                {
                                    "specifier": "",
                                    "props": [],
                                    "func": "",
                                    "alias": "",
                                    "operator": "",
                                    "obj_name": "dataset_b5925fe7e4926407",
                                    "prop_name": "JE_6612556773",
                                    "value": ""
                                },
                                {
                                    "specifier": ",",
                                    "props": [],
                                    "func": "",
                                    "alias": "",
                                    "operator": "",
                                    "obj_name": "",
                                    "prop_name": "",
                                    "value": 0
                                }
                            ],
                            "func": "ifNone",
                            "alias": "",
                            "operator": "",
                            "obj_name": "",
                            "prop_name": "",
                            "value": ""
                        }
                    ],
                    "func": "sum",
                    "alias": "sum_JE_6612556773",
                    "operator": "",
                    "obj_name": "",
                    "prop_name": "",
                    "value": ""
                }
            ],
            "limit": {
                "row": 100,
                "offset": ""
            },
            "group_by": [
                {
                    "specifier": "",
                    "props": [],
                    "func": "",
                    "alias": "",
                    "operator": "",
                    "obj_name": "dataset_b5925fe7e4926407",
                    "prop_ref": "39ea47c9-6f0e-e17d-8cb8-e5ac5892dd36",
                    "prop_name": "QY_7721104685",
                    "value": ""
                },
                {
                    "specifier": "",
                    "props": [],
                    "func": "",
                    "alias": "",
                    "operator": "",
                    "obj_name": "dataset_b5925fe7e4926407",
                    "prop_ref": "39ea47c9-6f0e-e17d-8cb8-e5ac5892dd36",
                    "prop_name": "SCZT_12480925146",
                    "value": ""
                }
            ],
            "order_by": []
        }"""
        dataset_data = {}
        from dataset.query.query_dataset_service import QueryDatasetService

        new_query_structure = QueryDatasetService().generate_query_structure(query_structure_json, dataset_data)
        print(new_query_structure)

        # 调用python 解析sql
        sql_python = query_sql_encoder.encode_query(new_query_structure)
        # 调用php sdk解析sql
        sql_php = ""
        if dataset_data.get('type') == DatasetType.Api.value:
            try:
                content = json.loads(dataset_data.get("content"))
                data_source_model = data_source_service.get_data_source(content['data_source_id'])
                api = DatasetAPIService.get_api(data_source_model)
                content_model = DatasetApiContentModel(**content)
                params = DatasetAPIService.merge_params(data_source_model.conn_str.params, content_model.params)
                sql_php = api.get_data_debug(params, json.loads(json.dumps(new_query_structure, cls=ModelEncoder)))
            except Exception as e:
                logging.exception(e)
                raise UserError(message="php解析sql错误：" + str(e))

        data = []
        try:
            data = query_data.get_query_data()
        except Exception as e:
            logging.exception(e)
            logging.exception(e)

        result = {
            "query_structure": json.loads(json.dumps(new_query_structure, cls=ModelEncoder)),
            "sql_python": sql_python,
            "sql_php": sql_php,
            "data": data[:100] if data else data,
        }
        return result

    def test_group_field_to_sql(self):
        expression = {
            "default_name": "其他",
            "origin_dataset_field_id": "39e42e0f-c257-2f9b-e8b1-8dfd6dd77d06",
            "groups": [
                {
                    "name": "运营者",
                    "logic": "and",
                    "conditions": [
                        {"operator": "like", "col_value": "%运%"},
                        {"operator": "like", "col_value": "%运营%"},
                        {"operator": "not like", "col_value": "%ff1%"},
                    ],
                },
                {
                    "name": "管理",
                    "logic": "or",
                    "conditions": [
                        {"operator": "like", "col_value": "%管理%"},
                        {"operator": "not like", "col_value": "%运%"},
                    ],
                },
            ],
        }
        field_name = "分组字段名称"
        group_str = ""
        col_name = "name"
        for group in expression.get("groups"):
            _condition = ''
            logic = group.get("logic")
            for condition in group.get("conditions"):
                temp = """ `{col_name}` {operator} "{col_value}" """.format(
                    col_name=col_name, operator=condition.get("operator"), col_value=condition.get("col_value")
                )
                if not _condition:
                    _condition = temp
                else:
                    _condition += logic + _condition
            single_group_str = """ WHEN ({condition}) THEN "{name}" """.format(
                condition=_condition, name=group.get("name")
            )
            if not group_str:
                group_str = " CASE %s" % single_group_str
            else:
                group_str += single_group_str
        else:
            if group_str:
                group_str = """ ({group_str} ELSE "{default_name}" END) as "{field_name}" """.format(
                    group_str=group_str, default_name=expression.get("default_name"), field_name=field_name
                )

        print(group_str)

    def test_deal_number_type(self):
        # 日期
        col_name = "col1"
        dataset_id = "39e630b3-0977-98cd-0b9e-3b346bf071d1"
        dataset = external_query_service.get_dataset(dataset_id)
        dataset_field = dataset_group_field.deal_group_field_by_dataset_filed(dataset_id, col_name)
        raw = deal_exexpression_to_sql(dataset, dataset_field)
        print(raw)

    def test_sql(self):
        dataset_id = '39efaa57-340b-ab2a-39db-3c5cbbea4a03'
        col_name = "FZB_12234120112"
        dataset = external_query_service.get_dataset(dataset_id)
        dataset_fields = external_query_service.get_dataset_fields(dataset_id)
        dataset_fields = {dataset_field.get("col_name"): dataset_field for dataset_field in dataset_fields}

        dataset_field = dataset_group_field.deal_group_field_by_dataset_filed(dataset_fields, col_name=col_name)

        r = deal_exexpression_to_sql(dataset, dataset_field)
        print(r)
        assert isinstance(r, str)


class TestDatasetGroupFiledService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev', account='admin')

    def test_delete_group_field(self):
        dataset_field_id = '39ea8525-c6dd-ff1a-1c05-026102e287b6'

        is_deleted = dataset_group_field.delete_group_field(dataset_field_id)

        self.assertEqual(True, is_deleted)


if __name__ == '__main__':
    # unittest.main()
    # 构造测试集
    suite = unittest.TestSuite()
    suite.addTest(TestDatasetGroupFiledService("test_delete_group_field"))
    # 执行测试
    runner = unittest.TextTestRunner()
    runner.run(suite)
