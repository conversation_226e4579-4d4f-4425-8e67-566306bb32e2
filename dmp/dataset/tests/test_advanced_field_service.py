#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    <NAME_EMAIL> on 2017/7/12.
"""
import unittest
import json

from dmplib.tests.base_test import BaseTest
from dataset.models import DatasetModel, DatasetFieldModel
from dataset.services import advanced_field_service, dataset_field_service, dataset_service
from dataset.repositories import dataset_field_repository
from dataset.common import advance_field_helper
from dmplib.hug import g
from dataset.query.result_data import DatasetQuerySqlException
from dmplib.utils.errors import UserError


class TestDashBoardService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='admin')

    def test_get_dashboard(self):
        g.userid = "4230bc6e-69e6-46a9-a39e-b929a06a84e8"
        kwargs = {
            "id": "",
            "rank": "",
            "dataset_id": "39e72d59-68a9-5a06-9e0b-0a3f01e48aa3",
            "alias_name": "1",
            "visible": 1,
            "format": "",
            "field_group": "度量",
            "data_type": "数值",
            "expression": "[{\"id\":\"39e72d59-ebb4-68fe-ec1b-adfa1d491166\",\"title\":\"a\"},{\"op\":\"+\"},{\"op\":\"input\",\"value\":\"1\"}]",
            "mode": "edit",
        }
        result = advanced_field_service.save_dataset_field(DatasetFieldModel(**kwargs))
        self.assertIsNone(result)


class TestSaveDatasetField(BaseTest):
    def __init__(self, method_name='runTest'):
        # super().__init__(method_name, code='dev', account='admin')
        super().__init__(method_name, code='test', account='test')

    def test_format_advanced_fields(self):
        expression_dict = [
            {"op": "IF"},
            {"op": "("},
            {"id": "39e53ff3-61cf-f2e3-7c22-7ca9f2baad9c", "title": "人均消费"},
            {"op": ">="},
            {"op": "input", "value": "300"},
            {"op": ","},
            {"op": "input", "value": "'Y'"},
            {"op": ","},
            {"op": "input", "value": "N"},
            {"op": ")"},
        ]

        d = advanced_field_service.format_advanced_fields(expression_dict)

        self.assertEqual(d, 'IF(人均消费>=300,Y,N)')

    def test_save_dataset_field_TODAY(self):
        g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
        kwargs = {
            "id": "",
            "rank": "",
            "dataset_id": "39e53ff3-6164-ee06-f1b1-e4b3ecd8534b",
            "alias_name": "测试",
            "visible": 1,
            "format": "",
            "field_group": "度量",
            "data_type": "数值",
            "expression": "[]",
            "dashboard_id": "39ea6fa1-6d9c-a6d3-9179-1217d6188090",
            "expression_advance": "[col000]+123",
            "mode": "edit",
        }

        result = advanced_field_service.save_dataset_field(DatasetFieldModel(**kwargs))
        self.assertEqual('A_CS_5134645036', result)

    def test_save_dataset_field_CASEWHEN(self):
        g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
        kwargs = {
            "id": "",
            "rank": "",
            "dataset_id": "39eaae3d-5870-614d-df9f-3a0637d83cbd",
            "alias_name": "case [签约率]",
            "visible": 1,
            "format": "",
            "field_group": "度量",
            "data_type": "数值",
            "expression": "[]",
            "dashboard_id": "39eadb87-0989-d97b-7741-d703e0d5d51d",
            "expression_advance": "case [QYL_14861211054]\n when [QYL_14861211054]>0 then '合格'\n when [QYL_14861211054]>0.5 then '优秀'\n else '不合格' end",
            "mode": "edit",
        }
        g.cookie = {'token': 'sadfafsdfggsdfgk'}
        result = advanced_field_service.save_dataset_field(DatasetFieldModel(**kwargs))
        self.assertEqual('A_CASERJXF_14840803801', result)

    def test_validate_expression(self):
        g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'

        expression = '[{"op": "DAY_DIFF"}, {"op": "("}, {"title": "col2", "id": "39e88c73-0af5-bb09-60a3-eb04573bf519"}, {"op": ","}, {"title": "col2", "id": "39e88c73-0af5-bb09-60a3-eb04573bf519"}, {"op": ")"}]'
        dataset_id = '39e88c73-0a67-2d59-ff5d-77cd5bf2a678'
        expression_str = advance_field_helper.expression_convertor(json.loads(expression))
        successed, msg = advanced_field_service.validate_expression(expression_str, dataset_id)
        self.assertEqual(True, successed)

    def test_validate_expressions(self):
        g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
        dataset_fields = dataset_field_repository.get_dataset_field_by_type(
            ('普通高级', '计算高级'), ('expression', 'dataset_id')
        )
        print(len(dataset_fields))
        # print(dataset_fields_json)

        for dfield in dataset_fields:
            try:
                expression = json.loads(dfield['expression'])
                dataset_id = dfield['dataset_id']

                expression_str = advance_field_helper.expression_convertor(expression)
                successed, msg = advanced_field_service.validate_expression(expression_str, dataset_id)

            except DatasetQuerySqlException as e:
                print(False, e)
            except UserError as e:
                print(False, e)
            except Exception as e:
                print(False, e)
            else:
                print(successed, msg, expression_str)

    def test_expression_convertor(self):
        """
        测试表达式转换函数
        :return:
        """
        expression_str = '[{"op": "DAY_DIFF"}, {"op": "("}, {"title": "col2", "id": "39e88c73-0af5-bb09-60a3-eb04573bf519"}, {"op": ","}, {"title": "col2", "id": "39e88c73-0af5-bb09-60a3-eb04573bf519"}, {"op": ")"}]'
        exp = advance_field_helper.expression_convertor((expression_str))
        print(exp)
        self.assertEqual(exp, 'IF([col3]>=20,"YES","NO")')

    def test_get_dataset_field(self):
        result = dataset_field_service.get_dataset_field('39e90c3c-e788-b195-d089-d84436ca27fe')
        print(result)

    def test_for_sentry_issue_f4a53f9eb21a4de0aa3b3cbe9ab63b04(self):
        g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
        from dataset.services import dataset_define_service

        kwargs = {
            "field": [
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "name",
                    "origin_col_name": "name",
                    "id": "39eacd9d-efda-ce9a-786e-35ef0abbbdb7",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "NAME_3939044202",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "urlslug",
                    "origin_col_name": "urlslug",
                    "id": "39eacd9d-efdc-34e8-957f-e5a4b78bb05d",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "URLSLUG_4570090711",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "ID",
                    "origin_col_name": "ID",
                    "id": "39eacd9d-efde-997f-21d6-033398f29e1e",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "ID_3555920470",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "ALIGN",
                    "origin_col_name": "ALIGN",
                    "id": "39eacd9d-efe0-2559-45c8-6562fcf24dfa",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "ALIGN_4104391476",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "EYE",
                    "origin_col_name": "EYE",
                    "id": "39eacd9d-efe2-9c61-6b3c-cb7cfd07b3a1",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "EYE_3735816876",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "HAIR",
                    "origin_col_name": "HAIR",
                    "id": "39eacd9d-efe3-03e9-d9e4-3c71ca038b71",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "HAIR_3916827373",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "SEX",
                    "origin_col_name": "SEX",
                    "id": "39eacd9d-efe5-61d4-cdab-18a85f99dd33",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "SEX_3737193145",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "GSM",
                    "origin_col_name": "GSM",
                    "id": "39eacd9d-efe6-c5a4-4ef0-914a67c64b35",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "GSM_3735947952",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "ALIVE",
                    "origin_col_name": "ALIVE",
                    "id": "39eacd9d-efe9-4822-aebc-bd966c490564",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "ALIVE_4105767738",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "APPEARANCES",
                    "origin_col_name": "APPEARANCES",
                    "id": "39eacd9d-efeb-c7ee-4e16-72bcf6f62e52",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "APPEARANCES_5333519596",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "note": None,
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "FIRST_APPEARANCE",
                    "origin_col_name": "FIRST_APPEARANCE",
                    "id": "39eacd9d-efec-385e-3735-53f25eab7bea",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "FIRSTAPPEARANCE_6527389312",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
                {
                    "data_type": "字符串",
                    "origin_table_id": None,
                    "visible": 1,
                    "alias_name": "YEAR",
                    "origin_col_name": "YEAR",
                    "id": "39eacd9d-efee-a971-715b-81d77df0578f",
                    "origin_table_name": None,
                    "origin_table_comment": None,
                    "col_name": "YEAR_3921021690",
                    "origin_table_alias_name": None,
                    "inspection_rules": "",
                    "field_group": "维度",
                    "origin_field_type": None,
                    "format": "",
                },
            ],
            "user_group_id": "00000000-0000-0000-1111-000000000000",
            "id": "39eacd9d-9618-bc88-8ee7-107c2802e8b9",
            "content": {
                "data_source_id": "00000000-1111-1111-2222-000000000000",
                "sql": "select name,urlslug,ID,ALIGN,EYE,HAIR,SEX,GSM,ALIVE,APPEARANCES,FIRST_APPEARANCE,YEAR from dc_wikia_data_1",
                "count": 6896,
                "create_table_sql": "create table if not exists dataset_tmp_5c19f46864caa (`NAME_3939044202` varchar(765) ,`URLSLUG_4570090711` varchar(765) ,`ID_3555920470` varchar(765) ,`ALIGN_4104391476` varchar(765) ,`EYE_3735816876` varchar(765) ,`HAIR_3916827373` varchar(765) ,`SEX_3737193145` varchar(765) ,`GSM_3735947952` varchar(765) ,`ALIVE_4105767738` varchar(765) ,`APPEARANCES_5333519596` varchar(765) ,`FIRSTAPPEARANCE_6527389312` varchar(765) ,`YEAR_3921021690` varchar(765) )",
                "tmp_table_name": "dataset_tmp_5c19f46864caa",
            },
            "connect_type": "直连",
            "name": "dc_wikia_data_1",
            "parent_id": "39eacd67-1857-2e47-63d8-3905ae14a320",
            "type": "SQL",
            "flow": None,
        }
        model = DatasetModel(**kwargs)
        dataset_name = dataset_define_service.update_dataset(model.id, model)
        print(dataset_name)

    def test_get_dataset(self):
        dataset_id = '39e90c3c-e788-b195-d089-d84436ca27fe'
        dataset = dataset_service.get_dataset(dataset_id)
        print(dataset)


if __name__ == '__main__':
    # 构造测试集
    suite = unittest.TestSuite()
    suite.addTest(TestSaveDatasetField("test_save_dataset_field_CASEWHEN"))
    # 执行测试
    runner = unittest.TextTestRunner()
    runner.run(suite)
