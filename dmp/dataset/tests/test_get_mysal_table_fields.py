#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import os
import json

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)
from tests.base import BaseTest
from dmplib.saas.project import get_data_db_config
from dmplib.db.mysql_wrapper import SimpleMysql
from dmplib.utils.sql_util import Description
from dataset.services.dataset_service import run_get_data
from dataset.models import DatasetModel


class TestDatasetSubject(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev', account='admin')

    def test_get_table_mysql_table_fields(self):
        table_name = '1zeng_3_copy22'
        sql = 'select * from %s' % table_name
        config = get_data_db_config()
        db_conn = SimpleMysql(
            host=config.get('host'),
            port=config['port'],
            db=config['database'],
            user=config['user'],
            passwd=config['password'],
            charset='latin1',
        )
        with db_conn as db:
            data = db.query(sql)
            descriptions = db.cur.description

        # 获取数据结构
        columns = [Description(*col) for col in list(descriptions)]
        structs = []
        for column in columns:
            struct = {"col_name": column.name, "data_type": column.col_type, "comment": column.alias_name}
            structs.append(struct)

        print(json.dumps(structs, indent=4, ensure_ascii=False))
        print(json.dumps(data, indent=4, ensure_ascii=False))

    def test_run_get_data(self):
        dataset_model = {
            "edit_mode": "sql",
            "type": "SQL",
            "content": '''{"data_source_id":"39ed2233-95da-bada-8313-579e1da0fdb6","sql":"select name,id,address,math,article,chinese,history,english,bit_field,new_numeric,new_bit,name_binary,name_varbinary,mysql,name_varchar,col22,col33,col66 from test_mysql_fields"}''',
            "relation_content": '''{"nodeDataArray":[],"linkDataArray":[]}''',
            "filter_content": [],
        }
        model = DatasetModel(**dataset_model)
        result = run_get_data(model)
        print(json.dumps(result, indent=4, ensure_ascii=False))
