# -*- coding: utf-8 -*-
"""
Created on 2017年6月16日

@author: chenc04
"""
import unittest
import os


os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)

from tests.base import BaseTest
from base.enums import DatasetType
from dataset.services import dataset_define_service, dataset_service
from dataset.models import DatasetModel
from dataset.services.dataset_mysql_service import DatasetMysqlService
from data_source.services import data_source_service


class TestDatasetPostgresqlService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev', account='test')

    def test_run_get_field_values(self):
        data_source_model = data_source_service.get_data_source('********-1111-1111-2222-************')

        dataset_model = DatasetModel()
        dataset_model.type = DatasetType.Sql.value
        dataset_model.content = '{"sql":"select * from dataset_0c24a502e584f51b;"}'
        data_set_service = DatasetMysqlService(dataset_model, data_source_model)
        result = data_set_service.run_get_field_values("col5")

        self.assertIsNotNone(result)
        self.assertIsNotNone(result.get("data"))
        self.assertIsNotNone(result.get("head"))
        self.assertIsNotNone(result.get("field"))

    def test_get_result_data(self):
        data_set_id = '39eacd7f-2702-04a7-4996-0de4b1f2666e'
        result = dataset_service.get_dataset_result_data(data_set_id)
        self.assertIsNotNone(result)

    def test_run_get_data(self):
        dataset_model = DatasetModel()
        dataset_model.type = DatasetType.Sql.value
        content = """
        {"dataset_id":"","data_source_id":"39e9daec-34a6-72a7-e400-3b5528d85ab1","sql":"select * from cc_test"}
        """
        dataset_model.content = content
        result = dataset_service.run_get_data(dataset_model)

        self.assertIsNotNone(result)
        self.assertIsNotNone(result.get("data"))
        self.assertIsNotNone(result.get("head"))
        self.assertIsNotNone(result.get("field"))

    def test_add_dataset(self):
        kwargs = {
            "name": "directly10",
            "connect_type": "直连",
            "type": "SQL",
            "user_group_id": "********-0000-0000-1111-************",
            "content": {
                "data_source_id": "39e51c07-5a2b-78e4-5355-0ac557de15c6",
                "count": 1,
                "sql": "select * from 1pdo;",
            },
            "flow": {"schedule": "0 0 0 ? * * *", "depend_flow_id": "", "status": "启用"},
            "field": [
                {
                    "alias_name": "",
                    "col_name": "aaa",
                    "data_type": "字符串",
                    "visible": 1,
                    "format": "",
                    "field_group": "维度",
                },
                {
                    "alias_name": "",
                    "col_name": "bbb",
                    "data_type": "数值",
                    "visible": 1,
                    "format": "",
                    "field_group": "度量",
                },
            ],
        }
        model = DatasetModel(**kwargs)
        result = dataset_define_service.add_dataset(model)
        self.assertIsNotNone(result)


if __name__ == '__main__':
    unittest.main()
