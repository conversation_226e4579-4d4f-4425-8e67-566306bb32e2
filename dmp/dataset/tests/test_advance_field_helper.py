# -*- coding:utf-8 -*-
"""
测试dataset.common.advance_field_helper模块下的公共方法
"""
import unittest
from dmplib.tests.base_test import BaseTest
from components.query_models import Var
from dataset.common import advance_field_helper


class TestAdvanceFieldHelper(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev', account='admin')

    def test_expression_convertor(self):
        """
        旧式expression转字符串:
        [
            {"op":"SUM"},{"op":"("},{"id":"39e6a242-997d-ebfa-de11-2197f58a82f4","title":"土地成交总价"},
            {"op":")"},{"op":"/"},{"op":"input","value":"10000"}
        ]
        转:
        SUM([col_name])/10000
        或, brackets=False时:
        SUM(col_name)/10000
        """
        expression = '''
        [
            {"op": "IF"}, {"op": "("}, {"id": "39e53ff3-61cf-f2e3-7c22-7ca9f2baad9c", "title": "\u4eba\u5747\u6d88\u8d39"},
            {"op": ">="}, {"op": "input", "value": "20"}, {"op": ","}, {"op": "input", "value": "YES"}, {"op": ","},
            {"op": "input", "value": "NO"}, {"op": ")"}
        ]
        '''
        expression_str = advance_field_helper.expression_convertor(expression)
        self.assertEqual('IF([col3]>=20,"YES","NO")', expression_str)

        expression_str = advance_field_helper.expression_convertor(expression, brackets=False)
        self.assertEqual('IF(col3>=20,"YES","NO")', expression_str)

    def test_expression_real_name(self):
        """
        若origin_table_name存在，则替换[col_name]为origin_table_name.origin_col_name
        否则，替换[col_name]为col_name:
        IF([col3],1,0) => IF(col3,1,0)
        IF([col3],1,0) => IF(origin_table_name.origin_col_name,1,0)
        :return:
        """
        expression = 'IF([col3]>=20,"YES","NO")' 
        dataset_id = '39e53ff3-6164-ee06-f1b1-e4b3ecd8534b'
        expression_str = advance_field_helper.expression_real_name(expression, dataset_id)
        self.assertEqual('IF(col3>=20,"YES","NO")', expression_str)

    def test_transform_var(self):
        """
        转换替换高字段表达式中的变量
        例如：[col_name_1]-{:var_1}  -->  [col_name_1]-变量值
        :return:
        """
        # 1、任意值
        expression_advance = "[col_name_1]-{:var_1}"
        dataset_var_model = Var(**{"var_id": "var_1", "var_type": 3, "value_type": 2, "value": "100"})
        expression_str = advance_field_helper.transform_var(expression_advance, [dataset_var_model])
        self.assertEqual("[col_name_1]-100", expression_str)

        # 2、列表
        expression_advance = "[col_name_1]-{:var_1}"
        dataset_var_model = Var(**{"var_id": "var_1", "var_type": 1, "value_type": 1, "value": ['1', '2', '3']})
        expression_str = advance_field_helper.transform_var(expression_advance, [dataset_var_model])
        self.assertEqual("[col_name_1]- ('1','2','3')", expression_str)

        # 3、区间
        expression_advance = "[col_name_1]-{:var_1}"
        dataset_var_model = Var(**{"var_id": "var_1", "var_type": 1, "value_type": 3, "value": ["1", "100"]})
        expression_str = advance_field_helper.transform_var(expression_advance, [dataset_var_model])
        self.assertEqual("[col_name_1]- '1' AND '100'", expression_str)


if __name__ == '__main__':
    # 构造测试集
    suite = unittest.TestSuite()
    suite.addTest(TestAdvanceFieldHelper("test_expression_real_name"))
    # 执行测试
    runner = unittest.TextTestRunner()
    runner.run(suite)
