#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/6/5 16:58
# <AUTHOR> caoxl
# @File     : dataset_var_value_parser.py
# pylint: disable=R0201
from base.enums import DatasetVarValueSource
from components.query_models import Var
from dmplib.utils.errors import UserError
from dmplib.locale import trans_msg
from dmplib.hug import g


class DatasetVarValueParser:
    # 允许的上下文变量
    enable_context_vars = ["userid", "account"]

    def parse(self, dataset_var: Var):
        """
        解析变量值
        用户自定义变量无需解析
        其他从平台获取的变量需要使用平台值赋给value
        若平台无法获取值 则 赋值默认值
        目前只实现从上下文取值 其他取值暂不实现
        :param dataset_var:
        :return:
        """
        if not dataset_var.value_source:
            return dataset_var.default_value
        if dataset_var.value_source in [DatasetVarValueSource.Userdefined.value, DatasetVarValueSource.Url.value]:
            return dataset_var.value
        func_name = "_".join(("parse", dataset_var.value_source, "value"))
        if not hasattr(self, func_name):
            raise UserError(message=trans_msg("变量值解析方法 {func_name} 不存在").format(func_name=func_name))
        func = getattr(self, func_name)
        return func(dataset_var)

    def parse_context_value(self, dataset_var: Var):
        """
        解析上下文类型的变量
        :param dataset_var:
        :return:
        """
        # default_value参数一直都为None
        val = dataset_var.value
        # 必须在可以获取的范围之列
        if dataset_var.value_identifier not in self.enable_context_vars:
            return val
        # 特殊处理userid (获取系统中的用户id，使用user_id)
        if dataset_var.value_identifier == "userid":
            return self._get_dataset_var_userid(dataset_var)
        if hasattr(g, dataset_var.value_identifier):
            val = getattr(g, dataset_var.value_identifier)
        return val

    def _get_dataset_var_userid(self, dataset_var: Var):
        # userid比较特殊
        # 先从第三方参数获取 若获取不到 再从上下文获取
        # 若都获取不到则取默认值
        user_id = self.get_context_external_param("user_id")
        if user_id is not None:
            return user_id
        if hasattr(g, "userid") and getattr(g, dataset_var.value_identifier):
            return getattr(g, dataset_var.value_identifier)
        return dataset_var.value

    def get_context_external_param(self, key):
        external_params = self._get_context_external_params()
        return external_params.get(key)

    def _get_context_external_params(self):
        result = dict()
        if hasattr(g, "external_params"):
            external_params = getattr(g, "external_params")
            if isinstance(external_params, dict):
                return external_params
            if not isinstance(external_params, str):
                return result
            external_params = external_params.split(",")
            for external_param in external_params:
                external_param = external_param.split("=")
                if isinstance(external_param, list) and len(external_param) > 1:
                    result[external_param[0]] = external_param[1]
        return result
