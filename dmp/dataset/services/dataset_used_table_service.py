# -*- coding: utf-8 -*-
from loguru import logger

from dmplib.hug import g
from dataset.models import DatasetModel
from dataset.repositories import dataset_used_table_repository


def save_used_table(model: DatasetModel):
    try:
        import app_celery
        kwargs = model.get_dict()
        del kwargs["cache_flow"]
        del kwargs["flow"]
        del kwargs["field"]
        kwargs["project_code"] = g.code
        app_celery.save_dataset_used_table.apply_async(kwargs=kwargs, queue='celery-slow')
    except Exception as e:
        logger.error("调用异步解析表名任务失败：%s"%str(e))


def get_used_table_list(table_name=None):
    data = dataset_used_table_repository.get_used_table_list(table_name)
    # 获取所有的数据集id
    dataset_ids = [row.get('dataset_id') for row in data] if data else []
    dashboard_info = dataset_used_table_repository.get_dashboard_by_dataset_ids(dataset_ids) if dataset_ids else []
    dataset_map = {}
    if dashboard_info:
        for dashboard in dashboard_info:
            item = {'dashboard_id': dashboard.get('dashboard_id'), 'dashboard_name': dashboard.get('dashboard_name')}
            if dataset_map.get(dashboard.get('dataset_id')):
                dataset_map.get(dashboard.get('dataset_id')).append(item)
            else:
                dataset_map[dashboard.get('dataset_id')] = [item]
    for row in data:
        row['dashboard_info'] = dataset_map.get(row.get('dataset_id')) \
            if dataset_map.get(row.get('dataset_id')) else []
    return data
