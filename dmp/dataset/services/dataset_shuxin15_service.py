import copy
import time
from datetime import datetime

import hug
import jwt
from loguru import logger
import json
import logging
import hashlib
import os
from typing import Union

from components.pular15_api import Pulsar15Api
from dataset.services.dataset_base_service import record_execute_time
from dmplib import config

from base import repository
from base.dmp_constant import PULSAR_DATASET_KEY
from components.redis_utils import stale_cache
from dataset.common import indicator_common
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.hug import g
from components.redis_utils import RCache
from dmplib.hug.context import DBContext
from components import wait_lock
from components.query_models import QueryStructure, Select, Limit, Object
from base.enums import DatasetType, DatasetFieldDataType, DatasetFieldGroup, DatasetFieldType, DataSourceType, \
    ExternalDatasetType, IndicatorModelOpt, PulsarSyncMode, DatasetEditMode, MysoftShuXinTableType, MysoftShuXinDimType, \
    DatasetVarValueType, DialectType
from dmplib.utils.strings import seq_id
from dataset.repositories import indicator_repository, dataset_field_repository
from dmplib.redis import RedisCache
from dataset.cache import dataset_meta_cache
from dataset.models import (
    PulsarDatasetParamsModel, PulsarDashParamsModel, DetailModelData, DetailModelField, DatasetModel,
    DatasetDataModel, DatasetApiContentModel, DesignContentModel
)
from dataset.services.dataset_service import DatasetBaseService
from components.remove_comment import remove_comment
from components.global_utils import validate_key_word
from dmplib.components.enums import DBType
from base.errors import MoqlCheckError


class Pulsar15DatasetService(DatasetBaseService):  # 数芯1.5版本新接口 支持SQL数据集和引入表形成数据集

    def __init__(self, dataset_model: DatasetModel = None, data_source_model=None):
        super().__init__(dataset_model, data_source_model)
        self._table_names = None
        model = self.data_source_model.conn_str
        self.pulsar_api = Pulsar15Api(
            _from='datasource',
            host=model.api_host,
            app_key=model.app_id,
            app_secret=model.app_secret,
            pulsar_key=model.app_key,
            pulsar_code=model.project_code,
            datasource_name=self.data_source_model.name
        )
        self.dataset_model = dataset_model
        self.content = self.dataset_model.content if isinstance(self.dataset_model.content, dict) else json.loads(
            self.dataset_model.content)

    def _get_dataset_fields(self):
        return dataset_field_repository.get_dataset_field(self.dataset_model.id)

    def get_query_sql_fields_structure(self, sql):
        ret_columns = []
        query_structure = QueryStructure()
        query_structure.select.append(
            Select(
                prop_raw="*"
            )
        )
        query_structure.limit = Limit(
            offset=0,
            row=100
        )
        table = Object()
        table.name = " ({}) ".format(sql)
        table.alias = ' a '
        query_structure.object = [table]

        api_data_structure = self.pulsar_api.get_sql_verify(query_structure)
        columns = api_data_structure.get('columns')

        for column in columns:
            ret_columns.append({'col_name': column.get('name'), 'data_type': column.get('type'), 'comment': ''})
        return ret_columns

    def get_query_table_fields_structure(self, table_id):
        ret_columns = []
        api_data_structure = self.pulsar_api.get_object_structs(code=table_id)
        columns = api_data_structure.get('field')
        for column in columns:
            ret_columns.append({'col_name': column.get('name'), 'data_type': column.get('storage_field_type'),
                                'comment': column.get('name_cn'), 'note': column.get('description')})
        return ret_columns

    def run_get_data_or_struct(self):
        """
        根据sql语句获取数据和结构，测试运行创建临时表（数据集的子类需要重写）  运行原理 和api完全一致
        :return: dataset.models.DatasetDataModel
        """
        # 1、加载数据集内容
        dataset_content = self.load_dataset_content(self.dataset_model.content)
        if dataset_content.get("dataset_id"):
            dataset_id = dataset_content.get("dataset_id")
        elif getattr(self.dataset_model, "id"):
            dataset_id = getattr(self.dataset_model, "id")
        else:
            dataset_id = seq_id()

        design = None
        # 判断不同模式，relation模式下走另外一套
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            link_datas, node_data = self.deal_relation_mode_data(self.dataset_model.relation_content)
            if not node_data:
                raise UserError(message="请至少拖入一个表！")
            columns = self.transfer_node_fields(node_data)

            # 1、测试运行创建表
            # （明源特殊api返回的结构是mysql字段类型, 这里与sql模式不同的原因是需要提前获取hash col_name作为as别名)
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(
                dataset_id, columns, create_table=False
            )
            # 2、根据结构获取数据
            query_indicator = self.get_query_indicator(self.dataset_model.relation_content)
            result_data, total_count = self.get_view_data_or_columns(new_struct, link_datas,
                                                                     filter_content=self.dataset_model.filter_content,
                                                                     query_indicator=query_indicator)
        elif self.dataset_model.edit_mode == DatasetEditMode.Graph.value:
            link, node, graph_filter, fields = self.deal_graph_mode_data(self.dataset_model.graph_content)
            if fields is None:
                if self.dataset_model.field is list:
                    fields = self.dataset_model.field
                else:
                    fields = dataset_field_repository.get_dataset_field(self.dataset_model.id)
            if not node:
                raise UserError(message="请至少拖入一个表！")
            columns = self.transfer_graph_fields(fields)
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(
                dataset_id, columns, create_table=False
            )

            query_indicator = self.graph_get_query_indicator(self.dataset_model.graph_content, columns)
            result_data, total_count = self.get_view_data_or_columns_for_graph(new_struct, link, node,
                                                                               var_content=self.dataset_model.var_content,
                                                                               filter_content=graph_filter,
                                                                               query_indicator=query_indicator)
            design = None
        # sql模式
        else:
            # 1、根据api的sql语句获取数据和字段结构
            result_data, columns, total_count, design = self.get_sql_data_or_columns()

            # 2、测试运行创建表 （明源特殊api返回的结构是mysql字段类型)
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(
                dataset_id, columns, create_table=False
            )
        dataset_data_model = DatasetDataModel()
        dataset_data_model.dataset_id = dataset_id
        dataset_data_model.result_data = result_data
        dataset_data_model.column_struct = columns
        dataset_data_model.total_count = total_count
        dataset_data_model.tmp_table_name = tmp_table_name
        dataset_data_model.new_column_struct = new_struct
        dataset_data_model.create_table_sql = create_table_sql
        dataset_data_model.design = design

        return dataset_data_model

    def run_get_total(self):
        """
        根据sql语句获取数据和结构，测试运行创建临时表（数据集的子类需要重写）  运行原理 和api完全一致
        只获取总数
        :return: dataset.models.DatasetDataModel
        """
        # 1、加载数据集内容
        dataset_content = self.load_dataset_content(self.dataset_model.content)
        if dataset_content.get("dataset_id"):
            dataset_id = dataset_content.get("dataset_id")
        elif getattr(self.dataset_model, "id"):
            dataset_id = getattr(self.dataset_model, "id")
        else:
            dataset_id = seq_id()

        # 判断不同模式，relation模式下走另外一套
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            link_datas, node_data = self.deal_relation_mode_data(self.dataset_model.relation_content)
            if not node_data:
                raise UserError(message="请至少拖入一个表！")
            columns = self.transfer_node_fields(node_data)

            # 1、测试运行创建表
            # （明源特殊api返回的结构是mysql字段类型, 这里与sql模式不同的原因是需要提前获取hash col_name作为as别名)
            _, new_struct, _ = self.create_tmp_table(
                dataset_id, columns, create_table=False
            )
            # 2、根据结构获取数据
            query_indicator = self.get_query_indicator(self.dataset_model.relation_content)
            _, total_count = self.get_view_data_or_columns(
                new_struct, link_datas,
                filter_content=self.dataset_model.filter_content,
                query_indicator=query_indicator, only_total=True
            )
        if self.dataset_model.edit_mode == DatasetEditMode.Graph.value:
            link, node, graph_filter, fields = self.deal_graph_mode_data(self.dataset_model.graph_content)
            if fields is None:
                if self.dataset_model.field is list:
                    fields = self.dataset_model.field
                else:
                    fields = dataset_field_repository.get_dataset_field(self.dataset_model.id)
            if not node:
                raise UserError(message="请至少拖入一个表！")
            columns = fields
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(
                dataset_id, columns, create_table=False
            )

            query_indicator = self.graph_get_query_indicator(self.dataset_model.graph_content, columns)
            _, total_count = self.get_view_data_or_columns_for_graph(new_struct, link, node,
                                                                               var_content=self.dataset_model.var_content,
                                                                               filter_content=graph_filter,
                                                                               query_indicator=query_indicator)
        # sql模式
        else:
            # 1、根据api的sql语句获取数据和字段结构
            total_count = self.get_sql_data_count()

        return total_count


    @record_execute_time()
    def get_view_data_or_columns_for_graph(self, columns, link_datas, node_datas, var_content=None, filter_content=None,
                                           query_indicator=None,
                                           only_total=False):
        """
        图形化模式请求api获取结构
        """
        query_structure = self.get_query_structure_view_for_graph(columns, link_datas, node_datas,
                                                                  var_content=var_content,
                                                                  filter_content=filter_content)
        query_type = 0
        # api 结构体中的过滤条件增进了参数，故进行参数替换
        for where_obj in query_structure.where:
            if where_obj:
                self.relace_where_sql_vars(where_obj, self.dataset_model.var_content, is_api=True)
        if query_indicator:  # 数芯指标打平取数
            for obj in query_structure.object:
                if obj.name == query_indicator.get('table_name'):
                    obj.name = "${query_indicator}"
                    obj.alias = query_indicator.get('table_name')
                    query_type = 3
        # 处理 指标自带的变量，需要将变量值传给数芯
        with_variables = self.append_shuxin15_var(self.dataset_model.var_content)
        # 先返回
        if not only_total:
            result_data = self.get_result_data(query_structure=query_structure, query_indicator=query_indicator,
                                               query_type=query_type, with_variables=with_variables)
        else:
            result_data = []
        new_struct_count = copy.deepcopy(query_structure)
        new_struct_count.select = [Select(prop_raw='count(1)', alias='data_count')]
        total_count = self.get_result_data(new_struct_count, query_indicator=query_indicator, query_type=query_type,
                                           with_variables=with_variables)[0]['data_count']
        return result_data, total_count

    @record_execute_time()
    def get_view_data_or_columns(self, columns, link_datas, filter_content=None, query_indicator=None,
                                 only_total=False):
        """
        视图模式请求api获取结构
        :param dataset_content:
        :return:
        """
        query_structure = self.get_query_structure_view(columns, link_datas, filter_content=filter_content)
        query_type = 0
        # api 结构体中的过滤条件增进了参数，故进行参数替换
        for where_obj in query_structure.where:
            if where_obj:
                self.relace_where_sql_vars(where_obj, self.dataset_model.var_content, is_api=True)
        if query_indicator:  # 数芯指标打平取数
            for obj in query_structure.object:
                if obj.name == query_indicator.get('table_name'):
                    obj.name = "${query_indicator}"
                    obj.alias = query_indicator.get('table_name')
                    query_type = 3
        # 处理 指标自带的变量，需要将变量值传给数芯
        with_variables = self.append_shuxin15_var(self.dataset_model.var_content)
        # 先返回
        if not only_total:
            result_data = self.get_result_data(query_structure=query_structure, query_indicator=query_indicator, query_type=query_type, with_variables=with_variables)
        else:
            result_data = []
        new_struct_count = copy.deepcopy(query_structure)
        new_struct_count.select = [Select(prop_raw='count(1)', alias='data_count')]
        total_count = self.get_result_data(new_struct_count, query_indicator=query_indicator, query_type=query_type,  with_variables=with_variables)[0]['data_count']
        return result_data, total_count

    def append_shuxin15_var(self, vars_data_list):
        with_variables = []
        for var in vars_data_list:
            if not var.get('external_content', ''):  # 非数芯变量绑定
                continue
            external_content_obj = json.loads(var.get('external_content', '{}'))
            var_id = var.get('id')
            value = DatasetBaseService.replace_dataset_sql_var_id_as_values(f'${{{var_id}}}', vars_data_list)
            if value.startswith("'"):
                value = value[1:len(value) - 1]
            var_variable_shuxin15 = {
                "name": external_content_obj.get('name'),
                "scope_type": external_content_obj.get('scope_type'),
                "single_value_content": {},
                "range_value_content": {}
            }
            if var.get('value_type') == DatasetVarValueType.Section.value:  # 只有日期类型区间 value 为 ['2023-01-3100: 00: 00',	'2024-01-3023: 59: 59']
                value_list = value.split(" AND ")
                if len(value_list) != 2:
                    continue
                var_variable_shuxin15['range_value_content'] = {"left": value_list[0].strip().strip("'"), "right": value_list[1].strip().strip("'")}
            elif var.get('value_type') == DatasetVarValueType.List.value:
                value_str = value.strip().strip("()")
                if value_str.startswith("'"):
                    value_list = value_str[1:len(value_str) - 1].split("','")
                else:
                    value_list = value_str.split(",")
                var_variable_shuxin15['sequence_value_content'] = {"value_list": value_list}
            else:
                var_variable_shuxin15['single_value_content'] = {"value": value}
            with_variables.append(var_variable_shuxin15)
        return with_variables


    @record_execute_time()
    def get_query_indicator(self, relation_content):
        if isinstance(relation_content, str):
            try:
                relation_content = json.loads(relation_content)
            except json.JSONDecodeError as e:
                raise Exception('数据集内容解析错误：' + str(e))
        return self.get_query_indicator_internal(relation_content.get('nodeDataArray'))

    @record_execute_time()
    def graph_get_query_indicator(self, relation_content, fields):
        if isinstance(relation_content, str):
            try:
                relation_content = json.loads(relation_content)
            except json.JSONDecodeError as e:
                raise Exception('数据集内容解析错误：' + str(e))
        node = relation_content.get('node')
        new_fields = copy.deepcopy(fields)
        for node_item in node:
            node_item['fields'] = []
            for field in new_fields:
                if field.get('type') in [DatasetFieldType.Calculate, DatasetFieldType.Customer, DatasetFieldType.Group]:
                    continue
                if node_item.get('name') == field.get('origin_table_name'):
                    field['name'] = field.get('origin_col_name')
                    node_item['fields'].append(field)
        return self.get_query_indicator_internal(node)

    @staticmethod
    def get_query_indicator_internal(node_data):
        for node in node_data:
            if node.get('table_type') == MysoftShuXinTableType.DWS_VIEW.value:
                indicators = []
                dimensions = []
                table_name = node.get('name')
                node_fields = node.get('fields', [])
                if not node_fields:
                    return None
                for field in node_fields:
                    code = field.get('code') if field.get('code') else field.get('external_id')
                    if not code:
                        continue
                    field_name = field.get('name')
                    dim_type = field.get('dim_type', '') or field.get('origin_dim_type', '')
                    if dim_type == MysoftShuXinDimType.维度.value:  # 维度
                        dim = {"expression": {"type": "Field",
                                              "field_content": {"field_type": "Dimension", "table_name": table_name,
                                                                "field_name": field_name, "op_type": "NoFieldOpType"}},
                               "alias": field_name}
                        dimensions.append(dim)
                    else:
                        ind = {"alias": field_name, "expression": {"type": "Field",
                                                                   "field_content": {"field_type": "Indicator",
                                                                                     "code": code}}}
                        indicators.append(ind)
                if not dimensions and not indicators:  # 此处存在一种场景： 之前拖拽的dws view 没有限制，所以会没有code 所以还是按默认表取数
                    return None
                return {
                    "indicators": indicators,
                    "dimensions": dimensions,
                    "filter": {},
                    "having_filter": {},
                    "sorts": [],
                    "limit": {},
                    "table_name": table_name,
                    "mode": "Agg"
                }
        return None

    @record_execute_time()
    def get_sql_data_or_columns(self):
        """
        sql模式请求api获取数据和结构
        :return:
        """
        # mosql
        sql, design = indicator_common.moql_check_and_tans(
            self.dataset_model.design, self.pulsar_api, self.content.get('sql'), var_content=self.dataset_model.var_content
        )

        sql = self.replace_dataset_sql_vars(sql)
        # 去注释
        sql, _ = remove_comment(sql)
        sql = sql.strip().strip(';')
        if self.dataset_model.is_import_table:
            code = self.dataset_model.relation_content['nodeDataArray'][0]['id']
            column_datas = self.get_table_field(code)
        else:
            column_datas = self.get_query_sql_fields_structure(sql)

        query_structure = self.get_sql_query_structure(sql)
        # 处理 指标自带的变量，需要将变量值传给数芯
        with_variables = self.append_shuxin15_var(self.dataset_model.var_content)
        result_data = self.get_result_data(query_structure, with_variables=with_variables)
        new_struct_count = self.get_sql_count_query_structure(sql)
        total_count = self.get_result_data(new_struct_count, with_variables=with_variables)[0]['data_count']

        for column_data in column_datas:
            if 'col_name' not in column_data:
                column_data['col_name'] = column_data.get('name')

            if 'data_type' not in column_data:
                column_data['data_type'] = column_data.get('type')

        design = [
            {
                'is_main': 0,
                'app_level_code': '',
                'db_type': '',
                'content': json.dumps(design),
                'is_enable': 1
            }
        ]
        return result_data, column_datas, total_count, design

    def get_sql_data_count(self):
        """
        sql模式请求api获取总数
        :return:
        """
        sql = self.replace_dataset_sql_vars(self.content.get('sql'))
        # 去注释
        sql, _ = remove_comment(sql)
        # 处理 指标自带的变量，需要将变量值传给数芯
        with_variables = self.append_shuxin15_var(self.dataset_model.var_content)
        new_struct_count = self.get_sql_count_query_structure(sql)
        total_count = self.get_result_data(new_struct_count, with_variables=with_variables)[0]['data_count']

        return  total_count

    def get_sql_query_structure(self, sql):
        """
        根据sql语句获取查询结构，例：select a.* from (sql语句) a
        :param sql:
        :return:
        """
        query_structure = QueryStructure()
        select = Select()
        select.obj_name = "a"
        select.prop_name = "*"

        query_structure.select.append(select)
        table = Object()
        sql = self.validation_sql(sql)
        validate_key_word(sql, exclude=["if"])
        table.name = " ({sql}) ".format(sql=sql)
        table.alias = "a"
        query_structure.object.append(table)
        limit = Limit()
        limit.row = self.data_limit
        query_structure.limit = limit
        return query_structure

    def get_sql_count_query_structure(self, sql):
        """
        根据sql语句获取查询结构，例：select a.* from (sql语句) a
        :param sql:
        :return:
        """
        query_structure_count = QueryStructure()
        query_structure_count.select.append(
            Select(
                prop_raw='count(1)',
                alias='data_count'
            )
        )
        table = Object()
        table.name = " ({}) ".format(sql)
        table.alias = ' a '
        query_structure_count.object = [table]
        return query_structure_count

    def get_result_data(self, query_structure, query_indicator=None, query_sql=None, query_type=0, with_variables=None):
        return self.pulsar_api.get_data(query_structure, query_indicator, query_sql, query_type, with_variables).get('data', [])

    def get_table_field(self, code):
        column_datas = []
        api_data_structure = self.pulsar_api.get_object_structs(code=code)
        columns = api_data_structure.get('field')
        for column in columns:
            column_datas.append({'name': column.get('name'), 'col_name': column.get('name'),
                                 'data_type': column.get('storage_field_type'),
                                 'comment': column.get('name_cn'), 'note': column.get('description'),
                                 'group_name': column.get('group_name', '')})
        return column_datas
