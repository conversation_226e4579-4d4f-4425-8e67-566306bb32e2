#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by zhangyx02 on 26/09/2017
# pylint: skip-file
import hashlib
import json

from base import repository
from base.enums import MysoftShuXinTableType, DatasetType, ExternalDatasetType, DatasetFieldType
from components import db_engine_transform
from components.log_setting import Debug
from data_source.models import ColumnQueryModel, TableQueryModel
from data_source.services import data_source_service
from dataset.cache import dataset_meta_cache
from dataset.models import DatasetModel, DatasetOperateRecordModel, DatasetFlowModel, DatasetCacheFlowModel
from dataset.repositories import dataset_repository, dataset_field_repository, dataset_subject_repository
from dataset.repositories.dataset_var_repository import batch_get_dataset_vars
from dataset.services import (
    dataset_service,
    dataset_field_service,
    dataset_var_service,
    dataset_subject_service,
    dataset_permission_service,
    dataset_field_group_service,
    dataset_used_table_service,
)
from dmplib.utils.errors import UserError
from dataset.common import sql_helper
from dmplib.utils.strings import seq_id

debugger = Debug(__name__)
import copy
from dataset.services import dataset_define_service


def import_table(model: DatasetModel):
    model.is_import_table = 1
    table_name = sql_helper.extract_tables(model.content['sql'])[0]
    table_info = get_import_table_info(model.content.get('data_source_id'), table_name)
    if table_info.get('type', '').lower() == 'DWS_VIEW'.lower():  # DWS_VIEW DWS 的需要手动添加dataset，dataset field
        import_shuxin_dws_view(model, table_info)
        return
    run_model = copy.deepcopy(model)
    run_model.content = json.dumps(run_model.content, ensure_ascii=False)
    columns = data_source_service.get_table_columns(
        ColumnQueryModel(**{'id': model.content.get('data_source_id'), 'table_name': table_name})).get_result_dict()
    # 构建结构型 run_model
    run_model.edit_mode = 'relation'

    table_info['fields'] = columns.get('items')
    run_model.relation_content = {"nodeDataArray": [table_info], "linkDataArray": []}
    run_data = dataset_service.run_get_data(run_model)
    model.id = run_data['id']
    model.field = run_data['head']
    model.edit_mode = 'relation'
    model.content['count'] = run_data['count']
    model.content['create_table_sql'] = run_data['create_table_sql']
    model.content['tmp_table_name'] = run_data['tmp_table_name']
    model.content['sql'] = ''
    model.relation_content = run_model.relation_content
    name = model.name
    model.import_table_name = table_name
    model.import_table_type = MysoftShuXinTableType[table_info.get('type', '').upper()].value if table_info.get('type', '').upper() in MysoftShuXinTableType.__members__ else \
        table_info.get('type', '').upper()
    times = 1

    while times < 5:
        try:
            # 按照第三方的业务分组进行分组
            dataset_define_service.add_dataset(model)
            save_import_table_folder(model, run_data)
            return True
        except Exception as ex:
            if '数据集名称不能重名' in str(ex):  # 添加的数据集已经存在重名 需要自动添加_1 此时会存在 并发场景 故在重试时会判断一次是否已经插入进去了
                used_table = data_source_service.get_imported_table(run_data['bind_source_id'])
                if table_name and table_name[0] not in used_table:
                    model.name = name + '_' + str(times)
                    times += 1
                    continue
            raise ex


def import_shuxin_dws_view(model: DatasetModel, table_info):
    # 1、先新增dataset
    dataset_id = table_info.get('code')
    data_source_id = model.content.get('data_source_id')
    table_name = table_info.get('name')
    item = {"id": dataset_id, "external_id": dataset_id, "name": model.name, "type": DatasetType.Indicator.value,
            "content": json.dumps({"data_source_id": data_source_id}),
            "external_type": ExternalDatasetType.PulsarIndicator.value,
            'level_code': dataset_define_service.generate_level_code(model.parent_id),
            'is_import_table': 1,
            "import_table_name": table_name,
            "import_table_type": MysoftShuXinTableType[table_info['type'].upper()].value if table_info[
                                                                                                'type'].upper() in MysoftShuXinTableType.__members__ else
            table_info['type'].upper(),
            "parent_id": model.parent_id
            }
    repository.add_data("dap_bi_dataset", item)
    # 2、新增datasetfield 、 分组 、 关系
    columns_obj = data_source_service.get_table_columns(
        ColumnQueryModel(**{'id': model.content.get('data_source_id'), 'table_name': table_name})).get_result_dict()
    columns = columns_obj.get('items')
    num_relation = {}
    for column in columns:
        column['id'] = seq_id()
        column['col_name'] = column.get("name")
        num_relation[column.get('name')] = []
    insert_columns = {}
    for column in columns:
        data_type = column.get('data_type')
        relation_fields = []
        if not column.get('analysis_dimension', {}):
            continue
        for analysis_dimension in column.get('analysis_dimension', {}):  # 此处只有度量会包含哪些指标，数见会存储 指标数据相互影响的字段，所以需要双重计算
            for relation_field in columns:
                name = relation_field.get('name')
                if name == analysis_dimension.get('name', ''):
                    relation_fields.append(relation_field.get('id'))
                    if name not in num_relation:
                        num_relation[name] = [column['id']]
                    else:
                        num_relation[name].append(column['id'])
        insert_columns[column.get('name')] = {
            "id": column['id'],
            "col_name": f"{table_name}_{column.get('name')}",
            "alias_name": column.get("name_cn"),
            "origin_col_name": column.get("name"),
            "dataset_id": dataset_id,
            "external_id": column.get("code"),
            "data_type": db_engine_transform.get_dmp_data_type(data_type),
            "field_group": '度量' if column.get('dim_type') == 'indicator' or column.get('analysis_dimension',
                                                                                         {}) else '维度',
            "type": DatasetFieldType.Normal.value,
            "origin_field_type": '',
            "visible": 1,
            "relation_fields": json.dumps(list(set(relation_fields))),
            "note": column.get("note"),
            "origin_table_name": table_name,
            "origin_table_comment": column.get("note"),
            "business_note": column.get("note")
        }
    for insert_column in insert_columns.values():
        if insert_column.get('field_group') == '度量':
            continue
        insert_column['relation_fields'] = json.dumps(list(set(num_relation[insert_column.get('origin_col_name')])))
    repository.add_list_data("dap_bi_dataset_field", [value for value in insert_columns.values()],
                             ['id', 'col_name', 'alias_name', 'origin_col_name', 'dataset_id', 'external_id',
                              'data_type', 'field_group',
                              'type', 'origin_field_type', 'visible', 'relation_fields', 'note', 'origin_table_name',
                              'origin_table_comment',
                              'business_note'])
    # 3 、字段分组
    model.id = dataset_id
    model.import_table_name = table_name
    save_import_table_dws_view_folder(model, {"org_column_struct": columns})
    dataset_meta_cache.del_dataset_cache(dataset_id)
    dataset_meta_cache.del_dataset_field_cache(dataset_id)
    dataset_meta_cache.del_multi_dataset_field_cache([value.get('id') for value in insert_columns.values()])


#  引入的表需要新增 刷新 可能存在改表类型的情况
def get_import_table_info(data_source_id, table_name, filter_view=True):
    table_list = data_source_service.import_tables(TableQueryModel(**{"id": data_source_id, "page_size": 9999}),filter_view)
    for table in table_list:
        if table.get('level_type') == 'table' and table.get('name') == table_name:
            table['alias_name'] = table['name']
            return table
    raise UserError(message=f'{table_name}表不存在')


def refresh_import_table(dataset_id):
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message="数据集不存在")
    dataset_model = DatasetModel(**dataset)
    content = json.loads(dataset_model.content)
    if dataset_model.is_import_table and dataset_model.type == DatasetType.Indicator.value:
        refresh_import_table_shuxin_dws_view(dataset_model)
        return
    if dataset_model.is_import_table:
        table_name = dataset_service.get_dataset_tables_collection(dataset_id).get('nodeDataArray')[0].get('name')
    elif dataset_model.edit_mode == 'table':  # 存在一部分之前上线 的引入表模型
        table_name = content.get('sql_table_names')[0]
    else:
        raise UserError(message="数据集不符合规范")

    run_model = copy.deepcopy(dataset_model)
    columns = data_source_service.get_table_columns(
        ColumnQueryModel(**{'id': content.get('data_source_id'), 'table_name': table_name})).get_result_dict()
    if not columns.get('items'):
        raise UserError(message=f'未获取到表{table_name}字段信息，该表可能已经被删！')
    # 构建结构型 run_model
    run_model.edit_mode = 'relation'
    table_info = get_import_table_info(content.get('data_source_id'), table_name)
    table_info['fields'] = columns.get('items')
    run_model.relation_content = {"nodeDataArray": [table_info], "linkDataArray": []}
    run_data = dataset_service.run_get_data(run_model)
    error_list = []
    for check_result in run_data['check_results']:
        if check_result['impact_level']:
            error_list.append(
                f'{check_result.get("field_data").get("alias_name")}({check_result.get("field_data").get("origin_col_name")}){check_result.get("message")},已经被引用！')
    if len(error_list) > 0:
        error_list = list(set(error_list))
        raise UserError(message=','.join(error_list))
    dataset_model.is_import_table = 1
    dataset_model.content = json.loads(dataset_model.content)
    dataset_model.field = map_import_table_comment(columns.get('items'), run_data['head'])
    dataset_model.edit_mode = 'relation'
    dataset_model.content['count'] = run_data['count']
    dataset_model.content['create_table_sql'] = run_data['create_table_sql']
    dataset_model.content['tmp_table_name'] = run_data['tmp_table_name']
    dataset_model.content['sql'] = ''
    dataset_model.relation_content = run_model.relation_content
    dataset_model.import_table_name = table_name
    dataset_model.import_table_type = MysoftShuXinTableType[table_info.get('type', '').upper()].value if table_info.get('type', '').upper() in MysoftShuXinTableType.__members__ else \
        table_info.get('type', '').upper()

    # 按照第三方的业务分组进行分组
    dataset_define_service.update_dataset(dataset_id, dataset_model)
    save_import_table_folder(dataset_model, run_data)
    return True


def refresh_import_table_shuxin_dws_view(dataset_model: DatasetModel):
    dataset_id = dataset_model.id
    content = json.loads(dataset_model.content)
    data_source_id = content.get('data_source_id')
    # 1 、 刷新字段 先获取字段，然后识别被删除的字段，如果删除了直接报错，不删除就匹配更新，删除的最后删除即可，
    remote_columns = data_source_service.get_table_columns(
        ColumnQueryModel(
            **{'id': data_source_id, 'table_name': dataset_model.import_table_name})).get_result_dict().get('items')
    remote_colname_map_id = {}
    for column in remote_columns:
        column['id'] = seq_id()
        column['col_name'] = f"{dataset_model.import_table_name}_{column.get('name')}"
        remote_colname_map_id[column['name']] = column['id']
    local_fields = dataset_field_repository.get_dataset_field(dataset_id)
    remote_columns_col_name = [col.get('name') for col in remote_columns]
    del_fields = []
    num_relation = {}
    local_columns_col_name = {}
    for field in local_fields:
        if field.get('origin_col_name') not in remote_columns_col_name:
            del_fields.append(field.get('id'))
            # 校验 字段是否引用 引用就报错
            continue
        local_columns_col_name[field.get('origin_col_name')] = field
    for column in remote_columns:
        storage_field_type = column.get('storage_field_type')
        data_type = column.get('data_type')
        relation_fields = []
        if column.get('dim_type') == 'indicator' and not column.get('analysis_dimension', {}):
            continue
        for analysis_dimension in column.get('analysis_dimension', {}):
            name = analysis_dimension.get('name', '')
            map_id = local_columns_col_name.get(name, {}).get('id', False) or remote_colname_map_id[name]
            relation_fields.append(map_id)
            relation_id = local_columns_col_name[column.get("name")].get('id') if column.get(
                'name') in local_columns_col_name.keys() else column['id']
            if name not in num_relation:
                num_relation[name] = [relation_id]
            else:
                num_relation[name].append(relation_id)
        field_group = '度量' if column.get('dim_type') == 'indicator' or column.get('analysis_dimension', {}) else '维度'
        if column.get('name') in local_columns_col_name.keys():
            local_columns_col_name[column.get("name")]['alias_name'] = column.get("description")
            local_columns_col_name[column.get("name")]['data_type'] = db_engine_transform.get_dmp_data_type(
                data_type)
            local_columns_col_name[column.get("name")]['field_group'] = field_group
            local_columns_col_name[column.get("name")]['relation_fields'] = json.dumps(list(set(relation_fields)))
            local_columns_col_name[column.get("name")]['alias_name'] = column.get("name_cn")
            local_columns_col_name[column.get("name")]['note'] = column.get("note")
            local_columns_col_name[column.get("name")]['origin_table_comment'] = column.get("note")
            local_columns_col_name[column.get("name")]['business_note'] = column.get("note")
            local_columns_col_name[column.get("name")]['external_id'] = column.get("code")
            continue
        local_columns_col_name[column.get("name")] = {
            "id": column['id'],
            "col_name": f"{dataset_model.import_table_name}_{column.get('name')}",
            "alias_name": column.get("name_cn"),
            "origin_col_name": column.get("name"),
            "dataset_id": dataset_id,
            "external_id": column.get("code"),
            "data_type": db_engine_transform.get_dmp_data_type(data_type),
            "field_group": field_group,
            "type": DatasetFieldType.Normal.value,
            "origin_field_type": '',
            "visible": 1,
            "relation_fields": json.dumps(list(set(relation_fields))),
            "note": column.get("note"),
            "origin_table_name": dataset_model.import_table_name,
            "origin_table_comment": column.get("note"),
            "business_note": column.get("note")
        }
    for local_columns in local_columns_col_name.values():
        if local_columns.get('field_group') == '度量':
            continue
        if local_columns.get('origin_col_name') not in num_relation:
            continue
        local_columns['relation_fields'] = json.dumps(list(set(num_relation[local_columns.get('origin_col_name')])))
    repository.delete_data('dap_bi_dataset_field', {'dataset_id': dataset_id})
    repository.add_list_data(
        'dap_bi_dataset_field', [value for value in local_columns_col_name.values()],
        ['id', 'col_name', 'alias_name', 'origin_col_name', 'dataset_id', 'external_id', 'data_type', 'field_group',
         'type', 'origin_field_type', 'visible', 'relation_fields', 'note', 'origin_table_name', 'origin_table_comment',
         'business_note']
    )

    dataset_field_repository.delete_field(del_fields)
    # 2、 刷新字段分组
    save_import_table_dws_view_folder(dataset_model, {"org_column_struct": remote_columns})
    # 删除数据集元数据所有缓存，再重新加载元数据和字段缓存， 保证version只改变一次。
    dataset_meta_cache.del_dataset_cache(dataset_id)
    dataset_meta_cache.del_dataset_field_cache(dataset_id)
    dataset_meta_cache.del_multi_dataset_field_cache([value.get('id') for value in local_columns_col_name.values()])


def save_import_table_dws_view_folder(model: DatasetModel, run_data):
    from dataset.models import DatasetFieldGroupModel, DatasetFieldGroupRelationModel
    #  1、 获取list 并构建list
    dataset_id = model.id
    fields = dataset_field_repository.get_dataset_field(dataset_id)
    org_columns = run_data['org_column_struct']
    child_field_group_list = []
    child_field_group_key = []
    org_col_keys = {f"{model.import_table_name}_{org_col.get('name')}": org_col for org_col in org_columns}
    org_group_sort = {org_col.get('group_name'): org_col.get('group_name_sort') for org_col in org_columns}
    for field in fields:
        if field.get('col_name') not in org_col_keys:
            continue
        org_group_name = org_col_keys.get(field.get('col_name')).get('group_name')
        if not org_group_name:
            continue
        field_group = field.get('field_group', '')
        field['child_field_group'] = field_group
        group_type = 1 if field_group == '维度' else 2
        if field_group not in child_field_group_key:
            child_field_group_list.append({"group_name": field_group, "group_type": group_type, "sort":  org_group_sort.get(org_group_name, 99)})
            child_field_group_key.append(field_group)
    group_relation_list = []
    dataset_field_group_service.delete_all_field_group_by_dataset_id(dataset_id)
    for child_field_group in child_field_group_list:
        #  2 、新建分组
        child_field_group['dataset_id'] = dataset_id
        child_field_group_model = DatasetFieldGroupModel(**child_field_group)
        child_field_group_data = dataset_field_group_service.save_dataset_field_group(child_field_group_model)
        #  3 、插入关系
        relation_sort = 1
        for field in fields:
            if field.get('child_field_group', '') == child_field_group["group_name"]:
                group_relation_model = DatasetFieldGroupRelationModel(
                    **{"group_id": child_field_group_data.get('id'), "field_id": field.get('id'), "sort": relation_sort,
                       "dataset_id": dataset_id})
                group_relation_list.append(group_relation_model)
                relation_sort += 1
    # 删除数据集对应下所有字段的绑定关系
    dataset_field_group_service.delete_field_group_by_dataset_id(dataset_id)
    dataset_field_group_service.save_field_group_relation(group_relation_list)


def save_import_table_folder(model: DatasetModel, run_data):
    from dataset.models import DatasetFieldGroupModel, DatasetFieldGroupRelationModel
    #  1、 获取list 并构建list
    dataset_id = model.id
    fields = dataset_field_repository.get_dataset_field(dataset_id)
    org_columns = run_data['org_column_struct']
    child_field_group_list = []
    child_field_group_key = []
    org_col_keys = {org_col.get('col_name'): org_col for org_col in org_columns}
    org_group_sort = {org_col.get('group_name'): org_col.get('group_name_sort') for org_col in org_columns}
    for field in fields:
        if field.get('col_name') not in org_col_keys:
            continue
        org_group_name = org_col_keys.get(field.get('col_name')).get('group_name')
        if not org_group_name:
            continue
        field_group = field.get('field_group', '')
        child_field_group = f'{org_group_name}_{field_group}'
        field['child_field_group'] = child_field_group
        group_type = 1 if field_group == '维度' else 2
        if child_field_group not in child_field_group_key:
            child_field_group_list.append({"group_name": child_field_group, "group_type": group_type, "sort": org_group_sort.get(org_group_name, 99)})
            child_field_group_key.append(child_field_group)
    group_relation_list = []
    dataset_field_group_service.delete_all_field_group_by_dataset_id(dataset_id)
    for child_field_group in child_field_group_list:
        #  2 、新建分组
        child_field_group['dataset_id'] = dataset_id
        child_field_group_model = DatasetFieldGroupModel(**child_field_group)
        child_field_group_data = dataset_field_group_service.save_dataset_field_group(child_field_group_model)
        #  3 、插入关系
        relation_sort = 1
        for field in fields:
            if field.get('child_field_group', '') == child_field_group["group_name"]:
                group_relation_model = DatasetFieldGroupRelationModel(
                    **{"group_id": child_field_group_data.get('id'), "field_id": field.get('id'), "sort": relation_sort,
                       "dataset_id": dataset_id})
                group_relation_list.append(group_relation_model)
                relation_sort += 1
    # 删除数据集对应下所有字段的绑定关系
    dataset_field_group_service.delete_field_group_by_dataset_id(dataset_id)
    dataset_field_group_service.save_field_group_relation(group_relation_list)


def repair_import_table_col_name(dataset_id):  # 修复历史模型的col_name,谨慎使用
    dataset = dataset_repository.get_dataset(dataset_id)
    dataset_model = DatasetModel(**dataset)
    content = json.loads(dataset_model.content)
    if dataset_model.is_import_table:
        table_name = dataset_service.get_dataset_tables_collection(dataset_id).get('nodeDataArray')[0].get('name')
    elif dataset_model.edit_mode == 'table':  # 存在一部分之前上线 的引入表模型
        table_name = content.get('sql_table_names')[0]
    else:
        raise UserError(message="数据集不符合规范")
    run_model = copy.deepcopy(dataset_model)
    columns = data_source_service.get_table_columns(
        ColumnQueryModel(**{'id': content.get('data_source_id'), 'table_name': table_name})).get_result_dict()
    # 构建结构型 run_model
    run_model.edit_mode = 'relation'
    table_info = get_import_table_info(content.get('data_source_id'), table_name)
    table_info['fields'] = columns.get('items')
    run_model.relation_content = {"nodeDataArray": [table_info], "linkDataArray": []}
    run_data = dataset_service.run_get_data(run_model)
    new_columns = run_data['head']
    old_columns = dataset_field_repository.get_dataset_field(dataset_id)
    content = dataset_model.content
    for old_column in old_columns:
        for new_column in new_columns:
            if new_column.get('origin_col_name') == old_column.get('origin_col_name') and new_column.get(
                    'col_name') != old_column.get('col_name'):
                content = content.replace(old_column.get('col_name'), new_column.get('col_name'))
                repository.update('dap_bi_dataset_field', {'col_name': new_column.get('col_name')},
                                  {'id': old_column.get('id')})
    if content != dataset_model.content:
        repository.update('dap_bi_dataset', {'content': content}, {'id': dataset_id})


def map_import_table_comment(table_columns, run_columns):
    #  编辑数据集界面的备注以数见为准，但是引入数据库表的备注是以数芯为准，所以此处需要映射一次
    for run_column in run_columns:
        for table_column in table_columns:
            if run_column.get('col_name') == table_column.get('col_name'):
                run_column['note'] = table_column.get('note')
                col_type = table_column.get("data_type")[
                           0: table_column.get("data_type").find("(")] if table_column.get(
                    "data_type") and table_column.get("data_type").find("(") != -1 else table_column.get("data_type")
                run_column['field_group'] = db_engine_transform.get_dmp_field_group(col_type)
                run_column['data_type'] = db_engine_transform.get_dmp_data_type(col_type)
    return run_columns

