#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/7/7 10:47
# <AUTHOR> wangf10
# @File     : dataset_field_group.py

from base import repository
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from ..models import DatasetFieldGroupModel, DatasetFieldGroupRelationModel

FIELD_GROUP = '分组'


def save_dataset_field_group(group_model: DatasetFieldGroupModel):
    group_model.validate()
    if not group_model.id:
        # 新增字段分组
        group_model.id = seq_id()
        repository.add_model(DatasetFieldGroupModel.__table__, group_model)
    else:
        # 修改字段分组信息
        repository.update_model(DatasetFieldGroupModel.__table__, group_model, {'id': group_model.id})
    data = group_model.get_dict()
    return data


def delete_field_group(group_id):
    if group_id:
        # 删除分组信息
        repository.delete_data(DatasetFieldGroupModel.__table__, {'id': group_id})
        # 删除分组与字段的关系
        repository.delete_data(DatasetFieldGroupRelationModel.__table__, {'group_id': group_id})


def change_group_sort(sort_dict: dict):
    if sort_dict:
        for k, v in sort_dict.items():
            if not k or not v:
                continue
            repository.update(DatasetFieldGroupModel.__table__, {'sort': v}, {'id': k})


def save_field_group_relation(model_list: list):
    insert_data = []
    field_ids = []
    if model_list:
        for model in model_list:
            model.id = seq_id()
            model.validate()
            field_ids.append(model.field_id)
            insert_data.append(model.get_dict())
        data = repository.get_value(DatasetFieldGroupRelationModel.__table__, {'field_id': field_ids}, 'field_id')
        if data:
            raise UserError(message="字段已经被其他分组绑定")
        fields = list(insert_data[0].keys())
        repository.add_list_model(DatasetFieldGroupRelationModel.__table__, model_list, fields)
        return insert_data


def delete_field_group_by_dataset_id(dataset_id: str):
    if dataset_id:
        return repository.delete_data(DatasetFieldGroupRelationModel.__table__, {'dataset_id': dataset_id})


def delete_field_group_relation(field_list: list):
    if field_list:
        return repository.delete_data(DatasetFieldGroupRelationModel.__table__, {'field_id': field_list})


def change_group_field_sort(sort_dict: dict):
    if sort_dict:
        for k, v in sort_dict.items():
            if not k or not v:
                continue
            repository.update(DatasetFieldGroupRelationModel.__table__, {'sort': v}, {'field_id': k})


def get_field_group_by_dataset_id(dataset_id: str):
    data = {'维度': [], '度量': []}
    if dataset_id:
        # 获取这个数据集下的所有分组
        group_info = repository.get_list(
            DatasetFieldGroupModel.__table__, {'dataset_id': dataset_id}, order_by=[('sort', 'asc')]
        )
        if group_info:
            group_ids = list(set([group.get('id', '') for group in group_info]))
            group_field = []
            if group_ids:
                # 获取对应分组下的所有字段
                group_field = repository.get_list(
                    DatasetFieldGroupRelationModel.__table__, {'group_id': group_ids}, order_by='sort asc'
                )
            for group in group_info:
                group_id = group.get('id')
                group_dict = {'group_id': group_id, 'folderName': group.get('group_name'),
                              'sort': group.get('sort'), 'children': []}
                for field in group_field:
                    if field.get('group_id') == group_id:
                        group_dict['children'].append(field.get('field_id'))
                if group.get('group_type') == 1:
                    data['维度'].append(group_dict)
                else:
                    data['度量'].append(group_dict)
    return data


def delete_all_field_group_by_dataset_id(dataset_id):
    if dataset_id:
        # 删除分组信息
        repository.delete_data(DatasetFieldGroupModel.__table__, {'dataset_id': dataset_id})
        # 删除分组与字段的关系
        repository.delete_data(DatasetFieldGroupRelationModel.__table__, {'dataset_id': dataset_id})


def get_field_group_list_by_dataset_id(dataset_id):
    return repository.get_list(
        DatasetFieldGroupModel.__table__, {'dataset_id': dataset_id}, order_by=[('sort', 'asc')]
    )


def get_field_group_relation_list_by_dataset_id(dataset_id):
    return repository.get_list(
        DatasetFieldGroupRelationModel.__table__, {'dataset_id': dataset_id}, order_by=[('sort', 'asc')]
    )
