# -*- coding: utf-8 -*-
"""
    数据集字段关联变量缓存
    <NAME_EMAIL> on 2019/4/9.
"""
from dataset.cache import dataset_cache_service
from dataset.cache.dataset_cache_service import DATASET_INCLUDE_VARS
from dataset.repositories import dataset_var_repository
from dmplib.hug import g
from dmplib.redis import conn as conn_redis
from components.log_setting import Debug

from typing import Any, List

debugger = Debug("dataset_meta_cache")


def get_dataset_include_vars_cache(dataset_ids: List[str]) -> List[Any]:
    """
    获取多数据集字段关联变量缓存数据
    :param dataset_ids:
    :return:
    """
    cache = conn_redis()
    multi_dataset_field_cache = dataset_cache_service.DatasetCache(g.code, DATASET_INCLUDE_VARS, cache)
    data = multi_dataset_field_cache.hmget(dataset_ids)
    result = []
    query_ids = []
    for i, dataset_field_cache in enumerate(data):
        if not dataset_field_cache:
            query_ids.append(dataset_ids[i])
        else:
            result.extend(dataset_field_cache)

    debugger.log({"使用多数据集字段关联变量缓存数据": {"cached_fields": result, "query_ids": query_ids}})

    if query_ids:
        dataset_include_vars_data = dataset_var_repository.batch_get_dataset_include_vars(query_ids)
        set_multi_dataset_include_vars_cache(dataset_include_vars_data)
        result.extend(dataset_include_vars_data)
    return result


def set_multi_dataset_include_vars_cache(data: List[Any]) -> None:
    """
    设置数据集字段关联变量缓存数据
    :param data: {"dataset_id":[]}
    :return:
    """
    if data:
        cache_data = {}
        for dataset_include_var in data:
            if dataset_include_var.get("dataset_id") in cache_data:
                cache_data.get(dataset_include_var.get("dataset_id")).append(dataset_include_var)
            else:
                cache_data[dataset_include_var.get("dataset_id")] = [dataset_include_var]

        cache = conn_redis()
        dataset_cache = dataset_cache_service.DatasetCache(g.code, DATASET_INCLUDE_VARS, cache)
        dataset_cache.set_mprop(cache_data)


def del_dataset_include_vars_cache(dataset_ids: []):
    """
    删除数据集字段关联变量缓存数据
    :param dataset_ids:
    :return:
    """
    if dataset_ids:
        cache = conn_redis()
        dataset_cache = dataset_cache_service.DatasetCache(g.code, DATASET_INCLUDE_VARS, cache)
        for dataset_id in dataset_ids:
            dataset_cache.del_prop(dataset_id)
