# -*- coding: utf-8 -*-
"""
dashboard相关业务缓存
"""
import json
from base.dmp_constant import DATASET_ROW_PERMISSION_USER_ID

from components.object_caching import ObjectCache

# 数据集多字段元数据缓存key前缀
from dmplib.redis import RedisCache

DATASET_MULTI_FIELD = "multi_field_meta"

# 数据集字段关联变量数据缓存key前缀
DATASET_INCLUDE_VARS = "dataset_include_vars"

# 外部主题数据缓存key前缀
DATASET_EXTERNAL_SUBJECT_FIELDS = "dataset_external_subject_fields"


class DatasetCache(ObjectCache):

    dataset_meta = "dataset_meta"

    dataset_field_meta = "dataset_field_meta"
    # 元数据版本号
    dataset_meta_version = "version"
    # 业务数据版本号
    dataset_data_version = "data_version"

    DATASET_FILTER_META = "dataset_filter_meta"

    def __init__(self, project_code: str, dataset_id: str, redis_conn: RedisCache) -> None:
        super().__init__('dataset', project_code, dataset_id, redis_conn)
    
    def set_mprop(self, mapping, expire_flag=True):
        return super(DatasetCache, self).set_mprop(mapping, expire_flag)

    def set_prop(self, prop, value, expire_flag=True):
        return super(DatasetCache, self).set_prop(prop, value, expire_flag)
    
    @staticmethod
    def batch_mget(cache_instances, props, redis_conn, _json_loads=None):
        """
        批量获取多个实例的一些属性
        :param _json_loads:
        :param cache_instances: DatasetCache
        :param props: []
        :param redis_conn:
        :return:
        """
        if not props:
            return []
        if _json_loads is None:
            _json_loads = json.loads

        pipe = redis_conn.pipe(transaction=False)
        for c in cache_instances:
            pipe.hmget(redis_conn._wrapper_key(c.cache_key), props)
        items = pipe.execute()
        return [[_json_loads(item.decode('utf-8')) if item else None for item in item] for item in items]


class DatasetRowCache(ObjectCache):
    def __init__(self, project_code, dataset_id, redis_conn):
        super().__init__('dataset', project_code, dataset_id, redis_conn)

    def remove(self):
        return self.redis_client.delete(DATASET_ROW_PERMISSION_USER_ID)
