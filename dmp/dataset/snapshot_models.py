#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@author: <EMAIL>
@time: 2021/9/6 10:44
"""
from datetime import datetime

from base import repository
from base.enums import DatasetVersionType
from dmplib.utils.strings import seq_id
from components.snapshot_service import (
    register_snapshot_service,
    SnapShotSingleTableBase,
)


@register_snapshot_service
class DatasetMetaDataSnapshot(SnapShotSingleTableBase):
    name = 'dap_bi_dataset_field'
    target_table = 'dap_bi_snapshot_dataset_field'
    exclude_fields = ['increment_id']
    filter_conditions = {'dataset_id': 'self*dataset_id'}

    def __add_dataset_version(self, dataset_id):
        from dataset.services.dataset_version_service import add_dataset_version
        from dataset.models import DatasetVersionModel
        dataset_version_model = DatasetVersionModel(
            id=seq_id(),
            dataset_id=dataset_id,
            content="数据集拍照",
            version_name="数据集拍照" + datetime.now().strftime('%Y%m%d%H%M%S'),
            type="拍照"
        )
        add_dataset_version(dataset_version_model, auto=False, version_type=DatasetVersionType.SNAP.value, snap=True)
        return dataset_version_model.id

    def __add_dataset_snapshot_relation_record(self, version_id, dataset_id):
        repository.add_data(
            "dap_bi_dataset_snapshot_relation",
            data=dict(
                snap_id=self.snapshot_id,
                dataset_id=dataset_id,
                dataset_version_id=version_id,
                created_by='dmp',
                modified_by="dmp"
            ),
            commit=False
        )

    def pre_action(self):
        dataset_ids = self.dataset_id
        if isinstance(dataset_ids, str):
            dataset_ids = [self.dataset_id]
        if isinstance(dataset_ids, list):
            for dataset_id in dataset_ids:
                # 复用原数据集生成版本逻辑
                dataset_version_id = self.__add_dataset_version(dataset_id)
                # dataset_snapshot_relation添加记录
                self.__add_dataset_snapshot_relation_record(dataset_version_id, dataset_id)

