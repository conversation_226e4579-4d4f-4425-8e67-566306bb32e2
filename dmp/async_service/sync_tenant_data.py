# pylint: skip-file
"""
同步不同租户的数据
"""
import logging
import json

from base.errors import RedisException
from dmplib.saas import project
from dmplib.redis import conn as conn_redis
from dashboard_chart.services import dashboard_service
from dmplib.hug import g
from dmplib.utils import strings

from . import sync_tenant_data_db

logger = logging.getLogger(__name__)


def _sync_dashboard_source_key(log_id):
    return 'source_dashboard_sync_to_projects_%s' % log_id


def stash_sync_dashboard_data(project_code, dashboard_id, apply_all, remark, action):
    """同步dashboard到特定的租户
    Args:
        project_code (str): 源项目代码
        dashboard_id (str): dashboard.id
        action (str): create, update, delete
        apply_all(bool): apply all
        remark(str): remark
    """
    # 生成要同步的sql, 写写入缓存, 以便给后面的任务批量处理
    if action == 'create' or action == 'update':
        g.code = project_code
        sql, data_sql = dashboard_service.export_dashboard_to_sqls(dashboard_id, False, True)
        # 导数据源
        data_source_sql = dashboard_service.export_datatable_to_sqls('data_source')
        sql = sql + ';' + data_source_sql
        seq = strings.seq_id()
        cache_key = _sync_dashboard_source_key(seq)
        result = conn_redis().set(cache_key, json.dumps({'sql': sql, 'data_sql': data_sql}), 3600)
        if not result:
            RedisException('写入redis失败')
        # 写入日志
        log_data = {
            'id': seq,
            'source_project_code': project_code,
            'apply_all': 1 if apply_all else 0,
            'remark': remark,
            'status': 1,
        }
        sync_tenant_data_db.write_sync_dashboard_log(log_data)
        return seq
    elif action == 'delete':
        raise ValueError('暂时不支持delete')
    else:
        raise ValueError('无效的action')


def sync_dashboard_one_project(log_id, source_project_code, target_project_code):
    cache_key = _sync_dashboard_source_key(log_id)
    result = conn_redis().get(cache_key)
    cache_data = json.loads(result.decode('utf-8'), encoding='utf-8')
    sql = cache_data.get('sql')
    data_sql = cache_data.get('data_sql')
    db = project.get_db(target_project_code)
    data_db = project.get_db(target_project_code, 'data')
    success = False
    errmsg = None
    try:
        db.exec_sql(sql)
        data_db.exec_sql(data_sql)
        success = True
    except Exception as e:
        success = False
        errmsg = e.__str__()
        raise e
    finally:
        # 写入日志
        log_data = {
            'id': strings.seq_id(),
            'source_project_code': source_project_code,
            'target_project_code': target_project_code,
            'status': 1 if success else 0,
            'exception': errmsg,
            'sync_dashboard_log_id': log_id,
        }
        sync_tenant_data_db.write_sync_dashboard_detail_log(log_data)
