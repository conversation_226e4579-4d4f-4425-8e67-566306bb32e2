# -*- coding: utf-8 -*-
# pylint: skip-file
"""
Created on 2017年6月16日

@author: chenc04
"""
import unittest
import os

from base.dmp_constant import SELF_SERVICE_VIRTUAL_USER_ID
from dashboard_chart.services import released_dashboard_service
from dmplib.hug import g
from dmplib.saas.project import set_correct_project_code

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)

from tests.base import BaseTest
from self_service.services import external_subject_service, pulsar_util_service


class TestExternalSubjectService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='yulian_dremio', account='yulian_dremio')

    def test_sync_model(self):
        external_subject_service.sync_all()

    def test_self_report_login(self):
        from dashboard_chart.services import dashboard_login_service

        login_service = dashboard_login_service.DashboardLoginService()
        token = "ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SndjbTlxWldOMFgyTnZaR1VpT2lKNWRXeHBZVzVmWkhKbGJXbHZJaXdpZFhObGNsOWhZMk52ZFc1MElqb2llWFZzYVdGdVgyUnlaVzFwYnlJc0ltVjRjQ0k2TVRZeE1EWXhPREF6TVN3aVkzVnpkRzl0YVhwbFgzSnZiR1Z6SWpwYklqTTVabUV3TWpZd0xUVTVNVFl0WXpneVpTMDVOVGhrTFRaaFlUYzVZamxoWldGbE1DSmRMQ0ppYVhwZlkyOWtaU0k2SWpobVptWTVNVGsyWkRJek9EUmpPR0k0WkdRNFlXRTJORFk1TlRNMU5ETXdJbjAubDB4Z3VGaU9EYjVGQ0hyNWJHRjNvNmJCMUMza256T2MwbFNMUUR0a1pZVQ=="
        redirect_url = "https://dmp-test.mypaas.com.cn/intelligent-report/share/39f9ba27-c36e-4212-c49e-791a5f4671dd?code=yulian_dremio"
        if not token:
            return False, '缺少token', token

        success, data, dashboard_page_url = login_service.login(token, redirect_url)
        print(success, data, dashboard_page_url)

    def test_get_self_login_token(self):
        import jwt
        import time
        import base64

        # jwt加密密钥
        jwt_secret = '0UZR4h#@'

        # jwt加密算法
        jwt_alg = 'HS256'

        # jwt过期时间，建议过期时间设置为60秒
        exp = int(time.time()) + 60 * 60 * 24

        # 用户登录payload数据
        payload = {
            "project_code": "yulian_dremio",
            "user_account": "yulian_dremio",
            'exp': exp,
            'customize_roles': ['39fa0260-1267-137a-eb71-1d09ee826daf', '39fa0260-5916-c82e-958d-6aa79b9aeae0'],
            'biz_code': "8fff9196d2384c8b8dd8aa6469535430",
        }

        # 生成的jwt标准token
        jwt_token = jwt.encode(payload, jwt_secret, jwt_alg)

        # 对jwt标准token进行base64生成最终: token
        token = base64.b64encode(jwt_token).decode('utf-8')
        print(token)

    def test_get_sso_login_token(self):
        import jwt
        import time
        import base64

        # jwt加密密钥
        jwt_secret = '0UZR4h#@'

        # jwt加密算法
        jwt_alg = 'HS256'

        # jwt过期时间，建议过期时间设置为60秒
        exp = int(time.time()) + 60 * 60 * 24

        # 用户登录payload数据
        payload = {
            'user_name': 'selfreport',
            'external_user_id': '39fa11e8-e708-4c1b-3b03-d3bb7f9760d4',
            'exp': exp,
            'project_code': 'yulian_dremio',
            'user_account': 'sdflasdfsfs23902',
            'tenant_code': 'yulian_dremio',
            'account': 'yul_dremio',
            'redirect': 'http://localhost/intelligent-report',
            'customize_roles': ["39fa0260-1267-137a-eb71-1d09ee826daf"],
        }

        # 生成的jwt标准token
        jwt_token = jwt.encode(payload, jwt_secret, jwt_alg)

        # 对jwt标准token进行base64生成最终: token
        token = base64.b64encode(jwt_token).decode('utf-8')
        print(token)

    def test_get_release_data(self):
        kwargs = {
            "dashboard_id": "39fa1972-4a5d-96a4-5a2b-42a837b4985e",
            "chart_params": [
                {
                    "id": "1d283a14-575e-11eb-b83e-ad1fdfc93807",
                    "report_id": "39fa1972-4a5d-96a4-5a2b-42a837b4985e",
                    "dashboard_id": "39fa1972-4a5d-96a4-5a2b-42a837b4985e",
                    "chart_code": "analysis_table",
                    "data_logic_type_code": "column",
                    "conditions": [],
                    "external_subject_id": "15f812e4-401b-11eb-960c-66ac8d81af42",
                    "penetrate_conditions": [],
                    "penetrate_filter_conditions": [],
                    "filter_conditions": [],
                    "chart_filter_conditions": [],
                    "chart_linkage_conditions": [],
                    "drill_conditions": [],
                    "dims": [],
                    "dashboard_conditions": [],
                    "query_vars": [],
                    "pagination": {"page": 1, "total": 0, "page_size": 150},
                    "column_display": [],
                }
            ],
        }
        code = kwargs.get("code")
        g.code = code
        g.userid = SELF_SERVICE_VIRTUAL_USER_ID
        g.cookie = {
            'token': 'ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjBaVzVoYm5SZlkyOWtaU0k2SW5sMWJHbGhibDlrY21WdGFXOGlMQ0oxYzJWeVgyRmpZMjkxYm5RaU9pSjVkV3hwWVc1ZlpISmxiV2x2SWl3aVpYaDBaWEp1WVd4ZmMzVmlhbVZqZEY5cFpDSTZiblZzYkN3aVpYaDBaWEp1WVd4ZmMzVmlhbVZqZEY5dVlXMWxJanB1ZFd4c0xDSmxlSEFpT2pFMk1UQTJPVGsyT1RoOS5MSkRhbmNQdldHOGdLZHk1NVNoSUdKcWVMQzAyYjk1RzRWZzI0aFp2SXhF'
        }
        g.customize_roles = ['39fa0260-1267-137a-eb71-1d09ee826daf']
        g.external_user_id = '39fa1877-19a6-be09-c17f-77bb5cd99c16'
        chart_params = kwargs.get("chart_params")
        results = released_dashboard_service.batch_get_released_chart_result(chart_params)
        print(results)

    def test_get_dws_common_dims(self):
        pass

    def test_get_dws_relations(self):
        pass

    def test_get_pulsar_token(self):
        print(pulsar_util_service.get_pulsar_api_token())


if __name__ == '__main__':
    suite = unittest.TestSuite()
    suite.addTest(TestExternalSubjectService('test_external_subject_service'))
    runner = unittest.TextTestRunner()
    runner.run(suite)
