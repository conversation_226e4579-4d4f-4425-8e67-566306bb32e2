# -*- coding: utf-8 -*-
# pylint: skip-file
"""
Created on 2017年6月16日

@author: chenc04
"""
import unittest
import os
import json

from base.dmp_constant import SELF_SERVICE_VIRTUAL_USER_ID
from dashboard_chart.services import released_dashboard_service, chart_service
from dmplib.hug import g

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)

from tests.base import BaseTest
from self_service.services import external_api_service


class TestExternalApiService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='yulian_dremio', account='yulian_dremio')

    def test_get_permission_fields(self):
        r = external_api_service.get_permission_fields(code='yulian_dremio', account='yulian_dremio')
        print(r)

    def test_get_permssion_filter(self):
        external_user_id = '39fa1877-19a6-be09-c17f-77bb5cd99c16'
        r = external_api_service.get_permission_filter_values(
            code='yulian_dremio', account='yulian_dremio', external_user_id=external_user_id
        )
        print(r)

    def test_get_release_data(self):
        kwargs = {
            "dashboard_id": "39fa1972-4a5d-96a4-5a2b-42a837b4985e",
            "chart_params": [
                {
                    "id": "1d283a14-575e-11eb-b83e-ad1fdfc93807",
                    "report_id": "39fa1972-4a5d-96a4-5a2b-42a837b4985e",
                    "dashboard_id": "39fa1972-4a5d-96a4-5a2b-42a837b4985e",
                    "chart_code": "analysis_table",
                    "data_logic_type_code": "column",
                    "conditions": [],
                    "external_subject_id": "15f812e4-401b-11eb-960c-66ac8d81af42",
                    "penetrate_conditions": [],
                    "penetrate_filter_conditions": [],
                    "filter_conditions": [],
                    "chart_filter_conditions": [],
                    "chart_linkage_conditions": [],
                    "drill_conditions": [],
                    "dims": [],
                    "dashboard_conditions": [],
                    "query_vars": [],
                    "pagination": {"page": 1, "total": 0, "page_size": 150},
                    "code": "yulian_dremio",
                    "column_display": [],
                }
            ],
        }
        _ = kwargs.get("code")
        g.code = "yulian_dremio"
        g.userid = SELF_SERVICE_VIRTUAL_USER_ID
        g.cookie = {
            'token': 'ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjBaVzVoYm5SZlkyOWtaU0k2SW5sMWJHbGhibDlrY21WdGFXOGlMQ0oxYzJWeVgyRmpZMjkxYm5RaU9pSjVkV3hwWVc1ZlpISmxiV2x2SWl3aVpYaDBaWEp1WVd4ZmMzVmlhbVZqZEY5cFpDSTZiblZzYkN3aVpYaDBaWEp1WVd4ZmMzVmlhbVZqZEY5dVlXMWxJanB1ZFd4c0xDSmxlSEFpT2pFMk1UQTJPVGsyT1RoOS5MSkRhbmNQdldHOGdLZHk1NVNoSUdKcWVMQzAyYjk1RzRWZzI0aFp2SXhF'
        }
        g.customize_roles = ['39fa0260-1267-137a-eb71-1d09ee826daf']
        g.external_user_id = '39fa1877-19a6-be09-c17f-77bb5cd99c16'
        chart_params = kwargs.get("chart_params")
        # results = released_dashboard_service.batch_get_released_chart_result(chart_params)

        result = chart_service.batch_get_chart_data(chart_params)
        print(result)


if __name__ == '__main__':
    suite = unittest.TestSuite()
    suite.addTest(TestExternalApiService('test_external_api_service'))
    runner = unittest.TextTestRunner()
    runner.run(suite)
