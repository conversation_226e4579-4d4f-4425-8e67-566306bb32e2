#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/8/3 20:35
# <AUTHOR> caoxl
# @File     : warning_worker.py
# pylint: disable= R0201
import copy
from datetime import datetime
import json
from typing import Tuple, List, Dict

from timeout_decorator import timeout_decorator

from base import repository
from base.enums import (
    IndicatorWarningCompareType,
    ColTypes,
    IndicatorWarningCompareMethod,
    IndicatorWarningResultStatus,
    IndicatorWarningItemRelation,
    IndicatorWarningTargetVersionType,
)
from dmplib.components import auth_util
from dmplib import config
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from indicator_new.repositories import indicator_repositories
from .warning_service import get_indicator_warning
from . import indicator_service
from dataset import external_query_service as dataset_external_service
from components.qywx_robot import QyRobotApi


class WarningWorker:
    def __init__(self, params: dict, tenant: str, data_from: int = 1):
        self.global_messages = {}
        self.tenant = tenant
        # 是否是测试，测试不发送邮件
        self.is_test = params.get("is_test", 0)
        # data_from  1: 来源于数据库， 2：来源于参数
        if data_from == 1:
            warning_config_id = params.get("warning_config_id")
            self.warning = get_indicator_warning(warning_config_id)
        else:
            self.warning = self._assign_warning_by_params(params)
        if not self.warning:
            raise UserError(message="巡检不存在")
        self.result_id = params.get("result_id", None)
        self.indicator_model = indicator_service.get_indicator_query_model_from_db(self.warning["indicator_id"])

    def _assign_warning_by_params(self, params):
        indicator_num_ids = [item.get("indicator_num_id") for item in params.get("items", [])]
        if not indicator_num_ids:
            return None
        indicator_nums = indicator_repositories.batch_get_indicator_nums(indicator_num_ids)
        indicator_map = {indicator_num["id"]: indicator_num for indicator_num in indicator_nums}
        for item in params["items"]:
            if not item.get("id"):
                item["id"] = seq_id()
            if "value" in item and not isinstance(item["value"], (str, float, int)):
                try:
                    item["value"] = json.dumps(item["value"])
                except Exception:
                    pass
            num_info = indicator_map.get(item.get("indicator_num_id"), {})
            item.update(
                {
                    "field_id": num_info.get("field_id"),
                    "indicator_id": num_info.get("indicator_id"),
                    "dataset_id": num_info.get("dataset_id"),
                    "formula_mode": num_info.get("formula_mode"),
                    "alias": num_info.get("alias"),
                }
            )
        return params

    def _get_indicator_nums_dict(self) -> dict:
        attr_name = "_nums_dict"
        if not hasattr(self, attr_name):
            num_dict = {}
            for indicator_num_model in self.indicator_model.nums:
                num_dict[indicator_num_model.field_id] = indicator_num_model.get_dict()
            setattr(self, attr_name, num_dict)
        return getattr(self, attr_name)

    def _get_indicator_dims_fieldid_dict(self) -> dict:
        attr_name = "_dims_fieldid_dict"
        if not hasattr(self, attr_name):
            dim_dict = {}
            for indicator_dim_model in self.indicator_model.dims:
                dim_dict[indicator_dim_model.field_id] = indicator_dim_model.get_dict()
            setattr(self, attr_name, dim_dict)
        return getattr(self, attr_name)

    def exec(self):
        try:
            self.run()
        except timeout_decorator.TimeoutError:
            status = IndicatorWarningResultStatus.Timeout.value
            repository.update(
                "dap_bi_dashboard_indicator_warning_result", {"status": status, "message": "运行超时，已终止！"}, {"id": self.result_id}
            )
        except Exception as e:
            status = IndicatorWarningResultStatus.Error.value
            repository.update(
                "dap_bi_dashboard_indicator_warning_result",
                {"status": status, "message": f"发生致命性错误，错误信息 {str(e)}"},
                {"id": self.result_id},
            )

    @timeout_decorator.timeout(1800, use_signals=False)
    def run(self):
        result_id = self._init_result()
        self.result_id = result_id
        if not result_id:
            raise UserError(message="初始化结果失败，请重试！")
        all_dataset_versions = []
        all_warning_nums = []
        warning_item_dataset_version_map = {}
        current_dataset_version = dataset_external_service.get_dataset_release_version_id(
            self.indicator_model.dataset_id
        )  # 当前版本
        for item in self.warning["items"]:
            if item["type"] == IndicatorWarningCompareType.Relative.value:
                target_dataset_version = self._get_target_dataset_version(
                    item["target_version_type"], item["target_version_type_value"]
                )  # 对比对象版本
                all_dataset_versions = [*all_dataset_versions, current_dataset_version, target_dataset_version]
                warning_item_dataset_version_map[item["id"]] = {
                    "current_version": current_dataset_version,
                    "target_version": target_dataset_version,
                }
            else:
                all_dataset_versions.append(current_dataset_version)
                warning_item_dataset_version_map[item["id"]] = {
                    "current_version": current_dataset_version,
                    "target_version": current_dataset_version,
                }
            all_warning_nums.append(item["indicator_num_id"])
        all_dataset_versions = list(set(all_dataset_versions))
        dataset_version_data_map = {}
        for dataset_version in all_dataset_versions:
            result = indicator_service.get_data(self.warning["indicator_id"], dataset_version)
            dataset_version_data_map[dataset_version] = self._build_dataset_version_data_map(result)
        warning_status, warning_result = self._run_warning_item(
            str(result_id), dataset_version_data_map, warning_item_dataset_version_map, current_dataset_version
        )
        self._finish_warning(str(result_id), warning_status, warning_result)
        self._send_warning_message(last_status=warning_status, result_id=str(result_id))

    def _warning_deadly(self, result_id: str, message: str):
        repository.update("dap_bi_dashboard_indicator_warning_result", {"message": message, "status": 4}, {"id": result_id})

    def _get_target_dataset_version(self, target_version_type: str, target_version_type_value):
        try:
            if target_version_type == IndicatorWarningTargetVersionType.Relative.value:
                # 上一个版本
                return dataset_external_service.get_dataset_last_history_version_id(
                    self.indicator_model.dataset_id, int(target_version_type_value)
                )
            if target_version_type == IndicatorWarningTargetVersionType.RelativeDay.value:
                # 相对天版本
                return dataset_external_service.get_dataset_date_history_version_id(
                    self.indicator_model.dataset_id, int(target_version_type_value)
                )
        except:
            return False
        if target_version_type == IndicatorWarningTargetVersionType.Fixed.value:
            return target_version_type_value
        return False

    def _finish_warning(self, result_id: str, warning_status: str, warning_result: List[Dict]):
        if not warning_result:
            # 没有明细结果的时候已经发生致命性错误
            return
        # 处理全局错误信息
        db_global_messages = {}
        for data_key, data in self.global_messages.items():
            num_field_id, message = data_key[0], data_key[1]
            if num_field_id not in db_global_messages:
                db_global_messages[num_field_id] = data
                del data["message"]
                db_global_messages[num_field_id]["messages"] = []
            db_global_messages[num_field_id]["messages"].append(message)
        re = repository.update(
            "dap_bi_dashboard_indicator_warning_result",
            {
                "indicator_id": self.warning["indicator_id"],
                "end_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": warning_status,
                "global_messages": json.dumps(list(db_global_messages.values())),
            },
            {"id": result_id},
        )
        data_list = [
            {"result_id": result_id, "indicator_id": self.indicator_model.id, "result": json.dumps(result)}
            for result in warning_result
        ]
        if not re:
            raise UserError(message=f"更新指标 {self.indicator_model.name} 为结束状态失败!")
        re, msg = indicator_repositories.batch_add_indicator_warning_result_item(data_list)
        if not re:
            raise UserError(message=f"批量插入结果明细失败，错误信息 {msg} !")

    def _send_warning_message(self, last_status: str, result_id: str):
        if not self.is_test and last_status in (
            IndicatorWarningResultStatus.Warning.value,
            IndicatorWarningResultStatus.Error.value,
        ):
            url = config.get("Indicator.warning_url")
            dommain = AppHosts.get(SkylineApps.DP, False)
            warning_url = "/".join(
                [dommain, "bi-visual/indicator_inspection/warning-detail", self.warning["indicator_id"], result_id]
            )
            env_name = auth_util.get_env_name()
            qywx_robot = QyRobotApi(url)
            qywx_robot.send_alarm_message(env_name, self.tenant, self.indicator_model.name, warning_url)

    def _init_result(self):
        start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        result_id = self.result_id or seq_id()
        re = repository.add_data(
            "dap_bi_dashboard_indicator_warning_result",
            {
                "id": result_id,
                "siblings_relation": self.warning["siblings_relation"],
                "start_time": start_time,
                "indicator_id": self.indicator_model.id,
                "status": IndicatorWarningResultStatus.Waiting.value,
                "is_show": 0 if self.is_test else 1,
            },
        )
        if not re:
            return False
        return result_id

    def _build_dataset_version_data_map(self, result: dict):
        data_map = {
            "data": result["data"],
            "position_data": {},
            "length": 0,
            "result": result["execute_status"],
            "message": result["msg"],
        }
        if result["execute_status"] != 200:
            return data_map
        if len(result["data"]) == 0:
            return data_map
        data_length = len(result["data"][0]["rows"])
        if not data_length:
            return data_map
        data_map["length"] = data_length
        for row_idx in range(0, data_length):
            position_keys = []
            position_values_map = {}
            for col in result["data"]:
                header = col["header"][0]
                if header["col_type"] == ColTypes.Num.value:
                    position_values_map[header["field_id"]] = {"header": header, "col_value": col["rows"][row_idx]}
                if header["col_type"] == ColTypes.Dim.value:
                    position_keys.append(self._get_position_key_item(header["field_id"], col["rows"][row_idx]))
            data_map["position_data"][tuple(position_keys)] = position_values_map
        return data_map

    def _validate_data_result(self, dataset_version, dataset_version_data_map):
        data = dataset_version_data_map[dataset_version]
        if data["length"] == 0:
            # 当前版本无数据 1. 取数报错 2. 无数据
            message = "取数接口出错，请联系管理员" if dataset_version_data_map[dataset_version]["result"] != 200 else "获取数据为空，请联系管理员"
            return False, message
        return True, ""

    def _run_warning_item(
        self,
        result_id: str,
        dataset_version_data_map: dict,
        warning_item_dataset_version_map: dict,
        current_dataset_version: str,
    ) -> Tuple[str, List[Dict]]:
        # 更新状态为检查中
        repository.update(
            "dap_bi_dashboard_indicator_warning_result",
            {"status": IndicatorWarningResultStatus.Checking.value},
            {"id": result_id},
        )
        if not current_dataset_version:
            self._warning_deadly(result_id, "当前数据集版本不存在，请联系管理员")
            return IndicatorWarningResultStatus.Error.value, []
        total = dataset_version_data_map[current_dataset_version]["length"]
        is_ok, message = self._validate_data_result(current_dataset_version, dataset_version_data_map)
        if not is_ok:
            self._warning_deadly(result_id, message)
            return IndicatorWarningResultStatus.Error.value, []
        indicator_nums_dict = self._get_indicator_nums_dict()
        current_data = dataset_version_data_map[current_dataset_version]["data"]
        indicator_dims_fieldid_dict = self._get_indicator_dims_fieldid_dict()
        warning_result = []
        last_status = (
            IndicatorWarningResultStatus.Warning.value
            if self.warning["siblings_relation"] == IndicatorWarningItemRelation.And.value
            else IndicatorWarningResultStatus.Success.value
        )
        for row_idx in range(0, total):
            dims = self._get_row_dims(current_data, indicator_dims_fieldid_dict, row_idx)
            nums_item_dict = {}
            last_status = self._compare_items(
                {
                    'current_dataset_version': current_dataset_version,
                    'dataset_version_data_map': dataset_version_data_map,
                    'indicator_nums_dict': indicator_nums_dict,
                    'last_status': last_status,
                    'nums_item_dict': nums_item_dict,
                    'row_idx': row_idx,
                    'warning_item_dataset_version_map': warning_item_dataset_version_map,
                }
            )
            self._get_num_result(nums_item_dict)
            warning_result.append({"dims": dims, "nums": list(nums_item_dict.values())})
        return last_status, warning_result

    def _get_num_result(self, nums_item_dict):
        for _, num_item in nums_item_dict.items():
            results = list(set([item["result"] for item in num_item["compare_items"]]))
            num_item["result"] = True
            if (
                self.warning["siblings_relation"] == IndicatorWarningItemRelation.And.value
                and len(results) == 1
                and not results[0]
            ):
                num_item["result"] = False
            if (
                self.warning["siblings_relation"] == IndicatorWarningItemRelation.Or.value
                and len(results) == 1
                and not results[0]
            ):
                num_item["result"] = False
            if self.warning["siblings_relation"] == IndicatorWarningItemRelation.Or.value and len(results) == 2:
                num_item["result"] = False

    def _compare_items(self, params: dict):
        current_dataset_version = params.get('current_dataset_version')
        dataset_version_data_map = params.get('dataset_version_data_map')
        indicator_nums_dict = params.get('indicator_nums_dict')
        last_status = params.get('last_status')
        nums_item_dict = params.get('nums_item_dict')
        row_idx = params.get('row_idx')
        warning_item_dataset_version_map = params.get('warning_item_dataset_version_map')
        for item in self.warning["items"]:
            indicator_num = indicator_nums_dict[item["field_id"]]
            num_field_id = indicator_num["field_id"]
            target_version = warning_item_dataset_version_map[item["id"]]["target_version"]
            self._assign_nums_item_dict(indicator_num, num_field_id, nums_item_dict)
            dataset_version = warning_item_dataset_version_map[item["id"]]["current_version"]
            position_key = self._get_position_key(row_idx, dataset_version_data_map[dataset_version]["data"])
            current_num_value, current_num_msg = self._get_num_value(
                dataset_version, position_key, num_field_id, dataset_version_data_map, current_dataset_version
            )
            num_item = {
                "type": item["type"],
                "operator": item["operator"],
                "target_version_type": int(item["target_version_type"]),
                "target_version_type_value": int(item["target_version_type_value"]),
                "value": item["value"],
                "result": True,
                "message": "",
                "target_version": target_version,
                "compare_method": item["compare_method"],
                "current": {"dataset_version": current_dataset_version, "value": current_num_value},
                "target": {"dataset_version": target_version, "value": None},
                "siblings_relation": self.warning["siblings_relation"],
            }
            # 环比
            if item["type"] == IndicatorWarningCompareType.Relative.value:
                # 环比版本不存在
                if not target_version:
                    num_item["result"] = False
                    num_item["message"] = "环比数据集版本不存在，环比预警不生效"
                    self._append_global_message(indicator_num, num_field_id, num_item)
                else:
                    is_ok, message = self._validate_data_result(target_version, dataset_version_data_map)
                    target_num_value, target_num_msg = self._get_num_value(
                        target_version, position_key, num_field_id, dataset_version_data_map, current_dataset_version
                    )
                    if is_ok and current_num_value is not None and current_num_value is not None:
                        num_item["current"]["value"] = current_num_value
                        num_item["target"]["value"] = target_num_value
                        num_item["result"], num_item["message"] = self._compare_value(
                            self._get_relative_value(
                                item["compare_method"],
                                self._convert_data_value(current_num_value),
                                self._convert_data_value(target_num_value),
                            ),
                            self._convert_data_value(item["value"]),
                            item["operator"],
                        )
                    else:
                        num_item["result"] = False
                        if not is_ok:
                            num_item["message"] = message
                        elif current_num_value is False:
                            num_item["message"] = current_num_msg
                        elif target_num_value is False:
                            num_item["message"] = target_version
                        self._append_global_message(indicator_num, num_field_id, num_item)
            else:
                # 直接对比
                if current_num_value is False:
                    num_item["result"] = False
                else:
                    num_item["result"], num_item["message"] = self._compare_value(
                        self._convert_data_value(current_num_value),
                        self._convert_data_value(item["value"]),
                        item["operator"],
                    )
                    num_item["target"]["value"] = current_num_value
            last_status = self._get_last_status_from_warning_item(last_status, num_item["result"])
            nums_item_dict[num_field_id]["compare_items"].append(num_item)
        return last_status

    def _append_global_message(self, indicator_num, num_field_id, num_item):
        global_message_key = tuple([num_field_id, num_item["message"]])
        if global_message_key not in self.global_messages:
            self.global_messages[global_message_key] = {
                "dataset_id": indicator_num["dataset_id"],
                "field_id": indicator_num["field_id"],
                "alias": indicator_num["alias"],
                "formula_mode": indicator_num["formula_mode"],
                "message": copy.deepcopy(num_item["message"]),
            }
        num_item["message"] = ''

    def _assign_nums_item_dict(self, indicator_num, num_field_id, nums_item_dict):
        if num_field_id not in nums_item_dict:
            nums_item_dict[num_field_id] = {
                "dataset_id": indicator_num["dataset_id"],
                "field_id": indicator_num["field_id"],
                "alias": indicator_num["alias"],
                "formula_mode": indicator_num["formula_mode"],
                "rank": indicator_num["rank"],
                "compare_items": [],
            }

    def _get_last_status_from_warning_item(self, last_status, result):
        if self.warning["siblings_relation"] == IndicatorWarningItemRelation.Or.value and (
            not result or not last_status
        ):
            last_status = IndicatorWarningResultStatus.Warning.value
        if self.warning["siblings_relation"] == IndicatorWarningItemRelation.And.value and (result or last_status):
            last_status = IndicatorWarningResultStatus.Success.value
        return last_status

    def _convert2number(self, value):
        try:
            value = float(value)
        except:
            value = 0
        return value

    def _convert_data_value(self, value):
        try:
            value = json.loads(value)
        except:
            pass
        if isinstance(value, (list, tuple)):
            return [self._convert2number(value[0]), self._convert2number(value[1])]
        return self._convert2number(value)

    def _get_row_dims(self, current_data, indicator_dims_fieldid_dict, row_idx):
        dims = []
        for col in current_data:
            header = col["header"][0]
            if header["col_type"] != ColTypes.Dim.value:
                continue
            dim_info = indicator_dims_fieldid_dict[header["field_id"]]
            dims.append(
                {
                    "dataset_id": header["dataset_id"],
                    "field_id": header["field_id"],
                    "alias": header["alias"],
                    "formula_mode": dim_info["formula_mode"],
                    "value": col["rows"][row_idx],
                }
            )
        return dims

    def _get_num_value(
        self,
        dataset_version: str,
        position_key: tuple,
        num_id: str,
        dataset_version_data_map: dict,
        current_dataset_version: str,
    ) -> Tuple[int, str]:
        indicator_nums_dict = self._get_indicator_nums_dict()
        nums_map = dataset_version_data_map[dataset_version]["position_data"].get(position_key, {})
        if not nums_map:
            # 对比细分维度不匹配 直接返回0
            return 0, "细分维度不匹配"
        num_field_id = indicator_nums_dict[num_id]["field_id"]
        if num_field_id not in nums_map:
            # 对比字段不存在
            message = "当前版本字段缺失，请检查指标配置是否正确" if dataset_version == current_dataset_version else "环比数据集版本字段缺失，环比预警不生效"
            return False, message
        col_value = nums_map.get(num_field_id).get("col_value", 0)
        if col_value is None:
            # 结果为None 的转换为0
            col_value = 0
        if not isinstance(col_value, (float, int)):
            # 结果不是 float int 的转换为0
            col_value = 0
        return col_value, ""

    def _get_position_key(self, row_idx: int, data: dict):
        position_keys = []
        for col in data:
            header = col["header"][0]
            if header["col_type"] != ColTypes.Dim.value:
                continue
            position_keys.append(self._get_position_key_item(header["field_id"], col["rows"][row_idx]))
        return tuple(position_keys)

    def _get_position_key_item(self, dim_field_id: str, col_value) -> Tuple:
        return tuple([dim_field_id, col_value])

    def _get_relative_value(self, compare_method, value, target_value):
        if compare_method == IndicatorWarningCompareMethod.Dif.value:
            return value - target_value
        else:
            if target_value == 0:
                if value == 0:
                    return 0
                return float("inf")
            return ((value - target_value) / target_value) * 100

    def _compare_value(self, value, target_value, operator):
        message = ""
        if operator == ">":
            result = value > target_value
            if result:
                message = f"目标值大于 {target_value}"
        elif operator == ">=":
            result = value >= target_value
            if result:
                message = f"目标值不小于 {target_value}"
        elif operator == "<":
            result = value < target_value
            if result:
                message = f"目标值小于 {target_value}"
        elif operator == "<=":
            result = value <= target_value
            if result:
                message = f"目标值不大于 {target_value}"
        elif operator == "=":
            result = value == target_value
            if result:
                message = f"目标值等于 {target_value}"
        elif operator == "!=":
            result = value != target_value
            if result:
                message = f"目标值不等于 {target_value}"
        elif operator == "between":
            result = value >= target_value[0] and value <= target_value[1]
            if result:
                message = f"目标值不在区间  [{target_value[0]}, {target_value[1]}] 内"
        elif operator == "is null":
            result = False
            if value is None:
                result = True
                message = "目标值为空"
        elif operator == "is not null":
            result = False
            if value is not None:
                result = True
                message = "目标值不为空"
        else:
            result = True
        return not result, message
