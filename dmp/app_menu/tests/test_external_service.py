#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
test external service
"""

import json
import logging
import unittest
from tests.base import BaseTest
from app_menu.services import external_service

logger = logging.getLogger(__name__)


class TestExternalService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='jifenglin', account='admin')

    def test_check_dashboard_in_application(self):
        application_id = "39e7293c-ac45-f084-9e5d-cd08ea51ca06"
        dashboard_id = "39f0e484-935c-da1f-5b16-c6470ee062c9"
        result = external_service.check_dashboard_in_application(application_id, dashboard_id)
        print(result)


if __name__ == '__main__':
    # unittest.main()
    s = unittest.TestSuite()
    s.addTest(TestExternalService("test_check_dashboard_in_application"))
    runner = unittest.TextTestRunner()
    runner.run(s)
