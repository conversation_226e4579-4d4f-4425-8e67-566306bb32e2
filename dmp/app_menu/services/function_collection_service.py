#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401
import logging
import traceback

from dmplib.hug import g
from dmplib.utils.errors import UserError
from base import repository
from app_menu.repositories import function_collection_repository
from app_menu.models import MoveCollectionModel

logger = logging.getLogger(__name__)


def save_func_collection(application_id, function_id):
    """
    门户收藏
    """
    if not application_id:
        raise UserError(message="门户ID不能为空")
    if not function_id:
        raise UserError(message="菜单ID不能为空")
    account = get_user_account()
    if not account:
        raise UserError(message="用户信息不能为空")
    result = repository.get_data('dap_bi_release_function', {'application_id': application_id, 'id': function_id})
    if not result:
        raise UserError(message="没有找到对应的菜单，请发布后重试")
    # 判断是否已收藏
    where = {'application_id': application_id, 'function_id': function_id, 'account': account}
    collection_info = repository.get_one('dap_bi_function_collection', where, ['id'])
    if not collection_info:
        where['sort'] = function_collection_repository.get_max_sort_by_application_id(application_id, account)
        where['rank'] = function_collection_repository.get_max_rank_by_account(account)
        repository.add_data('dap_bi_function_collection', where)
    return True


def remove_func_collection(application_id, function_id):
    """
    门户菜单取消收藏
    """
    if not application_id:
        raise UserError(message="门户ID不能数为空")
    if not function_id:
        raise UserError(message="菜单ID不能数为空")
    account = get_user_account()
    if not account:
        raise UserError(message="用户信息不能数为空")
    data = {'application_id': application_id, 'function_id': function_id, 'account': account}
    return repository.delete_data('dap_bi_function_collection', data)


def get_user_account():
    if g.account:
        return g.account
    if hasattr(g, 'external_params') and g.external_params.get('external_user_id'):
        return g.external_params.get('external_user_id')
    if hasattr(g, 'extend_yl_params') and g.extend_yl_params.get('external_user_id'):
        return g.extend_yl_params.get('external_user_id')


def get_function_collection_by_function_ids(function_ids, sort_field='sort'):
    if not function_ids:
        return []
    account = get_user_account()
    collect_list = function_collection_repository.get_function_collection_by_account(account, function_ids, sort_field)
    return collect_list


def get_user_function_collection_by_function_ids(request, kwargs):
    from user.services import user_service
    app_data = user_service.get_user_application_index(request, kwargs)
    function_ids = []
    collect_list = []
    for app in app_data:
        app_function = app.get('function') or []
        function_ids += get_auth_function_ids_by_tree(app_function)
    if function_ids:
        collect_list = get_function_collection_by_function_ids(function_ids, sort_field='rank')
    return collect_list


def get_auth_function_ids_by_tree(function_tree: list):
    function_ids = []
    for function in function_tree:
        function_ids.append(function.get('id'))
        if function.get('sub'):
            sub_ids = get_auth_function_ids_by_tree(function.get('sub'))
            function_ids = function_ids + sub_ids
    return function_ids


def move_function_collection(model: MoveCollectionModel):
    account = get_user_account()
    # 查询移动的菜单是否存在
    field = ['id', 'function_id', 'sort', 'rank']
    where = {'function_id': model.function_id, 'account': account, 'application_id': model.application_id}
    function_info = repository.get_one('dap_bi_function_collection', where, field)
    if not function_info:
        logger.info('没有查询到收藏菜单信息,菜单ID：{},用户：{}'.format(model.function_id, account))
        return
    try:
        if model.target_id:
            application_id = model.application_target_id if model.application_target_id else model.application_id
            where = {'function_id': model.target_id, 'account': account, 'application_id': application_id}
            target_info = repository.get_one('dap_bi_function_collection', where, field)
            if not target_info:
                logger.info('没有查询到目标收藏菜单信息,菜单ID：{},用户：{}'.format(model.target_id, account))
                return
            if model.sort_field == 'sort':
                function_collection_repository.update_function_collect_sort(function_info.get('id'), model.application_id, target_info.get(model.sort_field), account)
            else:
                function_collection_repository.update_all_function_collect_rank(function_info.get('id'), target_info.get(model.sort_field), account)
        else:
            if model.sort_field == 'sort':
                sort = function_collection_repository.get_max_sort_by_application_id(model.application_id, account)
            else:
                sort = function_collection_repository.get_max_rank_by_account(account)
            repository.update('dap_bi_function_collection', {model.sort_field: sort}, {'id': function_info.get('id')})
    except Exception as e:
        logger.error(f"调整收藏菜单顺序异常: {e}, 错误信息: {traceback.format_exc()}")
        raise UserError(message=str(e))
    return True

