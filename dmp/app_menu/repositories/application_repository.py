#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> on 2017/6/28.
"""
from business_tag.services import tag_relation_service
from dmplib.saas.project import get_db
from datetime import datetime, timedelta
from base import repository

from app_menu.models import ApplicationQueryModel


def get_application_list(query_model: ApplicationQueryModel) -> ApplicationQueryModel:
    """
    获取应用列表
    :param app_menu.models.ApplicationQueryModel query_model:
    :return tuple:
    """
    sql = """
        select `id`,`name`,`platform`,`description`,`icon`,`url`,`target`,`is_buildin`,`rank`,
            `enable`,`collapse`,`created_by`, `theme`, `menu_display_type`, `type_access_released`,`created_on`,`distribute_type`
            ,`relation_id`, `is_system`, `modified_on`,  `modified_by`
        from `dap_bi_application`
    """
    params = {}
    wheres = []
    if query_model.keyword:
        wheres.append('( `name` LIKE :keyword  OR  `description` LIKE :keyword)')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.is_buildin is not None:
        wheres.append('`is_buildin` = :is_buildin')
        params['is_buildin'] = query_model.is_buildin
    if query_model.enable is not None:
        if isinstance(query_model.enable, str):
            query_model.enable = query_model.enable.split(",")
        if isinstance(query_model.enable, list):
            query_model.enable = [int(i) for i in query_model.enable]
            wheres.append('`enable` in (:enable)')
        else:
            wheres.append('`enable` = :enable')
        params['enable'] = query_model.enable
    if query_model.begin_time:
        wheres.append('`modified_on` >= :start_time')
        params['start_time'] = query_model.begin_time
    if query_model.end_time:
        wheres.append('`modified_on` <= :end_time')
        end_time = query_model.end_time
        if query_model.begin_time == query_model.end_time:  # 2018-02-01 -> 2018-02-02
            end_time = datetime.strptime(query_model.end_time, '%Y-%m-%d')
            end_time = end_time + timedelta(days=1)
            end_time = end_time.strftime('%Y-%m-%d')
        params['end_time'] = end_time
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY `rank` '
    with get_db() as db:
        query_model.total = repository.get_total(sql,  params, db)
        if not query_model.skip_paging_flag:
            sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    tag_relation_service.append_tags_to_list(query_model.items, 2)
    return query_model


def get_application_by_id_list(id_list):
    """
    获取应用
    :param list id_list:
    :return:
    """
    sql = '''select `id`,`name`,`description`,`icon`,`url`,`target`,`is_buildin`,`rank`,`enable`,`collapse`, `platform`,
            `type_access_released`,`distribute_type` from `dap_bi_application` '''
    params = None
    if id_list:
        params = {'ids': id_list}
        sql += 'where id in (:ids)'
    sql += 'order by `is_buildin` desc ,`rank` '
    with get_db() as db:
        return db.query(sql, params)


def get_application_by_relation(relation_app_id):
    """
    通过关联门户信息查找门户
    :param relation_app_id:
    :return:
    """
    sql = "select * from `dap_bi_application` where `relation_id` = :relation_id"
    params = {"relation_id": relation_app_id}
    with get_db() as db:
        return db.query_one(sql, params)


def update_application_relation(cur_app_model, relation_app_id=""):
    """
    更新门户的关联信息
    :param cur_app_model:
    :param relation_app_id:
    :return:
    """
    with get_db() as db:
        try:
            app_table_name = "dap_bi_application"
            db.begin_transaction()
            # 当前门户关联
            db.update(app_table_name, {"relation_id": relation_app_id}, {"id": cur_app_model.id})
            # 原关联门户关联数据清除
            if cur_app_model.relation_id and cur_app_model.relation_id != relation_app_id:
                db.update(app_table_name, {"relation_id": ""}, {"id": cur_app_model.relation_id})
            # 目标门户关联
            if relation_app_id:
                db.update(app_table_name, {"relation_id": cur_app_model.id}, {"id": relation_app_id})
            db.commit()
            return True, '更新成功'
        except Exception as e:
            db.rollback()
            return False, f"数据更新异常，errs:{str(e)}"


def clear_application_relation(app_id):
    """
    清除指定门户的关联信息
    :param app_id:
    :return:
    """
    if not app_id:
        return False
    with get_db() as db:
        return db.update("dap_bi_application", {"relation_id": ""}, {"id": app_id})


def get_application_by_func_id(func_id):
    sql = "select * from `dap_bi_application` where `id` = (select `application_id`  from `dap_bi_function` where `id`=:func_id limit 1) limit 1"
    params = {"func_id": func_id}
    with get_db() as db:
        return db.query_one(sql, params) or {}
