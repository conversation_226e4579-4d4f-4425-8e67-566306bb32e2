# -*- coding: utf-8 -*-
"""
    关键字外部服务类
    <NAME_EMAIL> on 2021/4/19.
"""
import json
import logging
import traceback
from typing import Dict, List, Union
import requests
import curlify
from requests.exceptions import RequestException

from dmplib.components import auth_util
from dmplib.hug import g
from dmplib.saas.project import get_db
from system.services import system_setting_service
from dataset.services import dataset_service, dataset_field_service, advanced_field_service, dataset_field_group_service
from base.enums import AddFuncType
from dmplib.components.enums import SkylineApps
from dmplib.components.app_hosts import AppHosts
from dmplib.locale import trans_msg
from dmplib.utils.errors import UserError
from dmplib import config
from ppt.services.ppt_service import get_jwt_token
from components.data_center_api import request_data_center
from data_source.enums.mysoft_new_erp_enums import MysoftNewERPAPI
from dmplib.hug import debugger
from components.redis_utils import stale_cache
from urllib.parse import urljoin

_debugger = debugger.Debug(__name__)
logger = logging.getLogger(__name__)


def get_dataset_tree(parent_id: None = '', exclude_types: None = None, start_time: None = None, end_time: None = None,
                     with_filterable=False):
    """
    :param end_time:
    :param start_time:
    :param exclude_types:
    :获取数据集树形结构
    :param str parent_id :
    :param with_filterable
    :return list:
    """
    return dataset_service.get_dataset_tree(parent_id, exclude_types, start_time, end_time, with_filterable)


def get_dataset_field(dataset_id: str, is_not_category: bool = False, include_dataset_vars: bool = False):
    """
    根据dataset_id获取数据集字段
    :param dataset_id: 数据集ID
    :param is_not_category: 是否进行分类
    :param include_dataset_vars: 是否报告数据集变量
    :return:
    """
    return dataset_field_service.get_dataset_field(dataset_id, is_not_category, include_dataset_vars)


def get_field_group(dataset_id):
    return dataset_field_group_service.get_field_group_by_dataset_id(dataset_id)


def del_diagonal(dataset_fields: List[Dict[str, Union[str, int, None]]]) -> List[Dict[str, Union[str, int, None]]]:
    """
    去除高级字段输入框里面的双斜杆
    :param dataset_fields:
    :return:
    """
    return advanced_field_service.del_diagonal(dataset_fields)


def _fetch(url, params, headers=None, method="GET"):
    try:
        if method == "GET":
            res = requests.get(url, params, headers=headers, timeout=10)
        else:
            res = requests.post(url, json=params, headers=headers, timeout=10)
        _debugger.log({
            '_fetch请求的信息': curlify.to_curl(res.request, compressed=True),
            '返回的信息': res.text
        })
        res.encoding = "utf-8"
        if res.status_code != 200:
            raise UserError(message="请求异常：{}".format(res.text))
        return res.json()
    except RequestException as e:
        raise UserError(code=5005, message=f"{str(e)}") from e
    except Exception as e:
        raise UserError(code=5006, message=f"{str(e)}") from e


def get_ppt_list(**kwargs):
    """
    获取ppt列表
    """
    try:
        kwargs['token'] = get_jwt_token(config.get('PPT.sso_secret'))
        host = _get_ppt_host()
        if not host:
            return []
        res = _fetch(
            url=f"{host}/open/api/third/report/publishedReportList",
            params=kwargs,
            method="GET"
        )
        data = res.get("data")
        for row in data:
            row[
                'super_portal_integrate_url'] =urljoin(AppHosts.get(SkylineApps.DP, False),f"api/user/superportal/dashboard?report_id={row.get('id')}&type={AddFuncType.Ppt.value}&release_type={row.get('type_access_released')}")
            row['report_type'] = AddFuncType.Ppt.value
        return data
    except Exception as e:
        logger.error("get ppt list error:{}".format(str(e)))
        return []


def _get_ppt_host():
    """
    ppt在线报告接口请求支持内网host
    :return:
    """
    domain = config.get('PPT.private_host')
    if not domain:
        domain = urljoin(AppHosts.get(SkylineApps.DP, False), 'bi-slide')
    return domain


def get_active_report_list(**kwargs):
    """
    获取ar列表
    """
    try:
        sql = 'select * from dap_bi_dashboard '
        params = {}
        wheres = []
        dashboard_type = kwargs.get('type', None)
        if dashboard_type == 'FILE':
            wheres.append("type='FILE' and application_type in (5,6) and status=1")
        elif dashboard_type == 'FOLDER':
            wheres.append("type='FOLDER' and application_type in (5,6)")
        elif dashboard_type is None or dashboard_type == "":
            wheres.append("application_type in (5,6) and ((type='FILE' and status=1) or type='FOLDER')")
        else:
            return []

        parent_id = kwargs.get('parent_id', None)
        if parent_id:
            wheres.append('parent_id=%(parent_id)s')
            params["parent_id"] = parent_id

        sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
        with get_db() as db:
            data = db.query(sql, params)
        for row in data:
            row[
                'super_portal_integrate_url'] =urljoin(AppHosts.get(SkylineApps.DP, False),f"api/user/superportal/dashboard?report_id={row.get('id')}&type={AddFuncType.ActiveReport.value}&release_type={row.get('type_access_released')}")
            row['report_type'] = AddFuncType.ActiveReport.value
        return data
    except Exception as e:
        logger.error("get active report list error:{}".format(str(e)))
        return []


def get_menu_report_list(**kwargs):
    """
    获取门户的统计报告列表
    """
    try:
        kwargs['token'] = get_jwt_token(config.get('PPT.sso_secret'))
        url =urljoin(AppHosts.get(SkylineApps.DP, False) , 'bi-report/management')
        res = _fetch(
            url=f"{url}/api/open/dmp/menu_report_list",
            params=kwargs,
            method="GET"
        )
        data = res.get("data", [])
        return data
    except Exception as e:
        logger.error("get menu report list error:{}".format(str(e)))
        return []


@stale_cache(prefix='report_1_0', expire=300)
def get_erp_report_site_url():
    result = get_erp_report_site_url_from_db()
    if result:
        return result
    result = get_erp_report_site_url_from_config_center()
    if result:
        return result
    result = get_erp_report_site_url_from_data_center()
    return result

def get_erp_report_site_url_from_config_center():
    url = AppHosts.get(SkylineApps.SJBB, False)
    if not url:
        logger.warning("配置中心获取ERP报表服务地址为空")
        return None
    management_url = url.replace('/rpt-view', '/rpt-management')
    return (url, management_url)

def get_erp_report_site_url_from_db():
    setting = system_setting_service.get_system_setting_item('data_center', 'report_service_url')
    if not setting:
        return None
    setting_value = setting.get('value', "{}")
    result = json.loads(setting_value)
    report_service_url = result.get('ReportServiceUrl', '').rstrip('/')
    report_service_management_url = result.get('ReportServiceManagementUrl', '').rstrip('/')
    if not report_service_url and not report_service_management_url:
        return None
    return (report_service_url,
            report_service_management_url)


@stale_cache(prefix='report_1_0', expire=300)
def get_erp_report_list_setting():
    setting = system_setting_service.get_system_setting_item('data_center', 'report_service_url')
    if not setting:
        return None
    setting_value = setting.get('value', "{}")
    result = json.loads(setting_value)
    return result


def get_erp_report_site_url_from_data_center():
    result = None
    try:
        result = request_data_center(MysoftNewERPAPI.GetERPReportSiteUrl.value)
        if isinstance(result, list):
            result = result[0]
        front_url = result.get('ReportServiceUrl', '').rstrip('/')
        back_url = result.get('ReportServiceManagementUrl', '').rstrip('/')
        return front_url, back_url
    except Exception:
        logger.error(f'result: {result}, 获取erp报表站点url失败: {traceback.format_exc()}')
        raise UserError(message=trans_msg('获取erp报表站点url失败'))


def get_menu_erp_report_list(**kwargs):
    """
    获取门户的erp报告列表
    """
    res = None
    err = ''
    try:
        erp_report_list_setting = get_erp_report_list_setting()
        if erp_report_list_setting and erp_report_list_setting.get('FetchType', '') == 'jkgj':
            # 由接口管家获取数据
            return get_menu_erp_report_list_from_jkgj(erp_report_list_setting.get('erp_api_info_id', None)), err

        front_url, _ = get_erp_report_site_url()
        kwargs['token'] = get_jwt_token(config.get('PPT.sso_secret'))
        res = _fetch(
            url=f"{front_url}/api/open/dmp/report_list",
            params=kwargs,
            method="GET"
        )
        print('xxxxxxxxxxxx', res)
        data = res.get("data", [])
        return data, err
    except Exception:
        err = f"res: {res}, get menu erp report list error: {traceback.format_exc()}"
        logger.error(err)
        return [], err


def get_menu_erp_report_list_from_jkgj(erp_api_info_id):
    """
    从接口管家获取门户的erp报告列表
    """
    try:
        result = request_data_center(MysoftNewERPAPI.GetERPReportList.value, erp_api_info_id=erp_api_info_id)
        if isinstance(result, list):
            result = result[0]
        report_list_result = json.loads(result.get('Data', '[]'))
        return report_list_result.get("data", [])
    except Exception:
        logger.error(f'获取erp报表列表失败: {traceback.format_exc()}')
        raise UserError(message='获取erp报表列表失败')
