#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/6.
"""
from base import repository
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id


def add_line(model):
    """
    添加流程节点连线
    :param flow.models.FlowNodeLineModel model:
    :return:
    """
    model.id = seq_id()
    model.validate()
    fields = ['id', 'flow_id', 'ahead_node_id', 'behind_node_id']
    return repository.add_model('dap_bi_line', model, fields)


def update_line(model):
    """
    修改流程节点连线
    :param flow.models.FlowNodeLineModel model:
    :return:
    """
    model.validate()
    fields = ['flow_id', 'ahead_node_id', 'behind_node_id']
    return repository.update_model('dap_bi_line', model, {'id': model.id}, fields)


def delete_line(line_id):
    """
    删除流程节点连线
    :param str line_id:
    :return:
    """
    if not line_id or not repository.data_is_exists('dap_bi_line', {'id': line_id}):
        raise UserError(message='流程节点连线不存在')
    return repository.delete_data('dap_bi_line', {'id': line_id})


def delete_line_by_flow_id(flow_id):
    """
    删除流程下所有节点连线
    :param str flow_id:
    :return:
    """
    return repository.delete_data('dap_bi_line', {'flow_id': flow_id})


def get_line_id_list_by_flow_id(flow_id):
    """
    获取流程下所有节点连线id
    :param str flow_id:
    :return list:
    """
    return repository.get_columns('dap_bi_line', {'flow_id': flow_id}, 'id')


def get_lines_by_flow_id(flow_id):
    """
    获取流程下所有节点连线
    :param flow_id:
    :return:
    """
    fields = ['id', 'flow_id', 'ahead_node_id', 'behind_node_id']
    return repository.get_data('dap_bi_line', {'flow_id': flow_id}, fields, True)
