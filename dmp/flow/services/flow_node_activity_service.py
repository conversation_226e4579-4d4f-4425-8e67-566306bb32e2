#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/18.
"""
from dmplib.utils.errors import UserError
from flow.repositories import flow_node_activity_repository


from datetime import datetime
from typing import Dict, List, Union


def delete_activity_by_flow_id(flow_id):
    """
    删除节点执行记录
    :param str flow_id:
    :return:
    """
    if not flow_id:
        raise UserError(message='请指定指标模板')
    return flow_node_activity_repository.delete_activity_by_flow_id(flow_id)


def get_activity_list(instance_id: str) -> List[Dict[str, Union[str, int, datetime]]]:
    """
    获取实例节点
    :param str instance_id:
    :return list:
    """
    if not instance_id:
        raise UserError(message='请指定实例id')
    return flow_node_activity_repository.get_activity_list(instance_id)
