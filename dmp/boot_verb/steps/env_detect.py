#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : env_detect.py
# @Author: guq  
# @Date  : 2021/5/25
# @Desc  :

import os

from dmplib.umbrella.core.dog_type import SUPPORTED_DOG_TYPES_LOWER
from dmplib.umbrella.core import exceptions
from dmplib import config

# 所有的环境调试指定
# os.environ['CONFIG_CENTER_URL'] = 'http://10.20.23.2:507621'
# os.environ['DOG_LIB_PATH'] = '/home/<USER>/workspace/boot_verb/lib'



def which_dog_type():
    # _supported_dog_type = [v.value for v in DogType._member_map_.values()]
    # supported_dog_type = [i.lower() for i in _supported_dog_type]
    supported_dog_type = SUPPORTED_DOG_TYPES_LOWER.copy()

    # dog_type = config.get('App.dog_type', '')
    dog_type = ''
    if dog_type.lower() not in supported_dog_type:
        raise exceptions.BootEnvError('请检查app.config中dog_type设置，当前为：%s' % dog_type)
    return dog_type


if __name__ == '__main__':
    which_dog_type()
