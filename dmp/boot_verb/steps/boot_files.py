#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : boot_files.py
# @Author: guq  
# @Date  : 2021/5/24
# @Desc  :


import traceback
from boot_verb.utils.logger import logger
from dmplib.umbrella.core import exceptions

from boot_verb.boot_config import BootConfig
from boot_verb.steps.config_center import ConfigCenter
from boot_verb.steps.license_from_database import LicesnseFromDatabase


class FilesPrepare:
    def __init__(self, dog_type):
        self.dog_type = dog_type

    def files_dispatch(self):
        func_name = 'for_%s' % self.dog_type.lower()
        return getattr(self, func_name)

    def get_licenses_path(self):
        return self.files_dispatch()()

    def for_productdog(self):
        """如果连不上配置中心，需要尝试从配置库直接取"""

        # try:
        #     dog_ini_path, license_path, license_dog_path = ConfigCenter().pulling_files()
        # except Exception as e1:
        #     logger.debug('从配置配置中心拉取license和狗地址失败， 尝试从配置库拉取，原因： %s' % traceback.format_exc())
        #     try:
        #         dog_ini_path, license_path, license_dog_path = LicesnseFromDatabase().pulling_files(need_write_dog=True)
        #     except Exception as e2:
        #         raise exceptions.BootEnvError('先连接配置中心失败（%s），再从配置库取也是失败（%s）.' % (e1.msg, e2.msg))

        try:
            dog_ini_path, license_path, license_dog_path, = LicesnseFromDatabase().pulling_files(need_write_dog=True)
        except Exception as e1:
            logger.debug('从配置库拉取失败，原因： %s' % traceback.format_exc())
            try:
                dog_ini_path, license_path, license_dog_path = ConfigCenter().pulling_files()
            except Exception as e2:
                raise exceptions.BootEnvError('先从配置库取失败（%s），再从配置配置中心拉取也是失败（%s）.' %
                                              (self._get_error(e1), self._get_error(e2)))

        return dog_ini_path, license_path, license_dog_path

    def _get_error(self, e):
        if hasattr(e, 'msg'):
            return e.msg
        return str(e)

    def for_productsldog(self):
        return self.for_productdog()

    def for_clouddogforsaas(self):
        dog_ini_path, license_path, license_dog_path = LicesnseFromDatabase().pulling_files()
        return dog_ini_path, license_path, license_dog_path

    def for_clouddog(self):
        _dog_ini_path, _license_path, _license_dog_path = LicesnseFromDatabase().pulling_files()
        return _dog_ini_path, _license_path, _license_dog_path

    def for_developerdog(self):
        dog_ini_path = ''
        license_path = BootConfig.LICENSE2_FILE_PATH
        # 需要手动放入
        license_dog_path = BootConfig.DEVELOPER_DOG_FILE_PATH
        return dog_ini_path, license_path, license_dog_path
