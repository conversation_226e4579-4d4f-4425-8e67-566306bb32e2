#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : config_center.py
# @Author: guq
# @Date  : 2021/5/24
# @Desc  :
import base64
from boot_verb.utils.base import FilesProviderBase
from dmplib.umbrella.core import exceptions
from boot_verb.boot_config import BootConfig
from dmplib.hug.context import DBContext
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from base import repository


class LicesnseFromDatabase(FilesProviderBase):

    @staticmethod
    def get_license():

        thread_local_g = _AppCtxGlobals()
        _app_ctx_stack.push(thread_local_g)
        db_ctx = DBContext()
        db_ctx.inject(thread_local_g)

        license_data = repository.get_one(
            table_name='dap_bi_license_config', from_config_db=True, conditions={}, order_by='create_on desc',  # noqa
            fields=['license', 'license_dog', 'dog_host']
        )

        db_ctx.close_all()
        return license_data

    def pulling_files(self, need_write_dog=False):
        self.files_prepare()

        license_data = self.get_license()
        if not license_data:
            raise exceptions.BootEnvError('配置库中没有配置license文件！')

        try:
            license_content = base64.b64decode(license_data['license']).decode()
            license_dog_content = base64.b64decode(license_data['license_dog']).decode()
        except:
            raise exceptions.BootEnvError('从配置库中读取的license格式有问题，base64解码失败！')

        # 写文件
        license_path = self.write_license(license_content)
        license_dog_path = self.write_dog_license(license_dog_content)

        if need_write_dog:
            # 需要从配置库读取狗地址
            if not license_data['dog_host']:
                raise exceptions.BootEnvError('从配置库中读取的dog_host为空！')
            dog_ini_path = self.write_dog_ini_file(license_data['dog_host'])
        else:
            dog_ini_path = ''

        return dog_ini_path, license_path, license_dog_path

    def write_license(self, license_content):
        p = BootConfig.LICENSE2_FILE_PATH
        self.write_file(p, license_content)
        return p

    def write_dog_license(self, license_dog_content):
        p = BootConfig.LICENSE_DOG_FILE_PATH
        self.write_file(p, license_dog_content)
        return p

    def write_dog_ini_file(self, dog_url):
        content = """errorlog=1
requestlog=1
%s
broadcastsearch=1
""" % '\n'.join(['serveraddr=%s' % i for i in dog_url.split(',')])  # 多个是以逗号分隔

        p = BootConfig.DOG_INI_FILE_PATH
        self.write_file(p, content)
        return p
