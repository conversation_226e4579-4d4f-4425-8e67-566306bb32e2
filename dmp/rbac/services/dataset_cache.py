import json
from dmplib.redis import conn as conn_redis
from ..repositories import data_permission_repository


class DatasetFilterCache:
    """
    数据集过滤条件缓存
    """

    def __init__(self, dataset_id):
        self.dataset_id = dataset_id
        self.redis = conn_redis()

    def get_cache_data(self):
        data = self.redis.get(self.generate_key())
        if not data:
            data = self.get_data()
            self.redis.set(self.generate_key(), json.dumps(data), 3600 * 5)
        else:
            data = json.loads(data.decode('utf-8'), encoding='utf-8')
        return data

    def generate_key(self):
        return 'dmp:rbac:dataset:filter:%s' % self.dataset_id

    def get_data(self):
        rows = data_permission_repository.get_dataset_filter(self.dataset_id)
        for row in rows:
            row['dataset_filter'] = row.get("dataset_filter") if row.get("dataset_filter") else []
            row['hide_field_ids'] = row['hide_field_ids'].split(',') if row['hide_field_ids'] else []
        return rows

    def refresh(self):
        self.redis.delete(self.generate_key())


class ExternalSubjectFilterCache(DatasetFilterCache):
    """
    外部主题过滤条件缓存
    """

    def generate_key(self):
        return 'dmp:rbac:external_subject:filter:%s' % self.dataset_id

    def get_data(self):
        rows = data_permission_repository.get_dataset_filter(self.dataset_id, data_type='external_subject')
        for row in rows:
            row['dataset_filter'] = row.get("dataset_filter") if row.get("dataset_filter") else []
            row['hide_field_ids'] = row['hide_field_ids'].split(',') if row['hide_field_ids'] else []
        return rows
