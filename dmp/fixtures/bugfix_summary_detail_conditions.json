{"dashboard_id": "3a00bfe8-61a6-0672-42a0-a847306fcd25", "chart_params": [{"id": "81787c3f-5b24-11ec-8c10-1fd17ab5d804", "report_id": "3a00bfe8-61a6-0672-42a0-a847306fcd25", "dashboard_id": "3a00bfe8-61a6-0672-42a0-a847306fcd25", "chart_code": "analysis_table", "data_logic_type_code": "column", "conditions": [], "external_subject_ids": ["03e0d033-5327-11ec-8759-fa163e4b4cc9"], "penetrate_conditions": [], "penetrate_filter_conditions": [], "filter_conditions": [], "chart_filter_conditions": [], "common_datetime_conditions": [], "chart_linkage_conditions": [], "drill_conditions": [], "self_service_conditions": [{"alias_name": "公司ID", "col_name": "c_company_id", "col_value": "[\"c1\",\"c3\"]", "operator": "in", "dataset_field_id": "8a5d16e9-8486-e807e-ceaa7-d7729", "data_type": "字符串", "format": null, "formula_mode": null, "condition_type": 5}, {"alias_name": "项目生成日期", "col_name": "p_datax_time", "col_value": "2021-12-10 00:00:00", "operator": ">=", "dataset_field_id": "2f2cddda-b363-eb0de-915e0-bd7e9", "data_type": "日期", "format": "%Y-%m-%d %H:%i:%S", "formula_mode": "second", "condition_type": 2}, {"alias_name": "项目生成日期", "col_name": "p_datax_time", "col_value": "2021-12-10 23:59:59", "operator": "<=", "dataset_field_id": "2f2cddda-b363-eb0de-915e0-bd7e9", "data_type": "日期", "format": "%Y-%m-%d %H:%i:%S", "formula_mode": "second", "condition_type": 2}, {"col_name": "CCOMPANYID_9572979092", "col_value": "c1", "operator": "=", "dataset_field_id": "8a5d16e9-8486-e807e-ceaa7-d7729"}], "dims": [], "nums": [], "dashboard_conditions": [], "query_vars": [], "pagination": {"page": 1, "total": 0, "page_size": 150}, "column_display": [{"dataset_id": "00000000-0000-0000-0000-000000000001", "dataset_field_id": "8a5d16e9-8486-e807e-ceaa7-d7729", "data_type": "字符串", "col_name": "CCOMPANYID_9572979092", "alias_name": "公司ID", "order": 0, "rank": 0, "col_type": "dim", "is_show": 1, "group": null}, {"dataset_id": "00000000-0000-0000-0000-000000000001", "dataset_field_id": "a3c8ebfc-5cf5-9037e-b4bc9-ad2c8", "data_type": "字符串", "col_name": "CNAME_7904956178", "alias_name": "公司名称", "order": 1, "rank": 1, "col_type": "dim", "is_show": 1, "group": null}, {"dataset_id": "00000000-0000-0000-0000-000000000001", "dataset_field_id": "30aeb209-4a4d-68523-11a03-01103", "data_type": "数值", "col_name": "CCOMPANYMONEY_10504704751", "alias_name": "公司金额", "order": 2, "rank": 2, "col_type": "dim", "is_show": 1, "group": null}, {"dataset_id": "00000000-0000-0000-0000-000000000001", "dataset_field_id": "1d8b5457-179b-1e409-e1d5b-637a6", "data_type": "字符串", "col_name": "PCOMPANYID_9583202721", "alias_name": "项目公司ID", "order": 3, "rank": 3, "col_type": "dim", "is_show": 1, "group": null}, {"dataset_id": "00000000-0000-0000-0000-000000000001", "dataset_field_id": "2f2cddda-b363-eb0de-915e0-bd7e9", "data_type": "日期", "col_name": "PDATAXTIME_9577763230", "alias_name": "项目生成日期", "order": 4, "rank": 4, "col_type": "dim", "is_show": 1, "group": null}, {"dataset_id": "00000000-0000-0000-0000-000000000001", "dataset_field_id": "ada31283-19fc-bdb76-16404-800f3", "data_type": "字符串", "col_name": "BNAME_7904562961", "alias_name": "楼栋名称", "order": 5, "rank": 5, "col_type": "dim", "is_show": 1, "group": null}]}]}