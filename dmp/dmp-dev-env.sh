#!/bin/sh
# dmp docker开发环境部署工具
# wuhm@mingyuanyun 2018年02月10日
#


env_server="https://dmp-management-test2.mypaas.com.cn"
client_code="dmp_whm"
client_key=88bf3447ca651a83
dev_dir=/Users/<USER>/devel/py_workspace/dmp
dev_dir=/Users/<USER>/ming/dmp

docker_cmd=`which docker`

if [ $? -ne 0 ]; then

	echo "docker 命令未找到"
fi


set_project(){
``
#    project_name="dmp"

	echo "请输入你要运行的项目: dmp 或 dmp-admin"
	read project_name

#	echo "project_name="$project_name
	if [ "$project_name" != "dmp" ] && [ "$project_name" != "dmp-admin" ];then

		echo "目前只支持 dmp和dmp-admin 项目,请重新输入 \n"
		set_project

	fi

}


if [ -z "$project_name" ];then
	set_project
fi


echo "请输入client_code，不输入默认是 $client_code:"
read -t 5 my_client_code

if [ "$my_client_code" != "$client_code" ] && [ -n "$my_client_code" ];then

	client_code="$my_client_code"
	echo "请输入 $my_client_code client_key"
	read -t 5 client_key
fi

# 输入开发目录

echo "请输入你的代码目录，不输入默认是 $dev_dir"
read  my_dev_dir

if [ "$my_dev_dir" != "$dev_dir" ] && [ -n "$my_dev_dir" ];then
	dev_dir="$my_dev_dir"
fi


run_redis_docker(){

    echo "正在启动redis docker ，请稍候..."
    $docker_cmd pull daocloud.io/library/redis:3.2.8
    $docker_cmd stop redis
    $docker_cmd rm redis
    $docker_cmd run --name redis -p 6379:6379 -d redis:3.2.8

    echo "redis  docker 启动完毕！"

}


echo "是否使用公共redis , y/n , y是公共redis, n使用本地redis docker"
read use_public_redis

if [ "$use_public_redis" == "y" ] || [ -z "$use_public_redis" ];then
	use_public_redis=""
else
    run_redis_docker
    use_public_redis="--link redis"
fi

echo "use public="$use_public_redis

if [ "$project_name" == "dmp" ];then
	port=8000
else
	port=8001
fi


run_docker(){

	echo "正在更新 dmp docker ，请稍候..."
	$docker_cmd pull registry.cn-hangzhou.aliyuncs.com/mic-dmp/dmp-libbase:v3.4.6.191125

	echo "停止并清除当前运行的dmp docker 进程"
	$docker_cmd stop $project_name
	$docker_cmd rm $project_name

	echo "启动新的dmp docker 进程"


	$docker_cmd run -d --restart=always \
				--name $project_name \
				-p $port:8000 \
				-e APP_BIND=0.0.0.0:8000 \
				-e APP_WORK_CLASS=sync \
				-e APP_WORKS=1 \
				-e TIMEOUT=120 \
				-e APP_LOG_LEVEL=INFO \
				-e CONFIG_AGENT_SERVER=https://starship-ops.mypaas.com.cn \
				-e CONFIG_AGENT_CLIENT_CODE=$client_code \
				-e CONFIG_AGENT_CLIENT_KEY=$client_key \
				-e CONFIG_AGENT_APP_CODE="$project_name" \
				-v $dev_dir:/home/<USER>/webapp $use_public_redis \
				registry.cn-hangzhou.aliyuncs.com/mic-dmp/dmp-libbase:v3.4.6.191125

	echo "docker 执行完毕！"
	if [ $? -eq 0 ];then
	  echo " 开发环境部署成功！"
	else
	  echo "执行失败！！！！"
	fi

}

#调用 docker

run_docker $project_name
