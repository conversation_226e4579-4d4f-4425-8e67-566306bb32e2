#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/5.
"""
from base import repository, service
from base.enums import IndicatorType
from data_source.services import data_source_service
from dmplib.constants import (
    DATA_CLEAN_PRIMARY_KEY_NAME,
    DATA_CLEAN_PRIMARY_TABLE_NAME,
    DATA_CLEAN_ORGANIZATION_KEY_NAME,
)
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from indicator.models import IndicatorModel, DimensionModel
from indicator.repositories import indicator_repository
from indicator.services import dimension_service, template_service


def get_indicator(indicator_id, include_dim=None):
    """
    获取指标
    :param str indicator_id:
    :param bool include_dim:
    :return indicator.models.IndicatorModel:
    """
    if not indicator_id:
        raise UserError(message='指标id不能为空')
    fields = ['id', 'tmpl_id', 'type_id', 'name', 'odps_table', 'odps_field', 'type', 'rank']
    data = repository.get_data('dap_bi_indicator', {'id': indicator_id}, fields)
    if not data:
        raise UserError(message='指标不存在')
    model = IndicatorModel(**data)
    if include_dim and model.type == IndicatorType.Dimension.value:
        model.dimension = dimension_service.get_dimensions_by_indicator_id(indicator_id)
    return model


def get_indicator_by_template_id(template_id):
    """
    获取模板下所有指标
    :param str template_id:
    :return list:
    """
    if not template_id:
        raise UserError(message='模板id不能为空')
    return indicator_repository.get_indicator_by_template_id(template_id)


def get_indicator_list(query_model):
    """
    获取指标
    :param indicator.models.IndicatorQueryModel query_model:
    :return:
    """
    return indicator_repository.get_indicator_list(query_model)


def add_indicator(model):
    """
    添加指标
    :param indicator.models.IndicatorModel model:
    :return:
    """
    model.id = seq_id()
    model.validate()
    if not repository.data_is_exists('dap_bi_indicator_template', {'id': model.tmpl_id}):
        raise UserError(message='指标模板不存在')
    if not repository.data_is_exists('dap_bi_indicator_type', {'id': model.type_id, 'tmpl_id': model.tmpl_id}):
        raise UserError(message='指标类型不存在')
    model.rank = repository.get_data_max_rank('dap_bi_indicator', 'rank', {'type_id': model.type_id})
    fields = ['id', 'tmpl_id', 'type_id', 'name', 'type', 'rank']
    try:
        save_dimension(model)
    except UserError as e:
        delete_indicator_by_id(model.id)
        raise UserError(message='添加维度失败：' + e.message)
    return repository.add_model('dap_bi_indicator', model, fields)


def update_indicator(model):
    """
    修改指标
    :param indicator.models.IndicatorModel model:
    :return:
    """
    model.validate()
    indicator = get_indicator(model.id)
    if model.tmpl_id != indicator.tmpl_id or model.type_id != indicator.type_id:
        raise UserError(message='不允许修改指标模板与类型')
    fields = ['name', 'type']
    repository.update_model('dap_bi_indicator', model, {'id': model.id}, fields)
    if model.type == IndicatorType.Dimension.value:
        save_dimension(model)
    elif indicator.type == IndicatorType.Dimension.value:
        # 从维度类型修改为其他类型，将指标下所有配置的维度删除
        dimension_service.delete_dimension_by_indicator_id(model.id)
    return True


def update_indicator_rank(model):
    """
    更新指标顺序
    :param indicator.models.IndicatorRankModel model:
    :return:
    """
    model.validate()
    return service.update_data_rank(model, {'type_id': model.type_id})


def save_dimension(model):
    """
    保存指标维度
    :param indicator.models.IndicatorModel model:
    :return:
    """
    if model.type != IndicatorType.Dimension.value or not model.dimension:
        return
    model.dimension_to_model()
    dimension_id_list = dimension_service.get_dimension_id_list_by_indicator_id(model.id)
    rank = 1
    for dim in model.dimension:
        if not isinstance(dim, DimensionModel):
            continue
        if not dim.id:
            dim.indicator_id = model.id
            dim.rank = rank
            dimension_service.add_dimension(dim)
        else:
            dimension_service.update_dimension(dim)
            if dim.id in dimension_id_list:
                dimension_id_list.remove(dim.id)
        rank += 1
    if dimension_id_list:
        for dimension_id in dimension_id_list:
            dimension_service.delete_dimension(dimension_id)


def config_indicator_field(model):
    """
    配置指标ODPS字段
    :param indicator.models.IndicatorConfigModel model:
    :return:
    """
    model.validate()

    indicator = get_indicator(model.id)
    if model.odps_table and model.odps_field:
        master_table = template_service.get_key_table(indicator.tmpl_id, DATA_CLEAN_PRIMARY_TABLE_NAME)
        if not master_table:
            raise UserError(message='请设置指标模板业务主表')
        odps = data_source_service.get_project_odps_instance()
        if not odps.exist_table(model.odps_table):
            raise UserError(message=model.odps_table + '数据表不存')
        table = odps.get_table(model.odps_table)
        schema = table.schema
        if DATA_CLEAN_PRIMARY_KEY_NAME not in schema:
            raise UserError(message='数据表' + model.odps_table + '缺少字段：' + DATA_CLEAN_PRIMARY_KEY_NAME)

        if model.odps_table.lower() != master_table.lower() and DATA_CLEAN_ORGANIZATION_KEY_NAME not in schema:
            raise UserError(message='数据表' + model.odps_table + '缺少字段：' + DATA_CLEAN_ORGANIZATION_KEY_NAME)
        if repository.data_is_exists(
            'dap_bi_indicator', {'odps_table': model.odps_table, 'odps_field': model.odps_field}, {'id': model.id}
        ):
            raise UserError(message='数据表及字段已经被使用')
    else:
        model.odps_table = model.odps_field = ''
    return repository.update_model('dap_bi_indicator', model, {'id': model.id}, ['odps_table', 'odps_field'])


def delete_indicator_by_id(indicator_id):
    """
    删除指标
    :param indicator_id:
    :return:
    """
    record = repository.delete_data('dap_bi_indicator', {'id': indicator_id})
    record += dimension_service.delete_dimension_by_indicator_id(indicator_id)
    return record


def delete_indicator_by_template_id(template_id):
    """
    删除模板下所有指标
    :param str template_id:
    :return:
    """
    if not template_id:
        raise UserError(message='缺少指标模板id')
    record = dimension_service.delete_dimensions_by_template_id(template_id)
    record += repository.delete_data('dap_bi_indicator', {'tmpl_id': template_id})
    return record


def delete_indicator_by_type_id(type_id):
    """
    删除类型下所有指标
    :param type_id:
    :return:
    """
    if not type_id:
        raise UserError(message='缺少指标类型id')
    record = dimension_service.delete_dimensions_by_type_id(type_id)
    record += repository.delete_data('dap_bi_indicator', {'type_id': type_id})
    return record
