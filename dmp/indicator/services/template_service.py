#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/30.
"""
from base import repository, service
from base.enums import TemplateKeyTableName
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from indicator.models import TemplateKeyTableModel
from indicator.repositories import template_repository
from indicator.services import type_service
from label.services import label_service


def get_template(template_id):
    """
    获取指标模板
    :param str template_id:
    :return:
    """
    if not template_id:
        raise UserError(message='缺少模板id')
    fields = ['id', 'name', 'description', 'rank']
    return repository.get_data('dap_bi_indicator_template', {'id': template_id}, fields)


def add_template(model):
    """
    添加指标模板
    :param indicator.models.TemplateModel model:
    :return:
    """
    model.id = seq_id()
    model.rank = repository.get_data_max_rank('dap_bi_indicator_template', 'rank')
    model.validate()
    fields = ['id', 'name', 'description', 'rank']
    return repository.add_model('dap_bi_indicator_template', model, fields)


def update_template(model):
    """
    修改指标模板
    :param indicator.models.TemplateModel model:
    :return:
    """
    model.validate()
    condition = {'id': model.id}
    if not repository.data_is_exists('dap_bi_indicator_template', condition):
        raise UserError(message='指标模板不存在')
    fields = ['name', 'description']
    return repository.update_model('dap_bi_indicator_template', model, condition, fields)


def update_template_rank(model):
    """
    更新模板顺序
    :param indicator.models.TemplateRankModel model:
    :return:
    """
    model.validate()
    return service.update_data_rank(model)


def get_template_list(model):
    """
    获取模板列表
    :param indicator.models.TemplateQueryModel model:
    :return:
    """
    return template_repository.get_template_list(model)


def set_key_tables(model):
    """
    设置模板关键表
    :param indicator.models.SetTemplateKeyTableModel model:
    :return:
    """
    model.tables_to_model()
    model.validate()
    if len(model.tables) != len(TemplateKeyTableName.__members__):
        raise UserError(message='关键表设置不完整')
    if not repository.data_is_exists('dap_bi_indicator_template', {'id': model.id}):
        raise UserError(message='指标模板不存在')
    for table in model.tables:
        if not isinstance(table, TemplateKeyTableModel):
            continue
        if repository.data_is_exists('dap_bi_key_table', {'table_name': table.table_name}, {'tmpl_id': model.id}):
            raise UserError(message=table.table_name + '已经被设置')
    repository.delete_data('dap_bi_key_table', {'tmpl_id': model.id})
    fields = ['tmpl_id', 'name', 'table_name']
    return repository.add_list_model('dap_bi_key_table', model.tables, fields)


def get_key_tables(template_id):
    """
    获取模板关键表
    :param str template_id:
    :return:
    """
    if not template_id:
        return UserError(message='缺少模板id')
    if not repository.data_is_exists('dap_bi_indicator_template', {'id': template_id}):
        raise UserError(message='指标模板不存在')
    fields = ['name', 'table_name']
    return repository.get_data('dap_bi_key_table', {'tmpl_id': template_id}, fields, True)


def get_key_table(template_id, name):
    """
    根据name获取模板关键表
    :param str template_id:
    :param str name:
    :return:
    """
    if not template_id:
        return UserError(message='缺少模板id')
    if not name:
        return UserError(message='缺少名称')
    if name not in [e.value for e in TemplateKeyTableName.__members__.values()]:
        return UserError(message=name + '不属于自定义关键表')
    if not repository.data_is_exists('dap_bi_indicator_template', {'id': template_id}):
        raise UserError(message='指标模板不存在')
    table_name = repository.get_data_scalar('dap_bi_key_table', {'tmpl_id': template_id, 'name': name}, 'table_name')
    if not table_name:
        raise UserError(message='未配置' + name + '数据表')
    return table_name


def delete_template(template_id):
    """
    删除指标模板
    :param str template_id:
    :return:
    """
    if not repository.data_is_exists('dap_bi_indicator_template', {'id': template_id}):
        raise UserError(message='指标模板不存在')
    record = repository.delete_data('dap_bi_indicator_template', {'id': template_id})
    record += type_service.delete_type_by_template_id(template_id)
    record += repository.delete_data('dap_bi_key_table', {'tmpl_id': template_id})
    record += label_service.delete_label_by_template_id(template_id)
    return record
