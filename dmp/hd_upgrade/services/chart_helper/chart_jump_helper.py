import json
from hashlib import md5
from base import repository
from hd_upgrade.services.chart_upgrade import BaseHD<PERSON>hart
from dashboard_chart.dashboard_editor.editor.models import DashboardJumpConfigModel


class ChartJumpHelper(object):

    def __init__(self, chart: BaseHDChart):
        self.link_config = None
        self.config = chart.chart_config_obj
        self.chart_type = chart.chart_type
        self.convert_model = chart.convert_model
        self.jump_config = chart.jump_config
        self.dataset_column_list = chart.dataset_column_list
        self.dataset_id = chart.dataset_id
        self.chart_id = chart.chart_id
        self.dashboard_id = chart.dashboard_id
        self.report_type = chart.report_type
        self.report_info = chart.report_info
        self.target_dashboard_params = []
        self.target_chart_info = {}
        self.target_filter_info = {}
        self.jump_config_model = []
        self.jump_params_model = []
        self.global_param_model = []
        self.global_param_relation_model = []
        self._init_data()

    def upgrade_link_config(self):
        # 升级起跳字段配置
        self._upgrade_jump_link_config()
        # 升级当前报告的接收参数配置
        self._upgrade_receive_link_config()
        # 升级筛选组件接收参数
        self._upgrade_filter_link_config()
        return self._merge_model()

    def _upgrade_jump_link_config(self):
        for config in self.link_config:
            link_config = config.get('link')
            if not link_config:
                continue
            link_mode = int(link_config.get('linkMode', 1))
            col_name = config.get(self.col_path) or ''
            if link_mode == 1:
                link_model = self._prepare_link(link_config, col_name)
                link_model.sort = 0
                # 获取跳转报告所有配置了数据集的组件
                self._prepare_link_params(link_config, link_model)
            else:
                self._prepare_condition_link(link_config, col_name)

    def _upgrade_receive_link_config(self):
        params_config = json.loads(self.report_info.get('param_config', '[]'))
        if params_config:
            for param in params_config:
                var_name = param.get('link_key')
                filters = param.get('filters', [])
                params = param.get('params', [])
                if not var_name:
                    continue
                global_id = self._create_global(var_name, self.dashboard_id)
                if filters:
                    self.get_dashboard_global_params_2_dataset_field_relation(global_id, filters)
                if params:
                    self.get_dashboard_global_params_2_dataset_vars_relation(global_id, params)

    def _upgrade_filter_link_config(self):
        if self.config.get('type', '') == 'filtergroup':
            filter_list = self.config.get('list', [])
            if filter_list and isinstance(filter_list, list):
                for filters in filter_list:
                    var_name = filters.get('config', {}).get('link_key', '')
                    if not var_name:
                        continue
                    global_id = self._create_global(var_name, self.dashboard_id)
                    self.get_dashboard_global_params_2_filter_relation(global_id, filters)

    def _init_data(self):
        self._flush_jump_data()
        self._init_jump_config()

    def _flush_jump_data(self):
        jump_config_ids = repository.get_column('dap_bi_dashboard_jump_config', {'dashboard_id': self.dashboard_id}, ['id'])
        global_params_ids = repository.get_column('dap_bi_dashboard_jump_global_params', {'dashboard_id': self.dashboard_id},
                                                  ['id'])
        if jump_config_ids:
            repository.delete_data('dap_bi_dashboard_jump_config', {'dashboard_id': self.dashboard_id})
            repository.delete_data('dap_bi_dashboard_jump_relation', {'jump_config_id': jump_config_ids})
            repository.delete_data('dap_bi_dashboard_fixed_var_jump_relation', {'jump_config_id': jump_config_ids})
        if global_params_ids:
            repository.delete_data('dap_bi_dashboard_global_params_2_filter_chart_relation',
                                   {'global_params_id': global_params_ids})
            repository.delete_data('dap_bi_dashboard_global_params_2_dataset_vars_relation',
                                   {'global_params_id': global_params_ids})
            repository.delete_data('dap_bi_dashboard_global_params_2_dataset_field_relation',
                                   {'global_params_id': global_params_ids})
            repository.delete_data('dap_bi_dashboard_global_params_2_tab_relation',
                                   {'global_params_id': global_params_ids})

    def _init_jump_config(self):
        if self.config and self.chart_type in ['table', 'pivottable', 'scrolltable']:
            self.link_config = self.config.get('table', {}).get('table_body', {})
        elif self.config and self.chart_type in ['form', 'indicators']:
            if self.chart_type == 'indicators' and self.config.get('link_mode') == 'all':
                self.link_config = [self.config]
                self.jump_config['source_id'] = self.chart_id
                self.jump_config['source_type'] = 2
            else:
                self.link_config = self.config.get('indicators_value', {})
        else:
            self.link_config = [self.config]
        self._get_link_field_by_chart_type()

    def _get_link_field_by_chart_type(self):
        path_config = {
            'table': 'column_ename', 'form': 'name', 'indicators': 'name', 'pivottable': 'column_ename',
            'scrolltable': 'column_ename'
        }
        self.col_path = path_config.get(self.chart_type) or ''

    def _get_field_and_type_by_col_name(self, col_name):
        if not col_name:
            return self.jump_config.get('source_id', ''), self.jump_config.get('source_type', 0)
        col_id = self.dataset_column_list.get(col_name, {}).get('id', '')
        field_type = self.dataset_column_list.get(col_name, {}).get('field_group', '')
        col_type = 1 if field_type == '度量' else 0
        return col_id, col_type

    def _merge_model(self):
        return self.jump_config_model + self.global_param_model + self.jump_params_model + self.global_param_relation_model

    def _prepare_link(self, link_config, col_name=''):
        # 跳转方式配置
        open_config = {'jump': 1, 'iframe': 3, 'newtab': 2}
        redirect_window_config = {
            "type": 4, "position": "center", 'width': link_config.get('openConfig', {}).get('width', 720),
            'height': link_config.get('openConfig', {}).get('height', 600)
        }
        model = self.convert_model.dashboard_jump_config
        model.source_id, model.source_type = self._get_field_and_type_by_col_name(col_name)
        model.target = link_config.get('report_id', '') if link_config.get('report_id') else ''
        model.target_type = 'url' if link_config.get('type') == 'url' else 'dashboard'
        model.open_way = open_config.get(link_config.get('openConfig', {}).get('type', 1), 1)
        if model.open_way == 3:
            frame = link_config.get('openConfig', {}).get('frame', 'cst')
            self._get_frame_width_height(frame, redirect_window_config)
            model.redirect_window_config = json.dumps(redirect_window_config, ensure_ascii=False)
        model.is_default = 0 if link_config.get('mode', '') == 'filter' else 1
        model.condition_jump = self._get_condition_config(link_config.get('filter'))
        self.jump_config_model.append(model)
        return model

    @staticmethod
    def _get_frame_width_height(frame, redirect_window_config):
        frame = str(frame)
        if frame == '1':
            redirect_window_config['width'] = 720
            redirect_window_config['height'] = 360
        elif frame == '2':
            redirect_window_config['width'] = 760
            redirect_window_config['height'] = 600
        elif frame == '3':
            redirect_window_config['width'] = 860
            redirect_window_config['height'] = 600
        elif frame == '4':
            redirect_window_config['width'] = 960
            redirect_window_config['height'] = 600

    def _get_condition_config(self, condition: list):
        condition_config = []
        if not condition:
            return ''
        for con in condition:
            col_name = con.get('column', '')
            if col_name:
                field_group = self.dataset_column_list.get(col_name, {}).get('field_group', '')
                field_id = self.dataset_column_list.get(col_name, {}).get('id', '')
                field_col_name = self.dataset_column_list.get(col_name, {}).get('col_name', '')
                field_where = {
                    'id': field_id, 'dataset_id': self.dataset_id, 'alias_name': col_name, 'col_name': field_col_name,
                    'origin_col_name': col_name, 'visible': 1, 'field_group': field_group, 'text': col_name,
                    'alias': col_name, 'hidden': False, 'field_initiator_id': field_id, 'parent_id': field_group,
                    'condition_value': con.get('value', '')
                }
                condition_config.append(field_where)
        return json.dumps(condition_config, ensure_ascii=False)

    def _prepare_condition_link(self, link_config, col_name):
        tabs = link_config.get('tabs')
        link_filter = tabs.get('filter', [])
        default_link = tabs.get('default', {})
        link_filter.insert(0, default_link)
        for index, link in enumerate(link_filter):
            link_model = self._prepare_link(link, col_name)
            link_model.sort = index
            self._prepare_link_params(link, link_model)

    def _create_global(self, name, dashboard_id):
        global_name = '{}{}'.format(name, dashboard_id)
        global_id = md5(str(global_name).encode('utf-8')).hexdigest()
        global_id = '%s-%s-%s-%s-%s' % (
            global_id[:8],
            global_id[8:12],
            global_id[12:16],
            global_id[16:20],
            global_id[20:],
        )
        if global_id not in self.target_dashboard_params:
            # 全局变量的type、code 被系统级占用，必须更改个名称。
            if name.lower() in ('code', 'type'):
                name = 'hd_' + name
            model = self.convert_model.dashboard_global_params
            model.id = global_id
            model.name = name
            model.dashboard_id = dashboard_id
            self.target_dashboard_params.append(global_id)
            self.global_param_model.append(model)
        return global_id

    def _prepare_link_params(self, link_config: dict, link_model: DashboardJumpConfigModel):
        if not link_config or not link_model:
            return []
        link_params = link_config.get('params', [])
        link_model.unbound_related_dims = {}
        for params in link_params:
            link_type = params.get('type')
            relation_model = self.get_dashboard_jump_relation_model(params, link_model) \
                if link_type == 'dataset' else self.get_dashboard_jump_fixed_relation_model(params, link_model)
            if relation_model:
                self.jump_params_model.append(relation_model)
        if link_model.unbound_related_dims:
            link_model.unbound_related_dims = [field for field in link_model.unbound_related_dims.values()]
            link_model.unbound_related_dims = json.dumps(link_model.unbound_related_dims, ensure_ascii=False)
        else:
            link_model.unbound_related_dims = None

    def get_dashboard_jump_relation_model(self, params, link_model: DashboardJumpConfigModel):
        model = self.convert_model.dashboard_jump_relation
        target_dashboard_id = link_model.target
        var_name = params.get('action', '')
        col_name = params.get('ColumnName', '')
        dataset_field_id = self.dataset_column_list.get(col_name, {}).get('id', '')
        if col_name and dataset_field_id:  # 有些hd垃圾数据，导致跳转失败
            field_group = self.dataset_column_list.get(col_name, {}).get('field_group', '')
            formula_mode = 'sum' if field_group == '度量' else 'group_concat'
            field_col_name = self.dataset_column_list.get(col_name, {}).get('col_name', '')
            link_model.unbound_related_dims[col_name] = {
                'id': dataset_field_id, 'dataset_id': self.dataset_id, 'alias_name': col_name,
                'col_name': field_col_name,
                'origin_col_name': col_name, 'visible': 1, 'field_group': field_group, 'text': col_name,
                'alias': col_name, 'hidden': False, 'dataset_field_id': dataset_field_id, 'num': dataset_field_id,
                'dashboard_chart_id': self.chart_id, 'formula_mode': formula_mode
            }
            model.jump_config_id = link_model.id
            model.dataset_field_id = dataset_field_id
            model.global_params_id = self._create_global(var_name, target_dashboard_id)
            return model
        return None

    def get_dashboard_jump_fixed_relation_model(self, params, link_model: DashboardJumpConfigModel):
        model = self.convert_model.dashboard_jump_fixed_relation
        target_dashboard_id = link_model.target
        var_name = params.get('action')
        model.var_name = var_name
        var_value = params.get('value')
        model.var_value = var_value
        model.jump_config_id = link_model.id
        model.global_params_id = self._create_global(var_name, target_dashboard_id)
        return model

    def get_dashboard_global_params_2_dataset_field_relation(self, global_params_id, filter_list):
        if filter_list:
            for item in filter_list:
                dataset_id = item.get('dataset_id')
                if not dataset_id:
                    continue
                if dataset_id in self.dataset_id:
                    model = self.convert_model.global_params_2_dataset_field_relation
                    model.global_params_id = global_params_id
                    model.dashboard_id = self.dashboard_id
                    model.chart_id = self.chart_id
                    model.dataset_id = dataset_id
                    col_name = item.get('dataset_field_list')[0].get('ColumnName', '')
                    model.dataset_field_id = repository.get_data_scalar(
                        'dap_bi_dataset_field', {'dataset_id': dataset_id, 'origin_col_name': col_name}, 'id'
                    ) or ''
                    self.global_param_relation_model.append(model)

    def get_dashboard_global_params_2_dataset_vars_relation(self, global_params_id, params_list):
        if params_list:
            for item in params_list:
                dataset_id = item.get('dataset_id')
                if not dataset_id:
                    continue
                if dataset_id in self.dataset_id:
                    model = self.convert_model.global_params_2_dataset_vars_relation
                    model.global_params_id = global_params_id
                    model.dashboard_id = self.dashboard_id
                    model.dataset_id = dataset_id
                    var_name = item.get('params_field_list')[0].get('ParamName', '')
                    model.var_id = repository.get_data_scalar(
                        'dap_bi_dataset_vars', {'dataset_id': dataset_id, 'name': var_name}, 'id'
                    ) or ''
                    self.global_param_relation_model.append(model)

    def get_dashboard_global_params_2_filter_relation(self, global_params_id, filters):
        model = self.convert_model.global_params_2_filter_chart_relation
        model.global_params_id = global_params_id
        model.dashboard_id = self.dashboard_id
        model.filter_chart_id = filters.get('id', '')
        filter_type = filters.get('type')
        if filter_type == 'ddfilter':
            dataset_id = filters.get('config', {}).get('dataset_id')
            dataset_field_name = filters.get('config', {}).get('org_val_column_ename', '')
            if dataset_id and dataset_field_name:
                dataset_field_id = repository.get_data_scalar(
                    'dap_bi_dataset_field', {'dataset_id': dataset_id, 'origin_col_name': dataset_field_name}, 'id'
                ) or ''
                model.dataset_field_id = dataset_field_id
        self.global_param_relation_model.append(model)
