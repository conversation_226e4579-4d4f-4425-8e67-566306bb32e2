{"dashboard": [{"chart_code": "numerical_value_next", "model": "", "rule_list": [{"key": "rule_global_rowNum", "name": "全局样式每行显示的个数", "desc": "", "rule": "self.get_attr('style.stack')"}, {"key": "rule_global_splitLine_show", "name": "是否显示分割线", "desc": "", "rule": "False if self.get_attr('style.grid') == 'auto' else True"}, {"key": "rule_global_border", "name": "是否显示边框", "desc": "", "rule": "'solid' if self.get_attr('style.grid') == 'solid' else 'dashed'"}, {"key": "rule_global_paddingTB", "name": "上下间距", "desc": "", "rule": "10"}, {"key": "rule_global_align", "name": "显示对齐方式", "desc": "", "rule": "self.get_attr('style.align','left')"}, {"key": "rule_numberValue_config", "name": "数值配置对象", "desc": "", "rule": "self.get_rule_numbervalue_config()"}, {"key": "rule_title_style", "name": "标题对象", "desc": "", "rule": "self.get_rule_title_style()"}, {"key": "rule_cellsConfig", "name": "条件样式>条件", "desc": "", "rule": "self.get_rule_cells_config_style()"}, {"key": "rule_containerTitle_spread", "name": "标题是否展开", "desc": "", "rule": "False if self.get_attr('title').strip() else True"}, {"key": "rule_containerTitle_text", "name": "标题内容", "desc": "", "rule": "self.get_attr('title').strip()"}, {"key": "rule_containerTitle_fontSize", "name": "标题字号", "desc": "", "rule": "self.get_attr('title_size', 12)"}, {"key": "rule_containerTitle_color", "name": "标题文本颜色", "desc": "", "rule": "self.get_attr('title_color')"}, {"key": "rule_containerTitle_fontStyle", "name": "标题字体样式", "desc": "", "rule": "'bold' if self.get_attr('title_bold') else 'normal'"}, {"key": "rule_containerTitle_show", "name": "是否显示标题", "desc": "", "rule": "False if self.get_attr('title').strip(' ') else True"}, {"key": "rule_title_position", "name": "标签-显示位置", "desc": "", "rule": "False if self.get_attr('title').strip(' ') else True"}, {"key": "rule_containerBackground_show", "name": "是否显示背景", "desc": "", "rule": "True if not self.get_attr('componentBg',False) else self.get_attr('componentBg.enable', False)"}, {"key": "rule_containerBackground_backgroundColor", "name": "背景颜色", "desc": "", "rule": "'rgba(255,255,255,1)' if not self.get_attr('componentBg',False) else self.get_attr('componentBg.color') if self.get_attr('componentBg.type') == 'color' else self.get_attr('componentBg.startColor')"}, {"key": "rule_containerBorder_show", "name": "是否显示边框", "desc": "", "rule": "False if not self.get_attr('border.value',0) else self.get_attr('border.show', False)"}, {"key": "rule_containerBorder_borderColor", "name": "边框颜色", "desc": "", "rule": "self.get_attr('border.color')"}, {"key": "rule_containerBorder_borderWidth", "name": "边框宽度", "desc": "", "rule": "self.get_attr('border.value',0)"}, {"key": "rule_remarkDescription_textAreaLabel", "name": "指标说明", "desc": "", "rule": "json.dumps(self.get_attr('title_desc', '')).strip('\"') if (True if self.get_attr('title').strip(' ') else False) else ''"}]}], "m_report": [{"chart_code": "numerical_value_next_mobile", "model": "", "rule_list": [{"key": "rule_global_rowNum", "name": "全局样式每行显示的个数", "desc": "", "rule": "self.get_attr('style.stack')"}, {"key": "rule_global_splitLine_show", "name": "是否显示分割线", "desc": "", "rule": "False if self.get_attr('style.grid') == 'auto' else True"}, {"key": "rule_global_border", "name": "是否显示边框", "desc": "", "rule": "'solid' if self.get_attr('style.grid') == 'solid' else 'dashed'"}, {"key": "rule_global_paddingTB", "name": "上下间距", "desc": "", "rule": "0"}, {"key": "rule_global_align", "name": "显示对齐方式", "desc": "", "rule": "self.get_attr('style.align','left')"}, {"key": "rule_numberValue_config", "name": "数值配置对象", "desc": "", "rule": "self.get_rule_numbervalue_config()"}, {"key": "rule_title_style", "name": "标题对象", "desc": "", "rule": "self.get_rule_title_style()"}, {"key": "rule_cellsConfig", "name": "条件样式>条件", "desc": "", "rule": "self.get_rule_cells_config_style()"}, {"key": "rule_containerTitle_spread", "name": "标题是否展开", "desc": "", "rule": "False if self.get_attr('title').strip() else True"}, {"key": "rule_containerTitle_text", "name": "标题内容", "desc": "", "rule": "self.get_attr('title').strip()"}, {"key": "rule_containerTitle_fontSize", "name": "标题字号", "desc": "", "rule": "self.get_attr('title_size', 12)"}, {"key": "rule_containerTitle_color", "name": "标题文本颜色", "desc": "", "rule": "self.get_attr('title_color')"}, {"key": "rule_containerTitle_fontStyle", "name": "标题字体样式", "desc": "", "rule": "'bold' if self.get_attr('title_bold') else 'normal'"}, {"key": "rule_containerTitle_show", "name": "是否显示标题", "desc": "", "rule": "False if self.get_attr('title').strip(' ') else True"}, {"key": "rule_title_position", "name": "标签-显示位置", "desc": "", "rule": "False if self.get_attr('title').strip(' ') else True"}, {"key": "rule_containerBackground_show", "name": "是否显示背景", "desc": "", "rule": "True if not self.get_attr('componentBg',False) else self.get_attr('componentBg.enable', False)"}, {"key": "rule_containerBackground_backgroundColor", "name": "背景颜色", "desc": "", "rule": "'rgba(255,255,255,1)' if not self.get_attr('componentBg',False) else self.get_attr('componentBg.color') if self.get_attr('componentBg.type') == 'color' else self.get_attr('componentBg.startColor')"}, {"key": "rule_containerBorder_show", "name": "是否显示边框", "desc": "", "rule": "False if not self.get_attr('border.value',0) else self.get_attr('border.show', False)"}, {"key": "rule_containerBorder_borderColor", "name": "边框颜色", "desc": "", "rule": "self.get_attr('border.color')"}, {"key": "rule_containerBorder_borderWidth", "name": "边框宽度", "desc": "", "rule": "self.get_attr('border.value',0)"}, {"key": "rule_remarkDescription_textAreaLabel", "name": "指标说明", "desc": "", "rule": "json.dumps(self.get_attr('title_desc', '')).strip('\"') if (True if self.get_attr('title').strip(' ') else False) else ''"}], "not_support_rule_list": [{"rule": "self.get_attr('title_align', '') == 'center'", "name": "标题>标题位置", "description": "标题>标题位置>居中dmp中不存在", "level": 1}]}]}