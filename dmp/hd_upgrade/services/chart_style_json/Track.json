{"dashboard": [{"chart_code": "multiple_trajectory_diagram", "model": "", "rule_list": [{"key": "rule_containerTitle_text_data", "name": "标题名称", "desc": "", "rule": "self.get_attr('title', '')"}, {"key": "rule_containerTitle_fontSize", "name": "标题字体大小", "desc": "", "rule": "self.get_attr('title_size', 16)"}, {"key": "rule_containerTitle_color", "name": "标题颜色", "desc": "", "rule": "self.get_attr('title_color', '#222')"}, {"key": "rule_containerTitle_fontStyle_fontWeight", "name": "标题是否加粗", "desc": "", "rule": "'' if self.get_attr('title_bold')==False else 'bold'"}, {"key": "rule_containerTitle_icon_icon", "name": "标题图标标签", "desc": "", "rule": "self.get_attr('title_icon.name', '')"}, {"key": "rule_containerTitle_icon_color", "name": "标题图标颜色", "desc": "", "rule": "self.get_attr('title_icon.color', '')"}, {"key": "rule_containerTitle_textAlign", "name": "图标显示位置", "desc": "", "rule": "self.get_attr('title_align', '')"}, {"key": "rule_containerTitle_spread", "name": "标题是否显示", "desc": "", "rule": "True if self.get_attr('title','') == '' else False"}, {"key": "rule_containerTitle_icon_visible", "name": "标题图标是否显示", "desc": "", "rule": "False if self.get_attr('title_icon.name','') != '' else True"}, {"key": "rule_globalStyle_axisConfig_color", "name": "全局样式>轴线>默认颜色", "desc": "", "rule": "'#CECECE'"}, {"key": "rule_globalStyle_axisConfig_lineBorder_borderStyle", "name": "全局样式>轴线>样式", "desc": "", "rule": "'solid'"}, {"key": "rule_globalStyle_axisConfig_lineBorder_borderWidth", "name": "全局样式>轴线>粗细", "desc": "", "rule": "self.get_attr('split_width', 2)"}, {"key": "rule_globalStyle_nodeConfig_colWidth", "name": "全局样式>节点>节点列宽", "desc": "", "rule": "12"}, {"key": "rule_nodeTitle_fontSize", "name": "节点标题>字号", "desc": "", "rule": "self.get_attr('legend_font', 14)"}, {"key": "rule_nodeTitle_color", "name": "节点标题>文本颜色", "desc": "", "rule": "self.get_attr('legend_color', '')"}, {"key": "rule_nodeIndicator_lineHeight", "name": "节点指标>行高", "desc": "", "rule": "self.get_attr('field_lineheight', 12)"}, {"key": "rule_nodeIndicator_title_fontSize", "name": "节点指标>标题>字号", "desc": "", "rule": "self.get_attr('field_font', 12)"}, {"key": "rule_nodeIndicator_title_color", "name": "节点指标>标题>文本颜色", "desc": "", "rule": "self.get_rule_indicator_color().get('key_color')"}, {"key": "rule_nodeIndicator_value_color", "name": "节点指标>值>文本颜色", "desc": "", "rule": "self.get_rule_indicator_color().get('val_color')"}, {"key": "rule_containerBorder_show", "name": "是否显示边框", "desc": "", "rule": "self.get_attr('border.show', False)"}, {"key": "rule_containerBorder_borderColor", "name": "边框颜色", "desc": "", "rule": "self.get_attr('border.color', '')"}, {"key": "rule_containerBorder_borderStyle", "name": "边框粗细", "desc": "", "rule": "self.get_attr('border.type', '')"}, {"key": "rule_containerBorder_borderWidth", "name": "边框粗细值", "desc": "", "rule": "self.get_attr('border.value', '0') if   self.get_attr('border.value', '0') else 0"}, {"key": "rule_containerBorder_isShadow", "name": "边框粗细联动", "desc": "", "rule": "False if self.get_attr('border.value','') == 0 else 0"}, {"key": "rule_containerBackground_backgroundColor", "name": "背景颜色", "desc": "", "rule": "self.get_attr('componentBg.color') if self.get_attr('componentBg.type') == 'color' else self.get_attr('componentBg.startColor')"}, {"key": "rule_containerBackground_spread", "name": "背景颜色是否显示", "desc": "", "rule": "self.get_attr('componentBg.enable', False)"}, {"key": "rule_chartRemarkConfig_remarkDescription_textAreaLabel", "name": "指标说明", "desc": "", "rule": "json.dumps(self.get_attr('title_desc', '')).strip('\"')"}, {"key": "rule_statusIcon_statusContions", "name": "节点指标标题字体颜色", "desc": "", "rule": "self.get_rule_status_icon_style()"}]}]}