{"dashboard": [{"chart_code": "handsontable", "model": "", "rule_list": [{"key": "rule_column_cellsConfig_conditions", "name": "列>条件格式>配置设置", "desc": "", "rule": "self.get_column_cellsConfig_conditions_style()"}, {"key": "rule_column_colsConfig", "name": "列>列数据", "desc": "", "rule": "self.get_rule_column_colsConfig_style()"}, {"key": "rule_column_list", "name": "列>数据列", "desc": "", "rule": "self.get_columns_list_style()"}, {"key": "rule_container_background_backgroundColor", "name": "背景-背景-背景颜色", "desc": "", "rule": "self.get_attr('componentBg.color') if self.get_attr('componentBg.type') == 'color' else self.get_attr('componentBg.startColor')"}, {"key": "rule_container_background_backgroundImage", "name": "背景-背景图片-图片", "desc": "", "rule": "self.get_attr('componentBg.image','')"}, {"key": "rule_container_background_backgroundUpload", "name": "背景-背景图片-是否上传", "desc": "", "rule": "False if self.get_attr('componentBg.image','') == '' else True"}, {"key": "rule_container_background_show", "name": "背景-是否显示", "desc": "", "rule": "self.get_attr('componentBg.enable',False)"}, {"key": "rule_container_border_borderStyle_borderColor", "name": "边框>颜色", "desc": "", "rule": "self.get_attr('border.color','#dddddd')"}, {"key": "rule_container_border_borderStyle_borderRadius", "name": "边框>圆角", "desc": "", "rule": "0"}, {"key": "rule_container_border_borderStyle_borderStyle", "name": "边框>粗细", "desc": "", "rule": "self.get_attr('border.type','none')"}, {"key": "rule_container_border_borderStyle_borderWidth", "name": "边框>粗细度", "desc": "", "rule": "self.get_attr('border.value',1)"}, {"key": "rule_container_border_show", "name": "边框>是否启用", "desc": "", "rule": "self.get_attr('border.show',False)"}, {"key": "rule_container_title_color", "name": "标题>文本颜色", "desc": "", "rule": "self.get_attr('title_color')"}, {"key": "rule_container_title_content_align", "name": "标题>显示位置", "desc": "", "rule": "self.get_attr('title_align','left')"}, {"key": "rule_container_title_distance", "name": "标题>样式>上下间距", "desc": "", "rule": "10"}, {"key": "rule_container_title_fontSize", "name": "标题>字号", "desc": "", "rule": "self.get_attr('title_size', 12)"}, {"key": "rule_container_title_fontStyle_fontStyle", "name": "标题>样式>倾斜", "desc": "", "rule": "'normal'"}, {"key": "rule_container_title_fontStyle_fontWeight", "name": "标题-标题-字体加粗", "desc": "", "rule": "'normal' if self.get_attr('title_bold',False) == False else 'bold'"}, {"key": "rule_container_title_fontStyle_textDecoration", "name": "标题>样式>下划线", "desc": "", "rule": "'normal'"}, {"key": "rule_container_title_hideTitle_show", "name": "标题>隐藏标题", "desc": "", "rule": "True if self.get_attr('title','') == '' else False"}, {"key": "rule_container_title_icon_color", "name": "标题>图标>背景色标", "desc": "", "rule": "self.get_attr('title_icon.color')"}, {"key": "rule_container_title_icon_icon", "name": "标题>图标>图片", "desc": "", "rule": "self.get_attr('title_icon.name')"}, {"key": "rule_container_title_icon_visible", "name": "标题>图标>无背景", "desc": "", "rule": "False if self.get_attr('title_icon.no_bg') == True else True"}, {"key": "rule_container_title_text", "name": "标题>内容", "desc": "", "rule": "self.get_attr('title')"}, {"key": "rule_chartRemarkConfig_remarkDescription", "name": "图表备注>备注说明", "desc": "", "rule": "self.get_rule_remark_description_style()"}, {"key": "rule_global_cellGrid_lineHeight", "name": "全局样式>单元格布局>文字行高", "desc": "", "rule": "self.get_attr('tbody_line_height',40)"}, {"key": "rule_global_cellGrid_paddingX", "name": "全局样式>单元格布局>水平内边距", "desc": "", "rule": "5"}, {"key": "rule_global_cellGrid_paddingY", "name": "全局样式>单元格布局>垂直内边距", "desc": "", "rule": "0"}, {"key": "rule_global_cellGrid_textAlign", "name": "全局样式>单元格布局>对齐方式", "desc": "", "rule": "'auto'"}, {"key": "rule_global_cellGrid_textWrap", "name": "全局样式>单元格布局>自动换行", "desc": "", "rule": "False"}, {"key": "rule_global_cellStyle_color", "name": "全局样式>单元格文本>文本颜色", "desc": "", "rule": "self.get_attr('tbody_color','#666666')"}, {"key": "rule_global_cellStyle_fontStyle_fontSize", "name": "全局样式>单元格文本>字号", "desc": "", "rule": "self.get_attr('tbody_font_size',14)"}, {"key": "rule_global_cellStyle_fontStyle_fontStyle", "name": "全局样式>单元格文本>文本样式>倾斜", "desc": "", "rule": "'normal'"}, {"key": "rule_global_cellStyle_fontStyle_fontWeight", "name": "全局样式>单元格文本>文本样式>加粗", "desc": "", "rule": "'normal' if self.get_attr('tbody_bold',False) == False else 'bold'"}, {"key": "rule_global_cellStyle_fontStyle_textDecoration", "name": "全局样式>单元格文本>文本样式>下划线", "desc": "", "rule": "'none'"}, {"key": "rule_global_dmpTableType", "name": "全局样式>dmpTableType", "desc": "", "rule": "self.get_global_dmpTableType_config_style()"}, {"key": "rule_global_majorKeysData_dataKey", "name": "全局样式>majorKeysData>dataKey", "desc": "", "rule": "self.get_global_majorKeysData_config_style(False)"}, {"key": "rule_global_majorKeysData_parKey", "name": "全局样式>majorKeysData>parKey", "desc": "", "rule": "self.get_global_majorKeysData_config_style(True)"}, {"key": "rule_header_headerGrid_lineHeight", "name": "表头>表头布局>文字行高", "desc": "", "rule": "self.get_attr('thead_line_height', 44)"}, {"key": "rule_header_headerGrid_paddingX", "name": "表头>表头布局>水平内边距", "desc": "", "rule": "6"}, {"key": "rule_header_headerGrid_paddingY", "name": "表头>表头布局>垂直内边距", "desc": "", "rule": "0"}, {"key": "rule_header_headerGrid_textAlign", "name": "表头>表头布局>对齐方式", "desc": "", "rule": "'center' if self.get_attr('thead_align','auto') == 'auto' else self.get_attr('thead_align')"}, {"key": "rule_header_headerGrid_textOverflow", "name": "表头>表头布局>自动换行", "desc": "", "rule": "True"}, {"key": "rule_header_headerText_bgColor", "name": "表头>表头文本>背景颜色", "desc": "", "rule": "'transparent' if self.get_attr('thead_bg','') == '' else self.get_attr('thead_bg')"}, {"key": "rule_header_headerText_color", "name": "表头>表头文本>文本颜色", "desc": "", "rule": "self.get_attr('thead_color','#bbbbbb')"}, {"key": "rule_header_headerText_fontSize", "name": "表头>表头文本>字号", "desc": "", "rule": "self.get_attr('thead_font_size',14)"}, {"key": "rule_header_headerText_fontStyle_fontStyle", "name": "表头>表头文本>样式>倾斜", "desc": "", "rule": "'noraml'"}, {"key": "rule_header_headerText_fontStyle_fontWeight", "name": "表头>表头文本>样式>加粗", "desc": "", "rule": "'normal' if self.get_attr('thead_bold',False) == False else 'bold'"}, {"key": "rule_header_headerText_fontStyle_textDecoration", "name": "表头>表头文本>样式>下划线", "desc": "", "rule": "'none'"}, {"key": "rule_header_show", "name": "表头>是否启用", "desc": "", "rule": "True"}, {"key": "rule_pagination_fontSize", "name": "分页器>分页字号", "desc": "", "rule": "14"}, {"key": "rule_pagination_pageSize", "name": "分页器>每页条数", "desc": "", "rule": "self.get_hd_mapped_page_size()"}, {"key": "rule_pagination_pageStyle", "name": "分页器>样式", "desc": "", "rule": "'default'"}, {"key": "rule_pagination_show", "name": "分页器>是否启用", "desc": "", "rule": "True if self.get_attr('grid_value',0) > 0 else False"}, {"key": "rule_row_oddEven_evenBackgroundColor", "name": "行>偶行背景色", "desc": "", "rule": "self.get_attr('row_color','#fff')"}, {"key": "rule_row_oddEven_oddBackgroundColor", "name": "行>偶行背景色", "desc": "", "rule": "self.get_attr('even_color','#fff')"}, {"key": "rule_row_oddEven_show", "name": "行>区分奇偶行", "desc": "", "rule": "False if self.get_attr('table.striped',False) == False else True"}, {"key": "rule_row_splitLine_border_borderColor", "name": "行>分割线>边框>颜色", "desc": "", "rule": "self.get_attr('grid_color','#666666')"}, {"key": "rule_row_splitLine_border_borderStyle", "name": "行>分割线>边框>样式", "desc": "", "rule": "self.get_attr('grid_type','#dashed')"}, {"key": "rule_row_splitLine_border_borderWidth", "name": "行>分割线>边框>宽度", "desc": "", "rule": "self.get_attr('grid_value',0)"}, {"key": "rule_row_splitLine_show", "name": "行>分割线>是否启用", "desc": "", "rule": "False if self.get_attr('grid','no') == 'no' else True"}, {"key": "rule_statistics_colSummary_background", "name": "汇总统计>整列汇总>背景色", "desc": "", "rule": "self.get_attr('sum_bg','transparent')"}, {"key": "rule_statistics_colSummary_color", "name": "汇总统计>整列汇总>文本颜色", "desc": "", "rule": "self.get_attr('sum_color','#fff')"}, {"key": "rule_statistics_colSummary_fontSize", "name": "汇总统计>整列汇总>字号", "desc": "", "rule": "self.get_attr('sum_font_size',14)"}, {"key": "rule_statistics_colSummary_fontStyle_fontStyle", "name": "汇总统计>整列汇总>样式>样式", "desc": "", "rule": "'none'"}, {"key": "rule_statistics_colSummary_fontStyle_fontWeight", "name": "汇总统计>整列汇总>样式>加粗", "desc": "", "rule": "'normal' if self.get_attr('sum_bold',False) == False else 'bold'"}, {"key": "rule_statistics_colSummary_fontStyle_textDecoration", "name": "汇总统计>整列汇总>样式>下划线", "desc": "", "rule": "'none'"}, {"key": "rule_statistics_colSummary_label", "name": "汇总统计>整列汇总>别名", "desc": "", "rule": "'总计'"}, {"key": "rule_statistics_colSummary_position", "name": "汇总统计>整列汇总>显示位置", "desc": "", "rule": "'top' if self.get_attr('auto_sum','last') == 'first' else 'bottom'"}, {"key": "rule_tableIndex_fontColor", "name": "序号>文本颜色", "desc": "", "rule": "self.get_attr('thead_color','#000000')"}, {"key": "rule_tableIndex_fontSize", "name": "序号>字号", "desc": "", "rule": "self.get_attr('thead_font_size',14)"}, {"key": "rule_tableIndex_fontStyle", "name": "序号>样式>倾斜", "desc": "", "rule": "'normal'"}, {"key": "rule_tableIndex_fontWeight", "name": "序号>样式>加粗", "desc": "", "rule": "'normal' if self.get_attr('thead_bold',False) == False else 'bold'"}, {"key": "rule_tableIndex_header", "name": "序号>表头内容", "desc": "", "rule": "'序号'"}, {"key": "rule_tableIndex_indexCol_show", "name": "序号>列序号>是否启用", "desc": "", "rule": "False if self.get_attr('index_col','0') == '0' or self.get_attr('leveltree.enable', False) else True"}, {"key": "rule_tableIndex_specialConfig", "name": "序号>列序号>特殊样式设置", "desc": "", "rule": "self.get_tab_index_special_config_style()"}, {"key": "rule_tableIndex_specialConfig", "name": "序号>列序号>特殊样式设置", "desc": "", "rule": "self.get_tab_index_special_config_style()"}, {"key": "rule_animation", "name": "动效设置", "desc": "", "rule": "self.get_rule_animation()"}], "not_support_rule_list": [{"rule": "self.get_attr('title_icon.name', '') != ''", "name": "标题>图标>图片", "description": "标题>图标>highdata图片在dmp中不存在", "level": 1}, {"rule": "self.get_attr('componentBg.enable', False) == True and self.get_attr('componentBg.type', '') == 'gradient'", "name": "背景>颜色类型>渐变色", "description": "背景>颜色类型>dmpb不支持渐变色", "level": 1}, {"name": "分割线>类型>横线或竖线", "rule": "self.get_attr('grid', '') == 'h' or self.get_attr('grid', '') == 'v'", "description": "分割线>类型>横线或竖线在dmp不支持，dmp要么都显示，要么都隐藏", "level": 1}, {"name": "列>序号列>奖牌风格", "rule": "self.get_attr('index_col', '') == 'rank'", "description": "列>序号列>奖牌风格在dmp不支持", "level": 1}, {"name": "列>冻结列数", "rule": "self.get_attr('table.sticky_num', 0) > 1", "description": "列>冻结列数>不支持自定义冻结列数", "level": 1}]}], "m_report": [{"chart_code": "handsontable_mobile", "model": "", "rule_list": [{"key": "rule_column_cellsConfig_conditions", "name": "列>条件格式>配置设置", "desc": "", "rule": "self.get_column_cellsConfig_conditions_style()"}, {"key": "rule_column_colsConfig", "name": "列>列数据", "desc": "", "rule": "self.get_rule_column_colsConfig_style()"}, {"key": "rule_column_list", "name": "列>数据列", "desc": "", "rule": "self.get_columns_list_style()"}, {"key": "rule_container_background_backgroundColor", "name": "背景-背景-背景颜色", "desc": "", "rule": "self.get_attr('componentBg.color') if self.get_attr('componentBg.type') == 'color' else self.get_attr('componentBg.startColor')"}, {"key": "rule_container_background_backgroundImage", "name": "背景-背景图片-图片", "desc": "", "rule": "self.get_attr('componentBg.image','')"}, {"key": "rule_container_background_backgroundUpload", "name": "背景-背景图片-是否上传", "desc": "", "rule": "False if self.get_attr('componentBg.image','') == '' else True"}, {"key": "rule_container_background_show", "name": "背景-是否显示", "desc": "", "rule": "self.get_attr('componentBg.enable',False)"}, {"key": "rule_container_border_borderStyle_borderColor", "name": "边框>颜色", "desc": "", "rule": "self.get_attr('border.color','#dddddd')"}, {"key": "rule_container_border_borderStyle_borderRadius", "name": "边框>圆角", "desc": "", "rule": "0"}, {"key": "rule_container_border_borderStyle_borderStyle", "name": "边框>粗细", "desc": "", "rule": "self.get_attr('border.type','none')"}, {"key": "rule_container_border_borderStyle_borderWidth", "name": "边框>粗细度", "desc": "", "rule": "self.get_attr('border.value',1)"}, {"key": "rule_container_border_show", "name": "边框>是否启用", "desc": "", "rule": "self.get_attr('border.show',False)"}, {"key": "rule_container_title_color", "name": "标题>文本颜色", "desc": "", "rule": "self.get_attr('title_color')"}, {"key": "rule_container_title_content_align", "name": "标题>显示位置", "desc": "", "rule": "self.get_attr('title_align','left')"}, {"key": "rule_container_title_distance", "name": "标题>样式>上下间距", "desc": "", "rule": "10"}, {"key": "rule_container_title_fontSize", "name": "标题>字号", "desc": "", "rule": "self.get_attr('title_size')"}, {"key": "rule_container_title_fontStyle_fontStyle", "name": "标题>样式>倾斜", "desc": "", "rule": "'normal'"}, {"key": "rule_container_title_fontStyle_fontWeight", "name": "标题-标题-字体加粗", "desc": "", "rule": "'normal' if self.get_attr('title_bold',False) == False else 'bold'"}, {"key": "rule_container_title_fontStyle_textDecoration", "name": "标题>样式>下划线", "desc": "", "rule": "'normal'"}, {"key": "rule_container_title_hideTitle_show", "name": "标题>隐藏标题", "desc": "", "rule": "True if self.get_attr('title','') == '' else False"}, {"key": "rule_container_title_icon_color", "name": "标题>图标>背景色标", "desc": "", "rule": "self.get_attr('title_icon.color')"}, {"key": "rule_container_title_icon_icon", "name": "标题>图标>图片", "desc": "", "rule": "self.get_attr('title_icon.name')"}, {"key": "rule_container_title_icon_visible", "name": "标题>图标>无背景", "desc": "", "rule": "False if self.get_attr('title_icon.no_bg') == True else True"}, {"key": "rule_container_title_text", "name": "标题>内容", "desc": "", "rule": "self.get_attr('title')"}, {"key": "rule_chartRemarkConfig_remarkDescription", "name": "图表备注>备注说明", "desc": "", "rule": "self.get_rule_remark_description_style()"}, {"key": "rule_global_cellGrid_lineHeight", "name": "全局样式>单元格布局>文字行高", "desc": "", "rule": "self.get_attr('tbody_line_height',40)"}, {"key": "rule_global_cellGrid_paddingX", "name": "全局样式>单元格布局>水平内边距", "desc": "", "rule": "5"}, {"key": "rule_global_cellGrid_paddingY", "name": "全局样式>单元格布局>垂直内边距", "desc": "", "rule": "0"}, {"key": "rule_global_cellGrid_textAlign", "name": "全局样式>单元格布局>对齐方式", "desc": "", "rule": "'auto'"}, {"key": "rule_global_cellGrid_textWrap", "name": "全局样式>单元格布局>自动换行", "desc": "", "rule": "False"}, {"key": "rule_global_cellStyle_color", "name": "全局样式>单元格文本>文本颜色", "desc": "", "rule": "self.get_attr('tbody_color','#666666')"}, {"key": "rule_global_cellStyle_fontStyle_fontSize", "name": "全局样式>单元格文本>字号", "desc": "", "rule": "self.get_attr('tbody_font_size',14)"}, {"key": "rule_global_cellStyle_fontStyle_fontStyle", "name": "全局样式>单元格文本>文本样式>倾斜", "desc": "", "rule": "'normal'"}, {"key": "rule_global_cellStyle_fontStyle_fontWeight", "name": "全局样式>单元格文本>文本样式>加粗", "desc": "", "rule": "'normal' if self.get_attr('tbody_bold',False) == False else 'bold'"}, {"key": "rule_global_cellStyle_fontStyle_textDecoration", "name": "全局样式>单元格文本>文本样式>下划线", "desc": "", "rule": "'none'"}, {"key": "rule_global_dmpTableType", "name": "全局样式>dmpTableType", "desc": "", "rule": "self.get_global_dmpTableType_config_style()"}, {"key": "rule_global_majorKeysData_dataKey", "name": "全局样式>majorKeysData>dataKey", "desc": "", "rule": "self.get_global_majorKeysData_config_style(False)"}, {"key": "rule_global_majorKeysData_parKey", "name": "全局样式>majorKeysData>parKey", "desc": "", "rule": "self.get_global_majorKeysData_config_style(True)"}, {"key": "rule_header_headerGrid_lineHeight", "name": "表头>表头布局>文字行高", "desc": "", "rule": "self.get_attr('thead_line_height', 44)"}, {"key": "rule_header_headerGrid_paddingX", "name": "表头>表头布局>水平内边距", "desc": "", "rule": "6"}, {"key": "rule_header_headerGrid_paddingY", "name": "表头>表头布局>垂直内边距", "desc": "", "rule": "0"}, {"key": "rule_header_headerGrid_textAlign", "name": "表头>表头布局>对齐方式", "desc": "", "rule": "'center' if self.get_attr('thead_align','auto') == 'auto' else self.get_attr('thead_align')"}, {"key": "rule_header_headerGrid_textOverflow", "name": "表头>表头布局>自动换行", "desc": "", "rule": "True"}, {"key": "rule_header_headerText_bgColor", "name": "表头>表头文本>背景颜色", "desc": "", "rule": "'transparent' if self.get_attr('thead_bg','') == '' else self.get_attr('thead_bg')"}, {"key": "rule_header_headerText_color", "name": "表头>表头文本>文本颜色", "desc": "", "rule": "self.get_attr('thead_color','#bbbbbb')"}, {"key": "rule_header_headerText_fontSize", "name": "表头>表头文本>字号", "desc": "", "rule": "self.get_attr('thead_font_size',14)"}, {"key": "rule_header_headerText_fontStyle_fontStyle", "name": "表头>表头文本>样式>倾斜", "desc": "", "rule": "'noraml'"}, {"key": "rule_header_headerText_fontStyle_fontWeight", "name": "表头>表头文本>样式>加粗", "desc": "", "rule": "'normal' if self.get_attr('thead_bold',False) == False else 'bold'"}, {"key": "rule_header_headerText_fontStyle_textDecoration", "name": "表头>表头文本>样式>下划线", "desc": "", "rule": "'none'"}, {"key": "rule_header_show", "name": "表头>是否启用", "desc": "", "rule": "True"}, {"key": "rule_row_oddEven_evenBackgroundColor", "name": "行>偶行背景色", "desc": "", "rule": "self.get_attr('row_color','#fff')"}, {"key": "rule_row_oddEven_oddBackgroundColor", "name": "行>偶行背景色", "desc": "", "rule": "self.get_attr('even_color','#fff')"}, {"key": "rule_row_oddEven_show", "name": "行>区分奇偶行", "desc": "", "rule": "False if self.get_attr('table.striped',False) == False else True"}, {"key": "rule_row_splitLine_border_borderColor", "name": "行>分割线>边框>颜色", "desc": "", "rule": "self.get_attr('grid_color','#666666')"}, {"key": "rule_row_splitLine_border_borderStyle", "name": "行>分割线>边框>样式", "desc": "", "rule": "self.get_attr('grid_type','#dashed')"}, {"key": "rule_row_splitLine_border_borderWidth", "name": "行>分割线>边框>宽度", "desc": "", "rule": "self.get_attr('grid_value',0)"}, {"key": "rule_row_splitLine_show", "name": "行>分割线>是否启用", "desc": "", "rule": "False if self.get_attr('grid','no') == 'no' else True"}, {"key": "rule_statistics_colSummary_background", "name": "汇总统计>整列汇总>背景色", "desc": "", "rule": "self.get_attr('sum_bg','transparent')"}, {"key": "rule_statistics_colSummary_color", "name": "汇总统计>整列汇总>文本颜色", "desc": "", "rule": "self.get_attr('sum_color','#bbbbbb')"}, {"key": "rule_statistics_colSummary_fontSize", "name": "汇总统计>整列汇总>字号", "desc": "", "rule": "self.get_attr('sum_font_size',14)"}, {"key": "rule_statistics_colSummary_fontStyle_fontStyle", "name": "汇总统计>整列汇总>样式>样式", "desc": "", "rule": "'none'"}, {"key": "rule_statistics_colSummary_fontStyle_fontWeight", "name": "汇总统计>整列汇总>样式>加粗", "desc": "", "rule": "'normal' if self.get_attr('sum_bold',False) == False else 'bold'"}, {"key": "rule_statistics_colSummary_fontStyle_textDecoration", "name": "汇总统计>整列汇总>样式>下划线", "desc": "", "rule": "'none'"}, {"key": "rule_statistics_colSummary_label", "name": "汇总统计>整列汇总>别名", "desc": "", "rule": "'总计'"}, {"key": "rule_statistics_colSummary_position", "name": "汇总统计>整列汇总>显示位置", "desc": "", "rule": "'top' if self.get_attr('auto_sum','last') == 'first' else 'bottom'"}, {"key": "rule_tableIndex_fontColor", "name": "序号>文本颜色", "desc": "", "rule": "self.get_attr('thead_color','#000000')"}, {"key": "rule_tableIndex_fontSize", "name": "序号>字号", "desc": "", "rule": "self.get_attr('thead_font_size',14)"}, {"key": "rule_tableIndex_fontStyle", "name": "序号>样式>倾斜", "desc": "", "rule": "'normal'"}, {"key": "rule_tableIndex_fontWeight", "name": "序号>样式>加粗", "desc": "", "rule": "'normal' if self.get_attr('thead_bold',False) == False else 'bold'"}, {"key": "rule_tableIndex_header", "name": "序号>表头内容", "desc": "", "rule": "'序号'"}, {"key": "rule_tableIndex_indexCol_show", "name": "序号>列序号>是否启用", "desc": "", "rule": "False if self.get_attr('index_col','0') == '0' or self.get_attr('leveltree.enable', False) else True"}, {"key": "rule_tableIndex_specialConfig", "name": "序号>列序号>特殊样式设置", "desc": "", "rule": "self.get_tab_index_special_config_style()"}, {"key": "rule_tableIndex_textDecoration", "name": "序号>样式>下划线", "desc": "", "rule": "'none'"}, {"key": "rule_animation", "name": "动效设置", "desc": "", "rule": "self.get_rule_animation()"}], "not_support_rule_list": [{"rule": "self.get_attr('title_align', '') == 'center'", "name": "标题>标题位置", "description": "标题>标题位置>居中dmp中不存在", "level": 1}, {"name": "列>序号列>奖牌风格", "rule": "self.get_attr('index_col', '') == 'rank'", "description": "列>序号列>奖牌风格在dmp不支持", "level": 1}, {"name": "列>冻结列数", "rule": "self.get_attr('table.sticky_num', 0) > 1", "description": "列>冻结列数>不支持自定义冻结列数", "level": 1}]}]}