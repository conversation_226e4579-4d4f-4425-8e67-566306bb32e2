from typing import List
from hd_upgrade.services.chart_upgrade.BaseHDChart import BaseHD<PERSON>hart
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardChartNumModel, DashboardChartDesireModel, DashboardJumpConfigModel
)


class Dashboard(BaseHDChart):  # 仪表盘组件
    """
    {"legend_show":true,"legend_font":12,"legend_color":"#666","dataLabel_font":12,"dataLabel_color":"#666666","out_field_title_font":13,"out_field_title_color":"#666666","out_field_value_font":18,"out_field_value_color":"#000000","multiple_field_font":14,"multiple_field_color":"#333333","dashboard_type":"base","dashboard_radius":100,"dashboard_width":12,"pointer_length":75,"splitLine":true,"splitLine_number":10,"splitLine_color":"rgba(255,255,255,0.9)","axisLabel_color":"","axisLabel_font":12,"inner_field_name_position":20,"inner_field_name_font":12,"inner_field_name_color":"#AAAAAA","inner_field_value_position":50,"inner_field_value_font":24,"inner_field_value_color":"","chart_height":240,"title":"仪表盘","title_icon":{"name":"","color":"#FFD24D","no_bg":false},"title_size":16,"title_color":"#222","title_bold":false,"title_desc":"","min":0,"range_config":[{"name":"区间一","end":0.3,"color":"#02BBFF"},{"name":"区间二","end":0.7,"color":"#00D3AA"},{"name":"区间三","end":1,"color":"#FFD902"}],"link":"","no_gap":false,"dataset_id":"8c1da022-532d-4b81-28ed-08da2157801f","inner_field":{"cst_name":"销售额","id":"销售额","number_format":"6","unit":""},"dashboardListNum":1,"dashboardListField":null,"default_sort":{"order_type":0,"sort_field":"0"},"border":{"show":true,"color":"#dddddd","type":"solid","value":1},"layout_config":{"xLabel":690,"yLabel":40,"width":360,"height":400},"type":"dashboard","table":{"table_body":[{"column_ename":"面积","column_type":"数值","cst_name":"面积","number_format":"1","unit":""},{"column_ename":"销售额","column_type":"数值","cst_name":"销售额","number_format":"1","unit":""}]},"componentBg":{"type":"color","startColor":"#00354C","endColor":"#0050E6","angle":111,"color":"rgba(255,255,255,1)","enable":true,"image":""},"theme_class_name":"white","dashboard_list_num":1,"dashboard_list_field":null,"title_align":"left","splitLine_number_format":"6","no_data_tips":"暂无数据","dataset_filter":[]}
    """
    def convert_dashboard_chart(self):

        chart_model = super().convert_dashboard_chart()[0]
        chart_model.desired_value = 100
        self.add_no_support_log([
            {
                'dashboard_id': self.dashboard_id, 'report_type': self.report_type,
                'chart_name': '仪表盘组件',
                'chart_code': self.chart_type, 'chart_id': self.chart_id, 'description': "不支持仪表盘组件",
                'level': 1, 'info_type': 1, 'dmp_chart_code': chart_model.chart_code
            }
        ])
        return [chart_model]

    def convert_dashboard_chart_num(self) -> List[DashboardChartNumModel]:
        col_name = self.get_attr('inner_field.id', '')
        if not col_name:
            return []
        num_model = self.convert_model.dashboard_chart_num
        num_model.num = self.dataset_column_list.get(col_name, {}).get('id', '')
        col_cname = self.get_attr('inner_field.cst_name', '')
        num_model.alias = col_cname if col_cname else col_name
        num_model.formula_mode = 'sum'
        num_model.subtotal_formula_mode = 'sum'
        return [num_model]

    def convert_dashboard_chart_desire(self) -> List[DashboardChartDesireModel]:
        desire_model = self.convert_model.dashboard_chart_desire
        desire_model.value = 100
        return [desire_model]

    def convert_dashboard_jump_config(self) -> List[DashboardJumpConfigModel]:
        col_name = self.chart_config_obj.get('inner_field', {}).get('id', '')
        if not col_name:
            return []
        self.jump_config['source_id'] = self.dataset_column_list.get(col_name, {}).get('id', '')
        self.jump_config['source_type'] = 1
        return super().convert_dashboard_jump_config()
