import json
from typing import List
from hd_upgrade.services.chart_upgrade.BaseHDChart import Base<PERSON><PERSON>hart
from dashboard_chart.dashboard_editor.editor.models import DashboardChartModel, DashboardChartDimModel, \
    DashboardJumpConfigModel


class Comtitle(BaseHDChart):
    def convert_dashboard_chart_dim(self) -> List[DashboardChartDimModel]:
        if self.get_attr('text.type') == 'field':
            col_ename = self.get_attr('text.field.name')
            if not col_ename:
                return []
            dim = self.convert_model.dashboard_chart_dim
            dim.dim = self.dataset_column_list.get(col_ename, {}).get('id', '')
            dim.alias = col_ename
            dim.rank = 0
            return [dim]
        return []

    def convert_dashboard_jump_config(self) -> List[DashboardJumpConfigModel]:
        if self.get_attr('text.type') == 'field':
            col_name = self.get_attr('text.field.name')
            self.jump_config['source_id'] = self.dataset_column_list.get(col_name, {}).get('id', '')
            self.jump_config['source_type'] = 1
            return super().convert_dashboard_jump_config()
        return []


class ComtitleDashboard(Comtitle):
    def convert_dashboard_chart(self) -> List[DashboardChartModel]:
        self.get_hd_chart_position()
        if self.get_attr('text.type') == 'field':
            self.style_model_json = 'comtitle_complex_text'
            modal = super().convert_dashboard_chart()[0]
            modal.chart_code = 'complex_text'
            modal.data_logic_type_code = 'column'
        else:
            font = self.get_attr('text.fontSize')
            text = self.get_attr('text.text', ' ')
            width = font * len(text) * 2
            if self.position['size_x'] - width < 10:  # 识别到数见这边的文本组件有内边距导致遮挡
                self.position['size_x'] = self.position['size_x'] + 10
            if self.position['size_y'] - font < 15:  # 识别到数见这边的文本组件有内边距导致遮挡
                self.position['size_y'] = self.position['size_y'] + 15

            modal = super().convert_dashboard_chart()[0]
            modal.data_logic_type_code = 'nondataset'

        return [modal]


class ComtitleMreport(Comtitle):

    def convert_dashboard_chart(self) -> List[DashboardChartModel]:
        if self.get_attr('text.type') == 'field':
            self.style_model_json = 'comtitle_complex_text'
            modal = super().convert_dashboard_chart()[0]
            modal.chart_code = 'complex_text_mobile'
            modal.data_logic_type_code = 'column'
        else:
            modal = super().convert_dashboard_chart()[0]
            modal.data_logic_type_code = 'nondataset'
        return [modal]
