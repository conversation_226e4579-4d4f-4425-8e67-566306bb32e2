from typing import List
from hd_upgrade.services.chart_upgrade.BaseHDChart import BaseHDChart
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardChartNumModel, DashboardChartDesireModel, DashboardJumpConfigModel
)


class Dropdownnav(BaseHDChart):  # 下拉导航
    def convert_dashboard_chart(self):
        self.add_no_support_log([
            {
                'dashboard_id': self.dashboard_id, 'report_type': self.report_type,
                'chart_name': '下拉导航',
                'chart_code': self.chart_type, 'chart_id': self.chart_id, 'description': "不支持下拉导航组件",
                'level': 1, 'info_type': 1, 'dmp_chart_code': '下拉导航'
            }
        ])
        return super().convert_dashboard_chart()
