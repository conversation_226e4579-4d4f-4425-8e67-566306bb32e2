from typing import List
import json
from dmplib.utils.strings import seq_id
from hd_upgrade.services.chart_upgrade.BaseHDChart import BaseHDChart
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardChartDimModel, DashboardChartFieldSortModel, DashboardChartModel, DashboardJumpConfigModel,
    DashboardChartNumModel
)


class Scrolltable(BaseHDChart):
    """
    {"thead_line_height":44,"tbody_line_height":44,"sum_font_size":14,"grid_color":"#ddd","grid_type":"solid","grid_value":1,"title":"偏差率列表","title_size":15,"title_color":"rgba(68,68,68,1)","title_icon":{"name":"","color":"#FFD24D","no_bg":true},"title_desc":"1.差额：动态成本-目标成本\n2.偏差率：（动态成本-目标成本)\/目标成本\n3.已发生成本取值口径如下：\n 1）合同未结算时：合同+补充合同（不含变更转补充）+分包合同+变更+签证-扣款； \n 2）合同结算中、已结算时：合同结算金额+分包合同金额； \n 3）无合同：取无合同付款申请金额-退款金额；\n4.排序：按偏差率从大到小。","across":"all","acrossList":["1","2","3","4","5"],"auto_sum":"no","group":{"group_field":"","sum_field":""},"allow_merge":false,"sticky_first_column":true,"table":{"dynamic_show":0,"dataset_id":null,"merge_row":[[{"colspan":1,"rowspan":1,"merged":false,"align":"center","text":""},{"colspan":1,"rowspan":1,"merged":false,"align":"left","text":""},{"colspan":1,"rowspan":1,"merged":false,"align":"right","text":""},{"colspan":1,"rowspan":1,"merged":false,"align":"right","text":""},{"colspan":1,"rowspan":1,"merged":false,"align":"right","text":""},{"colspan":1,"rowspan":1,"merged":false,"align":"right","text":""},{"colspan":1,"rowspan":1,"merged":false,"align":"right","text":""},{"colspan":1,"rowspan":1,"merged":false,"align":"right","text":""}]],"sticky_num":1,"default_sort":{"sort_field":"ExcessSavingsRate","order_type":0},"sticky_first_column":true,"striped":true,"table_body":[{"fields":[{"text":"WarningStatus"},{"text":"预警状态"}],"column_ename":"WarningStatus","align":"center","hasSum":false,"checked":1,"dynamic":"","is_float":true,"number_format":"4","filter_formatter":[{"column":"","formatter":{"color":"#444","bold":"","background":"rgba(0,0,0,0)","icon":"\/report-micro\/images\/filtericons\/circle_red.png","show":"0"},"type":"","value":"-1","when":"equal"},{"column":"","formatter":{"color":"#444","bold":"","background":"rgba(0,0,0,0)","icon":"\/report-micro\/images\/filtericons\/circle_yellow.png","show":"0"},"type":"","value":"0","when":"equal"},{"column":"","formatter":{"color":"#444","bold":"","background":"rgba(0,0,0,0)","icon":"\/report-micro\/images\/filtericons\/circle_green.png","show":"0"},"type":"","value":"1","when":"equal"},{"column":"","formatter":{"color":"#444","bold":"","background":"rgba(0,0,0,0)","icon":"\/report-micro\/images\/filtericons\/circle_blue.png","show":"0"},"type":"","value":"-2","when":"equal"}],"tips":[{"tip":{"name":"icon-icon__1","color":"#FF5000","no_bg":true,"text":"超强控"}},{"tip":{"name":"icon-icon__1","color":"#FFD24D","no_bg":true,"text":"超预警"}},{"tip":{"name":"icon-icon__1","color":"#54BF39","no_bg":true,"text":"正常"}},{"tip":{"name":"icon-icon__1","color":"#02BBFF","no_bg":true,"text":"未设置业务参数或设置错误"}}],"width":1},{"fields":[{"text":"BUName"},{"text":"公司\/项目名称"}],"column_ename":"BUName","align":"left","hasSum":false,"checked":1,"dynamic":"","is_float":false,"number_format":"1","width":0},{"fields":[{"text":"TotalTargetCost"},{"text":"目标成本(万元)"}],"column_ename":"TotalTargetCost","align":"right","hasSum":false,"checked":1,"dynamic":"","is_float":true,"number_format":"2","width":0},{"fields":[{"text":"DynamicCost"},{"text":"动态成本(万元)"}],"column_ename":"DynamicCost","align":"right","hasSum":false,"checked":1,"dynamic":"","is_float":true,"number_format":"2","width":0},{"fields":[{"text":"ExcessSavings"},{"text":"差额(万元)"}],"column_ename":"ExcessSavings","align":"right","hasSum":false,"checked":1,"dynamic":"","is_float":true,"number_format":"2","width":0},{"fields":[{"text":"ExcessSavingsRate"},{"text":"偏差率(%)"}],"column_ename":"ExcessSavingsRate","align":"right","hasSum":false,"checked":1,"dynamic":"","is_float":true,"number_format":"1","width":0},{"fields":[{"text":"OccurredCost"},{"text":"已发生成本(万元)"}],"column_ename":"OccurredCost","align":"right","hasSum":false,"checked":1,"dynamic":"","is_float":true,"number_format":"2","width":0},{"fields":[{"text":"Paid"},{"text":"实付成本(万元)"}],"column_ename":"Paid","align":"right","hasSum":false,"checked":1,"dynamic":"","is_float":true,"number_format":"2","width":0}],"cell_padding":8},"no_gap":false,"thead_align":"auto","page_size":10,"thead_bg":"rgba(245,252,255,1)","thead_color":"rgba(102,102,102,1)","even_color":"rgba(245,252,255,1)","thead_bold":false,"sum_color":"#000","sum_bold":true,"sum_bg":"#fff","open":"1","grid":"no","index_col":"0","dataset_id":"8062ff14-ccc8-eb11-812e-8ed0eba3d8d3","type":"table","border":{"show":false,"color":"#dddddd","type":"solid","value":1},"leveltree":{"enable":false,"curdatakey":"bugid","pardatakey":"bugid"},"layout_config":{"xLabel":10,"yLabel":580,"width":1180,"height":400},"default_sort":{"sort_field":"is_end","order_type":0},"title_bold":true,"componentBg":{"type":"color","startColor":"#00354C","endColor":"#0050E6","angle":111,"color":"#fff","enable":true,"image":""},"row_color":"#ffffff","thead_font_size":12,"tbody_color":"rgba(102,102,102,1)","tbody_bold":false,"tbody_font_size":12,"title_align":"left","no_data_tips":"暂无数据"}
    """

    def convert_dashboard_chart(self) -> List[DashboardChartModel]:
        if self.dataset_id and len(self.dataset_id) > 50:
            self.dataset_id = self.get_attr('dataset_id')
            self.dataset_column_list = self.get_dataset_field_list()
        model = super().convert_dashboard_chart()[0]
        model.aggregation = 0
        model.data_logic_type_code = 'column'
        if self.get_attr('auto_sum') in ('last', 'first'):  # to do 数见支持last 就是未行做合计
            model.enable_subtotal_col = 1
            model.enable_subtotal_col_summary = 1
        return [model]

    def convert_dashboard_chart_dim(self) -> List[DashboardChartDimModel]:
        tbody = self.chart_config_obj.get('table').get('table_body')
        ret_list = []
        if not len(self.dataset_column_list):
            return []
        rank = 1
        sort_hd = self.get_attr('default_sort.order_type', -1)
        sort = '' if sort_hd == -1 else 'ASC' if sort_hd == 1 else "DESC"
        sort_field = self.get_attr('default_sort.sort_field')

        for column in tbody:
            col_ename = column.get('column_ename', '')
            if self.dataset_column_list.get(col_ename, {}).get('field_group') != '度量':
                dashboard_chart_dim_model = self.convert_model.dashboard_chart_dim
                dashboard_chart_dim_model.dim = self.dataset_column_list.get(col_ename, {}).get('id', '')
                dashboard_chart_dim_model.alias = column['fields'][1]['text']
                dashboard_chart_dim_model.rank = rank
                dashboard_chart_dim_model.id = seq_id()
                if column.get('is_datetime', False):
                    dashboard_chart_dim_model.formula_mode = self.calc_date_formula_mode(column['number_format'])
                rank += 1
                if sort_field == col_ename:
                    dashboard_chart_dim_model.sort = sort
                ret_list.append(dashboard_chart_dim_model)
        return ret_list

    def convert_dashboard_chart_num(self) -> List[DashboardChartNumModel]:
        tbody = self.get_attr('table.table_body', {})
        ret_list = []
        if not len(self.dataset_column_list):
            return []
        sort_hd = self.get_attr('table.default_sort.order_type', 0)
        sort = '' if sort_hd == -1 else 'ASC' if sort_hd == 1 else "DESC"
        sort_field = self.get_attr('table.default_sort.sort_field')
        auto_sum = False
        if self.get_attr('auto_sum') in ('last', 'first'):  # to do 数见支持last 就是未行做合计
            auto_sum = True
        for rank, column in enumerate(tbody):
            col_ename = column['column_ename']
            model = self.convert_model.dashboard_chart_num
            if self.dataset_column_list.get(col_ename, {}).get('field_group') != '度量':
                continue
            col_cname = column['fields'][1]['text']
            model.num = self.dataset_column_list.get(col_ename, {}).get('id', '')
            model.rank = rank
            model.formula_mode = ''
            model.subtotal_formula_mode = ''
            model.subtotal_col_formula_mode = ''
            number_format = column.get('number_format', '')
            model.display_format = self.calc_chart_num_display_format('', number_format)
            model.alias = col_cname
            if sort_field == col_ename:
                model.sort = sort
            # 自动合并的场景 默认所有都是求和 exp_type 为空 求和  2 求平均值 1 自定义
            if auto_sum and column['hasSum']:
                exp_type = column.get('exp_type', 0)
                expression = column.get('expression', '')
                model.subtotal_col_formula_mode, model.subtotal_col_formula_expression = self.calc_table_col_formula(
                    exp_type, expression)
            ret_list.append(model)
        return ret_list

    def convert_dashboard_chart_field_sort(self) -> List[DashboardChartFieldSortModel]:
        sort = self.chart_config_obj.get('table', {}).get('default_sort', {})
        if not sort:
            return []
        self.chart_config_obj['default_sort'] = {
            'sort_field': sort.get('sort_field', ''),
            'order_type': sort.get('order_type', 0)
        }
        model = super().convert_dashboard_chart_field_sort()
        if not model:
            return []
        model[0].field_source = 'dims'
        return model

    def get_rule_column_colsConfig_style(self):
        # 从hd获取指标值列表
        tbody = self.get_attr("table.table_body", {})
        column_configs = []
        column_index = 0
        for column in tbody:
            col_ename = column['column_ename']
            field = self.dataset_column_list.get(col_ename, {})
            dataset_field_id = field.get("id")
            dataset_id = self.dataset_id
            is_datetime = column.get("is_datetime", False)
            is_float = column.get("is_float", False)
            column_field_type = self.get_field_number_type(is_float, is_datetime, field.get("field_group"))
            column_config = {
                "dataset_id": dataset_id,
                "dataset_field_id": dataset_field_id,
                "data_type": column_field_type[0],
                "col_name": column['column_ename'],
                "alias_name": column['fields'][1]['text'],
                "order": column_index,
                "rank": column_index + 1,
                "col_type": column_field_type[1],
                "is_show": 1,
                "group": self.get_merge_row_group(column_index),
                "startPos": 0,
                "endPos": 0
            }
            column_configs.append(column_config)
            column_index = column_index + 1
        column_configs_json = json.dumps(column_configs)
        return column_configs_json

    def get_merge_row(self):
        table_merge_rows = self.get_attr("table.merge_row", {})
        if len(table_merge_rows) > 0:
            for row_index, merge_row_item in enumerate(table_merge_rows):
                for column_index, column_item in enumerate(merge_row_item):
                    is_colspan = column_item.get("colspan") > 1
                    if is_colspan:
                        return merge_row_item
                return merge_row_item
        return None

    def get_merge_row_group(self, current_column_index):
        merge_row = self.get_merge_row()
        if merge_row is None:
            return None

        current_colspan_pos = 0
        current_colspan_txt = ""
        for column_index, column_item in enumerate(merge_row):
            if not column_item.get("merged"):
                current_colspan_pos = current_colspan_pos + 1
                current_colspan_txt = column_item.get("text")
            if column_index == current_column_index:
                if current_colspan_txt == "":
                    return None
                else:
                    return {"name": current_colspan_txt}
        return None

    def get_field_number_type(self, is_float, is_datetime, field_group):
        number_type = ["字符串", "dim"]
        if is_float:
            number_type[0] = "数值"

        if field_group != "维度":
            number_type[1] = "num"

        return number_type

    def get_field_chart_number_type(self, hd_number_type):
        number_type = "dim"
        if hd_number_type != "维度":
            number_type = "num"
        return number_type

    def get_field_chart_data_type(self, is_float, is_datetime):
        data_type = "字符串"
        if is_float:
            data_type = "数值"
        return data_type

    def get_field_operator_value(self, operator, operator_value):
        math_operator = ""
        math_operator_value = []
        if operator == "bigger":
            math_operator = ">"
            math_operator_value.append(operator_value)
        elif operator == "equal":
            math_operator = "="
            math_operator_value.append(operator_value)
        elif operator == "smaller":
            math_operator = "<"
            math_operator_value.append(operator_value)
        elif operator == "bigger_equal":
            math_operator = ">="
            math_operator_value.append(operator_value)
        elif operator == "smaller_equal":
            math_operator = "<="
        elif operator == "in":
            math_operator_value = operator_value
            math_operator = "between"
        else:
            math_operator = ""
            math_operator_value = None
        return [math_operator, math_operator_value]

    # 序号>列序号>特殊样式设置
    def get_tab_index_special_config_style(self):
        special_configs = []
        return json.dumps(special_configs)


    # 列>数据列
    def get_columns_list_style(self):
        column_list_obj = {
            "cellTextStyle": {
                "checked": True,
                "fontSize": self.get_attr("tbody_font_size", 14),
                "color": self.get_attr("tbody_color", "#000000"),
                "fontStyle": {
                    "fontWeight": ("bold" if self.get_attr("tbody_bold", False) else "normal"),
                    "fontStyle": "normal",
                    "textDecoration": "none"
                },
                "textAlign": "left",
                "headerFollow": True,
                "background": self.get_attr("row_color", "transparent")
            },
            "cellOther": {
                "contentType": "文字"
            },
            "oddEvenRow": {
                "show": False,
                "oddBackground": "",
                "evenBackground": ""
            },
            "stretchWidths": {}
        }

        fields = self.get_attr("table.table_body", [])
        stretch_widths = self.get_field_column_stretch_widths(fields)
        for field in fields:
            column_name = self.get_field_column_name(field)
            column_list_obj[column_name] = {
                "cellTextStyle": {
                    "checked": True,
                    "fontSize": self.get_attr("tbody_font_size", 14),
                    "color": self.get_attr("tbody_color", "#000000"),
                    "fontStyle": {
                        "fontWeight": ("bold" if self.get_attr("tbody_bold", False) else "normal"),
                        "fontStyle": "normal",
                        "textDecoration": "none"
                    },
                    "textAlign": field.get("align", "left"),
                    "headerFollow": self.get_attr("thead_align", "center") == "auto",
                    "background": self.get_attr("row_color", "transparent")
                },
                "cellOther": {
                    "contentType": "文字"
                },
                "oddEvenRow": {
                    "show": self.get_attr("table.striped", False),
                    "oddBackground": self.get_attr("row_color", "transparent"),
                    "evenBackground": self.get_attr("even_color", "transparent")
                }
            }

        column_list_obj["stretchWidths"] = stretch_widths
        return json.dumps(column_list_obj)

    def get_field_column_stretch_widths(self, columns):
        stretch_widths = {}
        header_font_size = self.get_attr("thead_font_size", 14)
        for column in columns:
            col_ename = column['column_ename']
            field = self.dataset_column_list.get(col_ename, {})
            dataset_field_id = field.get("id")
            num_type = field.get("field_group")
            column_header_text = column['fields'][1]['text']
            if num_type and dataset_field_id:
                stretch_widths[dataset_field_id + "_" + num_type] = self.get_font_pixel_by_size(column_header_text,
                                                                                                header_font_size, True)
        stretch_widths["index_column_placeholder"] = self.get_font_pixel_by_size("序号", header_font_size, True)
        return stretch_widths

    def get_field_column_display_name(self, field):
        display_name = field.get("dynamic", "")
        if not display_name:
            filed_columns = field.get("fields", [])
            display_name = filed_columns[0].get("text") if filed_columns[0].get("text") == "" else filed_columns[1].get(
                "text")
        return display_name

    def get_field_column_name(self, field):
        filed_columns = field.get("fields", [])
        return filed_columns[0].get("text") if filed_columns[0].get("text") == "" else filed_columns[1].get("text")

    # 列>条件格式>条件列表
    def get_column_cellsConfig_conditions_style(self):
        tbody = self.get_attr("table.table_body", {})
        conditions_data = []
        field_formatter_order = 0
        for column in tbody:
            filter_style_alise_field = self.get_field_column_name(column)
            filter_formatter_fields = column.get("filter_formatter", None)
            if filter_formatter_fields is not None and len(filter_formatter_fields) > 0:
                for filter_formatter in filter_formatter_fields:
                    filter_condition_field = filter_formatter.get("column", '')
                    if filter_condition_field == "":
                        filter_condition_field = column.get("column_ename", "")
                    col_ename = filter_condition_field
                    field_id = self.dataset_column_list.get(filter_condition_field, {}).get("id")
                    field_data_type = self.dataset_column_list.get(col_ename, {}).get('field_group')
                    field_col_type = self.get_field_chart_number_type(field_data_type)
                    is_datetime = column.get("is_datetime", False)
                    is_float = column.get("is_float", False)
                    column_align = column.get("align", "center")
                    field_type = self.get_field_chart_data_type(is_float, is_datetime)
                    field_operator = self.get_field_operator_value(filter_formatter.get("when", ""),
                                                                   filter_formatter.get("value"))
                    if field_operator[0] == "":
                        continue

                    effect_way = "default" if filter_style_alise_field == filter_condition_field else "custom"
                    formatter_type = "style"
                    if filter_formatter.get("formatter").get("icon", "none") != "none":
                        formatter_type = "icon"
                    filter_formatter_obj = {
                        "dataset_field_id": field_id,
                        "col_name": col_ename,
                        "col_type": field_col_type,
                        "field_type": field_type,
                        "type": formatter_type,
                        "date_operator_type": "static",
                        "daysValue": 7,
                        "dynamic_date": "",
                        "range_date": [],
                        "operator": field_operator[0],
                        "value": field_operator[1],
                        "effect_options": {"way": effect_way, "lists": [filter_style_alise_field]},
                        "order": field_formatter_order,
                        "visible": filter_formatter.get("show", "0") == "0",
                        "rangeVisible": False,
                        "styles": {
                            "color": self.get_hd_mapped_status_icon(filter_formatter).get("color"),
                            "background": filter_formatter.get("formatter").get("background", "transparent"),
                            "icon": self.get_hd_mapped_status_icon(filter_formatter).get("icon"),
                            "icon_align": column_align,
                            "hide": filter_formatter.get("formatter").get("show", "0") == "0",
                            "gradation_colors": []
                        }
                    }
                    field_formatter_order = field_formatter_order + 1
                    conditions_data.append(filter_formatter_obj)
        return json.dumps(conditions_data)

    # <img src="/report-micro/images/filtericons/error01_red.png" class="fi_icon">
    def get_hd_mapped_status_icon(self, filter_formatter):
        result = {
            "color": filter_formatter.get("formatter").get("color", "#000000"),
            "icon": filter_formatter.get("formatter").get("icon", "default")
        }
        icon_uri = filter_formatter.get("formatter").get("icon", "default")
        icon_name = ""
        if str.rfind(icon_uri, ".") > -1:
            icon_name = str.split(icon_uri, "/")[-1]
        if icon_name and len(icon_name.split("_")) > 1:
            icon_type = icon_name.split("_")[0]
            icon_color = icon_name.split("_")[1]
            if icon_type and icon_color:
                icon_ext_index = str.rfind(icon_color, ".")
                icon_color = icon_color[0:icon_ext_index]
                if icon_type == "arrow03":
                    icon_type = "arrow_up"
                elif icon_type == "arrow04":
                    icon_type = "arrow_down"
                elif icon_type == "flog":
                    icon_type = "flag"
                elif icon_type == "tick":
                    icon_type = "tick_mark"
                elif icon_type == "error01" or icon_type == "error02":
                    icon_type = "cross_mark"
                elif icon_type == "circle":
                    icon_type = "round"
                elif icon_type == "null" or icon_type == "stop":
                    icon_type = "line"
                elif icon_type == "arrow01":
                    icon_type = "triangle_up"
                elif icon_type == "arrow02":
                    icon_type = "triangle_down"
                elif str.startswith(icon_type, "error"):
                    icon_type = "cross_mark"
                result["color"] = icon_color
                result["icon"] = icon_type
        return result

    # 全局样式>树形结构>dmpTableType
    def get_global_dmpTableType_config_style(self):
        target_data = {
            "type": "normal",
            "treeHeadName": "",
            "defaultSpreadIndex": "0"
        }
        is_tree_table = self.get_attr("leveltree.enable", False)
        if is_tree_table:
            target_data["type"] = "tree"
            target_data["treeHeadName"] = ""
            target_data["defaultSpreadIndex"] = self.get_attr("open", 1)
        return json.dumps(target_data)

    # 全局样式>树形结构>majorKeysData
    def get_global_majorKeysData_config_style(self, is_parent=False):
        target_data = ""
        is_tree_table = self.get_attr("leveltree.enable", False)
        if is_tree_table:
            field_name = self.get_attr("leveltree.curdatakey", "")
            if is_parent:
                field_name = self.get_attr("leveltree.pardatakey", "")
            field = self.dataset_column_list.get(field_name, {})
            target_data = {
                "value": field.get("id"),
                "dimsData": {
                    "id": field.get("id"),
                    "dataset_id": field.get("dataset_id"),
                    "alias_name": field.get("alias_name"),
                    "col_name": field.get("col_name"),
                    "origin_col_name": field.get("origin_col_name"),
                    "origin_field_type": field.get("origin_field_type"),
                    "origin_table_id": field.get("origin_table_id"),
                    "origin_table_comment": field.get("origin_table_comment"),
                    "origin_table_name": field.get("origin_table_name"),
                    "origin_table_alias_name": field.get("origin_table_alias_name"),
                    "data_type": field.get("data_type"),
                    "group_type": field.get("group_type"),
                    "rank": field.get("rank"),
                    "visible": field.get("visible"),
                    "field_group": field.get("field_group"),
                    "format": field.get("format"),
                    "type": field.get("type"),
                    "expression": field.get("expression"),
                    "expression_advance": field.get("expression_advance"),
                    "inspection_rules": field.get("inspection_rules"),
                    "note": field.get("note"),
                    "include_vars": []
                }
            }
        return json.dumps(target_data)

    def get_hd_mapped_page_size(self):
        hd_page_size = self.get_attr("page_size", 10)
        if hd_page_size <= 10:
            return 10
        elif 10 < hd_page_size <= 20:
            return 20
        elif 20 < hd_page_size <= 30:
            return 30
        elif 30 < hd_page_size <= 50:
            return 50
        elif 50 < hd_page_size <= 100:
            return 100
        elif 100 < hd_page_size <= 150:
            return 150
        elif 150 < hd_page_size <= 200:
            return 200
        elif 200 < hd_page_size <= 500:
            return 500
        elif 500 < hd_page_size <= 1000:
            return 1000
        else:
            return 1500

    def get_rule_remark_description_style(self):
        description = json.dumps(self.get_attr('title_desc', '')).strip('"')
        target = {
            "field": "remarkDescription", "label": "备注说明", "scope": "container.chartRemark.remarkDescription",
            "items": [
                {"field": "title", "label": "标题文本", "data": ""},
                {"field": "textAreaLabel", "label": "内容文本", "data": description},
                {"field": "position", "label": "显示位置", "data": "follow-title"}
            ],
            "data": {
                "title": "",
                "textAreaLabel": description,
                "position": "follow-title"
            }
        }
        return json.dumps(target)

    def get_rule_animation(self):
        target = {"title": "动效设置", "field": "animation", "spread": False,
                  "items": [
                      {
                          "field": "scroll",
                          "label": "滚动设置",
                          "show": {"field": "checked", "data": False},
                          "items": [
                              {"field": "interVal", "label": "间隔时间", "data": 5},
                              {"field": "scrollMode", "data": {"mode": "page", "rows": 1}}
                          ],
                          "data": {"checked": False, "interVal": 5, "scrollMode": {"mode": "page", "rows": 1}}
                      }]
                  }
        if self.chart_type == "scrolltable":
            scroll_mode = "page" if self.get_attr("scroll.carousel", "page") == "page" else "row"
            scroll_size = 1 if scroll_mode == "page" else self.get_attr("scroll.row_num", 10)
            scroll_interval = self.get_attr("scroll.wait_time", 2000) / 1000
            target["items"][0]["show"]["data"] = True
            target["items"][0]["items"][0]["data"] = scroll_interval
            target["items"][0]["items"][1]["data"]["mode"] = scroll_mode
            target["items"][0]["items"][1]["data"]["rows"] = scroll_size
            target["items"][0]["data"]["interVal"] = scroll_interval
            target["items"][0]["data"]["checked"] = True
            target["items"][0]["data"]["scrollMode"]["mode"] = scroll_mode
            target["items"][0]["data"]["scrollMode"]["rows"] = scroll_size
        return json.dumps(target)
