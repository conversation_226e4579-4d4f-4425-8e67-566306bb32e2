from dmplib.hug import APIWrapper
from base.enums import HdUpgradeModuleList
from hd_upgrade.services import dataset_service
from .services import upgrade_service, message_upgrade_service
import urllib.parse

api = APIWrapper(__name__)


@api.admin_route.get('/module_list')
def module_list():
    """
    /**
    @apiVersion 1.0.1
    @api  {get} /api/hd_upgrade/module_list 数据升级模块列表
    @apiGroup  hd数据升级
    @apiResponse 200 {
        "module_list":[
            {
                "module":"all",
                "module_name":"全量升级",
                "has_param":false
            },
            {
                "module":"datasource_keyword",
                "module_name":"数据源、关键字",
                "has_param":false
            },
            {
                "module":"dataset",
                "module_name":"数据集",
                "has_param":true
            },
            {
                "module":"dashboard",
                "module_name":"仪表板",
                "has_param":true
            }
        ],
        "upgrade_record":{
            "id":"39fc5c16-bb85-fd45-ee23-5383b2aa65ea",
            "module_name":"仪表板",
            "status":"完成",
            "completed_on":"2021-05-08 10:59:02"
        }
    }
    **/
    """
    return True, 'success', upgrade_service.get_module_list()


@api.admin_route.get('/log_view')
def log_view(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {get} /api/hd_upgrade/log_view 数据升级模块列表
    @apiGroup  hd数据升级
    @apiParam query {string}  upgrade_task_id 任务id
    @apiParam query {int}  index 索引值
    @apiResponse 200
    {
        "next_index":null,
        "log":"[HD数据升级任务开始]
Notice:模块：全量升级 任务id：39fba39f-56c9-2cd4-3461-193d3bdfdd63 参数：[]
",
        "is_end":true
    }
    **/
    """
    upgrade_task_id = kwargs.get('upgrade_task_id')
    index = kwargs.get('index')
    return True, 'success', upgrade_service.log_view(upgrade_task_id, index)


@api.admin_route.post('/exec')
def upgrade_exec(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {post} /api/hd_upgrade/exec hd数据升级接口入口
    @apiGroup  hd数据升级
    @apiBodyParam {
        "module": "模块标识",
        "params": [] // 附加参数（预留扩展，例如指定某些模块的业务id单独升级）
    }
    @apiResponse 200 {
        "result": true,
        "msg": "ok"
    }
    **/
    """
    set_erp_api_info(request, kwargs)
    status, upgrade_task_id = upgrade_service.trigger_upgrade(
        kwargs.get('module', 'all'),
        kwargs.get('params', []),
    )
    msg = '执行失败, 请稍候重试' if not status else '执行成功'
    return status, msg, upgrade_task_id


@api.admin_route.get('/dataset')
def dataset(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {post} /api/hd_upgrade/dataset 数据集升级
    @apiGroup  hd数据升级
    @apiBodyParam {
        "dataset_ids": "需要升级的数据集id,多个用逗号隔开；不传改参数，升级所有数据集",
    }
    @apiResponse 200 {
        "result": true,
    }
    **/
    """
    dataset_ids = kwargs.get('dataset_ids')
    set_erp_api_info(request, kwargs)
    return upgrade_service.sync_upgrade(HdUpgradeModuleList.DATASET.value, {"ids": dataset_ids})


@api.admin_route.get('/datasource')
def datasource(request, **kwargs):
    """
    数据源升级
    :return:
    """
    set_erp_api_info(request, kwargs)
    return upgrade_service.sync_upgrade(HdUpgradeModuleList.DATASOURCE_KEYWORD.value)


@api.admin_route.get('/dashboard')
def dashboard(request, **kwargs):
    """
    仪表板升级
    :param kwargs:
    :return:
    """

    dashboard_ids = kwargs.get('dashboard_ids')
    kwargs['ids'] = dashboard_ids
    return upgrade_service.sync_upgrade(HdUpgradeModuleList.DASHBOARD.value,kwargs)


@api.admin_route.get('/m_report')
def m_report(**kwargs):
    """
    移动报表升级
    :param kwargs:
    :return:
    """
    m_report_ids = kwargs.get('m_report_ids')
    tenant_code = kwargs.get('tenant_code', '')
    return upgrade_service.sync_upgrade(HdUpgradeModuleList.M_REPORT.value,
                                        {"ids": m_report_ids, "tenant_code": tenant_code})


@api.admin_route.get('/fix_task')
def fix_task():
    return upgrade_service.fix_task()


@api.admin_route.get('/role')
def role(**kwargs):
    """
    角色权限升级
    :return:
    """
    tenant_code = kwargs.get('tenant_code', '')
    return upgrade_service.sync_upgrade(HdUpgradeModuleList.ROLE.value, {"tenant_code": tenant_code})


def set_erp_api_info(request, kwargs):
    try:
        url = request.headers['REFERER']
        query = dict(urllib.parse.parse_qsl(urllib.parse.urlsplit(url).query))
        kwargs['params']['erpapi_host'] = query.get('erpapi_host')
        kwargs['params']['erpapi_access_id'] = query.get('erpapi_access_id')
        kwargs['params']['erpapi_access_secret'] = query.get('erpapi_access_secret')
        kwargs['params']['yzs_domain'] = query.get('yzs_domain')
        kwargs['params']['tenant_id'] = query.get('tenant_id')
    except:
        return


@api.admin_route.get('/test/dataset_upgrade')
def test_dataset_upgrade(request, **kwargs):  # 升级指定固定的数据集  防止过多数据集干扰
    set_erp_api_info(request, kwargs)
    dataset_ids = kwargs.get('dataset_ids')
    dataset_ids = dataset_ids.split(',')
    params = {"dataset_ids": dataset_ids}
    return dataset_service.dataset_upgrade(**params)


@api.admin_route.get('/test/dashboard')
def test_dashboard_upgrade(**kwargs):  # 升级固定看板 可以进行调试
    ids_list = kwargs.get('report_id', '')
    report_type = kwargs.get('report_type', 'dashboard')
    yzs_domain = kwargs.get('yzs_domain', '')
    params = {"report_type": report_type, "ids": ids_list, 'tenant_code': kwargs.get('tenant_code', ''),
              'yzs_domain': yzs_domain}
    return upgrade_service.dashboard_upgrade(ids_list, params)


@api.admin_route.get('/test/message')
def test_dashboard_upgrade(**kwargs):  # 升级固定简讯 可以进行调试
    ids_list = kwargs.get('msg_id', '')
    if isinstance(ids_list, str):
        if ids_list.find(',') > -1:
            ids_list = ids_list.split(',')
        else:
            if ids_list:
                ids_list = [ids_list]
    params = {"ids": ids_list, 'tenant_code': kwargs.get('tenant_code', '')}
    return message_upgrade_service.message_upgrade(params)

