#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file


import unittest
from tests.base import BaseTest


class TestHDLocalInitService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='uitest', account='admin')

    def test_init_menu(self):
        from hd_upgrade.services.hd_local_init_service import HDLocalInit
        hd_init = HDLocalInit(from_init='erpop')
        hd_init.init_menu()

    def test_init_third_party_app(self):
        from hd_upgrade.services.hd_local_init_service import HDLocalInit
        hd_init = HDLocalInit(from_init='erpop')
        hd_init.init_third_party_app()

    def test_init_cyjg_app(self):
        from hd_upgrade.services.hd_local_init_service import HDLocalInit
        hd_init = HDLocalInit(from_init='erpop')
        hd_init.init_cyjg_third_party_app()


if __name__ == '__main__':
    unittest.main()
