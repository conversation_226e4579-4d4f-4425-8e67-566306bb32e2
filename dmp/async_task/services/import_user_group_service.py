#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/7/20.
"""
import os

from async_task.services.read_excel import ReadExcel
from base.enums import AccountMode, UserChannel
from components.oss import OSSFileProxy
from dataset.models import DatasetExcelResultModel
from dmplib import redis
from dmplib.utils.errors import UserError
from user_group.models import UserGroupExcelModel
from user_group.repositories import user_group_repository


class GroupExcelService(ReadExcel):
    """
    用户组Excel业务类
    """

    def __init__(self, project_code=None, account=None, oss_url=None):
        super().__init__(project_code, account, oss_url)
        # 用户组列出限制：5列（组织ID、组织名称、上级组织id、上级组织名称、层级）
        self.columns_limit = 5
        self.is_erp_user_source = ""

    def async_process_data(self, task_id, user_source_id, is_preview=1):
        result = DatasetExcelResultModel()
        try:
            result.task_id = task_id
            infos, count, invalid_count = self.start_import(user_source_id, is_preview)
            if isinstance(infos, str):
                result.info = infos
            else:
                # redis存储需要将model转换为dict
                new_infos = []
                for info in infos:
                    new_infos.append(info.get_dict())
                result.info = new_infos
            result.status = 1
            result.count = count
            result.invalid_count = invalid_count
            redis.conn().set_data(task_id, result.get_dict())
        except Exception as ex:
            error_msg = "处理组织机构excel失败，错误内容：" + str(ex)
            result.error_msg = error_msg
            result.status = 1
            redis.conn().set_data(task_id, result.get_dict())
        finally:
            if self.file_path and os.path.exists(self.file_path):
                os.remove(self.file_path)
            if not is_preview:
                OSSFileProxy().delete(self.oss_url, is_url=True)
        return task_id

    def start_import(self, user_source_id, is_preview=1):
        user_source = user_group_repository.get_user_soruce_by_id(user_source_id)
        if not user_source:
            raise UserError(message='用户渠道不存在，请刷新页面再试')
        self.is_erp_user_source = True if user_source.get('type') == UserChannel.Erp.value else False
        # 处理excel数据
        self.file_path = self.download_oss_file()
        sheet = self.read_data()
        count = sheet.nrows - 2
        user_group_excel_models, error_models = self.process_data(sheet)
        # 预览数据直接返回前100条
        if is_preview == 1:
            if len(error_models) > 0:
                return error_models, count, len(error_models)
            return user_group_excel_models[:100], count, len(error_models)
        # 写入erp_user_group表
        self.write_data(user_source, user_group_excel_models)
        # 生成code
        new_tmp_table_name = user_group_repository.organ_code(user_source)
        table = 'dap_bi_external_user_group' if self.is_erp_user_source else 'dap_p_user_source_group'
        field = ['id', 'name', 'code', 'parent_id', 'hierarchy', 'account_mode']
        if not self.is_erp_user_source:
            field.append('user_source_id')
            field.append('user_source_group_id')
        delete_conditions = {} if self.is_erp_user_source else {'user_source_id': user_source_id}
        user_group_repository.sync_user_source_group(table, new_tmp_table_name, field, delete_conditions)
        return "成功导入{}条数据。".format(len(user_group_excel_models)), count, len(error_models)

    def validation_head_data(self, head_datas):
        """
        校验excel头部合法性
        :return:
        """
        if len(head_datas) < self.columns_limit:
            raise UserError(message='文件内容必须包含组织id、组织名称、上级组织id、上级组织名称、层级。')

        if "组织id" not in head_datas[0]:
            raise UserError(message='第二行表头缺少组织id')

        if "组织名称" not in head_datas[1]:
            raise UserError(message='第二行表头缺少组织名称')

        if "上级组织id" not in head_datas[2]:
            raise UserError(message='第二行表头缺少上级组织id')

        if "层级" not in head_datas[4]:
            raise UserError(message='第二行表头缺少层级')

    @staticmethod
    def validation_data(datas):
        """
        校验合法性
        :return:
        """
        error_msg = ""
        if not datas[0]:
            error_msg += "组织id不能为空 \n"
        return error_msg

    def process_data(self, sheet):
        duplicate = {}
        user_group_excel_models = []
        error_models = []
        for row in range(sheet.nrows):
            # 忽略第一行注意事项
            if row == 0:
                continue
            # 校验表头
            if row == 1:
                self.validation_head_data(sheet.row_values(row))
                continue

            # 读取内容
            user_group_excel_model = UserGroupExcelModel()
            user_group_excel_model.id = sheet.row_values(row)[0]
            user_group_excel_model.name = sheet.row_values(row)[1]
            # 父级用户组为空时使用默认用户组
            user_group_excel_model.parent_id = sheet.row_values(row)[2]
            user_group_excel_model.parent_name = sheet.row_values(row)[3]
            user_group_excel_model.hierarchy = sheet.row_values(row)[4]
            user_group_excel_model.account_mode = AccountMode.IMPORT.value

            error_msg = self.validation_data(sheet.row_values(row))
            if error_msg:
                user_group_excel_model.error_msg = error_msg
                error_models.append(user_group_excel_model)
            if sheet.row_values(row)[0] in duplicate:
                user_group_excel_model.error_msg = error_msg + "重复组织ID"
                error_models.append(user_group_excel_model)

            if not error_msg:
                duplicate[sheet.row_values(row)[0]] = 1
                user_group_excel_models.append(user_group_excel_model)

        return user_group_excel_models, error_models

    def write_data(self, user_source, user_group_excel_models):  # pylint: disable=no-self-use
        fields = ['id', 'name', 'parent_id', 'hierarchy', 'account_mode']
        user_group_datas = []
        for user_group_excel_model in user_group_excel_models:
            user_group_data = user_group_excel_model.get_dict(fields)
            user_group_datas.append(user_group_data)
        user_group_repository.add_multi_external_user_groups(user_source, user_group_datas)
        if self.is_erp_user_source:
            # 更新用户组织关系表
            user_group_repository.update_user_organization()
