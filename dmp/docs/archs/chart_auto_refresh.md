# 单图自动刷新

> 数据集元数据版本: 编辑数据集字段时更新

> 数据集数据版本: flow调度时更新

## 设计思想


1. 在前端轮询接口(60s): `/api/released_dashboard/released_on` 的返回值中增加属性(`dataset_versions`):

```
{
  "dataset_versions": {
    "39e806ae-36a4-ec20-cc31-15b47b1f34b6": {
      "version": "201811021121_121", // 数据集元数据版本
      "data_version": "201811021112_471" // 数据集数据版本
    }
  }
}
```

`39e806ae-36a4-ec20-cc31-15b47b1f34b6`为chart_id


2. 前端取数接口: `api/released_dashboard_chart/chart/data` 的返回值中增加属性(`dataset_versions`):

```json
{
  "data":  {
     "39e806ae-36a4-ec20-cc31-15b47b1f34b6": {
       "conditions": [],
       "data": [],
       "marklines": [],
       "dataset_versions": {
          "version": "201811021121_121",
          "data_version": "201811021112_471"
        }
     }
  }
}

```

3. 前端将取数结果中，单图数据集的版本信息缓存在客户端。并且，每次获取数据后更新该缓存。

```
{
  "dataset_versions": {
    "39e806ae-36a4-ec20-cc31-15b47b1f34b6": {
      "version": "201811021121_121", // 数据集元数据版本
      "data_version": "201811021112_471" // 数据集数据版本
    }
  }
}
```

## 前端重新刷新单图数据的时机

**每次请求`released_on`接口后, 增加动作来决定是否刷新单图数据(重新取数或整个报告重新加载)**

- 代码逻辑

    ```javascript 1.8

    const data = {} // 假设`released_on`接口返回数据`data`
    const local_dataset_versions = {} // 本地数据集版本缓存, 以单图id作为key

    function need_refresh() {

      // 本地从没有缓存, 则不刷新
      if (!local_dataset_versions) {
          return false  
      }
      refresh_chart_ids = []
      for (var chart_id in data) {  
        // 发现新单图数据,则重新刷新整个报告
        if (!local_dataset_versions.hasOwnProperty(chart_id)) {
          return true  
        }
          const {local_version, local_data_version} = local_dataset_versions[chart_id]
          const {version, data_version} = data[chart_id]

          // 元数据版本不一致, 刷新整个报告
          if (local_version !== version) {
            return true
          }

          // 数据版本不一致, 只刷新单图
          if (local_data_version !== data_version) {
            refresh_chart_ids.push(chart_id)
          }
      }
      return refresh_chart_ids
    }

    const refreshFacts = need_refresh()

    if (refreshFacts === true) {
       // 全部刷新
    } else if (typeof refreshFacts === Array && refreshFacts.length >0) {
      // 根据chart_id刷新
    }

    ```
