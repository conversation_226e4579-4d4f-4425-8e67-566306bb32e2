# -*- coding: utf-8 -*-

import os
import logging
import time
from urllib.parse import urlparse
from components.oss import OSSFileProxy
from dmplib.utils.errors import UserError
from open_data.oss_unzip import helper


def handler(url_file, oss_key):
    """
    解压oss的zip包的处理函数
    :param url_file: 解压文件url
    :param oss_key: 上传到OSS的文件名
    """
    if not url_file:
        raise UserError(message="解压文件的url不能为空")
    if not url_file.lower().startswith("http"):
        raise UserError(message="解压文件的url格式错误：" + url_file)
    file_type = os.path.splitext(url_file)[1]
    if file_type != ".zip":
        raise UserError(message="只支持解压zip文件：" + url_file)

    logging.info("start to decompress oss zip file")

    start_time = time.time()

    res = urlparse(url_file)
    object_name = res.path[1:] if res.path.startswith('/') else res.path
    msg = "url_file：" + url_file
    logging.info(msg)
    msg = "object_name：" + object_name
    logging.info(msg)
    oss_bucket = OSSFileProxy().bucket
    zip_fp = helper.OssStreamFileLikeObject(object_name)

    with helper.zipfile_support_oss.ZipFile(zip_fp) as zip_file:
        name_list = zip_file.namelist()

        is_folder = True
        for name in name_list:
            if not name.startswith(name_list[0]):
                is_folder = False

        for name in name_list:
            with zip_file.open(name) as file_obj:
                if is_folder:
                    name = name.replace(name_list[0], "")
                oss_bucket.put_object(oss_key + name, file_obj)

    end_time = time.time()
    end_msg = "end to decompress oss zip file, duration: " + str(int(round((end_time - start_time) * 1000)))
    logging.info(end_msg)
