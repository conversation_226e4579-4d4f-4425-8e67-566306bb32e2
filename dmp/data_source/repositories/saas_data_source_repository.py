#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/20.
"""
from dmplib.db.mysql_wrapper import SimpleMysql
from base import repository


def get_tenant_db_list(model):
    """
    获取租户列表
    :param data_source.models.SaaSConnStrModel model:
    :return:
    """

    with SimpleMysql(
        host=model.tenant_db_host,
        port=int(model.tenant_db_port),
        db=model.tenant_db_name,
        user=model.tenant_db_user,
        passwd=model.tenant_db_password,
    ) as db:
        return db.query(model.tenant_list_sql)


def get_tables(query_model):
    """
    获取数据表
    :param data_source.models.TableQueryModel query_model:
    :return:
    """
    sql = 'SELECT TABLE_NAME AS `name`,TABLE_COMMENT AS `comment` ' 'FROM information_schema.TABLES ' 'WHERE TABLE_TYPE=\'BASE TABLE\' ' 'AND TABLE_SCHEMA =DATABASE() '
    params = {}
    if query_model.keyword:
        sql += 'AND TABLE_NAME LIKE %(keyword)s '
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.data_source.conn_str.table_name_prefix:
        table_name_prefix = query_model.data_source.conn_str.table_name_prefix.split(',')
        for i in range(len(table_name_prefix)):
            if not table_name_prefix[i]:
                continue
            sql += ' AND TABLE_NAME LIKE %(prefix' + str(i) + ')s '
            params['prefix' + str(i)] = table_name_prefix[i].replace('_', '\\_') + '%'

    with _get_mysql_db(query_model.data_source) as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += 'LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
        return query_model


def get_table_columns(query_model):
    """
    获取数据表字段
    :param data_source.models.ColumnQueryModel query_model:
    :return:
    """
    sql = 'SELECT COLUMN_NAME AS `name`,COLUMN_TYPE AS `type` ,COLUMN_COMMENT AS `comment` ' 'FROM information_schema.COLUMNS ' 'WHERE TABLE_SCHEMA =DATABASE() AND TABLE_NAME=%(table_name)s ' 'ORDER BY ORDINAL_POSITION'
    with _get_mysql_db(query_model.data_source) as db:
        query_model.items = db.query(sql, {'table_name': query_model.table_name})
        query_model.total = len(query_model.items)
        return query_model


def _get_mysql_db(model):
    """
    获取mysql数据db
    :param data_source.models.SaaSDataSourceModel model:
    :return:
    """
    conn_str = model.conn_str
    return SimpleMysql(
        host=conn_str.standard_db_host,
        port=int(conn_str.standard_db_port),
        db=conn_str.standard_db_name,
        user=conn_str.standard_db_user,
        passwd=conn_str.standard_db_password,
    )
