#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/21.
"""

from dmplib.db.mysql_wrapper import SimpleMysql
from dmplib.components.enums import DBType
from base import repository
from base.sql_adapter import adapter_sql
from data_source.models import DataSourceModel


def get_tables(query_model):
    """
    获取数据表
    :param data_source.models.TableQueryModel query_model:
    :return:
    """
    with get_dm_db(query_model.data_source) as db:
        sql = """
        SELECT 
            DISTINCT t.TABLE_NAME as name,
            c.COMMENTS as "comment"
        FROM 
            all_tables t
        INNER JOIN 
            ALL_TAB_COMMENTS c
        ON 
        t.TABLE_NAME = c.TABLE_NAME 
        -- and t.TABLESPACE_NAME='MAIN' 
        AND t.OWNER='{}' AND c.OWNER='{}'
        """.format(query_model.data_source.conn_str.database, query_model.data_source.conn_str.database)
        params = {}
        if query_model.keyword:
            sql += ' AND (t.TABLE_NAME LIKE %(keyword_escape)s or c.COMMENTS LIKE %(keyword_escape)s or t.TABLE_NAME LIKE %(keyword)s) '
            params['keyword_escape'] = '%' + query_model.keyword_escape + '%'
            params['keyword'] = '%' + query_model.keyword + '%'
        if query_model.data_source.conn_str.table_name_prefix:
            table_name_prefix = query_model.data_source.conn_str.table_name_prefix.split(',')
            for i in range(len(table_name_prefix)):
                if not table_name_prefix[i]:
                    continue
                sql += ' AND t.TABLE_NAME LIKE %(prefix' + str(i) + ')s '
                params['prefix' + str(i)] = table_name_prefix[i].replace('_', '\\_') + '%'

        query_model.total = repository.get_total(sql, params, db)
        sql += 'LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
        return query_model


def get_table_columns(query_model):
    """
    获取数据表字段
    :param data_source.models.ColumnQueryModel query_model:
    :return:
    """
    with get_dm_db(query_model.data_source) as db:
        sql = adapter_sql('table_columns', db.db_type).format(owner=query_model.data_source.conn_str.database)
        query_model.items = db.query(sql, {'table_name': query_model.table_name})
        query_model.total = len(query_model.items)
        return query_model


def create_table(model):
    """
    创建数据表
    :param data_source.models.CreateTableModel model:
    :return:
    """
    with get_dm_db(model.data_source) as db:
        return db.exec_sql(model.create_sql)


def get_dm_db(model):
    """
    获取mysql数据db
    :param  model:
    :return:
    """
    if isinstance(model, DataSourceModel):
        conn_str = model.conn_str
    else:
        conn_str = model
    return SimpleMysql(
        host=conn_str.host,
        port=int(conn_str.port),
        db=conn_str.database,
        user=conn_str.user,
        passwd=conn_str.password,
        use_ssh=conn_str.use_ssh,
        ssh_host=conn_str.ssh_host,
        ssh_port=conn_str.ssh_port,
        ssh_user=conn_str.ssh_user,
        ssh_password=conn_str.ssh_password,
        db_type=DBType.DM.value
    )
