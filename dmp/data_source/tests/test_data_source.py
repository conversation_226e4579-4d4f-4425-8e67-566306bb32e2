#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    <NAME_EMAIL> on 2018/4/13.
"""
from data_source.models import DataSourceQueryModel
from data_source.services import data_source_service
from tests.base import BaseTest

from dmplib.utils.crypt import AESCrypt

import unittest


class TestDataSourceService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev', account='admin')

    def test_get_data_source_list(self):
        """
        获取数据源list
        :return:
        """
        kwargs = {'page': 1, 'page_size': 100}
        result = data_source_service.get_data_source_list(DataSourceQueryModel(**kwargs))
        self.assertIsInstance(result, dict, "测试结果返回类型不匹配")

    def test_decrpy(self):
        crypt = AESCrypt()
        res = crypt.decrypt(
            '106c8cbd58f97d51ffaa6c5de09143b50c50d98b4bf4c7f97943115d96a93e9aa854b45ac7705484bd8ba41f829d8c41'
        )
        print(res)


if __name__ == '__main__':
    unittest.main()
