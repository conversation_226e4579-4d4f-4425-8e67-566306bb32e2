# -*- coding: utf-8 -*-
# pylint: skip-file
import regex
import json
from hd_api.api.high_data_api import HighDataApi
from hd_api.models import MetaData
from base import repository
from data_source.models import DataSourceModel
from components.data_center_api import get_data_source_info, get_new_erp_datasource_model
from keywords.repositories.keyword_repositories import get_keyword_by_dataset_id, get_keyword_by_name_list
from keywords.external_service import get_keyword_result_by_id
from hd_api.repositories.dashboard_info_repositories import get_dashboard_filter_relation, get_dashboard_params_relation
from base.enums import DataSourceType


def get_data(data: MetaData):
    # 准备元数据
    prepare_data(data)
    api = HighDataApi()
    return api.api_request('get-data', data.get_dict())


def prepare_data(data: MetaData):
    """
    准备元数据
    @param data:
    @return:
    """
    # 获取组件的CONFIG
    get_chart_config(data)
    # 获取筛选组件的相关配置
    get_filter(data)
    # 获取数据集和数据源的信息
    get_dataset_and_data_source(data)
    # 获取页面参数和筛选配置信息
    get_dashboard_filter_and_var_params(data)


def get_chart_config(data: MetaData):
    """
    获取high_data组件配置
    @param data:
    @return:
    """
    if not data.chart_config:
        data.chart_config = repository.get_value('dap_bi_dashboard_chart', {'id': data.chart_id}, ['config'])
    match = r'(?<="dataset_id":\s+|"dataset_id":|"hk_dataset_id":\s+|"hk_dataset_id":)"([^"]+)"'
    # 获取数据集ID
    data.dataset_id = list(set(regex.findall(match, data.chart_config)))
    data.chart_config = json.loads(data.chart_config) if data.chart_config else dict()
    data.app_code = repository.get_value(
        'dap_bi_dashboard', {'id': data.dashboard_id}, ['erp_app_code']
    ) if data.dashboard_id else ''


def get_filter(data: MetaData):
    filter_chart_id = repository.get_columns('dap_bi_dashboard_chart', {
        'dashboard_id': data.dashboard_id, 'chart_type': 'filter'}, 'id') or []
    filter_chart_list = repository.get_list(
        'dap_bi_dashboard_filter_chart',
        {'dashboard_id': data.dashboard_id},
        ['id', 'chart_id', 'dataset_field_id', 'dataset_id']
    )
    filter_id = [filter_chart.get('id') for filter_chart in filter_chart_list]
    if filter_id:
        filter_chart_map = {}
        for filter_chart in filter_chart_list:
            if filter_chart.get('chart_id') in filter_chart_id:
                filter_chart_map[filter_chart.get('id')] = filter_chart.get('chart_id')
                filter_chart['id'] = filter_chart.get('chart_id')
        filter_chart_relation_list = repository.get_list(
            'dap_bi_dashboard_filter_chart_relation',
            {'filter_id': filter_id},
            ['filter_id', 'field_responder_id', 'dataset_responder_id', 'chart_responder_id'],
            group=['filter_id', 'field_responder_id', 'dataset_responder_id', 'chart_responder_id']
        )
        if filter_chart_map:
            for filter_chart_relation in filter_chart_relation_list:
                if filter_chart_map.get(filter_chart_relation.get('filter_id')):
                    filter_chart_relation['filter_id'] = filter_chart_map[filter_chart_relation.get('filter_id')]
    else:
        filter_chart_relation_list = []
    data.filter['filter_chart_list'] = filter_chart_list
    data.filter['filter_chart_list_relation'] = filter_chart_relation_list
    # 获取变量和组件的绑定关系
    data.var_relation = repository.get_list(
        'dap_bi_dashboard_dataset_vars_relation',
        {'dashboard_id': data.dashboard_id},
        ['chart_initiator_id', 'field_initiator_id', 'var_id', 'var_dataset_id']
    )


def get_dataset_and_data_source(data: MetaData):
    """
    查询数据源和数据集的相关元数据
    @param data:
    @return:
    """
    if data.dataset_id:
        data.data_set = repository.get_list(
            'dap_bi_dataset',
            {'id': data.dataset_id},
            ["id", "name", "table_name", 'content', 'connect_type', 'type']
        )

        dataset_fields = get_dataset_field_by_dataset_id(data.dataset_id)

        for data_set in data.data_set:
            data_set['content'] = json.loads(data_set['content']) if data_set.get('content') else dict()
            data_source_id = data_set.get('content').get('data_source_id')
            if data_source_id:
                data_source = repository.get_one('dap_m_data_source', {'id': data_source_id})
                model = DataSourceModel(**data_source)
                model.conn_str_to_model()
            else:
                model = get_new_erp_datasource_model(data_set.get('content'))
            data_source['conn_str'] = json.loads(data_source['conn_str']) if data_source.get('conn_str') else {}
            data_set['data_source'] = get_data_source_info(model.conn_str)
            data_set['data_set_params'], data_set['keyword_list'] = get_vars_and_keywords_by_dataset_id(
                data_set.get('id')
            )
            data_set['dataset_filter_keyword'] = get_keyword_info(data.chart_config, data_source_id)
            data_set['field'] = {}
            for fields in dataset_fields:
                if data_set.get('id') == fields.get('dataset_id'):
                    fields['data_type'] = '文本' if fields.get('data_type') == '字符串' else fields.get('data_type')
                    data_set['field'][fields.get('col_name')] = fields


def get_vars_and_keywords_by_dataset_id(dataset_id: str):
    """
    获取变量和关键字列表
    @param dataset_id:
    @return:
    """
    data_set_params = keyword_list = []
    if dataset_id:
        data_set_params = repository.get_list(
            "dap_bi_dataset_vars", {"dataset_id": dataset_id},
            ['id', 'name', 'dataset_id', 'var_type', 'value_type', 'default_value_type', 'default_value']
        )
        keyword_list = get_keyword_by_dataset_id(dataset_id)
        for keyword_info in keyword_list:
            keyword_info['value'] = get_keyword_result_by_id(keyword_info.get('keyword_id'))
    return data_set_params, keyword_list


def get_dataset_field_by_dataset_id(dataset_id: list = None):
    dataset_field = []
    if dataset_id:
        dataset_field = repository.get_list(
            "dap_bi_dataset_field",
            {"dataset_id": dataset_id},
            ["id", "dataset_id", "col_name", "origin_col_name", "data_type"]
        )
    return dataset_field


def get_keyword_info(chart_config, data_source_id):
    keyword_name = []
    keyword_list = []
    dataset_filter = chart_config.get('dataset_filter', list())
    for filter_info in dataset_filter:
        if str(filter_info.get('type', 0)) == '1':
            keyword_name.append(filter_info.get('value'))
    if keyword_name:
        keyword_name = list(set(keyword_name))
        keyword_list = get_keyword_by_name_list(keyword_name, data_source_id)
        for keyword_info in keyword_list:
            keyword_info['value'] = get_keyword_result_by_id(keyword_info.get('id'))
    return keyword_list


def get_dashboard_filter_and_var_params(data: MetaData):
    filter_relation = get_dashboard_filter_relation(data.dashboard_id)
    params_relation = get_dashboard_params_relation(data.dashboard_id)
    filters = {}
    for item in filter_relation:
        if item.get('var_name'):
            if item.get('var_name') not in filters:
                filters[item.get('var_name')] = {'link_key': item.get('var_name'), 'filters': list(), 'params': list()}
            filter_arr = {'dataset_id': item.get('related_dataset_id'), 'dataset_field_list': list()}
            dataset_field_list = {'ColumnName': item.get('col_name')}
            filter_arr.get('dataset_field_list').append(dataset_field_list)
            filters[item.get('var_name')].get('filters').append(filter_arr)
    for params in params_relation:
        if params.get('value_source_name'):
            if params.get('value_source_name') not in filters:
                filters[params.get('value_source_name')] = {
                    'link_key': params.get('value_source_name'), 'filters': list(), 'params': list()
                }
            params_arr = {'dataset_id': params.get('dataset_id'), 'params_field_list': list()}
            params_field_list = {'ParamName': params.get('name')}
            params_arr.get('params_field_list').append(params_field_list)
            filters[params.get('value_source_name')].get('params').append(params_arr)
    data.var_filter_params = list(filters.values())

