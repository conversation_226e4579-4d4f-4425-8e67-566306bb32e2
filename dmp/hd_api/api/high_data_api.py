#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json

import requests
import jwt
from hashlib import md5
from requests import RequestException
from dmplib import config
from dmplib.utils.errors import HttpError, UserError
from datetime import datetime, timedelta
from dmplib.saas.project import get_mysoft_erp


class HighDataApi:
    """
    HighData接口调用API
    """

    API_PATH = 'dmp/chart'

    HIGH_DATA_SECRET_KEY = "My$oft_"

    def __init__(self):
        super().__init__()

    def api_request(self, action_name, params: dict):
        """
        @param action_name:
        @param params:
        @return:
        """
        host = config.get('Domain.dmp_high_data', 'https://dmp-highdata-service-test.mypaas.com.cn/')
        host = host[: len(host) - 1] if host.endswith('/') else host
        url = '%s/%s/%s' % (host, self.API_PATH, action_name)
        params["token"] = self.get_token()
        params["erp_info"] = self.get_erp_info()
        try:
            response = requests.post(url, json=params, timeout=60)
        except Exception as be:
            msg = "请检查网络是否正常:{}|url:{}|parameters{}".format(str(be), url, str(params))
            raise HttpError(message=msg, status=response.status_code)
        if response.status_code == 200:
            if params.get('export_table') == 0:
                result = response.json() if response.text else {'isSuccess': 0, 'message': '接口数据返回为空'}
                if not result.get('isSuccess'):
                    raise UserError(message=result.get('message'))
                return result
            else:
                return response.text
        else:
            msg = "错误反馈信息：" + str(response.status_code) + ' , ' + str(response.reason)
            raise HttpError(message=msg, status=response.status_code)

    def get_token(self):
        """
        获取秘钥
        :return string:
        """
        try:
            payload = {"exp": self.get_exp()}
            token = jwt.encode(payload, self._get_secret_key())
            return token
        except RequestException as e:
            raise UserError(message='获取jwt token失败:' + str(e))

    def _get_secret_key(self):
        """
        获取jwt 的秘钥
        :return string:
        """
        return md5(self.HIGH_DATA_SECRET_KEY.encode("utf-8")).hexdigest()

    def get_login_hd_token(self, tenant_id):
        channel_id = 10
        secret = f"{tenant_id}{self.HIGH_DATA_SECRET_KEY}{channel_id}"
        md5_str = md5(secret).hexdigest()
        exp = self.get_exp(60 * 120)
        payload = {
            tenant_id,
            exp,
            "MDC", channel_id
        }
        token = jwt.encode(payload, md5_str)
        return token

    @staticmethod
    def get_erp_info():
        """
        获取接口管家地址
        @return:
        """
        erp_site = get_mysoft_erp()
        if erp_site['erpapi_host'] is None:
            raise UserError(message='未配置接口管家地址!')
        erp_site['erpapi_access_id'] = '' if erp_site['erpapi_access_id'] is None else erp_site['erpapi_access_id']
        erp_site['erpapi_access_secret'] = (
            '' if erp_site['erpapi_access_secret'] is None else erp_site['erpapi_access_secret']
        )
        return erp_site

    @staticmethod
    def get_exp(seconds=60):
        """
        获取exp时间
        :return:
        """
        # 获取当前时间
        d1 = datetime.now()
        # 当前时间加上60秒
        d2 = d1 + timedelta(seconds=seconds)
        return int(d2.timestamp())
