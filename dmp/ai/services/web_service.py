import json
from datetime import datetime

import requests

from ai.common.utils import transform_subject_tree_data, group_scenes_by_category
from ai.repositories.web_repository import get_scene_list_by_subject_id, scene_category_is_existed, \
    add_scene_category_by_data, update_scene_category_data, get_subject_list, delete_scene_category_by_id
from base import repository
from dashboard_chart.services import metadata_service, dashboard_service
from dmplib.components import auth_util
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from dmplib.hug import g
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id


def get_category_name(category_id):
    dashboard_data = repository.get_data(
        table_name='dap_ai_scene_category',
        conditions={'id': category_id, 'is_deleted': 0},
        fields=['name'],
        multi_row=False
    )
    return dashboard_data.get('name') if dashboard_data else ''


def get_category_list():
    category_list = repository.get_list(
        table_name='dap_ai_scene_category',
        conditions={'is_deleted': 0},
        fields=['id', 'name'],
        multi_row=False
    )
    return category_list


def get_dashboard_list():
    params = {
        'status': 1,
        'file_type': 'new_mobile'
    }
    results = dashboard_service.get_dashboard_list(**params)

    return [{
        'id': item['id'],
        'name': item['name'],
    } for item in results.get('tree', [])]


def get_scene(scene_id):
    scene = repository.get_data(
        table_name='dap_ai_scene',
        conditions={'id': scene_id, 'is_deleted': 0},
        fields=[
            'id',
            'dashboard_id',
            'category_id',
            'description',
            'prompt',
            'params',
            'field_list',
            'question_list',
        ]
    )
    if not scene:
        raise UserError(message=f'场景不存在: {scene_id}')

    scene['params'] = json.loads(scene['params'])
    scene['field_list'] = json.loads(scene['field_list'])
    scene['question_list'] = json.loads(scene['question_list'])

    scene['dashboard_name'] = metadata_service.get_dashboard_name(scene['dashboard_id'])
    scene['category_name'] = get_category_name(scene['category_id'])
    return scene


def get_init_scene(dashboard_id):
    dashboard_info = repository.get_data(
        table_name='dap_bi_dashboard_released_snapshot_dashboard',
        conditions={'id': dashboard_id},
        fields=['name', 'global_params'],
        multi_row=False
    )
    if not dashboard_info:
        raise UserError(message=f'报表不存在: {dashboard_id}')

    field_group_keys = [
        'dims',
        'nums',
        'comparisons',
        'filters',
        'zaxis',
        'chart_params',
        'desires',
        'marklines',
        'field_sorts'
    ]

    dashboard_chart_infos = repository.get_data(
        table_name='dap_bi_dashboard_released_snapshot_chart',
        conditions={'dashboard_id': dashboard_id},
        fields=['id', 'name'] + field_group_keys,
        multi_row=True
    )
    if not dashboard_chart_infos:
        raise UserError(message=f'报表中无任何图表: {dashboard_id}')

    dashboard_name = dashboard_info.get('name', '')
    global_params = json.loads(dashboard_info.get('global_params', '[]'))

    params = [{
        'id': item['id'],
        'name': item['name'],
        'type': 'string',
        'description': item['alias_name']
    } for item in global_params]

    field_list = []
    for chart_info in dashboard_chart_infos:
        all_fields = []
        for key in field_group_keys:
            try:
                all_fields.extend(json.loads(chart_info.get(key, '[]')))
            except Exception:
                pass

        dimensions = [
            {
                'id': f.get('dim') or f.get('id'),
                'name': f.get('alias_name')
            } for f in all_fields if f.get('field_group') == '维度'
        ]
        nums = [
            {
                'id': f.get('num') or f.get('id'),
                'name': f.get('alias_name'),
                'description': f.get('alias_name'),
                'dimensions': dimensions
            } for f in all_fields if f.get('field_group') == '度量'
        ]

        field_list.append({
            'id': chart_info['id'],
            'name': chart_info['name'],
            'fields': nums,
        })

    return {
        'dashboard_id': dashboard_id,
        'dashboard_name': dashboard_name,
        'category_id': '',
        'category_name': '',
        'description': '',
        'prompt': '',
        'params': params,
        'field_list': field_list,
        'question_list': [],
    }


def get_sync_scene(scene_id, dashboard_id):
    scene = get_scene(scene_id)
    if not scene:
        raise UserError(message=f'场景不存在: {scene_id}')

    target_scene = get_init_scene(dashboard_id)

    def sync_params(target_params, existing_params):
        result = []
        is_changed = False
        existing_map = {param['id']: param for param in existing_params}
        target_ids = {param['id'] for param in target_params}

        for existing_param in existing_params:
            if existing_param['id'] not in target_ids:
                is_changed = True
                break

        for param in target_params:
            if param['id'] in existing_map:
                result.append(existing_map[param['id']])
            else:
                result.append(param)
                is_changed = True
        return result, is_changed

    def sync_field_list(target_fields, existing_fields):
        result = []
        is_changed = False
        existing_map = {field['id']: field for field in existing_fields}
        target_ids = {field['id'] for field in target_fields}

        for existing_field in existing_fields:
            if existing_field['id'] not in target_ids:
                is_changed = True
                break

        for field in target_fields:
            if field['id'] in existing_map:
                existing_field = existing_map[field['id']]
                synced_field = {
                    'id': field['id'],
                    'name': field['name']
                }

                if existing_field.get('name') != field.get('name'):
                    is_changed = True

                synced_fields, fields_changed = sync_field_list(field.get('fields', []),
                                                                existing_field.get('fields', []))
                if synced_fields:
                    synced_field['fields'] = synced_fields
                    is_changed = is_changed or fields_changed

                synced_dimensions, dimensions_changed = sync_field_list(field.get('dimensions', []),
                                                                        existing_field.get('dimensions', []))
                if synced_dimensions:
                    synced_field['dimensions'] = synced_dimensions
                    if dimensions_changed or existing_field.get('dimensions') != field.get('dimensions'):
                        is_changed = True

                if existing_field.get('description'):
                    synced_field['description'] = existing_field['description']

                result.append(synced_field)
            else:
                new_field = {
                    'id': field['id'],
                    'name': field['name']
                }

                if field.get('fields'):
                    new_field['fields'], _ = sync_field_list(field['fields'], [])

                if field.get('dimensions'):
                    new_field['dimensions'], _ = sync_field_list(field['dimensions'], [])

                if field.get('description'):
                    new_field['description'] = field['description']

                result.append(new_field)
                is_changed = True
        return result, is_changed

    new_params, params_changed = sync_params(target_scene['params'], scene['params'])
    new_field_list, field_list_changed = sync_field_list(target_scene['field_list'], scene['field_list'])

    return {
        'id': scene_id,
        'dashboard_id': dashboard_id,
        'dashboard_name': target_scene['dashboard_name'],
        'category_id': scene['category_id'],
        'category_name': scene['category_name'],
        'description': scene['description'],
        'prompt': scene['prompt'],
        'params': new_params,
        'field_list': new_field_list,
        'question_list': scene['question_list'],
        'is_changed': params_changed or field_list_changed
    }


def add_scene(params):
    if not params:
        raise UserError(message='参数不能为空')

    scene_data = dict()

    def add_field(field, json_encode=False):
        if field not in params or not params[field]:
            raise UserError(message=f'缺少必要参数: {field}')
        else:
            value = params[field]
            scene_data[field] = json.dumps(value) if json_encode else value

    add_field('dashboard_id')
    add_field('category_id')
    add_field('description')
    add_field('prompt')
    add_field('params', json_encode=True)
    add_field('field_list', json_encode=True)

    scene_data['id'] = seq_id()
    scene_data['question_list'] = json.dumps(params.get('question_list', []))
    scene_data['created_on'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    scene_data['created_by'] = getattr(g, 'account')

    with get_db() as db:
        try:
            db.begin_transaction()
            db.insert('dap_ai_scene', scene_data)
            sync_to_ai_platform(scene_data['id'], scene_data)
            db.commit()
        except Exception as e:
            db.rollback()
            raise UserError(message=f"新增场景失败: {str(e)}")

    return scene_data['id']


def update_scene(params):
    scene_id = params.get('id', '')
    if not scene_id:
        raise UserError(message='场景ID不能为空')

    scene = repository.get_data(
        table_name='dap_ai_scene',
        conditions={'id': scene_id, 'is_deleted': 0}
    )
    if not scene:
        raise UserError(message=f'场景不存在: {scene_id}')

    scene_data = dict()

    def add_field(field, json_encode=False):
        if field in params:
            value = params[field]
            scene_data[field] = json.dumps(value) if json_encode else value

    add_field('dashboard_id')
    add_field('category_id')
    add_field('description')
    add_field('prompt')
    add_field('params', json_encode=True)
    add_field('field_list', json_encode=True)
    add_field('question_list', json_encode=True)

    scene_data['modified_on'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    scene_data['modified_by'] = getattr(g, 'account')

    # 确保scene_data包含category_id，如果没有则使用已查询的scene数据
    if 'category_id' not in scene_data:
        if scene and scene.get('category_id'):
            scene_data['category_id'] = scene['category_id']

    with get_db() as db:
        try:
            db.begin_transaction()
            db.update(
                'dap_ai_scene',
                scene_data,
                {'id': scene_id, 'is_deleted': 0}
            )
            sync_to_ai_platform(scene_id, scene_data)
            db.commit()
        except Exception as e:
            db.rollback()
            raise UserError(message=f"更新场景失败: {str(e)}")

    return scene_id


def delete_scene(params):
    scene_id = params.get('id', '')
    if not scene_id:
        raise UserError(message='场景ID不能为空')

    scene = repository.get_data(
        table_name='dap_ai_scene',
        conditions={'id': scene_id, 'is_deleted': 0}
    )
    if not scene:
        raise UserError(message=f'场景不存在: {scene_id}')

    with get_db() as db:
        try:
            db.begin_transaction()
            db.update(
                'dap_ai_scene',
                {'is_deleted': 1},
                {'id': scene_id, 'is_deleted': 0}
            )
            sync_to_ai_platform(scene_id, scene, True)
            db.commit()
        except Exception as e:
            db.rollback()
            raise UserError(message=f"删除场景失败: {str(e)}")


def create_knowledge_base(category_data):
    """
    创建知识库
    :param category_data: 分类数据，包含id, name, description等字段
    """
    host = AppHosts.get(SkylineApps.APAAS, False).rstrip('/')
    url = f'{host}/pub/42000401/gpt/knowledge/create'

    try:
        headers = {
            'Content-Type': 'application/json',
            'my-api-authorization': auth_util.gen_auth_token(),
            'tenantCode': getattr(g, 'code'),
        }

        # 生成基于分类ID的knowledgeCode
        category_id = category_data.get('id', '')
        if category_id:
            # 去掉category_id中的中划线
            category_id_clean = category_id.replace('-', '')
            knowledge_code = f'tj_dap_kn_tj_dap_chatbi_{category_id_clean}'
        else:
            # 如果没有category_id，使用默认值
            knowledge_code = 'tj_dap_kn_system_chatbi'

        params = {
            'spaceCode': 'tj_dap',  # 写死的工作空间代码
            'code': knowledge_code,
            'name': category_data.get('name', ''),
            'description': category_data.get('description', ''),
            'typeEnum': 3,  # 写死为3
            'searchServicesEnum': 0,  # 写死为0
            'searchServicesURL': '',  # 空字符串
            'searchServicesParams': ''  # 空字符串
        }

        print(f'创建知识库 url={url}\nheaders={headers}\nparams={params}')

        response = requests.post(url=url, headers=headers, json=params)
        response.raise_for_status()
        try:
            result = response.json()
            if not result.get('success'):
                raise UserError(message=result.get("message", "未知错误"))
            print(f'知识库创建成功: {result}')
        except:
            raise UserError(message=response.text)
    except Exception as e:
        raise UserError(message=f'call `{url}` failed: {str(e)}')


def sync_to_ai_platform(scene_id, scene, isRemove=False):
    scene['id'] = scene_id

    host = AppHosts.get(SkylineApps.APAAS, False).rstrip('/')
    if isRemove:
        url = f'{host}/pub/42000401/gpt/knowledge/removeDocuments'
    else:
        url = f'{host}/pub/42000401/gpt/knowledge/saveSections'

    try:
        headers = {
            'Content-Type': 'application/json',
            'my-api-authorization': auth_util.gen_auth_token(),
            'tenantCode': getattr(g, 'code'),
        }

        # 生成基于分类ID的knowledgeCode
        category_id = scene.get('category_id', '')
        if category_id:
            # 去掉category_id中的中划线
            category_id_clean = category_id.replace('-', '')
            knowledge_code = f'tj_dap_kn_tj_dap_chatbi_{category_id_clean}'
        else:
            # 如果没有category_id，使用默认值
            knowledge_code = 'tj_dap_kn_system_chatbi'

        name = metadata_service.get_dashboard_name(scene['dashboard_id'])
        if isRemove:
            params = {
                'knowledgeCode': knowledge_code,
                'documentNames': [name]
            }
        else:
            params = {
                'sections': [
                    {
                        'title': name,
                        'content': scene['description'],
                        'metadata': json.dumps(scene),
                        'thirdId': scene_id,
                    }
                ],
                'knowledgeCode': knowledge_code,
                'knowledgeType': 3,
                'documentName': name
            }

        print(f'url={url}\nheaders={headers}\nscene={scene}')

        response = requests.post(url=url, headers=headers, json=params)
        response.raise_for_status()
        try:
            result = response.json()
            if not result.get('success'):
                raise UserError(message=result.get("message", "未知错误"))
        except:
            raise UserError(message=response.text)
    except Exception as e:
        raise UserError(message=f'call `{url}` failed: {str(e)}')


# 获取业务域-主题树
def get_subject_tree():
    data = transform_subject_tree_data(get_subject_list())
    return data


def get_scene_list(subject_id):
    scene_list = get_scene_list_by_subject_id(subject_id)
    data = group_scenes_by_category(scene_list) if scene_list else []
    return data


def add_scene_category(params):
    name = params.get("name")
    is_existed = scene_category_is_existed(name)
    if is_existed:
        raise UserError(message=f"分类已存在：{name}")

    # 创建分类
    result = add_scene_category_by_data(params)

    # 创建知识库
    try:
        create_knowledge_base(result)
    except Exception as e:
        # 如果知识库创建失败，记录错误但不影响分类创建
        print(f"创建知识库失败: {str(e)}")

    return result


def update_scene_category(params):
    id = params.get("id")
    name = params.get("name", '').strip()
    description = params.get("description", '').strip()
    subject_id = params.get("subject_id")
    # 所有更新字段均为空时，报错
    if all(not field for field in [name, description, subject_id]):
        raise UserError(message="请传入有效参数")
    return update_scene_category_data(id, name, description, subject_id)


def delete_scene_category(id):
    return delete_scene_category_by_id(id)


def get_dashboard_dataset_info(dashboard_id):
    """
    获取报表关联的数据集描述和字段信息
    :param dashboard_id: 报表ID
    :return: 数据集和字段信息
    """
    # 1. 获取报表信息
    dashboard_info = repository.get_data(
        table_name='dap_bi_dashboard_released_snapshot_dashboard',
        conditions={'id': dashboard_id},
        fields=['name', 'global_params'],
        multi_row=False
    )
    if not dashboard_info:
        raise UserError(message=f'报表不存在: {dashboard_id}')

    # 2. 获取报表下的图表信息
    field_group_keys = [
        'dims',
        'nums',
        'comparisons',
        'filters',
        'zaxis',
        'chart_params',
        'desires',
        'marklines',
        'field_sorts'
    ]

    dashboard_chart_infos = repository.get_data(
        table_name='dap_bi_dashboard_released_snapshot_chart',
        conditions={'dashboard_id': dashboard_id},
        fields=['id', 'name', 'chart_type', 'chart_code'] + field_group_keys,
        multi_row=True
    )
    if not dashboard_chart_infos:
        return {
            'dashboard_id': dashboard_id,
            'dashboard_name': dashboard_info.get('name', ''),
            'datasets': []
        }

    # 3. 收集所有字段ID
    all_field_ids = []
    for chart_info in dashboard_chart_infos:
        for key in field_group_keys:
            try:
                fields = json.loads(chart_info.get(key, '[]'))
                for field in fields:
                    field_id = field.get('dataset_field_id') or field.get('dim') or field.get('num') or field.get('id')
                    if field_id:
                        all_field_ids.append(field_id)
            except Exception:
                pass

    if not all_field_ids:
        return {
            'dashboard_id': dashboard_id,
            'dashboard_name': dashboard_info.get('name', ''),
            'datasets': []
        }

    # 4. 获取图表信息和字段关联（包含数据集信息和SQL）
    charts_info = _get_charts_info_with_datasets_and_sql(dashboard_chart_infos, field_group_keys)

    # 5. 获取全局参数配置及其与数据集的关联关系
    global_params_info = _get_dashboard_global_params_info(dashboard_id, dashboard_info)

    return {
        'dashboard_id': dashboard_id,
        'dashboard_name': dashboard_info.get('name', ''),
        'global_params': global_params_info,
        'charts': charts_info
    }


def _get_datasets_info_by_field_ids(field_ids):
    """
    通过字段ID列表获取数据集信息
    :param field_ids: 字段ID列表
    :return: 数据集信息列表
    """
    if not field_ids:
        return []

    # 查询字段信息，包含数据集ID
    sql = """
        SELECT
            df.id as field_id,
            df.dataset_id,
            df.alias_name,
            df.col_name,
            df.origin_col_name,
            df.note,
            df.business_note,
            df.data_type,
            df.field_group,
            d.name as dataset_name,
            d.description as dataset_description,
            d.type as dataset_type
        FROM dap_bi_dataset_field df
        LEFT JOIN dap_bi_dataset d ON df.dataset_id = d.id
        WHERE df.id IN %(field_ids)s
        ORDER BY d.name, df.alias_name
    """

    with get_db() as db:
        field_dataset_data = db.query(sql, {'field_ids': field_ids})

    if not field_dataset_data:
        return []

    # 按数据集分组整理数据
    datasets_dict = {}
    for row in field_dataset_data:
        dataset_id = row['dataset_id']
        if dataset_id not in datasets_dict:
            datasets_dict[dataset_id] = {
                'dataset_id': dataset_id,
                'dataset_name': row['dataset_name'] or '',
                'dataset_description': row['dataset_description'] or '',
                'dataset_type': row['dataset_type'] or '',
                'fields': []
            }

        # 字段描述优先级：note > business_note > alias_name
        field_description = row['note'] or row['business_note'] or row['alias_name'] or ''

        datasets_dict[dataset_id]['fields'].append({
            'field_id': row['field_id'],
            'field_name': row['alias_name'] or row['col_name'] or row['origin_col_name'] or '',
            'field_description': field_description,
            'col_name': row['col_name'] or '',
            'origin_col_name': row['origin_col_name'] or '',
            'data_type': row['data_type'] or '',
            'field_group': row['field_group'] or ''
        })

    return list(datasets_dict.values())


def _get_chart_type_display_name(chart_type, chart_code):
    """
    获取图表类型的显示名称
    :param chart_type: 图表类型代码
    :param chart_code: 图表代码
    :return: 图表类型显示名称
    """
    # 基于chart_code的映射（更准确）
    chart_code_mapping = {
        # 基础图表
        'pie': '饼图',
        'line': '折线图',
        'cluster_column': '柱状图',
        'area': '面积图',
        'scatter': '散点图',
        'radar': '雷达图',
        'table': '表格',
        'treegrid': '树形表格',
        'numerical_value': '指标卡',
        'double_axis': '双轴图',
        'circle_pie': '环形饼图',
        'circle_rose_pie': '玫瑰图',
        'simple_tab': '简单标签',

        # 地图类
        'area_map': '区域地图',
        'label_map': '标签地图',
        'scatter_map': '散点地图',
        'heat_map': '热力地图',

        # 特殊图表
        'funnel': '漏斗图',
        'waterfall': '瀑布图',
        'gauge': '仪表盘',
        'sankey': '桑基图',
        'treemap': '矩形树图',
        'sunburst': '旭日图',
        'liquidfill': '水球图',
        'box': '箱线图',
        'candlestick': '蜡烛图',
        'parallel': '平行坐标图',
        'graph': '关系图',

        # 表格类
        'excel_table': 'Excel表格',
        'pagination_table': '分页表格',
        'pagination_table_yk': '分页表格(预警)',
        'statistic_table': '统计表格',

        # 筛选控件
        'select_filter': '下拉筛选',
        'checkbox_filter': '复选框筛选',
        'time_interval_filter': '时间区间筛选',
        'number_filter': '数值筛选',
        'label_filter': '标签筛选',
        'timeline': '时间轴',
        'tablist': '标签列表',

        # 其他组件
        'simple_text': '文本',
        'simple_image': '图片',
        'comparison_line': '对比折线图',
        'stack_bar': '堆叠柱状图',
    }

    # 基于chart_type的映射（备用）
    chart_type_mapping = {
        'line': '折线图',
        'bar': '柱状图',
        'pie': '饼图',
        'area': '面积图',
        'scatter': '散点图',
        'radar': '雷达图',
        'funnel': '漏斗图',
        'gauge': '仪表盘',
        'table': '表格',
        'card': '指标卡',
        'map': '地图',
        'heatmap': '热力图',
        'chart': '图表'
    }

    # 优先使用chart_code映射，如果没有则使用chart_type映射
    if chart_code and chart_code in chart_code_mapping:
        return chart_code_mapping[chart_code]
    elif chart_type and chart_type in chart_type_mapping:
        return chart_type_mapping[chart_type]
    else:
        return chart_code or chart_type or '未知类型'


def _get_charts_info_with_datasets_and_sql(dashboard_chart_infos, field_group_keys):
    """
    获取图表信息，包含数据集信息和SQL
    :param dashboard_chart_infos: 图表信息列表
    :param field_group_keys: 字段组键列表
    :return: 图表信息列表
    """
    if not dashboard_chart_infos:
        return []

    charts_info = []

    for chart_info in dashboard_chart_infos:
        chart_id = chart_info['id']
        chart_name = chart_info['name']
        chart_type_raw = chart_info.get('chart_type', '')
        chart_code_raw = chart_info.get('chart_code', '')
        chart_type = _get_chart_type_display_name(chart_type_raw, chart_code_raw)

        # 收集该图表的所有字段ID
        chart_field_ids = []
        chart_fields_by_group = {}

        for key in field_group_keys:
            try:
                fields = json.loads(chart_info.get(key, '[]'))
                group_fields = []

                for field in fields:
                    field_id = field.get('dataset_field_id') or field.get('dim') or field.get('num') or field.get('id')
                    if field_id:
                        chart_field_ids.append(field_id)
                        group_fields.append({
                            'field_id': field_id,
                            'alias_name': field.get('alias_name', ''),
                            'field_group': field.get('field_group', ''),
                            'data_type': field.get('data_type', '')
                        })

                if group_fields:
                    chart_fields_by_group[key] = group_fields

            except Exception:
                pass

        # 获取该图表关联的数据集信息
        chart_datasets = []
        if chart_field_ids:
            chart_datasets = _get_chart_datasets_info(chart_field_ids)

        # 获取该图表的SQL信息
        chart_sql_info = _get_chart_sql_info(chart_id)

        charts_info.append({
            'chart_id': chart_id,
            'chart_name': chart_name,
            'chart_type': chart_type,
            'field_count': len(chart_field_ids),
            'fields_by_group': chart_fields_by_group,
            'datasets': chart_datasets,
            'sql_info': chart_sql_info
        })

    return charts_info


def _get_chart_datasets_info(field_ids):
    """
    获取图表关联的数据集信息
    :param field_ids: 字段ID列表
    :return: 数据集信息列表
    """
    if not field_ids:
        return []

    sql = """
        SELECT
            df.id as field_id,
            df.dataset_id,
            df.alias_name,
            df.col_name,
            df.origin_col_name,
            df.note,
            df.business_note,
            df.data_type,
            df.field_group,
            d.name as dataset_name,
            d.description as dataset_description,
            d.type as dataset_type,
            d.content as dataset_content
        FROM dap_bi_dataset_field df
        LEFT JOIN dap_bi_dataset d ON df.dataset_id = d.id
        WHERE df.id IN %(field_ids)s
        ORDER BY d.name, df.alias_name
    """

    with get_db() as db:
        field_dataset_data = db.query(sql, {'field_ids': field_ids})

    if not field_dataset_data:
        return []

    # 按数据集分组整理数据
    datasets_dict = {}
    for row in field_dataset_data:
        dataset_id = row['dataset_id']
        if dataset_id not in datasets_dict:
            datasets_dict[dataset_id] = {
                'dataset_id': dataset_id,
                'dataset_name': row['dataset_name'] or '',
                'dataset_description': row['dataset_description'] or '',
                'dataset_type': row['dataset_type'] or '',
                'dataset_content': row['dataset_content'] or '',
                'fields': []
            }

        # 字段描述优先级：note > business_note > alias_name
        field_description = row['note'] or row['business_note'] or row['alias_name'] or ''

        datasets_dict[dataset_id]['fields'].append({
            'field_id': row['field_id'],
            'field_name': row['alias_name'] or row['col_name'] or row['origin_col_name'] or '',
            'field_description': field_description,
            'col_name': row['col_name'] or '',
            'origin_col_name': row['origin_col_name'] or '',
            'data_type': row['data_type'] or '',
            'field_group': row['field_group'] or ''
        })

    return list(datasets_dict.values())


def _get_chart_sql_info(chart_id):
    """
    获取图表生成的SQL信息（不实际执行）
    :param chart_id: 图表ID
    :return: SQL信息
    """
    try:
        # 获取图表快照信息
        chart_snapshot = repository.get_data(
            table_name='dap_bi_dashboard_released_snapshot_chart',
            conditions={'id': chart_id},
            fields=['source', 'dims', 'nums', 'comparisons', 'filters', 'zaxis', 'chart_params', 'desires', 'marklines', 'field_sorts'],
            multi_row=False
        )

        if not chart_snapshot:
            return {
                'dataset_id': '',
                'dataset_name': '',
                'generated_sql': ''
            }

        dataset_id = chart_snapshot.get('source', '')
        if not dataset_id:
            return {
                'dataset_id': '',
                'dataset_name': '',
                'generated_sql': ''
            }

        # 获取数据集信息
        dataset_info = repository.get_data(
            table_name='dap_bi_dataset',
            conditions={'id': dataset_id},
            fields=['id', 'name'],
            multi_row=False
        )

        if not dataset_info:
            return {
                'dataset_id': dataset_id,
                'dataset_name': '',
                'generated_sql': ''
            }

        # 从快照数据中解析字段信息
        field_group_keys = ['dims', 'nums', 'comparisons', 'filters', 'zaxis', 'chart_params', 'desires', 'marklines', 'field_sorts']
        model = ChartDataModel(id=chart_id, dataset_id=dataset_id)
        model.bootstrap_flag = True
        model.dataset = dataset_info

        for key in field_group_keys:
            try:
                field_data = json.loads(chart_snapshot.get(key, '[]'))
                setattr(model, key, field_data)
            except:
                setattr(model, key, [])

        # 适配模型并创建图表查询对象
        chart_model_adapter.adapt_get_data_model(model)
        chart_class = ChartFactory.create_chart(model.id, model)

        if not chart_class:
            return {
                'dataset_id': dataset_id,
                'dataset_name': dataset_info.get('name', ''),
                'generated_sql': ''
            }

        # 获取查询结构并生成真实SQL
        query_data, json_struct = chart_class.get_query_underlying_data()
        from dataset.external_query_service import get_dataset_query_sql

        query_params = {
            "user_id": "system",
            "dataset_id": model.dataset_id,
            "chart_id": model.id,
            "query_structure_json": json_struct,
            "is_order_master_id": False,
            "external_subject_ids": getattr(model, 'external_subject_ids', []) or []
        }

        generated_sql = get_dataset_query_sql(**query_params)

        return {
            'dataset_id': dataset_id,
            'dataset_name': dataset_info.get('name', ''),
            'generated_sql': generated_sql
        }

    except Exception as e:
        return {
            'dataset_id': '',
            'dataset_name': '',
            'generated_sql': ''
        }


def _get_dashboard_global_params_info(dashboard_id, dashboard_info):
    """
    获取报表全局参数配置及其与数据集的关联关系
    :param dashboard_id: 报表ID
    :param dashboard_info: 报表基本信息
    :return: 全局参数信息
    """
    try:
        # 1. 解析报表的全局参数配置
        global_params_raw = dashboard_info.get('global_params', '[]')
        global_params = []

        try:
            global_params = json.loads(global_params_raw) if global_params_raw else []
        except Exception as e:
            return {
                'has_global_params': False,
                'params_count': 0,
                'params': [],
                'dataset_relations': [],
                'error': f'解析全局参数失败: {str(e)}'
            }

        if not global_params:
            return {
                'has_global_params': False,
                'params_count': 0,
                'params': [],
                'dataset_relations': [],
                'error': ''
            }

        # 2. 获取全局参数的详细配置
        params_info = []
        for param in global_params:
            param_info = {
                'id': param.get('id', ''),
                'name': param.get('name', ''),
                'alias_name': param.get('alias_name', ''),
                'type': param.get('type', ''),
                'default_value': param.get('default_value', ''),
                'description': param.get('description', ''),
                'required': param.get('required', False),
                'options': param.get('options', [])
            }
            params_info.append(param_info)

        # 3. 获取全局参数与数据集的关联关系
        dataset_relations = _get_global_params_dataset_relations(dashboard_id)

        return {
            'has_global_params': True,
            'params_count': len(global_params),
            'params': params_info,
            'dataset_relations': dataset_relations,
            'error': ''
        }

    except Exception as e:
        return {
            'has_global_params': False,
            'params_count': 0,
            'params': [],
            'dataset_relations': [],
            'error': f'获取全局参数信息失败: {str(e)}'
        }


def _get_global_params_dataset_relations(dashboard_id):
    """
    获取全局参数与数据集的关联关系
    :param dashboard_id: 报表ID
    :return: 关联关系列表
    """
    try:
        # 1. 获取全局参数信息
        global_params_sql = """
            SELECT
                id as global_param_id,
                name as global_param_name,
                alias_name,
                dashboard_id
            FROM dap_bi_dashboard_jump_global_params
            WHERE dashboard_id = %(dashboard_id)s
        """

        with get_db() as db:
            global_params_data = db.query(global_params_sql, {'dashboard_id': dashboard_id})

        if not global_params_data:
            return []

        relations_list = []

        for global_param in global_params_data:
            global_param_id = global_param['global_param_id']
            global_param_name = global_param['global_param_name']

            # 2. 获取该全局参数与数据集字段的关联关系
            relations_sql = """
                SELECT DISTINCT
                    gpr.dataset_id,
                    d.name as dataset_name,
                    d.description as dataset_description,
                    gpr.dataset_field_id,
                    df.alias_name as field_name,
                    df.col_name,
                    df.data_type as field_type
                FROM dap_bi_dashboard_global_params_2_dataset_field_relation gpr
                LEFT JOIN dap_bi_dataset d ON gpr.dataset_id = d.id
                LEFT JOIN dap_bi_dataset_field df ON gpr.dataset_field_id = df.id
                WHERE gpr.global_params_id = %(global_param_id)s
                  AND gpr.dashboard_id = %(dashboard_id)s
                ORDER BY d.name
            """

            relations_data = db.query(relations_sql, {
                'global_param_id': global_param_id,
                'dashboard_id': dashboard_id
            })

            # 按数据集分组整理关联关系
            datasets_dict = {}
            for row in relations_data:
                dataset_id = row['dataset_id']
                if dataset_id and dataset_id not in datasets_dict:
                    datasets_dict[dataset_id] = {
                        'dataset_id': dataset_id,
                        'dataset_name': row['dataset_name'] or '',
                        'dataset_description': row['dataset_description'] or '',
                        'related_fields': []
                    }

                # 添加关联字段信息
                if row['dataset_field_id'] and dataset_id:
                    field_info = {
                        'field_id': row['dataset_field_id'],
                        'field_name': row['field_name'] or '',
                        'col_name': row['col_name'] or '',
                        'field_type': row['field_type'] or ''
                    }
                    datasets_dict[dataset_id]['related_fields'].append(field_info)

            param_relation = {
                'global_param_id': global_param_id,
                'global_param_name': global_param_name,
                'alias_name': global_param.get('alias_name', ''),
                'datasets': list(datasets_dict.values())
            }
            relations_list.append(param_relation)

        return relations_list

    except Exception as e:
        return []
