#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    API Route
    <NAME_EMAIL> on 2021/3/19.
"""
import traceback
from loguru import logger
from dmplib.hug import APIWrapper
from urllib.parse import urlparse
from components.global_utils import kwargs_aes_decode
from keywords.models import KeywordModel, KeywordQueryModel
from keywords.services import keyword_service, keyword_result_service
from rbac.validator import PermissionValidator


api = APIWrapper(__name__)


@api.admin_route.post('/save', validate=PermissionValidator('keyword.edit'))
@kwargs_aes_decode()
def keyword_save(request, **kwargs):
    """
    /*
     @apiVersion 1.0.0
     @api {post} /api/keywords/save 新增||修改关键字
     @apiGroup  keywords
     @apiBodyParam {
         "id": "关键字ID",
         "datasource_id":"数据源ID",
         "keyword_name": "关键字名称",
         "keyword_type": "关键字类型：0:程序关键字;1:SQL关键字",
         "data_type": "关键字的值类型：文本，日期，数值",
         "is_system": "是否系统级关键字：0：自定义关键字；1：系统关键字",
         "sql_text": "关键字SQL定义"，
         "remark": "关键字说明"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": {
            "id": "关键字ID",
             "datasource_id":"数据源ID",
             "keyword_name": "关键字名称",
             "keyword_type": "关键字类型：0:程序关键字;1:SQL关键字",
             "data_type": "关键字的值类型：文本，日期，数值",
             "is_system": "是否系统级关键字：0：自定义关键字；1：系统关键字",
             "sql_text": "关键字SQL定义"，
             "remark": "关键字说明"
         }
    }
    """
    keyword_model = KeywordModel(**kwargs)
    try:
        if keyword_model.is_system == 1:
            return False, '系统关键字不支持修改', {}
        status, data = keyword_service.keyword_save(keyword_model)
        return status, '保存成功', data
    except Exception as e:
        logger.error(f'系统关键字保存失败： {traceback.format_exc()}')
        return False, e.message if hasattr(e, 'message') else '', {}


@api.admin_route.post('/delete')
def keyword_del(**kwargs):
    """
    /*
     @apiVersion 1.0.0
     @api {post} /api/keywords/delete 删除关键字
     @apiGroup  keywords
     @apiBodyParam {
         "id": "关键字ID",
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": true
    }
    """
    if not kwargs.get('id'):
        return False, '关键字ID不能为空', {}
    keyword_info = keyword_service.get_keyword_by_id(kwargs.get('id'))
    if not keyword_info:
        return False, '没有查询到对应关键字，请刷新页面后重试', {}
    model = KeywordModel(**keyword_info)
    try:
        keyword_service.keyword_delete(model)
        return True, '删除成功', {}
    except Exception as e:
        return False, e.message if hasattr(e, 'message') else '', {}


@api.admin_route.post('/list')
def keyword_list(**kwargs):
    """
    /*
     @apiVersion 1.0.0
     @api {get} /api/keywords/list 关键字列表
     @apiGroup  keywords
     @apiBodyParam {
         "datasource_id": "数据源ID",
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": [
             {
                "id": "关键字ID",
                 "datasource_id":"数据源ID",
                 "keyword_name": "关键字名称",
                 "keyword_type": "关键字类型：0:程序关键字;1:SQL关键字",
                 "data_type": "关键字的值类型：文本，日期，数值",
                 "is_system": ""
             },
             {
                "id": "关键字ID",
                 "datasource_id":"数据源ID",
                 "keyword_name": "关键字名称",
                 "keyword_type": "关键字类型：0:程序关键字;1:SQL关键字",
                 "data_type": "关键字的值类型：文本，日期，数值",
                 "is_system": "是否系统级关键字：0：自定义关键字；1：系统关键字",
                 "sql_text": "关键字SQL定义"，
                 "remark": "关键字说明"
             },
         ]
    }
    """
    model = KeywordQueryModel(**kwargs)
    model.datasource_id = str(model.datasource_id).split(',') if model.datasource_id else [""]
    data = keyword_service.keyword_by_datasource_id(model)
    return True, None, data


@api.admin_route.post('/list_with_permission', validate=PermissionValidator('keyword.view'))
def keyword_list_with_permission(**kwargs):
    """
    /*
     @apiVersion 1.0.0
     @api {get} /api/keywords/list 关键字列表
     @apiGroup  keywords
     @apiBodyParam {
         "datasource_id": "数据源ID",
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": [
             {
                "id": "关键字ID",
                 "datasource_id":"数据源ID",
                 "keyword_name": "关键字名称",
                 "keyword_type": "关键字类型：0:程序关键字;1:SQL关键字",
                 "data_type": "关键字的值类型：文本，日期，数值",
                 "is_system": ""
             },
             {
                "id": "关键字ID",
                 "datasource_id":"数据源ID",
                 "keyword_name": "关键字名称",
                 "keyword_type": "关键字类型：0:程序关键字;1:SQL关键字",
                 "data_type": "关键字的值类型：文本，日期，数值",
                 "is_system": "是否系统级关键字：0：自定义关键字；1：系统关键字",
                 "sql_text": "关键字SQL定义"，
                 "remark": "关键字说明"
             },
         ]
    }
    """
    model = KeywordQueryModel(**kwargs)
    model.datasource_id = str(model.datasource_id).split(',') if model.datasource_id else [""]
    data = keyword_service.keyword_by_datasource_id(model)
    return True, None, data


@api.admin_route.get('/get_result')
def get_keyword_result_by_id(**kwargs):
    keyword_id = kwargs.get('id')
    try:
        data = keyword_result_service.get_keyword_result_by_id(keyword_id)
        return True, None, data
    except Exception as e:
        return False, e.message if hasattr(e, 'message') else '', {}
