#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/18 10:40
# <AUTHOR> wangf10
# @File     : models.py
from base.models import (
    BaseModel,
    QueryBaseModel
)
from base.enums import (
    KeywordType,
    KeywordDataType,
)
from dmplib.utils.errors import UserError
from keywords.repositories import keyword_repositories


class KeywordModel(BaseModel):
    __table__ = 'dap_bi_keyword'
    __slots__ = ['id', 'datasource_id', 'keyword_name', 'keyword_type', 'sql_text', 'data_type', 'is_system', 'remark', 'design_content']

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.datasource_id = None
        self.keyword_name = None
        self.keyword_type = None
        self.sql_text = None
        self.data_type = None
        self.is_system = None
        self.remark = None
        self.design_content = None
        super(KeywordModel, self).__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36, 'required': False}))
        rules.append(('datasource_id', 'string', {'max': 36, 'required': False}))
        rules.append(('keyword_name', 'string', {'max': 64, 'required': True}))
        rules.append(
            ("keyword_type", "in_range", {"range": [e.value for e in KeywordType.__members__.values()]})
        )
        rules.append(
            ("data_type", "in_range", {"range": [e.value for e in KeywordDataType.__members__.values()]})
        )
        same_name_keyword = keyword_repositories.get_keyword_by_name(self.keyword_name, self.datasource_id)
        if same_name_keyword:
            word = "同一个数据源下关键字名称不能重复!"
            if not self.id or same_name_keyword.get("id") != self.id:
                raise UserError(message=word)
        return rules


class KeywordQueryModel(QueryBaseModel):
    __slots__ = ['datasource_id']

    def __init__(self, **kwargs):
        self.datasource_id = None
        super(KeywordQueryModel, self).__init__(**kwargs)

    def rules(self):
        rules = super(KeywordQueryModel, self).rules()
        rules.append(('keyword_name', 'string', {'max': 64}))
        return rules


class KeywordTemplateModel(BaseModel):
    __table__ = 'dap_bi_keyword_template'
    __slots__ = ['id', 'keyword_name', 'sql_text', 'data_type', 'db_type', 'remark']

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.keyword_name = None
        self.sql_text = None
        self.data_type = None
        self.db_type = None
        self.remark = None
        super(KeywordTemplateModel, self).__init__(**kwargs)


class KeywordDetailsModel(BaseModel):
    __table__ = 'dap_bi_keyword_details'
    __slots__ = ['keyword_id', 'var_id', 'dataset_id']

    def __init__(self, **kwargs) -> None:
        self.keyword_id = None
        self.var_id = None
        self.dataset_id = None
        super(KeywordDetailsModel, self).__init__(**kwargs)
