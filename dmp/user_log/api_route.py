#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Created by <PERSON><PERSON> on 2018/1/12

import hug
import json
from urllib.parse import unquote

from dmplib.hug import APIWrapper
from dmplib.hug import g
from rbac.validator import PermissionValidator
from dmplib.saas.project import set_correct_project_code
from user_log.models import ReportVisitLogModel, QueryVisitLogModel
from user_log.services import user_log_service, report_visit_log_service

api = APIWrapper(__name__)


class DataApiWrapper(APIWrapper):
    __slots__ = ["_route", "_data_route"]

    def __init__(self, name: str) -> None:
        super().__init__(name)
        self._route = hug.http(api=self.api)
        self._data_route = None

    @property
    def data_route(self):
        from dashboard_chart.services.authority_service import verify_data_handle
        if not self._data_route:
            # pylint: disable=E1120
            self._data_route = hug.http(api=self.api, requires=verify_data_handle(None))
        return self._data_route


dataApi = DataApiWrapper(__name__)


@api.admin_route.post('/view_log', validate=PermissionValidator('log.view'))
def view_log(**kwargs):
    return True, '操作成功', user_log_service.view_log(**kwargs)


@api.admin_route.get('/export_log', validate=PermissionValidator('log.view'))
def export_log(response):
    return True, '操作成功', user_log_service.export_log(response)


@dataApi.data_route.post('/report_visit_log')
def report_visit_log(request, **kwargs):
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    model = ReportVisitLogModel(**kwargs)
    model.account = model.account or request.cookies.get('account', '')
    return True, '', report_visit_log_service.write_visit_log(model)


@api.admin_route.post('/visit_log_list')
def visit_log_list(**kwargs):
    model = QueryVisitLogModel(**kwargs)
    return True, '', report_visit_log_service.get_visit_list(model)


@api.admin_route.get('/export_visit_log')
def export_visit_log(response, **kwargs):
    model = QueryVisitLogModel(**kwargs)
    return True, '', report_visit_log_service.export_visit_list(model, response)


@api.admin_route.post('/report_log_list')
def get_report_visit_list(**kwargs):
    model = QueryVisitLogModel(**kwargs)
    return True, '', report_visit_log_service.get_report_visit_list(model)


@api.admin_route.get('/export_report_log')
def export_visit_log(response, **kwargs):
    model = QueryVisitLogModel(**kwargs)
    return True, '', report_visit_log_service.export_report_visit(model, response)


@api.admin_route.post('/user_log_list')
def get_user_visit_list(**kwargs):
    model = QueryVisitLogModel(**kwargs)
    return True, '', report_visit_log_service.get_user_visit_list(model)


@api.admin_route.get('/export_user_log')
def export_visit_log(response, **kwargs):
    model = QueryVisitLogModel(**kwargs)
    return True, '', report_visit_log_service.export_user_visit(model, response)


@api.admin_route.post('/report_user_detail_log')
def report_detail_log(**kwargs):
    model = QueryVisitLogModel(**kwargs)
    return True, '', report_visit_log_service.get_report_user_detail_log(model)


@api.admin_route.get('/export_report_user_detail_log')
def export_visit_log(response, **kwargs):
    model = QueryVisitLogModel(**kwargs)
    return True, '', report_visit_log_service.export_report_user_detail_log(model, response)


@api.admin_route.get('/get_visit_time')
def get_visit_time():
    return True, '', report_visit_log_service.get_visit_time()
