import sys
from typing import Any, Callable, Dict, List

from mylog.exceptions import APIBusinessError, APIRequestError, ServerError

if sys.version_info < (3, 11):
    from typing_extensions import TypedDict
else:
    from typing import TypedDict

import requests


class Filter(TypedDict):
    expr: str
    val: Any


class QueryOneResponse(TypedDict):
    columns: List[str]
    result: Dict[str, Any]


class QueryListResponse(TypedDict):
    columns: List[str]
    list: List[Dict[str, Any]]


class APIClient:
    # 默认重试次数, 包含第一次请求
    DEFAULT_HTTP_REQUEST_RETRY_COUNT = 3
    # 默认客户端ID
    DEFAULT_JWT_SET_CLIENT_ID = "app"

    # 统一认证头
    AUTH_HEADER = "my-api-Authorization"
    # 明细查询
    API_QUERY_ONE = "/api/v1/log/query_one"
    # 列表查询
    API_QUERY_LIST = "/api/v1/log/query"
    # 查询数量
    API_QUERY_COUNT = "/api/v1/log/query_count"

    # 成功响应码
    API_RESPONSE_CODE_SUCCESS: int = 0
    # 内部错误响应码
    API_RESPONSE_CODE_INTERNAL_ERROR: int = 5000

    def __init__(self, get_url: Callable[[], str], get_token: Callable[[], str]):
        self.get_url = get_url
        self.token = get_token

    def get_api_uri(self, path: str) -> str:
        return self.get_url() + path

    def _request(self, api_path: str, data: dict) -> Any:
        uri = self.get_api_uri(api_path)
        try:
            response = requests.post(
                uri,
                headers={self.AUTH_HEADER: self.token()},
                json=data,
            )
        except requests.RequestException as e:
            raise ServerError(f"请求失败 {uri} 失败") from e

        if response.status_code < 200 or response.status_code >= 300:  # noqa: PLR2004
            raise APIRequestError(
                f"请求失败 {uri}, 响应码 {response.status_code}, 响应内容 {response.text}", response.status_code)

        try:
            data = response.json()
        except ValueError as e:
            raise ServerError(f"解析响应失败 {uri} {response.text}") from e

        try:
            if data["code"] != self.API_RESPONSE_CODE_SUCCESS:
                raise APIBusinessError(data["message"], data["code"])

            return data["data"]
        except (KeyError, IndexError, TypeError) as e:
            raise APIRequestError(f"解析响应失败 {uri} {response.text}", response.status_code) from e

    def query_one(self, product_code: str, log_name: str, cols: str, filters: List[Filter]) -> QueryOneResponse:
        """查询单个日志
        
        :param product_code: 产品编码
        :param log_name: 日志名
        :param cols: 查询的列: `col1, col2, col3`
        :param filters: 过滤条件 [{"expr": "id = ?", "val": 100}]

        :return: 查询结果

            {
                "columns": ["name", "age"],   // 字段列表
                "result": {                   // 数据
                    "name": "张三",
                    "age": 18
                }
            }

        :raise: ServerError
        """

        return self._request(
            self.API_QUERY_ONE,
            {
                "product_code": product_code,
                "log_name": log_name,
                "cols": cols,
                "filters": filters,
            })

    def query_list(  # noqa: PLR0913
            self, product_code: str, log_name: str, cols: str, filters: List[Filter], orders: str, limit: int,
            offset: int) -> QueryListResponse:
        """查询日志列表

        :param product_code: 产品编码
        :param log_name: 日志名
        :param cols: 查询的列: `col1, col2, col3`
        :param filters: 过滤条件 [{"expr": "id = ?", "val": 100}]
        :param orders: 排序条件 `id DESC`
        :param limit: 查询数量
        :param offset: 查询偏移

        :return: 查询结果
            {
                "columns": ["name", "age"],   // 字段列表
                "list": [{                    // 数据
                    "name": "张三",
                    "age": 18
                }, {
                    "name": "李四",
                    "age": 20
                }]
            }

        :raise: ServerError
        """
        return self._request(
            self.API_QUERY_LIST,
            {
                "product_code": product_code,
                "log_name": log_name,
                "cols": cols,
                "filters": filters,
                "orders": orders,
                "limit": limit,
                "offset": offset,
            })

    def query_count(self, product_code: str, log_name: str, filters: List[Filter]) -> int:
        """查询日志数量

        :param product_code: 产品编码
        :param log_name: 日志名
        :param filters: 过滤条件 [{"expr": "id = ?", "val": 100}]
        :return: 日志数量

        :raise: ServerError
        """

        return self._request(
            self.API_QUERY_COUNT,
            {
                "product_code": product_code,
                "log_name": log_name,
                "filters": filters,
            })
