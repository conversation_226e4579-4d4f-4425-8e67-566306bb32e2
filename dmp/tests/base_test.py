# -*- coding: utf-8 -*-
"""
Created on 2017年6月16日

@author: chenc04
"""
import unittest
import os
from contextlib import contextmanager
import random
import string

from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dmplib.hug.context import DBContext

# 有的模块在import的时候就会调用config，所以这里要在那些模块之前引用
os.environ['DMP_CFG_FILE_PATH'] = os.path.abspath(os.path.join(os.path.dirname(__file__), 'app.config'))

from dmplib import config


# 开发环境单测租户
# 云端环境：cloud cloud 1qaz@WSX
# 本地环境：local local 1qaz@WSX


class BaseTest(unittest.TestCase):
    def __init__(self, method_name='runTest', code='unittest', account='unittest'):
        super().__init__(method_name)
        tenant = config.get("External.tenant")
        if tenant:
            self.code = tenant
            self.account = config.get("External.account") or "skylineadmin"
        else:
            self.code = code
            self.account = account

    def setUp(self):
        g = _AppCtxGlobals()
        self.g = g
        g.code = self.code
        g.account = self.account
        g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
        g.group_ids = []
        _app_ctx_stack.push(g)
        db_ctx = DBContext()
        db_ctx.inject(g)

    def tearDown(self):

        db_ctx = DBContext.instance(self.g)
        db_ctx.close_all()


class Request:
    url = 'http://dmp.mypaas.com.cn'


@contextmanager
def patch_property(object: object, property: str, value):
    # value可以是某个值，甚至是一个函数
    hidden_value = ''.join(random.sample(string.printable, 20))
    old = hidden_value
    try:
        old = getattr(object, property)
        setattr(object, property, value)
        yield value
    finally:
        if old != hidden_value:
            setattr(object, property, old)
