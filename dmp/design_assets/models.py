#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/17.
"""

from base.models import BaseModel


class AssetConfigModel(BaseModel):
    def __init__(self, **kwargs) -> None:
        self.asset = ''
        self.name = ''
        self.dataset_zip = ''
        self.dataset_ids = ''
        self.asset_type = ''
        self.layout = ''
        super().__init__(**kwargs)
