# -*- coding: utf-8 -*-
# pylint: skip-file
"""
Created on 2019年4月26日
@author: <EMAIL>
"""
import os
import json
import unittest

os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)
os.environ['prometheus_multiproc_dir'] = '/tmp'
os.environ['DMP_CFG_FILE_PATH'] = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'app.config'
)

from tests.base import BaseTest
from imports.api_route import *


class TestDashboardImportsDetail(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='admin')

    def test_get_dashboard_list(self):
        query_kwargs = {'page': 1, 'page_size': 20}
        print(json.dumps(list_dashboard_imports(**query_kwargs), indent=4))

    def test_get_dashboard_import_detail(self):
        id = '39ee1f37-28ae-642d-9b99-bdd2abca2beb'
        print(json.dumps(dashboard_imports_services.get_dashboard_import_detail(id), indent=4))

    def test_dashboard_preview_data(self):
        oss_url = 'http://oss-cn-shenzhen.aliyuncs.com/upload-file/****************/版本导出_20190522103420.zip'
        result = dashboard_import_data_services.preview_data(oss_url)
        print(json.dumps(result, indent=4))

    def test_dashboard_import(self):
        oss_url = 'http://oss-cn-shenzhen.aliyuncs.com/upload-file/****************/版本导出_20190522103420.zip'
        select_data = {
            "dashboards": [
                {
                    "id": "39edd201-c840-b5c5-30e6-aa62691c97c2",
                    "name": "巡检",
                    "type": "FILE",
                    "parent_id": "39e43359-87a6-f4e5-2968-d8b10d72b917",
                    "level_code": "0643-0143-",
                    "created_on": "2019-05-16T16:49:24",
                    "check_status": False,
                }
            ],
            "datasets": [
                {
                    "id": "39edd1fe-1988-d2a2-feec-8b64d433e747",
                    "name": "测试版本mysql",
                    "type": "SQL",
                    "parent_id": "39e903ae-6336-940a-d638-06eaa3bb5da0",
                    "level_code": "1037-0112-",
                    "created_on": "2019-05-16T16:46:31",
                    "check_status": False,
                }
            ],
        }
        target_dataset_folder_id = '39ed0cbe-0b65-3621-0352-6c5bd81589d5'
        target_dashboard_folder_id = '39edf9c5-68c0-d617-e808-c37f096b60f6'
        kwargs = {
            'oss_url': oss_url,
            'pick_data': select_data,
            'target_dataset_folder_id': target_dataset_folder_id,
            'target_dashboard_folder_id': target_dashboard_folder_id,
        }
        dashboard_import(**kwargs)
        print('success!')

    def test_preview_data(self):
        oss_url = "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/tmp/123_20200219121903.zip"
        result = dashboard_import_data_services.preview_data(oss_url)
        print(result)

    def test_dashboard_import_data(self):
        kwargs = {
            "oss_url": "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/tmp/123_20200219121903.zip",
            "pick_data": {
                "applications": [
                    {
                        "id": "39e7293c-ac45-f084-9e5d-cd08ea51ca06",
                        "name": "门户-所有报告",
                        "created_on": "",
                        "check_status": True,
                    }
                ]
            },
        }
        result = dashboard_import_data_services.dashboard_import_data(**kwargs)
        print(result)


if __name__ == '__main__':
    # unittest.main()
    s = unittest.TestSuite()
    s.addTest(TestDashboardImportsDetail("test_dashboard_import_data"))
    runner = unittest.TextTestRunner()
    runner.run(s)
