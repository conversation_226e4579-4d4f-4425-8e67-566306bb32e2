import io
import json
import zipfile

import xmltodict

from dmplib.utils.errors import UserError

from imports.services import import_helper


def parse_zip_file_data(file_data):
    # 指定 base_info.json 文件路径
    base_info_file_path = 'base/info.json'

    export_data = None
    with zipfile.ZipFile(file_data, "r") as zip_file:
        # 判断 ZIP 文件中是否存在 base_info.json 文件
        if base_info_file_path in zip_file.namelist():
            # 如果存在 base_info.json 文件，读取该文件的内容
            with zip_file.open(base_info_file_path) as base_info_file:
                try:
                    export_data = json.loads(base_info_file.read().decode('utf-8'))
                except:
                    raise UserError(400, 'zip压缩文件内容格式有误')

                read_map(export_data, 'dashboards', zip_file)
                read_list(export_data, 'datasets', zip_file)
                read_map(export_data, 'large_screens', zip_file)
                read_map(export_data, 'applications', zip_file)
                read_map(export_data, 'data_reporting', zip_file)
                read_feeds(export_data, 'feeds', zip_file)
                read_active_reports(export_data, zip_file)
                read_other(export_data, 'ppt', zip_file)

        else:
            # 如果不存在 base_info.json 文件，读取根目录下的 .json 文件
            json_files = [name for name in zip_file.namelist() if name.endswith('.json')]
            for file_name in json_files:
                with zip_file.open(file_name) as file:
                    r_data = file.read()
                    if r_data:
                        try:
                            export_data = json.loads(r_data.decode('utf-8'))
                        except:
                            raise UserError(400, 'zip压缩文件内容格式有误')
                        break
    return export_data

def parse_zip_file(file_url):
    # 指定 base_info.json 文件路径
    base_info_file_path = 'base/info.json'

    bytes_data = import_helper.read_file_from_oss(file_url)
    data = io.BytesIO(bytes_data)
    export_data = None
    with zipfile.ZipFile(data, "r") as zip_file:
        # 判断 ZIP 文件中是否存在 base_info.json 文件
        if base_info_file_path in zip_file.namelist():
            # 如果存在 base_info.json 文件，读取该文件的内容
            with zip_file.open(base_info_file_path) as base_info_file:
                try:
                    export_data = json.loads(base_info_file.read().decode('utf-8'))
                except:
                    raise UserError(400, 'zip压缩文件内容格式有误')

                read_map(export_data, 'dashboards', zip_file)
                read_list(export_data, 'datasets', zip_file)
                read_map(export_data, 'large_screens', zip_file)
                read_map(export_data, 'applications', zip_file)
                read_map(export_data, 'data_reporting', zip_file)
                read_feeds(export_data, 'feeds', zip_file)
                read_active_reports(export_data, zip_file)
                read_other(export_data, 'ppt', zip_file)

        else:
            # 如果不存在 base_info.json 文件，读取根目录下的 .json 文件
            json_files = [name for name in zip_file.namelist() if name.endswith('.json')]
            for file_name in json_files:
                with zip_file.open(file_name) as file:
                    r_data = file.read()
                    if r_data:
                        try:
                            export_data = json.loads(r_data.decode('utf-8'))
                        except:
                            raise UserError(400, 'zip压缩文件内容格式有误')
                        break
    return export_data


def read_map(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith(directory + '/') and name.endswith('.json')]
    export_data[directory] = {}
    # 遍历二级目录下的文件，并读取其内容
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            _map = json.loads(content.decode('utf-8'))
            for key,value in _map.items():
                export_data.get(directory)[key] = value


def read_list(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith(directory + '/') and name.endswith('.json')]
    export_data[directory] = []
    # 遍历二级目录下的文件，并读取其内容
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            _list = json.loads(content.decode('utf-8'))
            export_data[directory].append(_list)


def read_feeds(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith(directory + '/') and name.endswith('.json')]
    export_data[directory] = {}
    export_table = {
        'dashboard_email_subscribe': 'id', 'dashboard_subscribe_display_format': 'subscribe_id',
        'mobile_subscribe_filter': 'email_subscribe_id',
        'mobile_subscribe_rules': 'email_subscribe_id', 'mobile_subscribe_chapters': 'email_subscribe_id', 'flow': 'id'
    }
    export_data[directory] = {}
    # 遍历二级目录下的文件，并读取其内容
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            _map = json.loads(content.decode('utf-8'))
            for key, value in export_table.items():
                if not export_data.get(directory).get(key):
                    export_data.get(directory)[key] = []
                _val = _map.get(key) or []
                export_data.get(directory).get(key).extend(_val)


def read_active_reports(export_data, zip_file):
    def report_1_0_postprocessor(path, key, value):
        if isinstance(value, dict) and '@p4:nil' in value and value['@p4:nil'] == 'true':
            return key, None
        return key, value

    is_report_center = True
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith('report_center/') and name.endswith('.rptx')]
    if not files_in_directory:
        is_report_center = False
        files_in_directory = [name for name in zip_file.namelist() if
                              name.startswith('active_reports/') and name.endswith('.rptx')]
    if not files_in_directory:
         return

    group_map = {}
    detail_map = {}
    detail_ids = []
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            report_package_dict = xmltodict.parse(content, postprocessor=report_1_0_postprocessor, encoding='utf-8')
            group_id = read_active_reports_group(report_package_dict, group_map)
            read_active_reports_detail(report_package_dict, detail_map, group_id, detail_ids)
    # form_reports_group_tree(group_map, result)
    # detail_list = format_report_detail_tree(detail_map)
    # format_report(detail_list, group_map, result)

    result = format_report_tree(list(detail_map.values()), list(group_map.values()))
    remove_file_parent_id(result, group_map.keys())
    result = sort_report_tree(result)
    export_data["active_reports"] = {"data": result, "ids": detail_ids}
    if is_report_center:
        export_data["active_reports"]["ids"] = []
        export_data["report_center"] = {"data": {}, "ids": detail_ids}


def read_other(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if name == (directory+"/base.json")]
    if files_in_directory and len(files_in_directory) > 0:
        file_name = files_in_directory[0]
        with zip_file.open(file_name) as file:
            content = file.read()
            export_data[directory] = json.loads(content.decode('utf-8'))


def read_active_reports_group(report_package_dict, group_map):
    report_package_group = report_package_dict["ReportPackage"].get("Groups", {})
    report_groups = report_package_group.get("ReportGroup", [])
    root_id = ''
    if not isinstance(report_groups, list):
        report_groups = [report_groups]
    for group in report_groups:
        if not root_id:
            root_id = group.get('MyRptGroupId')
        if group_map.get(group.get('MyRptGroupId')):
            continue
        group_map[group.get('MyRptGroupId')] = {
            "id": group.get('MyRptGroupId'),
            "name": group.get('GroupCName'),
            "type": "FOLDER",
            "parent_id": group.get('ParentId'),
            "sub":[],
            "release_type": None,
            "publish_status": None
        }
    return root_id


def format_report_tree(files, folders):
    # Step 1: 过滤掉name为系统分发的folder
    folders = [folder for folder in folders if folder['name'] != '系统分发']

    # Step 2: 将所有节点放入一个字典，方便查找和构建树
    all_items = files + folders
    node_dict = {item['id']: item for item in all_items}

    # Step 3: 初始化根节点集合和子节点集合
    roots = []
    children = {}

    for item in all_items:
        if not item.get('sub'):
            item['sub'] = []  # 初始化子节点列表
        parent_id = item.get('parent_id')

        # 处理parent不存在的场景
        if parent_id is None or parent_id not in node_dict:
            item['parent_id'] = None
            roots.append(item)
        else:
            if parent_id not in children:
                children[parent_id] = []
            children[parent_id].append(item)

    # Step 4: 处理存在环的场景
    visited = set()
    stack = []

    def add_to_stack(item):
        if item['id'] in visited:
            return
        visited.add(item['id'])
        stack.append(item)

    for root in roots:
        add_to_stack(root)

    while stack:
        node = stack.pop()
        node_id = node['id']

        if node_id in children:
            sub_nodes = children[node_id]
            for sub_node in sub_nodes:
                if sub_node['id'] in visited:
                    # 如果存在环，修正parent_id
                    sub_node['parent_id'] = None
                    if sub_node not in roots:
                        roots.append(sub_node)
                else:
                    node['sub'].append(sub_node)
                    add_to_stack(sub_node)

    return roots


def read_active_reports_detail(report_package_dict, detail_map, group_id, detail_ids):
    report_package_reports = report_package_dict["ReportPackage"].get("Reports", {})
    report_details = report_package_reports.get("ReportDetail", [])
    if not isinstance(report_details, list):
        report_details = [report_details]
    for detail in report_details:
        release_type = 4
        row_state = 0
        if detail.get('PublishStatus') and detail.get('PublishStatus') in [1, 2, '1', '2']:
            row_state = 1

        if detail.get('ReleaseType') and detail.get('ReleaseType') in [3, '3']:
            release_type = 3
        report = {
            "id": detail.get('MyRptDetailId'),
            "name": detail.get('RptCName'),
            "type": "FILE",
            "parent_id": detail.get('ParentId') or group_id,
            "sub": [],
            "release_type": release_type,
            "publish_status": row_state,
            "is_screen_rpt": detail.get('IsScreenRpt'),
            "rank": detail.get('Rank'),
            "tag_relation": json.loads(detail.get('BusinessTags')) if detail.get('BusinessTags') else [],
        }
        detail_ids.append(report.get('id'))
        detail_map[report.get('id')] = report



def remove_file_parent_id(tree, folder_id_list):
    def flatten_tree(file_tree):
        flat_list = []
        _stack = [file_tree]

        while _stack:
            current_node = _stack.pop()
            flat_list.append(current_node)

            # 将子节点添加到堆栈中，以便进一步处理
            if 'sub' in current_node and current_node['sub']:
                for child in reversed(current_node['sub']):
                    _stack.append(child)

            # 如果当前节点有子节点，清空它的子节点
            current_node['sub'] = []

        return flat_list

    stack = tree[:]
    file_set = set()
    new_sub_dict = {}
    while stack:
        current = stack.pop()
        if current['parent_id'] in folder_id_list and current['type'] == 'FILE' and current['id'] not in file_set:
            # 此处current一定是FILE而不是CHILD_FILE
            new_sub = flatten_tree(current)
            if current['parent_id'] not in new_sub_dict.keys():
                new_sub_dict[current['parent_id']] = []
            new_sub_dict[current['parent_id']].extend(new_sub)
            current['parent_id'] = None
            file_set.add(current['id'])
        stack.extend(current['sub'])

    stack = tree[:]
    while stack:
        current = stack.pop()
        if current["id"] in new_sub_dict.keys():
            current['sub'] = new_sub_dict[current["id"]]
        stack.extend(current['sub'])


def sort_report_tree(tree):
    def sort_key(item):
        # 处理不存在rank字段或rank字段不是数值的情况
        try:
            rank = int(item.get('rank', 0) or 0)
        except:
            rank = 0
        return rank, item.get('name', '') or ''

    def sort_node(node):
        node['sub'].sort(key=sort_key)
        for sub_node in node['sub']:
            sort_node(sub_node)

    for root in tree:
        sort_node(root)

    return tree
