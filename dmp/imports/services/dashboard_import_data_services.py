# -*- coding: utf-8 -*-
# pylint: skip-file
import collections
import datetime
import os
import json
import queue
from urllib.parse import urlparse
from loguru import logger

from base import repository
from celery_app.celery import CeleryTask
from components.pular15_api import Pulsar15Api
from dashboard_chart.models import DashboardModel
from dashboard_chart.repositories import dashboard_extra_repository
from dashboard_chart.services import dashboard_extra_service
from dataset.repositories import dataset_repository
from dmplib import config
from dmplib.components import auth_util
from dmplib.hug import g
from dmplib import redis
from dmplib.utils.errors import UserError
from dmplib.locale import trans_msg
from base.enums import ProjectValueAddedFunc, ImportFileType
from imports.services import import_helper
from imports.models import DashboardImportsModel
from imports.repositories import dashboard_imports_repositories
from dashboard_chart.services.dashboard_service import license_control, generate_level_code
from data_source.services.data_source_service import get_data_source_by_id
from exports.services.biz_link_service import BizLinkService
from exports import EXPORT_STATUS_CREATED, EXPORT_STATUS_FAILURE
from exports.services import external_export_service
from dmplib.utils.strings import seq_id
from components.storage_setting import get_project_setting


def preview_data_report_1_0(file_url: str, result_with_tree=True):
    """
    预览统计报表1.0数据，只返回树形结构
    """
    package_dict = import_helper.parse_file(file_url, ImportFileType.RPTX.value)

    reports = get_import_report_data_1_0(package_dict)
    dataset_data = get_import_dataset_data_1_0(package_dict)
    return {
        "dashboards": [],
        "datasets": dataset_data[0],
        "applications": [],
        "datasource": dataset_data[1],
        ProjectValueAddedFunc.PPT.value: [],
        ProjectValueAddedFunc.ACTIVE_REPORT.value: [],
        ProjectValueAddedFunc.REPORT_CENTER.value: reports,
        "feeds": [],
        "source_project": None
    }


def preview_data(file_url: str, result_with_tree=True):
    """
    预览数据
    :param file_url:
    :param result_with_tree:结果返回树形结构，默认是
    :return:
    """

    # 如果文件是.rptx结尾，则为统计报表1.0
    if file_url.lower().endswith('.rptx'):
        return preview_data_report_1_0(file_url, result_with_tree)

    export_data = import_helper.parse_zip_file(file_url)
    dashboards = export_data.get('dashboards', {})
    preview_dashboards = import_helper.get_preview_dashboards(dashboards)
    large_screens = export_data.get('large_screens', {})
    preview_large_screens = import_helper.get_preview_dashboards(large_screens)
    data_reporting = export_data.get('data_reporting', {})
    preview_data_reporting = import_helper.get_preview_dashboards(data_reporting)
    datasets = export_data.get('datasets', '')
    preview_datasets = import_helper.get_preview_datasets(datasets)
    applications = export_data.get('applications', {})
    preview_applications = import_helper.get_preview_applications(applications)
    preview_datasource = import_helper.get_preview_datasource(datasets)
    # 在线、明细报告解析
    ppts = get_import_report_data(ProjectValueAddedFunc.PPT.value, export_data.get(ProjectValueAddedFunc.PPT.value, []))
    active_reports = get_import_report_data(ProjectValueAddedFunc.ACTIVE_REPORT.value,
                                            export_data.get(ProjectValueAddedFunc.ACTIVE_REPORT.value, []))
    # TODO 目前报表中心和统计报表一致，如果报表中心存在数据，认为的报表中心导入
    report_center = []
    if export_data.get(ProjectValueAddedFunc.REPORT_CENTER.value):
        report_center = active_reports
        report_center = report_center_folder_tree(report_center)
        active_reports = []

    # 简讯
    feeds = export_data.get('feeds', {})
    preview_feeds = import_helper.get_preview_feed(feeds)

    dashboard_fields = {
        'id', 'name', 'created_on', 'type', 'level_code', 'parent_id', 'check_status',
        'platform', 'new_layout_type', 'application_type'
    }

    if result_with_tree:
        simple_data_reporting_tree = import_helper.build_record_tree(
            import_helper.filter_records_fields(preview_data_reporting, dashboard_fields))
        return {
            "dashboards": import_helper.build_record_tree(
                import_helper.filter_records_fields(preview_dashboards, dashboard_fields)
            ),
            "large_screens": import_helper.build_record_tree(
                import_helper.filter_records_fields(preview_large_screens, dashboard_fields)
            ),
            "datasets": import_helper.build_record_tree_v1(import_helper.filter_records_fields(preview_datasets)),
            "applications": import_helper.build_record_tree(import_helper.filter_records_fields(preview_applications)),
            "datasource": preview_datasource,
            ProjectValueAddedFunc.PPT.value: diff_ppt(ppts, simple_data_reporting_tree),
            ProjectValueAddedFunc.ACTIVE_REPORT.value: active_reports,
            ProjectValueAddedFunc.REPORT_CENTER.value: report_center,
            "feeds": preview_feeds,
            ProjectValueAddedFunc.DATA_REPORTING.value: simple_data_reporting_tree,
            "source_project": export_data.get("source_project")
        }
    else:
        simple_data_reporting_tree = import_helper.filter_records_fields(preview_data_reporting, dashboard_fields)
        return {
            "dashboards": import_helper.filter_records_fields(preview_dashboards, dashboard_fields),
            "large_screens": import_helper.filter_records_fields(preview_large_screens, dashboard_fields),
            "datasets": import_helper.filter_records_fields(preview_datasets),
            "applications": import_helper.filter_records_fields(preview_applications),
            "datasource": preview_datasource,
            ProjectValueAddedFunc.PPT.value: diff_ppt(ppts, simple_data_reporting_tree),
            ProjectValueAddedFunc.ACTIVE_REPORT.value: active_reports,
            ProjectValueAddedFunc.REPORT_CENTER.value: report_center,
            "feeds": preview_feeds,
            ProjectValueAddedFunc.DATA_REPORTING.value: simple_data_reporting_tree,
            "source_project": export_data.get("source_project")
        }

def diff_ppt(ppts, data_reports):
    if not ppts or not data_reports:
        return ppts
    ids = get_all_report_ids([], data_reports)
    res = []
    for ppt in ppts:
        if not ppt.get('id') in ids:
            res.append(ppt)
    return res

def get_all_report_ids(ids, data_reports):
    for report in data_reports:
        if report.get('type') == 'FILE':
            ids.append(report.get('id'))
        if report.get('sub'):
            get_all_report_ids(ids, report.get('sub', []))
    return ids

def report_center_folder_tree(report_center):
    tree_map = {}
    new_list = []
    for report in report_center:
        if report.get("type") == 'FOLDER':
            new_list.append(report)
            report["sub"] = report_center_folder_tree(report.get("sub"))
        else:
            if not report.get("parent_id"):
                new_list.append(report)
            tree_map[report.get("id")] = report
    for _key in tree_map:
        report = tree_map.get(_key)
        parent_report = tree_map.get(report.get("parent_id")) or {}
        if parent_report:
            sub = parent_report.get("sub") or []
            if not sub:
                parent_report["sub"] = sub
            sub.append(report)
    return new_list


def get_import_file_type(oss_url):
    if oss_url.lower().endswith('.rptx'):
        return ImportFileType.RPTX.value
    return ImportFileType.ZIP.value


def dashboard_import_data(**kwargs):
    oss_url = kwargs.get('oss_url')
    force_update = kwargs.get('force_update') or False
    file_type = get_import_file_type(oss_url)

    if file_type == ImportFileType.RPTX.value:
        # 报表1.0不需要解压校验
        import_data = {}
    else:
        import_data = import_helper.parse_zip_file(oss_url)
        validate_import_data(import_data, kwargs.get('pick_data'), force_update=force_update)

    model = DashboardImportsModel()
    url_path = urlparse(oss_url).path
    model.filename = os.path.basename(url_path)
    model.target_dataset_folder_id = kwargs.get('target_dataset_folder_id')
    model.target_dashboard_folder_id = kwargs.get('target_dashboard_folder_id')
    model.target_large_screen_folder_id = kwargs.get('target_large_screen_folder_id')
    model.target_application_folder_id = kwargs.get('target_application_folder_id')
    dashboards = kwargs.get('pick_data', {}).get('dashboards', [])
    large_screens = kwargs.get('pick_data', {}).get('large_screens', [])
    data_reporting = kwargs.get('pick_data', {}).get('data_reporting', [])
    data_source = kwargs.get('pick_data', {}).get('data_source', {})
    feeds = kwargs.get('pick_data', {}).get('feeds', []) or []
    # 支持在线报告、明细报告导入
    selected_ppt_list = kwargs.get('pick_data', {}).get(ProjectValueAddedFunc.PPT.value, [])
    active_report_list = kwargs.get('pick_data', {}).get(ProjectValueAddedFunc.ACTIVE_REPORT.value, [])
    report_center_list = kwargs.get('pick_data', {}).get(ProjectValueAddedFunc.REPORT_CENTER.value, [])
    ppt_list = selected_ppt_list
    if data_reporting:
        ppt_list = selected_ppt_list + data_reporting
    if ppt_list or active_report_list or report_center_list:
        # 判断在线、明细报告是否有权限导入
        check_report_import_auth(ppt_list, active_report_list, report_center_list)

    if data_source:
        kwargs['pick_data']['data_source'] = get_data_source_by_map(data_source)

    if dashboards or large_screens:
        license_control()

    dashboards = get_all_dashboard_include_child(dashboards)
    large_screens = get_all_dashboard_include_child(large_screens)
    if 'pick_data' in kwargs.keys():
        kwargs['pick_data']['dashboards'] = dashboards
        kwargs['pick_data']['large_screens'] = large_screens
    datasets = kwargs.get('pick_data', {}).get('datasets', [])
    applications = kwargs.get('pick_data', {}).get('applications', [])
    model.content = json.dumps(
        {
            'dashboards': import_helper.filter_records_fields(dashboards, {'id', 'name', 'type', 'created_on'}),
            'large_screens': import_helper.filter_records_fields(large_screens, {'id', 'name', 'type', 'created_on'}),
            'data_reporting': import_helper.filter_records_fields(data_reporting, {'id', 'name', 'type', 'created_on'}),
            'datasets': import_helper.filter_records_fields(datasets, {'id', 'name', 'created_on'}),
            'applications': import_helper.filter_records_fields(applications, {'id', 'name', 'created_on'}),
            'feeds': feeds,
            ProjectValueAddedFunc.PPT.value: selected_ppt_list,
            ProjectValueAddedFunc.ACTIVE_REPORT.value: active_report_list,
            ProjectValueAddedFunc.REPORT_CENTER.value: report_center_list,
        }
    )
    if len(model.content) > 65500:
        raise UserError(message='选择的报告或数据集超出数量限制，请重新选择')
    dashboard_imports_repositories.add_dashboard_import(model)

    task_params = {
        "dashboard_import_id": model.id,
        "file_url": oss_url,
        "file_type": file_type,
        # 选中的报告
        "select_import_data": kwargs.get('pick_data'),
        "target_dashboard_folder_id": model.target_dashboard_folder_id,
        "target_ppt_folder_id": kwargs.get('target_ppt_folder_id'),
        "target_active_report_folder_id":kwargs.get('target_active_report_folder_id'),
        "target_large_screen_folder_id": model.target_large_screen_folder_id,
        "target_data_reporting_folder_id": kwargs.get('target_data_reporting_folder_id'),
        "target_dataset_folder_id": model.target_dataset_folder_id,
        "target_application_folder_id": model.target_application_folder_id,
        "include_dashboard_folder": kwargs.get('include_dashboard_folder', False),
        "include_large_screen_folder": kwargs.get('include_large_screen_folder', False),
        "include_dataset_folder": kwargs.get('include_dataset_folder', False),
        "include_application_folder": kwargs.get('include_application_folder', False),
        "include_data_reporting_folder": kwargs.get('include_data_reporting_folder', False),
        # 报告导出租户code
        "source_project": kwargs.get('source_project', ""),
        # 报告导出的user_id 当导入报表1.0时，import_data为None
        "source_user_id": import_data.get("source_user_id", ""),
    }
    # 存在在线、明细报告时，首先调用对方系统的导入接口，待对方系统处理完成回调添加导入任务
    if ppt_list or active_report_list or report_center_list:
        # TODO 目前报表中心只有复杂报表，后期上了简单报表后，这个地方要过滤掉简单报表
        if report_center_list:
            active_report_list = report_center_list
        # 导入接口调用
        biz_system_report_import(task_params, ppt_list, active_report_list)
    else:
        # 添加导入报告任务
        add_import_report_task(**task_params)
    return model.id


def get_all_dashboard_include_child(dashboards):
    """
    获取所有报表包含子报表
    """
    all_dashboards = []
    recursive_get_all_dashboard(dashboards, all_dashboards)
    return all_dashboards


def add_import_report_task(**kwargs):
    """
    添加导入报告任务
    :param kwargs:
    :return:
    """
    try:
        sync_import_data(**kwargs)
    except Exception as e:
        logger.exception(f"导入报告任务添加失败，errs:{str(e)}")
        raise e


def check_report_import_auth(ppt_ids, active_report_ids, report_center_list):
    """
    判断在线、明细报告是否有权限导入
    :param ppt_ids:
    :param active_report_ids:
    :return:
    """
    if ppt_ids and not external_export_service.check_export_biz_is_open(ProjectValueAddedFunc.PPT.value):
        raise UserError(message="当前租户不支持在线报告的导入")
    if active_report_ids and not external_export_service.check_export_biz_is_open(
            ProjectValueAddedFunc.ACTIVE_REPORT.value):
        raise UserError(message="当前租户不支持明细报告的导入")
    if report_center_list and not external_export_service.check_export_biz_is_open(
            ProjectValueAddedFunc.REPORT_CENTER.value):
        raise UserError(message="当前租户不支持报表中心的导入")

CACHE_IMPORT_COUNT_KEY = "import:%s:count"
CACHE_IMPORT_KEY_TIME_OUT = 3600

def biz_system_report_import(task_params, ppt_list, active_report_list):
    """
    报告导入接口调用
    :param task_params:
    :param ppt_list: 在线报告的数据
    :param active_report_list: 明细报告的数据
    :return:
    """
    try:
        if not ppt_list and not active_report_list:
            raise UserError(message="报告不存在，导入失败")

        import_id = task_params.get("dashboard_import_id")
        file_url = task_params.get("file_url")
        file_type = task_params.get('file_type')
        target_dashboard_folder_id = task_params.get("target_dashboard_folder_id")
        source_project = task_params.get("source_project")
        source_user_id = task_params.get("source_user_id")

        import_data = {
            "tenant_code_list": [],
            "file_url": file_url,
            "target_dashboard_folder_id": target_dashboard_folder_id,
            "source_project": source_project,
            "source_user_id": source_user_id,
        }
        # 统计报表1.0需要将数据集id名称传递给业务系统
        if file_type == ImportFileType.RPTX.value:
            import_data['datasets'] = task_params.get("select_import_data", {}).get("datasets")

        count = 0
        count = count + 1 if ppt_list else count + 0
        count = count + 1 if active_report_list else count + 0
        if count > 1:
            redis.conn().set(CACHE_IMPORT_COUNT_KEY % import_id, count, CACHE_IMPORT_KEY_TIME_OUT)

        if ppt_list:
            ppt_ids = [item.get("id") for item in ppt_list]
            biz_link_service = BizLinkService(ProjectValueAddedFunc.PPT.value, g.code, g.userid)
            if task_params.get("target_ppt_folder_id"):
                import_data["target_dashboard_folder_id"] = task_params.get("target_ppt_folder_id")
            biz_link_service.import_report(ppt_ids, "import", import_id, import_data)

        if active_report_list:
            active_report_ids = [item.get("id") for item in active_report_list]
            biz_link_service = BizLinkService(ProjectValueAddedFunc.ACTIVE_REPORT.value, g.code, g.userid)
            if task_params.get("target_active_report_folder_id"):
                import_data["target_dashboard_folder_id"] = task_params.get("target_active_report_folder_id")
            biz_link_service.import_report(active_report_ids, "import", import_id, import_data)

        import_row = {"import_task_params": json.dumps(task_params, ensure_ascii=False)}
        dashboard_imports_repositories.update_import(task_params.get("dashboard_import_id"), import_row)
    except Exception as e:
        msg = f"业务系统报告导入失败-{str(e)}"
        logger.exception(msg)
        import_row = {"status": EXPORT_STATUS_FAILURE, "message": msg,
                      "import_task_params": json.dumps(task_params, ensure_ascii=False)}
        dashboard_imports_repositories.update_import(task_params.get("dashboard_import_id"), import_row)
        raise UserError(message=msg)


def get_data_source_by_map(data_source_map: dict):
    for source_id, target_id in data_source_map.items():
        if source_id != target_id:
            data_source_map[source_id] = get_data_source_by_id(target_id)
        else:
            data_source_map[source_id] = None
    return data_source_map


def recursive_get_all_dashboard(original_dashboards, dashboards):
    # 递归获取子报告
    for dashboard in original_dashboards:
        dashboards.append(dashboard)
        if dashboard.get('sub'):
            recursive_get_all_dashboard(dashboard.get('sub'), dashboards)


def get_chart_redirect_is_new_jump():
    from dashboard_chart.services.chart_service import METADATA_CONFIG
    return METADATA_CONFIG.get_is_new_jump()


def validate_import_data(import_data: dict, select_import_data: dict, force_update: bool = True):
    """
    校验导入
    :param import_data: oss解压出的导入数据
    :param select_import_data: 前端勾选的导入数据
    :param force_update: 覆盖更新（默认开启）
    :return:
    """
    if force_update:
        return

    # 处理历史版本问题
    version = import_data.get("version")
    if not version or (version != "v1.0.0.0" and version != "v1.5.0.0"):
        raise UserError(400, '模板文件已过期，请重新导出后再导入')

    # 新跳转的报告导入旧模式的租户，不让导入
    is_new_jump = bool(import_data.get("is_new_jump"))
    is_new_jump_mode = bool(get_chart_redirect_is_new_jump())
    if is_new_jump and (not is_new_jump_mode):
        raise UserError(400, '当前导入的报表版本较高，请联系产品管理员升级到新版本后再导入')

    _custom_import_valid_for_yk(import_data, select_import_data=select_import_data)

    # if select_import_data:
    #     validate_import_select_data(select_import_data)
    # else:
    #     dashboards = import_data.get('dashboards', {})
    #     dashboard_ids = [dashboard_id for dashboard_id in dashboards.keys()]
    #     if dashboard_ids:
    #         exist_dashboards = dashboard_repository.get_dashboard_by_ids(dashboard_ids)
    #         if exist_dashboards:
    #             raise UserError(400, '检测到数据集和报告导入冲突，请取消已存在的数据集和报告，再导入')
    #
    #     datasets = import_data.get('datasets', '')
    #     dataset_ids = [dataset_data.get("dataset_id") for dataset_data in datasets]
    #     if dataset_ids:
    #         exist_datasets = dataset_repository.get_dataset_by_ids(dataset_ids)
    #         if exist_datasets:
    #             raise UserError(400, '检测到数据集和报告导入冲突，请取消已存在的数据集和报告，再导入')
    #
    #     applications = import_data.get('applications', {})
    #     application_ids = [application_id for application_id in applications.keys()]
    #     if application_ids:
    #         exist_applications = external_application_repository.batch_get_application_info(application_ids)
    #         if exist_applications:
    #             raise UserError(400, '检测到门户导入冲突，请取消已存在的门户，再导入')


def _custom_import_valid_for_yk(import_data: dict, select_import_data: dict):
    """
    特殊为云客定制导入的数据校验
    https://tapd.cn/38229611/prong/stories/view/1138229611001597936
    仅在包含数据集的情况下触发
    """

    from_env = import_data.get("env") or ""
    curr_env = config.get('App.dmp_env_code', '') or auth_util.get_env_code()

    yk_ali_envs_str = config.get('External.yunke_aliyun_env_code', 'yumke_dmp,yunke_aliyun_prod_gray,dmp_yunke')
    yk_ali_envs = [s.strip() for s in yk_ali_envs_str.split(',')]
    yk_hw_envs_str = config.get('External.yunke_huawei_env_code', 'yk-hw-dap-saas-new,yk-hw-dap-test')
    yk_hw_envs = [s.strip() for s in yk_hw_envs_str.split(',')]
    # yk_ali_envs = ['yumke_dmp', 'yunke_aliyun_prod_gray', 'dmp_yunke']
    # yk_hw_envs = ['yk-hw-dap-saas-new', 'yk-hw-dap-test']

    if not from_env:
        # 2024.08.22 如果是来自华为环境的导出历史包，标记为阿里导出的（不让导入历史的包）
        from_env = yk_ali_envs[0]

    if from_env in yk_hw_envs and curr_env in yk_ali_envs:
        # 从华为云客导入阿里云客
        raise UserError(message="导入文件与当前环境数据引擎不同，不支持导入。")

    if not select_import_data.get("datasets", []):
        return

    if from_env in yk_ali_envs and curr_env in yk_hw_envs:
        # 从阿里云客导入华为云客
        raise UserError(message="导入文件与当前环境数据引擎不同，不支持直接导入。如需导入可参考如下文档进行转换操作。<a href='https://doc.weixin.qq.com/doc/w3_ACkAGQYnALYCW1zG93vSpKPIvHNVD?scode=AIsAVQcAABIy82E5BpAAYAmgafAB0'>操作指引</a>")


def validate_import_select_data(select_import_data: dict):
    for dashboard in select_import_data.get("dashboards", []):
        if dashboard.get("check_status"):
            raise UserError(400, '检测到数据集和报告导入冲突，请取消已存在的数据集和报告，再导入')

    for dataset in select_import_data.get("datasets", []):
        if dataset.get("check_status"):
            raise UserError(400, '检测到数据集和报告导入冲突，请取消已存在的数据集和报告，再导入')

    for application in select_import_data.get("applications", []):
        if application.get("check_status"):
            raise UserError(400, '检测到门户导入冲突，请取消已存在的门户，再导入')


CELERY_APP_NAME = "dmp_admin_celery"
CELERY_TASK_NAME = "app_celery.import_dashboard_dataset"
CELERY_DELIVER_NAME = "app_celery.auto_deliver_single_dashboard"
ADMIN_CELERY_QUEUE_NAME = "admin-celery"


def sync_import_data(**kwargs):
    """
    异步导入数据集和报告数据。（启动admin的celery执行）
    :param dashboard_import_id: 导入ID
    :param file_url: 文件地址
    :param select_import_data: 前端勾选的导入数据
    :param target_dashboard_folder_id: 指定目标报告文件夹ID
    :param target_dataset_folder_id: 指定目标数据集文件夹ID
    :param include_folder: 是否包含文件夹 （默认否）
    :return:
    """
    from celery_app.celery import get_backend

    backend = get_backend(config.get('Redis.db') or 6)

    celery_app = CeleryTask(name=CELERY_APP_NAME, backend=backend, queue_name=ADMIN_CELERY_QUEUE_NAME)
    celery_app.celery.send_task(
        CELERY_TASK_NAME,
        kwargs={
            'project_code': g.code,
            'dashboard_import_id': kwargs.get('dashboard_import_id'),
            'file_url': kwargs.get('file_url'),
            'select_import_data': kwargs.get('select_import_data'),
            'target_dashboard_folder_id': kwargs.get('target_dashboard_folder_id'),
            'target_large_screen_folder_id': kwargs.get('target_large_screen_folder_id'),
            'target_dataset_folder_id': kwargs.get('target_dataset_folder_id'),
            'target_application_folder_id': kwargs.get('target_application_folder_id'),
            'target_data_reporting_folder_id': kwargs.get('target_data_reporting_folder_id'),
            'include_dashboard_folder': kwargs.get('include_dashboard_folder', False),
            'include_large_screen_folder': kwargs.get('include_large_screen_folder', False),
            'include_dataset_folder': kwargs.get('include_dataset_folder', False),
            'include_data_reporting_folder': kwargs.get('include_data_reporting_folder', False),
            'include_application_folder': kwargs.get('include_application_folder', False),
        },
    )


def sync_deliver_data(deliver_id):
    """
    异步分发数据集和报告数据。（启动admin的celery执行）
    """
    from celery_app.celery import get_backend

    backend = get_backend(config.get('Redis.db') or 6)
    celery_app = CeleryTask(name=CELERY_APP_NAME, backend=backend, queue_name=ADMIN_CELERY_QUEUE_NAME)
    celery_app.celery.send_task(
        CELERY_DELIVER_NAME,
        kwargs={
            'project_code': g.code,
            'deliver_id': deliver_id
        },
    )


def get_import_dataset_data_1_0(report_package_dict):
    """
    统计报表1.0导入，获取导入的数据集列表,将报表的数据集和关键字数据集列出
    """
    report_package_reports = report_package_dict["ReportPackage"].get("Reports", {})
    report_details = report_package_reports.get("ReportDetail", []) if report_package_reports else []
    if type(report_details) is collections.OrderedDict:
        report_details = [report_details]
    report_package_keywords = report_package_dict["ReportPackage"].get("ReportKeywords", {})
    report_keywords = report_package_keywords.get("ReportKeyword", []) if report_package_keywords else []
    if type(report_keywords) is collections.OrderedDict:
        report_keywords = [report_keywords]
    group_id = seq_id()
    datasource_dict = {}
    dataset_default_group = {
        'check_status': False,
        'created_on': datetime.datetime.now(),
        'id': group_id,
        'level_code': '0000-',
        'name': '报表1.0数据集',
        'parent_id': None,
        'type': 'FOLDER',
        'sub': []
    }
    for report_detail in report_details:
        if report_detail["IsScreenRpt"] == "1":
            continue
        ds_define_json = report_detail["DsDefine"]
        ds_define_list = json.loads(ds_define_json)
        for ds in ds_define_list:
            import_dataset = {
                'check_status': False,
                'created_on': datetime.datetime.now(),
                'id': seq_id(),
                'level_code': '0000-0000-',
                'name': f'{report_detail["RptCName"]}_{ds["Name"]}',
                'parent_id': group_id,
                'type': "SQL"
            }
            dataset_default_group['sub'].append(import_dataset)
            add_import_report_dataset_to_datasource(datasource_dict=datasource_dict, import_entity=ds,
                                                    import_dataset=import_dataset)

    processed_keyword_set = set()
    for report_keyword in report_keywords:
        keyword_name = f'{report_keyword["DataSourceName"]}_{report_keyword["MyKeywordName"]}'
        if keyword_name not in processed_keyword_set:
            import_dataset = {
                'check_status': False,
                'created_on': datetime.datetime.now(),
                'id': seq_id(),
                'level_code': '0000-0000-',
                'name': keyword_name,
                'parent_id': group_id,
                'type': "SQL"
            }
            processed_keyword_set.add(keyword_name)
            dataset_default_group['sub'].append(import_dataset)
            add_import_report_dataset_to_datasource(datasource_dict=datasource_dict, import_entity=report_keyword,
                                                    import_dataset=import_dataset)

    dataset_names = [dataset_item['name'] for dataset_item in dataset_default_group['sub']]
    exist_dataset_names = dataset_repository.get_dataset_id_map_by_names(dataset_names)
    for dataset_item in dataset_default_group['sub']:
        if dataset_item['name'] in exist_dataset_names:
            dataset_item['check_status'] = True
            dataset_item['id'] = exist_dataset_names[dataset_item['name']]

    datasource_list = list(datasource_dict.values())
    return [dataset_default_group], datasource_list


def add_import_report_dataset_to_datasource(datasource_dict, import_entity, import_dataset):
    """
    统计报表1.0导入，给导入的数据集指定数据源
    """
    datasource_id = import_entity["DataSourceGuid"]
    if datasource_id not in datasource_dict:
        datasource_dict[datasource_id] = {
            'source_id': import_entity["DataSourceGuid"],
            'name': import_entity["DataSourceName"],
            'dataset_list': []
        }
    datasource_dict[datasource_id]['dataset_list'].append({
        'dataset_id': import_dataset['id'],
        'dataset_name': import_dataset['name'],
    })


def get_import_report_data_1_0(report_package_dict):
    """
    统计报表1.0导入获取导入的报告列表
    :param report_package_dict：
    """
    report_package_reports = report_package_dict["ReportPackage"].get("Reports", {})
    report_details = report_package_reports.get("ReportDetail", []) if report_package_reports else []
    result = []
    if isinstance(report_details, collections.OrderedDict):
        report_details = [report_details]
    # 将报表加入结果
    for report_detail in report_details:
        if report_detail["IsScreenRpt"] == "1":
            continue
        report_item = {
            'id': report_detail["MyRptDetailId"],
            'name': report_detail["RptCName"],
            'parent_id': report_detail["GroupGuid"] if report_detail["ParentId"] is None else report_detail["ParentId"],
            'sub': [],
            'publish_status': None,
            'release_type': None,
            'type': 'FILE'
        }
        result.append(report_item)
    # 将文件夹加入结果
    report_package_groups = report_package_dict["ReportPackage"].get("Groups", {})
    report_groups = report_package_groups.get('ReportGroup', []) if report_package_groups else []
    if isinstance(report_groups, collections.OrderedDict):
        report_groups = [report_groups]
    for report_group in report_groups:
        report_item = {
            'id': report_group["MyRptGroupId"],
            'name': report_group["GroupCName"],
            'parent_id': report_group["ParentId"],
            'sub': [],
            'publish_status': None,
            'release_type': None,
            'type': 'FOLDER'
        }
        result.append(report_item)
    return convert_report_list_to_tree(result)


def convert_report_list_to_tree(reports):
    """
    报表转为树结构
    """
    tree_reports = []
    convert_queue = queue.Queue()
    visited = set()
    for item in reports:
        if item['parent_id'] is None:
            tree_reports.append(item)
            convert_queue.put(item)
            visited.add(item['id'])
    while not convert_queue.empty():
        curr = convert_queue.get()
        visited.add(curr['id'])
        for item in filter(lambda x: x['parent_id'] == curr['id'], reports):
            if item['id'] not in visited:
                convert_queue.put(item)
                curr['sub'].append(item)

    return tree_reports


def get_import_report_data(biz_type, report_info):
    """
    获取导入的在线、明细报告的数据
    :param biz_type:
    :param report_info:
    :return:
    """
    data = []
    if not report_info:
        return data
    biz_link_service = BizLinkService(biz_type, g.code, g.userid)
    ids = report_info.get("ids")
    ids = list(set(ids))
    exists_report_list = biz_link_service.get_report_by_id(ids)
    exists_report_id_list = [item.get("id") for item in exists_report_list]
    report_data_list = report_info.get("data")
    iter_report_data(report_data_list, exists_report_id_list)
    return report_data_list


def iter_report_data(report_data_list, exists_report_id_list):
    """
    遍历报告的数据，检查报告是否存在
    :param report_data_list:
    :param exists_report_id_list:
    :return:
    """
    if not report_data_list:
        return report_data_list
    for item in report_data_list:
        if item.get("type") == "FOLDER" and item.get("sub"):
            iter_report_data(item.get("sub"), exists_report_id_list)
        elif item.get("type") == "FILE":
            item["check_status"] = True if item.get("id") in exists_report_id_list else False


def import_callback(**kwargs):
    """
    在线、明细报告导入任务的回调处理逻辑
    :param kwargs:
    :return:
    """
    try:
        logger.info("报告导入回调请求参数：" + json.dumps(kwargs, ensure_ascii=False))
        # 操作类型进行区分，不同类型处理的逻辑不同 import：导入，deliver：分发
        import_id = kwargs.get("import_id")
        action_type = kwargs.get("action_type")
        if action_type == 'import':
            import_callback_project(**kwargs)
        else:
            raise UserError(message=trans_msg("报告回调类型错误"))

        logger.error(f"导入任务回调完成！import_id:{import_id}")
        return True
    except Exception as e:
        raise UserError(message=trans_msg("报告导入回调接口异常，{}").format(str(e)))


def import_callback_project(**kwargs):
    """
    报告导入的回调处理逻辑，业务系统报告导入成功，添加异步任务执行数据集导入
    :param kwargs:
    :return:
    """
    import_row = {'callback_result': json.dumps(kwargs, ensure_ascii=False)}
    try:
        import_id = kwargs.get("import_id")
        import_task_data = dashboard_imports_repositories.get_dashboard_import_by_id(import_id)
        if not import_task_data:
            raise UserError(message="导入任务不存在")

        import_task_params_json = import_task_data.get("import_task_params")
        if not import_task_params_json:
            raise UserError(message="导入任务参数不能为空")
        import_task_params = json.loads(import_task_params_json)

        if import_task_data.get("callback_result") and import_task_data.get("status") != EXPORT_STATUS_CREATED:
            raise UserError(message="导入任务已处理，不能再次执行")

        result_status = kwargs.get("import_result")
        # 业务系统报告导入失败
        if not result_status:
            msg = f"报告元数据导入失败，errs：{kwargs.get('msg')}"
            raise UserError(message=msg)

        # 触发celery导入任务
        if "active_reports" == kwargs.get("biz_type"):
            logger.info("执行active_reports回调任务")
            import_active_report_to_dashboard(import_task_params)
            import_dashboard_tag_relations(kwargs)

        if check_import_all_finished(import_task_params, import_id):
            add_import_report_task(**import_task_params)
            # 记录回调信息
        dashboard_imports_repositories.update_import_callback_result(import_id, import_row.get('callback_result'))
    except Exception as e:
        import_row['status'] = EXPORT_STATUS_FAILURE
        import_row['message'] = (kwargs.get('biz_type') or '') + "业务系统报告导入回调失败，errs：" + str(e)
        dashboard_imports_repositories.update_import_error(import_id, import_row['status'],
                                                           import_row['message'], import_row.get('callback_result'))
        check_import_all_finished(import_task_params, import_id)
        raise e


def import_dashboard_tag_relations(import_callback):
    callback_business_tags_map = import_callback.get('business_tags_map', {})
    insert_tag_relations = []
    for dashboard_id in callback_business_tags_map.keys():
        tag_list_json = callback_business_tags_map[dashboard_id]
        if not tag_list_json:
            tag_list_json = "[]"
        tag_list = json.loads(tag_list_json)
        if tag_list:
            for tag in tag_list:
                insert_tag_relations.append({
                    'tag_name': tag['tag_name'],
                    'tag_id': tag['tag_id'],
                    'relation_id': dashboard_id,
                    'type': '1'
                })
        repository.delete_data('dap_bi_tag_relation', {
            'relation_id': dashboard_id,
            'type': '1'
        })
    if insert_tag_relations:
        repository.add_list_data('dap_bi_tag_relation', insert_tag_relations,
                                 ['relation_id', 'type', 'tag_id', 'tag_name'])


def check_import_all_finished(import_task_params, import_id):
    if import_task_params and import_task_params.get("select_import_data"):
        select_import_data = import_task_params.get("select_import_data")
        count = 0
        count += 1 if select_import_data.get("ppt") else 0
        count += 1 if select_import_data.get("active_reports") or select_import_data.get("report_center") else 0
        if count < 2:
            return True
        count = redis.conn().decr(CACHE_IMPORT_COUNT_KEY % import_id)
        if count == 0:
            logger.info("redis分布式锁，处理任务")
            status = dashboard_imports_repositories.get_import_status_by_id(import_id)
            if EXPORT_STATUS_FAILURE == status:
                return False
            return True
        return False
    return True


# 报表中心导入后同步数据到dashboard表中
def import_active_report_to_dashboard(import_task_params):
    if (import_task_params and import_task_params.get("select_import_data")
            and import_task_params.get("select_import_data").get("report_center")):
        active_reports = import_task_params.get("select_import_data").get("report_center")
        ids = [report.get("id") for report in active_reports]
        if len(ids) <= 0:
            return
        biz_link_service = BizLinkService(ProjectValueAddedFunc.ACTIVE_REPORT.value, g.code, g.userid)
        exists_report_list = biz_link_service.get_report_all_by_id({"ids": ids})
        new_ids = []
        data_list = []
        get_all_active_report_ids(exists_report_list, new_ids, data_list, "")
        repository.delete_data("dap_bi_dashboard", {"id": new_ids, "application_type": [5, 6]})
        get_all_active_report(data_list, import_task_params)


def get_all_active_report(data_list, import_task_params):
    for report in data_list:
        model = DashboardModel(
            application_type=5, platform="pc", theme="colorful_white",
            is_show_mark_img=1, new_layout_type=1, create_type=1
        )
        model.id = report.get("id")
        model.name = report.get("name")
        model.layout = report.get("is_screen_rpt", None)
        if report.get("parent_id"):
            model.parent_id = report.get("parent_id")
            model.type = "CHILD_FILE"
        else:
            model.parent_id = import_task_params.get("target_active_report_folder_id") \
                              or import_task_params.get("target_dashboard_folder_id")
            model.type = "FILE"
        row_state = report.get("publish_status") or 0

        if report.get("release_type") and report.get("release_type") in ['3', 3]:
            model.type_access_released = 3
        else:
            model.type_access_released = 4
        model.created_by = g.account
        model.modified_by = g.account
        model.level_code = generate_level_code(model.parent_id)
        model.biz_code = model.id.replace('-', '')
        data = model.get_dict(DashboardModel.fields)
        data["created_on"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        data["modified_on"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if row_state in [1, 2, '1', '2']:
            data["status"] = 1
        modified_time = report.get('modified_time') or data["modified_on"]
        modified_time = convert_datetime_string(modified_time)
        repository.add_data('dap_bi_dashboard', data)
        if row_state in [1, '1']:
            dashboard_extra_repository.replace_insert_released_edit(model.id, modified_time, modified_time)
        elif row_state in [2, '2']:
            dashboard_extra_repository.replace_insert_released(model.id, modified_time)
        else:
            dashboard_extra_service.reset_edit_and_released_on(model.id)

def convert_datetime_string(date_str):
    if 'T' in date_str:
        date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%S")
        return date_obj.strftime("%Y-%m-%d %H:%M:%S")
    else:
        # 如果不包含 'T'，则直接返回原始字符串
        return date_str

def get_all_active_report_ids(exists_report_list, new_ids, data_list, parent_id):
    for report in exists_report_list:
        data_list.append(report)
        new_ids.append(report.get("id"))
        report["parent_id"] = parent_id
        if report.get("sub"):
            get_all_active_report_ids(report.get("sub"), new_ids, data_list, report.get("id"))
