# -*- coding: UTF-8 -*-
# pylint: skip-file
# from gevent import monkey;monkey.patch_all()
import logging
import os
import time
import json
import traceback
from datetime import datetime, timedelta

from loguru import logger

os.environ["DMP_CFG_FILE_PATH"] = os.path.join(os.path.dirname(__file__), "app.config")
os.environ['DMP_LOCALE_FILE_PATH'] = os.path.join(os.path.dirname(__file__), 'locale')
os.environ["DMP_ROOT_PATH"] = os.path.dirname(__file__)
os.environ.setdefault('PROMETHEUS_MULTIPROC_DIR', '/tmp')

from components.utils import jwt_patch  # noqa: E402

jwt_patch()

from dmplib.hug import g
from dmplib.hug.context import DBContext
from dmplib.utils.errors import UserError
from dmplib.utils.logs import init_logging
from system.services.zip_transform_service import YkZipTransformService
from self_service.models import SyncModel

from dmplib.db.mysql_wrapper import get_db  # noqa: E402
from celery_app import celery_base  # noqa: E402
from celery_app.celery import celery  # noqa: E402
from celery_app.celery_base import restore_all_unacknowledged_messages  # noqa: E402

from dmplib.redis import RedisCache  # noqa: E402
from dmplib import config  # noqa: E402

from celery.signals import worker_init  # noqa: E402

from base.enums import FlowInstanceStatus, SnapshotType, DashboardStatus  # noqa: E402
from base import repository  # noqa: E402

from dmplib.constants import ADMINISTRATORS_ID, ADMIN_ROLE_ID
from dmplib.conf_constants import SELFSERVICE_PULSAR_PROJECT_CODE
from app_hook import include_snapshot_models
from dashboard_snapshot.services import external_service  # noqa: E402
from dashboard_chart.models import ChartDownloadContext  # noqa: E402
from healthy.services import dashboard_healthy_service, subject_dataset_full_link_inspection  # noqa: E402
from dashboard_chart.services.download import chart_column_download_async, chart_normal_download_service
import sync_data # 不能去掉
from user_log.models import UserLogModel # 不能去掉

# 注册拍照表
from app_hook import update_ssl_certifi

update_ssl_certifi()

redis_cache = RedisCache()

REDIS_TASK_KEY_PREFIX = "celery-task-meta-"

init_logging()
include_snapshot_models()

# Not needed for RabbitMQ
@worker_init.connect
def configure(sender=None, conf=None, **kwargs):
    # 标识celery_app，在其他模块可能需要根据该判断来特殊处理, 比如在DirectMysqlQueryData.get_data
    os.environ.setdefault("CELERY_APP", "1")
    restore_all_unacknowledged_messages()


@celery.task
def add(x, y):
    return x + y


@celery.task
def test_celery(task_tag):
    redis_cache.add(task_tag, "ok")

    return task_tag


@celery.task(base=celery_base.BaseTask)
def upload_log_to_aliyun(log_data):
    """异步把日志上传到阿里云日志服务里

    Decorators:
        celery

    Args:
        project_code (str): 源项目代码
        dashboard_id (str): dashboard.id
        target_project_codes (iterable): 目标项目代码组
        apply_all (bool): 是否应用到所有项目
        action (str): 支持create,update,delete
    """
    # 已废弃
    pass


@celery.task(base=celery_base.BaseTask)
def log_to_logstore(logstore, topic, contents):
    """
    异步把日志上传到阿里云日志服务里
    """
    pass


@celery.task(base=celery_base.BaseTask)
def sync_dashboard(action, project_code, dashboard_id, remark=None, target_project_codes=None, apply_all=False):
    """同步dashboard到特定的租户库

    Decorators:
        celery

    Args:
        project_code (str): 源项目代码
        dashboard_id (str): dashboard.id
        target_project_codes (iterable): 目标项目代码组
        apply_all (bool): 是否应用到所有项目
        action (str): 支持create,update,delete
    """
    from async_service import sync_tenant_data  # noqa: E402

    seq = sync_tenant_data.stash_sync_dashboard_data(project_code, dashboard_id, apply_all, remark, action)
    # 定
    if apply_all:
        db = get_db()
        rows = db.query("select code from dap_p_tenant")
        for row in rows:
            target_project = row["code"]
            if target_project == project_code:
                continue
            args = {"seq": seq, "target_project_code": target_project, "project_code": project_code}
            sync_dashboard_one_project.apply_async(kwargs=args)
    elif target_project_codes:
        for target_project in target_project_codes:
            if target_project == project_code:
                continue
            args = {"seq": seq, "target_project_code": target_project, "project_code": project_code}
            sync_dashboard_one_project.apply_async(kwargs=args)


@celery.task(base=celery_base.BaseTask)
def sync_dashboard_one_project(seq, project_code, target_project_code):
    """
    同步到单个项目
    :param project_code:
    :param seq:
    :param target_project_code:
    :return:
    """
    from async_service import sync_tenant_data  # noqa: E402
    sync_tenant_data.sync_dashboard_one_project(seq, project_code, target_project_code)


@celery.task(base=celery_base.BaseTask)
def create_dataset_data_index(*args, **opts):
    """
    根据数据集id和字段，自动创建索引
    :param project_code:
    :param dataset_fields:
    :return:
    """
    from dataset.services import dataset_service  # noqa: E402
    from dashboard_chart.services import external_dashboard_service  # noqa: E402

    project_code = opts.get("project_code")
    set_globals(project_code, "celery")
    dashboard_id = opts.get("dashboard_id")
    dataset_fields = external_dashboard_service.get_dataset_fields_by_dashboard(dashboard_id)
    dataset_service.create_dataset_data_index(dataset_fields)


@celery.task(base=celery_base.BaseTask)
def export_dataset(task_id, dataset_id, code, account, userid, version_id=None, file_type=None):
    from dataset.services.dataset_base_service import DatasetBaseService  # noqa: E402

    g.userid = userid
    set_globals(code, account)
    try:
        dataset_base_service = DatasetBaseService()
        dataset_base_service.export_dataset(task_id, dataset_id, version_id=version_id, file_type=file_type)
    except Exception:
        logging.error("执行{task_id}异步任务错误".format(task_id=task_id), exc_info=True)


@celery.task(base=celery_base.BaseTask)
def async_upgrade_released_dashboard(task_id, code, account):
    set_globals(code, account)
    # 无人使用
    # try:
    #     async_released_dashboard.upgrade_released_dashboard(task_id)
    # except Exception:
    #     logging.error("执行{task_id}异步任务错误".format(task_id=task_id), exc_info=True)


@celery.task(base=celery_base.BaseTask)
def async_upgrade_chart_filter_config(task_id, code, account):
    from dashboard_chart.services import chart_service, metadata_service  # noqa: E402
    set_globals(code, account)
    try:
        chart_service.upgrade_dashboard_chart_filter(task_id)
    except Exception as e:
        traceback.print_exc()
        logging.error("执行{task_id}异步任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()))


@celery.task(base=celery_base.BaseTask)
def generate_excel_dataset(task_id, content, code, account, is_get_sheet):
    from dataset.services import dataset_service  # noqa: E402

    set_globals(code, account)
    try:
        if is_get_sheet:
            dataset_service.generate_excel_sheet(task_id, content)
        else:
            dataset_service.generate_excel_dataset(task_id, content)
    except Exception as e:
        traceback.print_exc()
        logging.error("执行{task_id}异步任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()))


@celery.task(base=celery_base.BaseTask)
def update_excel_index(task_id, dataset, code, account):
    from dataset.services import dataset_service  # noqa: E402
    set_globals(code, account)
    try:
        dataset_service.update_excel_index(task_id, code, dataset)
    except Exception as e:
        traceback.print_exc()
        logging.error("执行{task_id}异步任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()))


@celery.task(base=celery_base.BaseTask)
def generate_user_group(task_id, project_code, account, oss_url, is_preview, user_source_id):
    from async_task.services.import_user_group_service import GroupExcelService  # noqa: E402

    set_globals(project_code, account)
    try:
        service = GroupExcelService(project_code, account, oss_url)
        service.async_process_data(task_id, user_source_id, is_preview)
    except Exception as e:
        traceback.print_exc()
        logging.error("执行{task_id}异步任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()))


@celery.task(base=celery_base.BaseTask)
def generate_user(task_id, project_code, account, oss_url, is_preview, user_source_id):
    from async_task.services.import_user_service import UserExcelService  # noqa: E402
    set_globals(project_code, account)
    try:
        service = UserExcelService(project_code, account, oss_url)
        service.async_process_data(task_id, user_source_id, is_preview)
    except Exception as e:
        traceback.print_exc()
        logging.error("执行{task_id}异步任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()))


@celery.task(base=celery_base.BaseTask)
def async_download_chart_data(**kwargs):
    """
    获取图表数据并上传oss
    :return:
    """
    from dashboard_chart.services.download import chart_column_download_async, chart_normal_download_service
    from message.services import message_service  # noqa: E402
    from message.models import MessageModel  # noqa: E402

    task_id = kwargs.get("task_id")
    flow_instance_id = kwargs.get("flow_instance_id")
    download_id = kwargs.get("download_id")
    filename = kwargs.get("filename")
    code = kwargs.get("code")
    account = kwargs.get("account")
    cookie = kwargs.get("cookie")
    dumped_chart_params = kwargs.get("dumped_chart_params")
    userid = kwargs.get("userid")
    external_params = kwargs.get("external_params")
    logger.info("async_download_chart_data task start. task_id:{0}".format(task_id))
    set_globals(code, account)
    setattr(g, "cookie", cookie)
    setattr(g, "userid", userid)
    setattr(g, 'external_params', external_params)
    setattr(g, 'snap_id', kwargs.get('snap_id'))
    # 将当前任务执行超时时间调大到300，因为单图取数业务逻辑复制的时候时长很长，会超时
    os.environ["TIMEOUT"] = "300"

    try:
        download_service = chart_normal_download_service.DownloadChart()
        download_service.exec_async_download_chart_data(
            task_id, flow_instance_id, download_id, filename, dumped_chart_params, userid
        )
    except Exception as e:
        repository.update_data("dap_bi_dashboard_chart_download_task", {"status": 3}, {"download_id": download_id})
        _message = {
            'source_id': '',
            'source': '通知',
            'user_id': userid,
            'type': '个人消息',
            'title': '异常错误，无法生成数据{}，请重试'.format(filename),
            'url': '/flow/ops',
        }
        message_service.message_add(MessageModel(**_message))
        repository.update_data("dap_bi_instance", {"status": FlowInstanceStatus.Failed.value}, {"id": flow_instance_id})
        if isinstance(e, UserError):
            logger.warning("执行{task_id}异步任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()),
                           exc_info=True)
        else:
            logger.error("执行{task_id}异步任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()), exc_info=True)


@celery.task(base=celery_base.BaseTask)
def async_download_chart_data_new(download_id: str, project_code: str, snap_id=None):

    logger.info(f"download_id: {download_id}")
    g.code = project_code
    if snap_id:
        setattr(g, "snap_id", snap_id)
    params = repository.get_data_scalar('dap_bi_dashboard_chart_download_task', {'download_id': download_id},
                                        col_name='params')
    if not params:
        logger.error(f"下载任务未保存任务参数， download_id: {download_id}")
        return
    if isinstance(params, str):
        download_context = json.loads(params)
    else:
        download_context = params

    # 将当前任务执行超时时间调大到300，因为单图取数业务逻辑复制的时候时长很长，会超时300
    os.environ["TIMEOUT"] = "300"
    download_context = ChartDownloadContext(**download_context)
    download_context.chart_params = json.loads(download_context.chart_params)
    chart_column_download_async.AsyncChartDownloadService(download_context).run()


@celery.task(base=celery_base.BaseTask)
def export_dashboard(project_code, export_id, account, userid):
    """导出报告，提供给分发

    Args:
        project_code (str): project.code
        export_id (str): exports.id
    """
    from exports.services import export_service  # noqa: E402

    set_globals(project_code, None)
    g.account = account
    g.userid = userid
    export_service.exec_export_task(project_code, export_id)


@celery.task(base=celery_base.BaseTask)
def feed_dashboard(*args, **opts):
    """订阅报告

    Args:
        args (list):
        opts (dict)：{'project_code': 'dev', 'data_id': '123', 'is_rundeck': 1, 'feed_detail_id':'', 'retry':0}
                    project_code: 项目编码
                    feed_id: 订阅ID
                    feed_detail_id: 订阅明细ID（默认为空，重发时需要订阅明细ID）
                    retry: 重发（默认0：不重发，1：重发全部收件人，2：重发失败收件人）
                    is_rundeck: 是否rundeck调度任务（默认0：否）
    """
    from feed.services import feed_service  # noqa: E402

    project_code = opts.get("project_code")
    set_globals(project_code, "celery")
    feed_id = opts.get("data_id")
    is_rundeck = opts.get("is_rundeck")
    feed_detail_id = opts.get("feed_detail_id")
    retry = opts.get("retry")
    dmp_send_time = (
        opts.get('created_time') if opts.get('created_time') else time.strftime("%Y-%m-%d %H:%M:00", time.localtime())
    )
    feed_service.send_feed_message(
        project_code, feed_id, dmp_send_time, feed_detail_id=feed_detail_id, retry=retry, is_rundeck=is_rundeck
    )


@celery.task(base=celery_base.BaseTask)
def feed_used_dataset_stat(*args, **opts):
    """
    环境级的任务处理
    所有简讯的调度数据集统计
    :param args:
    :param opts:
    :return:
    """
    from feed.services import feed_service  # noqa: E402

    feed_service.feed_used_dataset_stat()


@celery.task(base=celery_base.BaseTask)
def healthy_dashboard(*args, **opts):
    """巡检报告

    Args:
        args (list):
        opts (dict)：{'project_code': 'dev', 'data_id': '123'}
                    project_code: 项目编码
                    data_id（dashboard_id）: 报告ID
                    is_released: 报告是否发布. 0:未发布, 1:已发布 （默认1：已发布）
                    dashbaord_metadata: 报告元数据快照
    """

    project_code = opts.get("project_code")
    set_globals(project_code, "celery")
    setattr(g, "userid", ADMINISTRATORS_ID)  # 暂时使用admin的uid用于取数
    healthy_id = opts.get("data_id") or ''
    is_released = opts.get("is_released")
    is_released = (
        DashboardStatus.Released.value
        if is_released not in [e.value for e in DashboardStatus.__members__.values()]
        else is_released
    )

    # 取出healthy关联的报告id
    healthy_id = healthy_id.replace('healthy_new_', '')
    data = repository.get_one('dap_bi_healthy_dashboard', conditions={'id': healthy_id}) or {}
    if not data:
        raise UserError(message=f'healthy_id：{healthy_id} 不存在！')
    dashboard_id = data.get('dashboard_id') or ''
    logger.info(f"healthy_dashboard task params:{project_code}, {healthy_id} {dashboard_id}, {is_released}")

    dashboard_healthy_service.start_dashboard_healthy(project_code, healthy_id, dashboard_id, is_released)


@celery.task(base=celery_base.BaseTask)
def release_application(*args, **opts):
    """
    发布开放数据的应用程序
    Args:
        args (list):
        opts (dict)：{"project_code": "dev", "data_id": "123", "url_file": "https://dmp.com/oss/test.zip"}
                    project_code: 项目编码
                    data_id: 应用程序ID
                    url_file: 文件url链接地址
    """
    from open_data.services import custom_dashboard_service  # noqa: E402

    project_code = opts.get('project_code')
    set_globals(project_code, 'celery')
    data_id = opts.get('data_id')
    url_file = opts.get('url_file')
    custom_dashboard_service.run_release_application(data_id, url_file)


def set_globals(project_code, account):
    g.code = project_code
    g.account = "celery" if not account else account
    return g


def set_tenant_globals(tenant_code=None):
    if tenant_code:
        g.tenant = tenant_code
    return g


def set_admin_user(project_code):
    """
    设置admin用户
    :param project_code:
    :return:
    """
    set_globals(project_code, 'skylineadmin')
    user_id = repository.get_data_scalar('dap_p_user_user_role', {'role_id': ADMIN_ROLE_ID}, 'user_id') or ADMINISTRATORS_ID
    setattr(g, 'userid', user_id)


@celery.task(base=celery_base.BaseTask)
def async_run_subject_full_inspection_storage(project_code: str, subject_id: str, data_souce_type: str):
    """
    主题数据集数据存储全链路巡检
    :param project_code: 租户代码
    :param subject_id: 主题ID
    :param data_souce_type: 数据源类型
    :return:
    """
    from healthy.services import dashboard_healthy_service, subject_dataset_full_link_inspection  # noqa: E402

    set_globals(project_code, "skylineadmin")
    setattr(g, 'userid', ADMINISTRATORS_ID)
    subject_dataset_full_link_inspection.async_run_subject_full_inspection_storage(subject_id, data_souce_type)


@celery.task(base=celery_base.BaseTask)
def async_run_subject_full_inspection_dashboard_node(project_code: str, subject_id: str, data_souce_type: str):
    """
    主题数据集报告全链路巡检
    :param project_code: 租户代码
    :param subject_id: 主题ID
    :return:
    """
    from healthy.services import dashboard_healthy_service, subject_dataset_full_link_inspection  # noqa: E402

    set_globals(project_code, "skylineadmin")
    setattr(g, 'userid', ADMINISTRATORS_ID)
    subject_dataset_full_link_inspection.async_run_subject_full_inspection_dashboard_node(subject_id, data_souce_type)


@celery.task(base=celery_base.BaseTask)
def async_run_chart_copy(project_code: str, userid: str, account: str, transaction_id: str):
    """
    主题数据集数据存储全链路巡检
    :param project_code: 租户代码
    :param userid: 用户ID
    :param transaction_id: 事务ID
    :return:
    """
    from dashboard_chart.chart_reproducer.transaction import Transaction  # noqa: E402
    set_globals(project_code, account)
    transaction = Transaction(userid, account, transaction_id)
    transaction()


@celery.task(base=celery_base.BaseTask)
def async_run_indicator_warning(project_code: str):
    """
    指标巡检
    :param project_code:
    :return:
    """
    set_globals(project_code, "skylineadmin")
    setattr(g, 'userid', ADMINISTRATORS_ID)


@celery.task(base=celery_base.BaseTask)
def async_run_indicator_new_warning(config_id: str, project_code: str):
    """
    指标巡检
    :param config_id:
    :param project_code:
    :return:
    """
    # 需要保证在import api_route前
    from indicator_new.service.warning_worker import WarningWorker

    set_globals(project_code, "skylineadmin")
    setattr(g, 'userid', ADMINISTRATORS_ID)
    indicator_warning = WarningWorker({"warning_config_id": config_id}, project_code)
    indicator_warning.exec()


@celery.task(base=celery_base.BaseTask)
def async_indicator_new_warning(*args, **opts):
    """"""
    # 需要保证在import api_route前
    from indicator_new.service.warning_worker import WarningWorker

    project_code = opts.get("project_code")
    set_globals(project_code, "celery")
    setattr(g, "userid", ADMINISTRATORS_ID)  # 暂时使用admin的uid用于取数
    config_id = opts.get("data_id")
    logger.info("async indicator task params:%s, %s, %s", project_code, config_id)

    indicator_warning = WarningWorker({"warning_config_id": config_id}, project_code)
    indicator_warning.exec()


@celery.task(base=celery_base.BaseTask)
def async_test_indicator_new_warning(params: dict, project_code: str):
    """
    指标巡检
    :param params:
    :param project_code:
    :return:
    """
    # 需要保证在import api_route前
    from indicator_new.service.warning_worker import WarningWorker

    set_globals(project_code, "skylineadmin")
    setattr(g, 'userid', ADMINISTRATORS_ID)
    indicator_warning = WarningWorker(params, project_code, 2)
    indicator_warning.exec()


@celery.task(base=celery_base.BaseTask)
def refresh_all_self_report(**kwargs):
    from dmplib.hug.globals import _app_ctx_stack, _AppCtxGlobals
    from self_service.services import external_sync_service

    dmp_project_codes = kwargs.get('dmp_project_codes', [])
    for dmp_project_code in dmp_project_codes:
        setattr(g, 'code', dmp_project_code)
        new_g = _AppCtxGlobals()
        new_g.code = dmp_project_code
        new_g.account = getattr(g, "account", None)
        _app_ctx_stack.push(new_g)
        db_ctx = DBContext()
        db_ctx.inject(new_g)
        try:
            logging.info(f"start refresh all <{dmp_project_code}> model")
            external_sync_service.sync_all()
        except Exception:
            logging.exception(f"refresh all <{dmp_project_code}> model failed")
        finally:
            db_ctx.close_all()
            _app_ctx_stack.pop()


@celery.task(base=celery_base.BaseTask)
def sync_self_report(**kwargs):
    from dmplib.hug.globals import _app_ctx_stack, _AppCtxGlobals
    from self_service.services import external_sync_service

    model = SyncModel(
        pulsar_project_code=kwargs.get('pulsar_project_code'),
        external_subject_id=kwargs.get('subject_code'),
        external_space_id=kwargs.get('space_id'),
        action=kwargs.get('action'),
        model_category=kwargs.get('model_category'),
    )

    logger.info(
        f'receive sync message<{model.pulsar_project_code}, {model.action}, {model.external_space_id}, {model.external_subject_id}, {model.model_category}>'
    )

    from rbac import external_service
    dmp_project_codes = external_service.get_value_added_func_project_codes("self-service")
    expected_pulsar_project_code = SELFSERVICE_PULSAR_PROJECT_CODE
    """获取配置库中所有开启自助报表租户配置的数芯业务板块code"""
    dmp_project_codes = repository.get_list('dap_bi_tenant_setting', {'code': dmp_project_codes}, ['code', 'pulsar_project_code'],
                                            from_config_db=True)
    for dmp_project_code in dmp_project_codes:
        pulsar_project_code = dmp_project_code.get('pulsar_project_code')
        pulsar_project_code = pulsar_project_code if pulsar_project_code else expected_pulsar_project_code
        # pulsar_project_code如果和配置的数芯pulsar_project_code不同, 跳过同步
        if model.pulsar_project_code and model.pulsar_project_code.upper() != pulsar_project_code.upper():
            continue
        dmp_code = dmp_project_code.get('code')
        logger.info(f'start refresh <{dmp_code}> model info')
        new_g = _AppCtxGlobals()
        new_g.code = dmp_code
        new_g.account = getattr(g, "account", None)
        _app_ctx_stack.push(new_g)
        db_ctx = DBContext()
        db_ctx.inject(new_g)
        try:
            external_sync_service.sync(model)
        except Exception:
            logger.exception(f"refresh <{dmp_code}> model info failed")
        finally:
            db_ctx.close_all()
            _app_ctx_stack.pop()


@celery.task(base=celery_base.BaseTask)
def async_release_message_push(status, dashboard: dict, project_code: str):
    """
    指标巡检
    :param params:
    :param project_code:
    :return:
    """
    from dashboard_chart.services.released_dashboard_service import handle_message_push_params

    set_globals(project_code, "skylineadmin")
    setattr(g, 'userid', ADMINISTRATORS_ID)
    return handle_message_push_params(status, dashboard, project_code)


@celery.task(base=celery_base.BaseTask)
def async_set_user_active_time(project_code: str, userid, account):
    """
    指标巡检
    :param account:
    :param userid:
    :param project_code:
    :return:
    """
    from dashboard_chart.services import chart_service, metadata_service  # noqa: E402
    set_globals(project_code, "skylineadmin")
    metadata_service.set_user_last_active_time(userid, account)


@celery.task(base=celery_base.BaseTask)
def hd_upgrade_data(project_code, upgrade_task_id, params, userid):
    """
    HighData数据升级异步处理
    :param project_code: project.code
    :param upgrade_task_id: 任务id
    :param params: 某个模块升级指定参数
    :param userid: 用户id
    :return:
    """
    from hd_upgrade.services import upgrade_service  # noqa: E402
    g.userid = userid
    set_globals(project_code, 'skylineadmin')
    return upgrade_service.exec_upgrade_data_task(upgrade_task_id, params)


@celery.task(base=celery_base.BaseTask)
def async_analysis_cache(**kwargs):
    from system.services import analysis_service, sql_adb_to_st
    analysis_service.analysis_cache(**kwargs)


@celery.task(base=celery_base.BaseTask)
def snapshot_service(**params):
    """
    简讯拍照异步任务
    :return:
    """
    from app_hook import include_snapshot_models

    include_snapshot_models()

    from components.snapshot_service import Snapshot
    project_code = params.get('project_code')
    set_admin_user(project_code)
    snapshot = Snapshot(
        code=project_code,
        snap_type=SnapshotType.Dashboard.value,
        params=params
    )
    snapshot.do()


@celery.task(base=celery_base.BaseTask)
def dashboard_snapshot(project_code, **params):
    """
    报表拍照异步任务
    :return:
    """
    from app_hook import include_snapshot_models

    include_snapshot_models()

    from dashboard_snapshot.services import external_service  # noqa: E402

    set_admin_user(project_code)
    external_service.execute_dashboard_snap(dashboard_id=params.get("data_id"), snap_id=params.get("snap_id"))


@celery.task(base=celery_base.BaseTask)
def generate_filling_batch(project_code, **params):
    """
    生成填报批次
    :param project_code:
    :param params:
    :return:
    """
    from manual_filling.services.filling_template_service import ManualFillingService

    set_admin_user(project_code)
    # repository.update_data("dap_bi_flow", {"status": FlowInstanceStatus.Running.value}, {"id": params.get("data_id")})
    ManualFillingService.generate_input_batch(params.get("data_id"))
    # repository.update_data("dap_bi_flow", {"status": FlowInstanceStatus.Successful.value}, {"id": params.get("data_id")})


@celery.task(base=celery_base.BaseTask)
def send_message(**params):
    """
    发送消息
    :param project_code:
    :param params:
    :return:
    """
    from components.message import send_msg

    project_code = params.get('code')
    set_globals(project_code, 'skylineadmin')

    msg_id = params.get("msg_id")
    repository.update_data("dap_bi_message_record", {"status": "运行中"}, {"id": msg_id})
    try:
        result = send_msg(
            to_user=params.get("to_user"),
            code=project_code,
            channel=params.get("channel"),
            body=params.get("body")
        )
        status = "成功"
    except Exception as e:
        status = "失败"
        result = str(e)
    repository.update_data("dap_bi_message_record", {"status": status, "result": result}, {"id": msg_id})


@celery.task(base=celery_base.BaseTask)
def filling_data(**kwargs):
    """
    异步处理数据落地
    :param params:
    :return:
    """
    from manual_filling.repositories.filling_data_repositories import update_filling_data_task
    from manual_filling.common.enums import FillDataTaskStatus
    from manual_filling.services import filling_data_service

    project_code = kwargs.get("project_code")
    set_globals(project_code, kwargs.get("account"))
    setattr(g, "userid", kwargs.get("userid"))

    task_id = kwargs.get("task_id")
    update_filling_data_task(task_id, FillDataTaskStatus.Running.value)
    try:
        del kwargs["project_code"]
        del kwargs['account']
        del kwargs['userid']
        del kwargs['task_id']
        filling_data_service.filling_data_service(**kwargs)
        status = FillDataTaskStatus.Suc.value
        message = "success"
    except Exception as e:
        status = FillDataTaskStatus.Failed.value
        message = str(e)
        traceback.print_exc()
    finally:
        update_filling_data_task(task_id, status, message)  # NOSONAR


@celery.task(base=celery_base.BaseTask)
def sync_filling_data(**kwargs):
    """
    异步同步填报数据
    :return:
    """
    from manual_filling.services import filling_datacenter_service
    project_code = kwargs.get("project_code")
    table_name = kwargs.get("table_name")
    set_globals(project_code, '')
    try:
        filling_datacenter_service.fill_data_to_datacenter(table_name)
    except Exception as e:
        logger.error(str(e))


@celery.task(base=celery_base.BaseTask)
def save_dataset_used_table(**kwargs):
    """
    解析数据集和表名关系
    """
    from dataset.models import DatasetModel
    from base.enums import DatasetEditMode
    from dataset.common.sql_helper import get_table_names
    from dmplib.utils.strings import seq_id

    set_globals(kwargs.get("project_code"), None)
    model = DatasetModel(**kwargs)
    edit_mode = model.edit_mode
    table_list = list()
    try:
        if edit_mode == DatasetEditMode.Sql.value:
            if not isinstance(model.content, dict):
                model.content = json.loads(model.content) if model.content else dict()
            table_list = get_table_names(model.content.get("sql")) if model.content.get("sql") else []
        elif edit_mode in (DatasetEditMode.Relation.value, DatasetEditMode.Graph.value):
            relation_content = model.relation_content.get('nodeDataArray', [])
            for relation in relation_content:
                if relation.get('name'):
                    table_list.append(relation.get('name'))
        if table_list:
            data = list()
            for table in table_list:
                if not table:
                    continue
                row = dict()
                row['id'] = seq_id()
                row['dataset_id'] = model.id
                row['table_name'] = table.lower().replace('[', '').replace(']', '').replace('`', '').replace('\'', '')
                data.append(row)
            # 删除原有数据中使用的表
            repository.delete_data('dap_bi_dataset_used_table', {'dataset_id': model.id})
            # 插入使用的表
            if data:
                repository.add_list_data('dap_bi_dataset_used_table', data, list(data[0].keys()))
    except Exception as e:
        logger.error(str(e))


@celery.task(base=celery_base.BaseTask)
def sync_indicator_model(**kwargs):
    """
    同步指标模型
    指标数据集接口文档：https://apifox.com/apidoc/shared-e9726831-ba0c-4de9-934d-2600ae8b1e7f/api-27841193
    :param kwargs:
    :return:
    """
    from dataset.services import indicator_service
    from dmplib.components.fast_logger import FastLogger
    from base.enums import DatasetType, PulsarSyncMode

    def record_dataset_sync_log(dataset_id, dataset_name, error):
        log_dict = {
            'module_type': FastLogger.ModuleType.DATASET,
            'biz_type': FastLogger.BizType.DATASET_SYNC,
            'biz_id': dataset_id,
            'biz_name': dataset_name,
            'error_type': DatasetType.Indicator.value + FastLogger.ErrorType.DATASET_SYNC_ERROR,
            'error_msg': error,
            'error_traceback': traceback.format_exc(),
        }
        FastLogger.BizErrorFastLogger(**log_dict).record()

    # 租户code
    code = kwargs.get("code") or []
    mode = kwargs.get("mode") or PulsarSyncMode.ALL.value
    instance_id = kwargs.get("instance_id")
    if not all([code, instance_id]):
        raise UserError(message=f"参数错误 code: {code}, instance_id: {instance_id}")

    set_globals(code, None)

    logger.info(f"租户：{code}, 数芯数据集同步类型：{mode}, 实例ID: {instance_id}")

    instance = repository.get_data('dap_bi_instance', {'id': instance_id}, fields=['id', 'flow_id', 'status']) or {}
    if not instance:
        raise UserError(message=f"实例ID{instance_id} 不存在")
    status = instance.get("status")
    if status != FlowInstanceStatus.Created.value:
        logger.info(f'当前实例的状态是：{status}, 跳过同步')
        return

    # 更新实例状态: 运行中
    indicator_service.update_instance(
        instance_id, instance.get('flow_id'), status=FlowInstanceStatus.Running.value, startup_time=True)

    logger.info(f"租户：{code}, 数芯数据集同步类型：{mode}, 实例ID: {instance_id}, 实例状态：运行中")

    # 设置指标数据集对应的tenant_code
    try:
        from dataset.query.query_dataset_service import QueryDatasetService
        data_source_model = QueryDatasetService.get_shuxin_data_source()
        tenant_code = data_source_model.conn_str.tenant_code
        logger.info("设置指标数据集对应的tenant_code。tenant_code:%s" % tenant_code)
        set_tenant_globals(tenant_code=tenant_code)
    except:
        import traceback
        error_msg = traceback.format_exc()
        logger.error(error_msg)
        logger.error("设置指标数据集对应的tenant_code失败")

    msg = ''
    try:
        # 新同步
        if indicator_service.is_support_new_sync():
            from dataset.services.pulsar_sync_v2_service import PulsarSync

            logger.info("当前数芯数据集同步为新模式")

            sync = PulsarSync(mode)
            # 数芯主动通知的场景需要先清缓存
            if kwargs.get("clear_cache") is True:
                logger.info(f"清缓存: {mode}")
                sync.clear_cache()
            sync.sync()

        # 旧同步
        else:
            logger.info("当前数芯数据集同步为旧模式")

            indicator_service.indicator_model_sync_all(mode)

        msg = 'success'
        status = FlowInstanceStatus.Successful.value
    except Exception as e:
        status = FlowInstanceStatus.Failed.value
        msg = str(e)
        record_dataset_sync_log("指标数据集", mode, str(e))
        raise e
    finally:
        logger.info(f"租户：{code}, 数芯数据集同步类型：{mode}, 实例ID: {instance_id}, 实例状态：{status}")
        indicator_service.update_instance(
            instance_id, instance.get('flow_id'), status=status, end_time=True, msg=msg
        )


@celery.task(base=celery_base.BaseTask)
def dataset_download(**kwargs):
    from dataset.services.dataset_async_service import dataset_local_download
    dataset_id = kwargs.get('dataset_id')
    version_id = kwargs.get('version_id')
    task_id = kwargs.get('task_id')
    download_id = kwargs.get('download_id')
    set_globals(kwargs.get('code'), 'celery')
    g.userid = kwargs.get('user_id', '')
    try:
        dataset_local_download(dataset_id, download_id, version_id)
    except Exception as e:
        traceback.print_exc()
        logging.error("执行{task_id}异步任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()))


@celery.task(base=celery_base.BaseTask)
def clean_flow_log(**kwargs):
    from datetime import datetime, timedelta
    from dmplib.db.mysql_wrapper import get_mysql_source_db

    logging.info('清理 dmp-flow-log')
    with get_mysql_source_db(
            host=config.get('DB.host'),
            port=int(config.get('DB.port')),
            db=config.get('DB.flow_log_database') or 'dmp_flow_log',
            user=config.get('DB.user'),
            passwd=config.get('DB.password'),
    ) as db:
        times = (datetime.now() - timedelta(days=3)).strftime("%Y%m%d")
        tables = db.query_columns(
            f"select table_name from information_schema.tables where table_schema='dmp_flow_log' and table_name < {times}") or []
        for table in tables:
            db.exec_sql(f'drop table {table}')

    logging.info('清理完成，已删除{}之前的数据'.format(times))


@celery.task(base=celery_base.BaseTask)
def clean_dataset_history_table(**kwargs):
    from dataset.common.clean_dataset_version import CleanDatasetHistoryTable
    code = kwargs.get('project_code')
    env = kwargs.get('env', 'local')
    openapi = kwargs.get('openapi', False)
    try:
        clean_table = CleanDatasetHistoryTable(code)
        # 清理instance表数据
        clean_table.clean_project_data()
        if openapi:
            if env == 'local':
                total = clean_table.clean_local_data_table()
                logging.info('数据服务中心垃圾表删除成功,一共删除{}张垃圾表'.format(total))
            elif env == 'cloud':
                total = clean_table.clean_new_cloud_data_table()
                logging.info('云端DATA库垃圾表删除成功,一共删除{}张垃圾表'.format(total))
        else:
            if env == 'local':
                total = clean_table.clean_datacenter_table()
                logging.info('数据服务中心历史表删除成功,一共删除{}张历史表'.format(total))
            elif env == 'cloud':
                total = clean_table.clean_cloud_data_table()
                logging.info('云端DATA库历史表删除成功,一共删除{}张历史表'.format(total))
            elif env == 'all':
                total = clean_table.clean_datacenter_table()
                logging.info('数据服务中心历史表删除成功,一共删除{}张历史表'.format(total))
                total = clean_table.clean_cloud_data_table()
                logging.info('云端DATA库历史表删除成功,一共删除{}张历史表'.format(total))
    except Exception as e:
        traceback.print_exc()
        logging.error("清理历史表失败，错误内容：{e}".format(e=e.__str__()))


# 缓存命中统计
@celery.task(base=celery_base.BaseTask)
def cache_hit_statistics(**kwargs):
    dataset_id = kwargs.get('dataset_id')
    chart_id = kwargs.get('chart_id')
    dashboard_id = kwargs.get('dashboard_id')
    data_source_id = kwargs.get('data_source_id')
    hit_cache = kwargs.get('hit_cache', 0)
    # need_cache = kwargs.get('need_cache', 0)
    # source_class = kwargs.get('source_class', '')
    # cache_class = kwargs.get('cache_class', '')
    set_globals(kwargs.get('code'), 'celery')
    hit_date = kwargs.get('hit_date', '')
    account = kwargs.get('account', '')
    g_request_data = kwargs.get('g_request_data', {})

    url_path = g_request_data.get('path', '')
    source = ''
    if url_path.startswith('/api/dashboard_chart/chart/data'):
        source = 'unreleased'
    elif url_path.startswith('/api/released_dashboard_chart/chart/data'):
        source = 'released'
    if not source:
        return

    if not all([dashboard_id, chart_id, dataset_id, data_source_id]):
        # logger.info(f'不是来自组件的取数SQL， 不统计本次的缓存命中数据')
        return

    data = {
        "dataset_id": dataset_id,
        "data_source_id": data_source_id,  # 冗余字段
        "chart_id": chart_id,
        "dashboard_id": dashboard_id,
        "account": account,
        "source": source,
        "hit_date": hit_date,
    }
    is_existed = repository.get_one('dap_bi_cache_hit_data', conditions=data, fields=['id'])
    if not is_existed:
        has_hit_init = 1 if hit_cache else 0
        not_hit_init = 1 if not hit_cache else 0
        init_data = {
            "dataset_id": dataset_id,
            "data_source_id": data_source_id,
            "chart_id": chart_id,
            "dashboard_id": dashboard_id,
            "account": account,
            "source": source,
            "hit_date": hit_date,
            "not_hit": not_hit_init,
            "has_hit": has_hit_init,
        }
        repository.add_data(table_name='dap_bi_cache_hit_data', data=init_data)
    else:
        fields = ' and '.join(['`%s`=%r' % (key, val) for key, val in data.items()])
        sql = 'UPDATE `dap_bi_cache_hit_data` SET `{}`=`{}`+1 WHERE %s LIMIT 1' % fields
        if hit_cache:
            repository.exec_sql(sql.format('has_hit', 'has_hit'))
        else:
            repository.exec_sql(sql.format('not_hit', 'not_hit'))


@celery.task(base=celery_base.BaseTask)
def record_dashboard_edit(**kwargs):
    user_id = kwargs.get('user_id')
    dashboard_id = kwargs.get('dashboard_id')
    account = kwargs.get('account', '')
    edit_time = kwargs.get('edit_time', '')
    set_globals(kwargs.get('code'), 'celery')
    num_limit = 10

    if not edit_time:
        return

    data = {
        'account': account,
        'user_id': user_id,
        'dashboard_id': dashboard_id,
        'edit_time': edit_time
    }
    repository.replace_list_data('dap_bi_dashboard_recent_edit_record', list_data=[data], fields=list(data.keys()),
                                 condition_field=['account', 'user_id', 'dashboard_id'])

    # 删除多余的记录
    delete_sql = """
    DELETE FROM dap_bi_dashboard_recent_edit_record WHERE id IN (
        SELECT id FROM (
            SELECT
                ( @row_numbers := @row_numbers + 1 ) AS ROW_NUMBERS,
                a.id AS id
            FROM dap_bi_dashboard_recent_edit_record a,(select @row_numbers := 0) b
            WHERE account = %(account)r
            ORDER BY a.edit_time DESC
        ) tmp WHERE ROW_NUMBERS > %(num_limit)r
    )
    """ % {'account': account, 'num_limit': num_limit}
    repository.exec_sql(delete_sql)


@celery.task(base=celery_base.BaseTask)
def auto_export_dashboard(**params):
    code = params.get('project_code')
    set_globals(code, 'skylineadmin')
    setattr(g, 'userid', ADMINISTRATORS_ID)
    from exports.services.export_service import auto_export_dashboard
    return auto_export_dashboard()


@celery.task(base=celery_base.BaseTask)
def auto_export_template_data(**params):
    code = params.get('project_code')
    set_globals(code, 'skylineadmin')
    setattr(g, 'userid', ADMINISTRATORS_ID)
    from exports.services.export_service import auto_export_template_data
    version = params.get('version')
    apps = params.get('apps')
    return auto_export_template_data(version, apps)

@celery.task(base=celery_base.BaseTask)
def adb_2_st_sql(**params):
    from system.services import analysis_service, sql_adb_to_st
    sql_adb_to_st.transform(**params)


@celery.task(base=celery_base.BaseTask)
def adb_2_st_sql_zip(**params):
    from system.services.sql_transform_service import SqlTransformService

    file_url = params.get("file_url")
    SqlTransformService(file_url).transform()

@celery.task(base=celery_base.BaseTask)
def adb_2_st_sql_zip_yk(**params):
    file_url = params.get("file_url")
    upload_id = params.get("upload_id")
    YkZipTransformService(file_url, upload_id).transform()

@celery.task(base=celery_base.BaseTask)
def dashboard_error_alarm(**kwargs):
    from dashboard_chart.services import external_dashboard_service  # noqa: E402
    code = kwargs.get('org_code')
    set_globals(code, 'skylineadmin')
    external_dashboard_service.dashboard_error_alarm(**kwargs)


@celery.task(base=celery_base.BaseTask)
def key_dashboard_stat(**params):
    code = params.get('project_code')
    set_globals(code, None)
    from dashboard_chart.services.key_dashboard_service import key_dashboard_stat
    return key_dashboard_stat(params)


@celery.task(base=celery_base.BaseTask)
def move_attach_task(**kwargs):
    code = kwargs.get('code')
    set_globals(code, 'celery')
    from download.services.oss_attach_move_service import MoveAttach
    MoveAttach(
        code,
        origin_endpoint=kwargs.get("origin_endpoint"),
        origin_bucket=kwargs.get("origin_bucket"),
        origin_oss_service=kwargs.get("origin_oss_service"),
        table_name=kwargs.get("table_name")
    ).move_attach()


@celery.task(base=celery_base.BaseTask)
def async_move_dashboard_as_large_screen(**kwargs):
    code = kwargs.get('code')
    userid = kwargs.get('userid')
    account = kwargs.get('account')
    task_id = kwargs.get('task_id')
    g.userid = userid
    set_globals(code, account)
    from dashboard_chart.services.large_dashboard_service import FolderMoveOldScreenUtil
    FolderMoveOldScreenUtil(task_id=task_id).move_all_screens()


@celery.task(base=celery_base.BaseTask)
def async_sync_data_to_fast(**kwargs):
    assign_rules = kwargs.get('assign_rules') or []
    assign_code = kwargs.get('assign_code')
    set_globals('', '')
    from sync_data.services.sync_fast_tracker import SyncDataToFast
    SyncDataToFast(assign_rules=assign_rules, assign_code=assign_code).sync_all_org_code()


@celery.task(base=celery_base.BaseTask)
def reg_rundeck_task(**kwargs):
    code = kwargs.get('code')
    set_globals(code, 'celery')
    action = kwargs.get("action")
    from flow.services.register_flow_service import reg_rundeck_task_do
    reg_rundeck_task_do(code, action)


@celery.task(base=celery_base.BaseTask)
def fix_level_code(**kwargs):
    from dashboard_chart.utils.level_code_utils import FixLevelCode

    code = kwargs.get('code')
    set_globals(code, 'celery')
    FixLevelCode(kwargs.get('data_id'), kwargs.get('table'), kwargs.get('extra_params')).fix()


# api数据集调度
@celery.task(base=celery_base.BaseTask)
def run_api_dataset_schedule(**kwargs):
    logging.error(f"api数据集调度，数据集id: {kwargs.get('dataset_id')}")
    from dataset.services.dataset_service import get_api_dataset_result_data, g_update_schedule_data

    key = f"dataset-{kwargs.get('dataset_id')}"
    cache = RedisCache()
    rs = cache.set_nx_ex(key, 1, nx=True, ex=1800)
    if rs is None:
        # redis key已经存在了
        data = dict(status='已中止')
        c_kwargs = kwargs.copy()
        c_kwargs['flow_instance_data'] = data
        c_kwargs['node_instance_data'] = data
        g_update_schedule_data(c_kwargs)
        logging.error(f"有其他的异步任务正在调度这个数据集【{kwargs.get('dataset_id')}】，退出")
        return
    try:
        get_api_dataset_result_data(kwargs)
    finally:
        cache.delete(key)


@celery.task(base=celery_base.BaseTask)
def heartbeat_task(**kwargs):
    logger.info("heartbeat task")


@celery.task(base=celery_base.BaseTask)
def application_snapshot_task(**kwargs):
    from components.snapshot_service import ApplicationSnapshot
    code = kwargs.get('code')
    set_admin_user(code)
    snap_id = kwargs.get('snap_id')
    application_id = kwargs.get('application_id')
    application_snapshot = ApplicationSnapshot(application_id, snap_id, code)
    application_snapshot.snapshot()
    application_snapshot.deal_snapshot_version()


@celery.task(base=celery_base.BaseTask)
def ind_upgrade_data(project_code, upgrade_task_id, params, userid):
    """
    工程数据升级异步处理
    :param project_code: project.code
    :param upgrade_task_id: 任务id
    :param params: 某个模块升级指定参数
    :param userid: 用户id
    :return:
    """
    from shuxin15_upgrade.services import shuxin15_upgrade_service, upgrade_project_service
    g.userid = userid
    set_globals(project_code, 'skylineadmin')
    return upgrade_project_service.exec_upgrade_data_task(upgrade_task_id, params)

@celery.task(base=celery_base.BaseTask)
def v55_shuxin15_upgrade_data(project_code, upgrade_task_id, params, userid):
    """
    HighData数据升级异步处理
    :param project_code: project.code
    :param upgrade_task_id: 任务id
    :param params: 某个模块升级指定参数
    :param userid: 用户id
    :return:
    """
    from shuxin15_upgrade.services import shuxin15_upgrade_service
    g.userid = userid
    set_globals(project_code, 'skylineadmin')
    return shuxin15_upgrade_service.exec_upgrade_data_task(upgrade_task_id, params)

@celery.task(base=celery_base.BaseTask)
def shuxin15_upgrade_data(project_code, upgrade_task_id, params, userid):
    """
    HighData数据升级异步处理
    :param project_code: project.code
    :param upgrade_task_id: 任务id
    :param params: 某个模块升级指定参数
    :param userid: 用户id
    :return:
    """
    from shuxin15_upgrade.services import shuxin15_upgrade_service, upgrade_project_service
    g.userid = userid
    set_globals(project_code, 'skylineadmin')
    return shuxin15_upgrade_service.exec_upgrade_data_task(upgrade_task_id, params)


@celery.task(base=celery_base.BaseTask)
def ds_conn_metrics(**kwargs):
    from data_source.services.data_source_metrics_service import get_metrics
    set_globals('', '')
    get_metrics(kwargs)


@celery.task(base=celery_base.BaseTask)
def republish_application_task(**kwargs):
    from app_menu.services.application_service import republish_application
    set_globals('', kwargs.get('account'))
    republish_application(kwargs)


@celery.task(base=celery_base.BaseTask)
def dashboard_sync_task(**kwargs):
    from ai.services.dashboard_service import sync_dashboard
    set_globals('', '')
    sync_dashboard(kwargs)


@celery.task(base=celery_base.BaseTask)
def tenant_dashboard_sync_task(**kwargs):
    tenant_code = kwargs.get('code')
    # tenant_code = kwargs.get('kwargs').get('code')
    set_globals(tenant_code, 'celery')
    # 查询问数场景有哪些报表id
    scenes = repository.get_data('dap_ai_scene', {'is_deleted': 0}, multi_row=True)
    if not scenes:
        return
    scene_map = {}
    for scene in scenes:
        scene_map[scene.get('id')] = scene.get('dashboard_id')

    from ai.services.dashboard_service import sync_dashboard_by_scene_map
    sync_dashboard_by_scene_map(scene_map)


@celery.task(base=celery_base.BaseTask)
def dashboard_sync_task(**kwargs):
    dashboard_id = kwargs.get('dashboard_id')
    tenant_code = kwargs.get('tenant_code')
    set_globals(tenant_code, 'celery')
    # 查询问数场景有哪些报表id
    scenes = repository.get_data('dap_ai_scene', {'dashboard_id':dashboard_id, 'is_deleted': 0}, multi_row=True)
    if not scenes:
        return
    scene_map = {}
    for scene in scenes:
        scene_map[scene.get('id')] = dashboard_id

    from ai.services.dashboard_service import sync_dashboard_by_scene_map
    sync_dashboard_by_scene_map(scene_map)

