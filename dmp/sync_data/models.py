#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/17.
"""

from base.models import BaseModel


class RuleModel(BaseModel):
    def __init__(self, **kwargs) -> None:
        self.type = ''
        self.sync_type = 'tenant_type'  # 同步类型，表在哪里租户库(tenant_type)还是配置库（config_type）
        self.full_sql = ''
        self.incremental_sql = ''
        self.sync_time = ''
        self.curr_mode = ''
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('type', 'string', {'required': True}))
        rules.append(('full_sql', 'string', {'required': True}))
        rules.append(('incremental_sql', 'string', {'required': True}))
        return rules


class SyncApiModel(BaseModel):
    def __init__(self, **kwargs) -> None:
        self.reset_cron = ''
        self.sync_time = ''
        self.sync_type = ''
        self.code = ''
        self.reset_cron = ''
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        # rules.append(('sign', 'string', {'required': True}))
        if not self.reset_cron:
            rules.append(('sync_type', 'in_range', {'range': [
                'tenant_type_all', 'config_type_all', 'trigger'
            ], 'required': True}))

        return rules
