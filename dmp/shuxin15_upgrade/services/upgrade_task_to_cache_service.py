#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from dmplib.redis import RedisCache

logger = logging.getLogger(__name__)

# 数据升级任务id缓存key
UPGRADE_TASK_ID_CACHE_KEY = 'ind_upgrade_task_id'
# 数据升级最大执行时间，3小时
UPGRADE_EXEC_MAX_TIME = 10800


def upgrade_task_id_write_cache(upgrade_task_id):
    """
    升级任务写入缓存
    :param upgrade_task_id:
    :return:
    """
    redis_cache = RedisCache()
    return redis_cache.add(UPGRADE_TASK_ID_CACHE_KEY, upgrade_task_id, UPGRADE_EXEC_MAX_TIME)


def upgrade_task_id_get_cache():
    """
    从缓存获取任务id
    :return:
    """
    redis_cache = RedisCache()
    storage = redis_cache.get(UPGRADE_TASK_ID_CACHE_KEY)
    if not storage:
        return ""
    if isinstance(storage, bytes):
        storage = storage.decode()
    return storage


def upgrade_task_id_del_cache():
    """
    升级任务id缓存删除
    :return:
    """
    redis_cache = RedisCache()
    return redis_cache.delete(UPGRADE_TASK_ID_CACHE_KEY)
