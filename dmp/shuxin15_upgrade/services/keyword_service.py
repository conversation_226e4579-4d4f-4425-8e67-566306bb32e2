import json
import logging

from dmplib.hug import g
from keywords.repositories import keyword_repositories
from dmplib.utils.strings import seq_id
from shuxin15_upgrade.services.external_upgrade_service import \
    update_upgrade_err_log, update_upgrade_notice_log, update_upgrade_warning_log

logger = logging.getLogger(__name__)

erp_site = {}


def keyword_upgrade(params):
    update_upgrade_notice_log('开始新增数据服务中心本人关键字')
    add_keyword()
    update_upgrade_notice_log('完成新增数据服务中心本人关键字')

    return True, f'关键字升级成功;'  # 关键字升级成功


def add_keyword():
    try:
        keywords = keyword_repositories.get_keyword_by_name_list(['本人'], 'c5eea4aa-25b2-4ba1-b16c-4bbaedc5ff9a')
        if keywords and len(keywords) > 0:
            update_upgrade_notice_log(f'已经存在数据服务中心的本人关键字')
            return
        else:
            from keywords.models import KeywordModel
            from base import repository
            keyword_model = KeywordModel(
                **{'datasource_id': 'c5eea4aa-25b2-4ba1-b16c-4bbaedc5ff9a', 'keyword_name': '本人', 'keyword_type': '1',
                   'data_type': '文本',
                   'sql_text': 'SELECT UserGUID FROM data_wide_mdm_MyUser WHERE UserCode= [key:本人账号]',
                   'is_system': 1})
            # 新增关键字
            keyword_model.id = seq_id()
            repository.add_model(keyword_model.__table__, keyword_model)
    except Exception as e:
        update_upgrade_err_log(f'新增数据服务中心本人关键字，报错：' + str(e))
        logger.error(str(e))
