import json
import logging

from shuxin15_upgrade.services.external_upgrade_service import \
    update_upgrade_err_log, update_upgrade_notice_log, update_upgrade_warning_log

logger = logging.getLogger(__name__)

dataset_upgrade_page_size = 20  # 数据集升级分页大小

erp_site = {}


def dataset_upgrade(params):
    from dataset.services import dataset_service
    update_upgrade_notice_log('开始数据集升级')

    datasets = dataset_service.delete_unused_system_dataset()
    update_upgrade_notice_log(f'删除数据集：{json.dumps(datasets)}')

    # 删除系统分发数据集文件夹下文件夹数据集下的空目录
    folders = dataset_service.delete_dataset_tmp_folder()
    update_upgrade_notice_log(f'删除文件夹：{json.dumps(folders)}')

    return True, f'数据集升级成功;'  # 数据升级成功
