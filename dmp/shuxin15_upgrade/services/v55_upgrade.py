import json

from shuxin15_upgrade.services.chart_check_service import check_chart
from shuxin15_upgrade.services.dashboard_service import delete_dashboard_sys_tmp_folder
from shuxin15_upgrade.services.dataset_service import dataset_upgrade
from shuxin15_upgrade.services.datasource_service import update_mysoftnewerp_shuxin, update_storage_type, \
    update_is_data_cloud_1_5_enabled
from shuxin15_upgrade.services.external_upgrade_service import update_upgrade_notice_log
from shuxin15_upgrade.services.role_folder_service import role_folder_sql_exec
from shuxin15_upgrade.services.upgrade_application_function_service import upgrade_application_function


def v55_dmp_upgrade(_):
    return

def v55_biz_upgrade(params):
    ### 数据源部分升级
    update_upgrade_notice_log('开始升级为数见1.5标识')
    update_is_data_cloud_1_5_enabled()
    update_upgrade_notice_log('完成升级为数见1.5标识')

    ### 数据集升级
    update_upgrade_notice_log('开始数据集升级')
    rv, msg = dataset_upgrade(dict())
    update_upgrade_notice_log(f'完成数据集升级: {rv}, {msg}')

    ### 看板升级
    update_upgrade_notice_log('开始删除看板中系统级空文件夹')
    folders = delete_dashboard_sys_tmp_folder()
    update_upgrade_notice_log(f'删除看板中系统级空文件夹：{json.dumps(folders)}')

    ### 门户替换
    update_upgrade_notice_log('开始门户替换')
    rv, msg, _ = upgrade_application_function(dict())
    update_upgrade_notice_log(f'完成门户替换: {rv}, {msg}')

    ### 目录权限补充
    update_upgrade_notice_log('开始目录权限补充')
    rv, msg = role_folder_sql_exec()
    update_upgrade_notice_log(f'完成目录权限补充: {rv}, {msg}')

    ### 升级后报表取数检查
    update_upgrade_notice_log('开始检查报表取数')
    rv, msg = check_chart(params, check_type='after', is_active_report=True)
    update_upgrade_notice_log(f'完成检查报表取数: {rv}, {msg}')
    return