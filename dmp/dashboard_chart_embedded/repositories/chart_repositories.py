#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : token_services.py
# @Author: guq  
# @Date  : 2021/11/19
# @Desc  :

from base import repository


def get_dashboard_ids_by_chart_ids(chart_ids: list):
    sql = "select `dashboard_id` from dap_bi_dashboard_chart where `id` in %(id)s"
    result = repository.get_data_by_sql(sql, params={'id': chart_ids})
    return [r.get('dashboard_id') for r in (result or []) if r.get('dashboard_id')]


def get_dashboard_id_by_chart_id(chart_id):
    sql = "select `dashboard_id` from dap_bi_dashboard_chart where `id` = %(id)s"
    result = repository.get_data_scalar_by_sql(sql, params={'id': chart_id})
    return result or ''


def get_released_chart_and_dashboard_data():
    # sql = "select dc.dashboard_id, dc.id as chart_id, dc.chart_code as chart_component_code, " \
    #       "dc.name as chart_name, dd.name as dashboard_name from dashboard_released_snapshot_dashboard as dd " \
    #       "left join dashboard_released_snapshot_chart as dc on dd.id = dc.dashboard_id " \
    #       "where dd.status = 1 and dd.type_access_released = 0 and dd.`type`='FILE' " \
    #       "order by dd.created_on desc"
    sql = "select dc.dashboard_id, dc.id as chart_id, dc.chart_code as chart_component_code, " \
          "dc.name as chart_name, (select name from dap_bi_dashboard_released_snapshot_dashboard where id = dc.dashboard_id limit 1) " \
          "as dashboard_name from dap_bi_dashboard_released_snapshot_chart as dc " \
          "order by dc.created_on desc"
    result = repository.get_data_by_sql(sql, params={})
    return result or []
