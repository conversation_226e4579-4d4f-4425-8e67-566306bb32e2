#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
test application openapi
"""

import json
import logging
import unittest
from tests.base import BaseTest
from app_menu.services import application_openapi_service, application_auth_service

logger = logging.getLogger(__name__)


class TestApplicationOpenapi(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='fangzhiadmin_test', account='admin')

    def test_get_portal_list_for_openapi(self):
        kwargs = {"user_account": "test", "platform": "mobile_screen", "enable": 1, "created_by": ""}
        result = application_openapi_service.get_portal_list_for_openapi(**kwargs)
        print(result)

    def test_get_relational_dashboards(self):
        portal_id = "39e7293c-ac45-f084-9e5d-cd08ea51ca06"
        result = application_openapi_service.get_relational_dashboards(portal_id=portal_id)
        print(result)

    def test_upload_auth(self):
        auth_id = "22b11db4-e907-4f1f-8835-b9daab6e1f23"
        portal_id = "39f44703-665b-35eb-549f-9b6f1a03d915"
        dashboard_auths = [
            {"biz_code": "6e9716ccfc7348ed81c9a2cb03449a2d", "auth": "view,download"},  # A
            {"biz_code": "8b9854c0c8d94ea8b02378ba48989897", "auth": "download"},  # B
            {"biz_code": "65e1fc9c33614c6f87b3341f22159641", "auth": "view"},  # C
            {"biz_code": "464113e345a148cd8b2478ebb4261877", "auth": ""},  # F
            {"biz_code": "2837ede1b32847b690105433e0d88813", "auth": ""},  # D
            {"biz_code": "4c2b51bdef1d4dbab4f71f486813d8a3", "auth": ""},  # E
        ]
        result = application_auth_service.upload_auth(auth_id, portal_id, dashboard_auths)
        print(result)


if __name__ == '__main__':
    # unittest.main()
    s = unittest.TestSuite()
    s.addTest(TestApplicationOpenapi("test_upload_auth"))
    runner = unittest.TextTestRunner()
    runner.run(s)
