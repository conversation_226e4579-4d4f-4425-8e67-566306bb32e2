#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import copy
import json
import uuid
from collections import defaultdict
from hashlib import md5
from urllib.parse import quote
import os
import zipfile
import shutil

import loguru
import requests
import hug
import traceback

from components.oss import OSSFileProxy
from components.redis_utils import stale_cache

from dmplib.hug import g
from dmplib import config
from dmplib.saas.project import get_db
from base.enums import ApplicationType, AddFuncType, DataSourceType

from base import repository
from app_menu.models import FunctionModel
from components.utils import debugger_and_logger, compress_folder_to_zip
from imports.services import import_helper
from imports.services.import_helper import build_record_tree_v1

local_debugger = debugger_and_logger(__name__, prefix='[更新中心] ')


# def developer_login(request: hug.Request, response: hug.Response, code, account, redirect):
#     """
#     提供给建模的开发者登录接口，并可以跳转指定页面
#     """
#     from user.services.developer_service import Developer
#
#     is_exists = repository.get_column(
#         'developer_auth_user',
#         {'developer_account': account, "code": code}, ['code'],
#         from_config_db=True
#     ) or []
#     host = (config.get('Domain.dmp') or '').rstrip('/')
#
#     if is_exists:
#         # 已经绑定了该租户开发者，直接登录
#         Developer().change_developer_login_tenant(request, response, f"developer_{account}", code)
#         # hug.redirect.to(redirect)
#         url = f'{host}{redirect}'
#     else:
#         # 没有绑定了该租户开发者，直接登录，暂时不支持跳转指定地址
#         url = """https://bi.mypaas.com/static/rdc-callback.html?originUrl={originUrl}&config={%22authority%22:%22https://auth.mingyuanyun.com%22,%22client_id%22:%22dmp%22,%22redirect_uri%22:%22https://bi.mypaas.com/static/rdc-callback.html%22,%22response_type%22:%22code%22,%22scope%22:%22defaultApi%22}"""
#         url = url.replace('{originUrl}', host)
#         # hug.redirect.to(url)
#     return is_exists, url

def get_account_ref_codes(account):
    """
    获取开发者租户信息
    """
    if not account:
        return []
    sql = """
    select p.code as tenant_code , p.name as tenant_name from dap_bi_developer_auth_user as dau 
    left join dap_p_tenant as p  on p.code = dau.code
    join dap_bi_tenant_setting as s on p.code = s.code 
    where ( developer_account = %(account)s  or  account = %(account)s )
    and s.is_rdc_auth = 1
    """
    return repository.get_data_by_sql(sql, params={'account': account}, from_config_db=True) or []


# def redirect_dashboard_edit_page(dashboard_id):
#     """
#     跳转到报表的编辑页面
#     """
#     # 先判断是哪个类型的报表
#     dashboard_ids, large_screen_ids, ppt_ids, report_center_ids, application_ids = classify_dashboard_ids(
#         [dashboard_id])
#     domain = config.get('Domain.dmp', '').rstrip('/')
#
#     if dashboard_ids:
#         # 属于仪表板
#         page_url = f'{domain}/dataview/design/{dashboard_ids[0]}'
#     elif large_screen_ids:
#         # 大屏
#         page_url = f'{domain}/dataview/design/{large_screen_ids[0]}'
#     elif ppt_ids:
#         # 在线报告
#         page_url = f'{domain}/slide/designer/{ppt_ids[0]}'
#     elif report_center_ids:
#         # 复杂报表
#         data = _get_activate_reports(report_center_ids)
#         name = data[0].get('name') or '' if data else ''
#         redirect_uri = f'/index.html#/designer-host?id={report_center_ids[0]}&is_report_center=1&name={name}'
#         page_url = f'{domain}/api/report_center/go_app?application_type=5&redirect_uri={quote(redirect_uri)}'
#     else:
#         local_debugger("未知的报表类型！")
#         return "未知的报表类型！"
#
#     local_debugger(f"报表跳转地址： {page_url}")
#     hug.redirect.to(page_url)


# def get_all_report(kwargs):
#     """
#     获取所有的报表
#     """
#     from dashboard_chart.services import dashboard_openapi_service
#     from ppt import external_service
#
#     dashboard = dashboard_openapi_service.get_dashboard_list_for_openapi('dashboard', **kwargs)
#     large_screen = dashboard_openapi_service.get_dashboard_list_for_openapi('large_screen', **kwargs)
#
#     ppt = get_ppt_list()
#
#     active_report = get_activate_report_list()
#
#     # try:
#     #     active_report = external_service.get_active_report_list(**kwargs)
#     # except:
#     #     logger.error(f"获取active_report报错：{traceback.format_exc()}")
#     #     active_report = []
#     return dashboard + large_screen + active_report + ppt


# def filter_dashboard_status(data, filter_status):
#     """
#
#     """
#     result = []
#     for row in data:
#         if row.get('type') == 'FILE' and row.get('status') == filter_status:
#             continue
#         result.append(row)
#     return result


def get_export_dashboard_tree(business_tags, name):
    """
    获取导出的仪表版文件夹的树形结构
    """
    from dashboard_chart.services import dashboard_service

    # 先获取所有仪表版文件夹
    # folders = repository.get_data(
    #     'dashboard', conditions={'application_type': ApplicationType.Dashboard.value, 'type': 'FOLDER'},
    #     multi_row=True) or []

    # 在获取所有的仪表版
    # ?include_child_file=0&include_hd_report=1&include_external_report=1&parent_id=&order_by=mtime&file_type=file&status=&business_tags=06fb6a69-7318-49fb-b4df-72dd9ae5cf53&reverse=1

    dashboard_kwargs = {
        'include_child_file': 0,
        'include_hd_report': 0,
        'include_external_report': 0,
        'parent_id': '',
        'order_by': 'mtime',
        'file_type': 'file',
        'business_tags': business_tags,
        'reverse': 1,
        'application_type': ApplicationType.Dashboard.value,
    }
    dashboards = dashboard_service.get_dashboard_list(**dashboard_kwargs)
    dashboards = dashboards.get('tree') or []
    # dashs = preformat_data(folders + dashboards)
    result = preformat_data(dashboards, name)
    # result = _deal_data_to_tree(dashs)
    return result


def get_export_big_screen_tree(business_tags, name):
    """
    获取导出的大屏文件夹的树形结构
    """
    from dashboard_chart.services import dashboard_service

    # 先获取所有仪表版文件夹
    # folders = repository.get_data(
    #     'dashboard', conditions={'application_type': ApplicationType.LargeScreen.value, 'type': 'FOLDER'},
    #     multi_row=True) or []

    # 在获取所有的大屏
    dashboard_kwargs = {
        'include_child_file': 0,
        'include_hd_report': 0,
        'include_external_report': 0,
        'parent_id': '',
        'order_by': 'mtime',
        'file_type': 'file',
        'business_tags': business_tags,
        'reverse': 1,
        'application_type': ApplicationType.LargeScreen.value,
    }
    dashboards = dashboard_service.get_large_screen_list(**dashboard_kwargs)
    dashboards = dashboards.get('tree') or []
    # dashs = preformat_data(folders + dashboards)
    result = preformat_data(dashboards, name)
    # result = _deal_data_to_tree(dashs)
    return result


def get_export_activate_report_tree(request, business_tags, name):
    """
    获取导出的复杂报告的树形结构
    """
    from report_center.services.report_service import ReportService

    # 先获取所有仪表版文件夹
    # folders = repository.get_data(
    #     'dashboard',
    #     conditions={'application_type': [ApplicationType.ActiveReport.value, ApplicationType.SimpleReport.value],
    #                 'type': 'FOLDER'},
    #     multi_row=True) or []

    # 在获取所有的大屏
    dashboard_kwargs = {
        'include_child_file': 0,
        'include_hd_report': 0,
        'include_external_report': 0,
        'parent_id': '',
        'order_by': 'mtime',
        'file_type': 'report_center',
        'business_tags': business_tags,
        'reverse': 1,
        'application_type': ApplicationType.ActiveReport.value,
    }
    report_service = ReportService(request, **dashboard_kwargs)
    rs = report_service.get_dashboard_list()
    dashboards = rs.get('tree') or []
    # dashs = preformat_data(folders + dashboards)
    result = preformat_data(dashboards, name)
    # result = _deal_data_to_tree(dashs)
    return result


def get_export_ppt_tree(request, business_tags, name):
    """
    获取导出数据报告的树形结构
    """
    from report_center.services.data_report_service import DataReportService
    # 先获取所有仪表版文件夹
    # folders = repository.get_data(
    #     'dashboard',
    #     conditions={'application_type': [ApplicationType.DataReporting.value],
    #                 'type': 'FOLDER'},
    #     multi_row=True) or []
    #
    # parent_id:
    # order_by: mtime
    # file_type: file
    # status:
    # business_tags:
    # reverse: 1
    #
    dashboard_kwargs = {
        # 'include_child_file': 0,
        # 'include_hd_report': 0,
        # 'include_external_report': 0,
        'parent_id': '',
        'order_by': 'mtime',
        'file_type': 'file',
        'business_tags': business_tags,
        'reverse': 1,
    }
    rs = DataReportService.get_list(**dashboard_kwargs)
    dashboards = rs.get('tree') or []
    # dashs = preformat_data(folders + dashboards)
    result = preformat_data(dashboards, name)
    # result = _deal_data_to_tree(dashs)
    return result


def get_export_application_data(business_tags, name):
    """
    获取导出数据门户的树形结构
    """
    from app_menu.services import application_service
    from app_menu.models import ApplicationQueryModel

    app_kwargs = {
        'include_child_file': 0,
        'include_hd_report': 0,
        'include_external_report': 0,
        'parent_id': '',
        'order_by': 'mtime',
        'business_tags': business_tags,
        'reverse': 1,
        'enable': '1,2',
        'skip_paging_flag': 1
    }

    if isinstance(business_tags, str):
        business_tags = set([s for s in business_tags.split(',') if s])
    rs = application_service.get_application_list(ApplicationQueryModel(**app_kwargs))
    result = rs.get('items') or []
    result = preformat_data(result, name)
    if not business_tags:
        return result
    else:
        # 取有交集的主题标签门户
        return [d for d in result if set([i.get('tag_id') for i in d.get('tag_list', [])]) & business_tags]


def get_export_feeds_data(business_tags, name):
    """
    获取导出简讯的树形结构
    """
    from feed.models import FeedsQueryModel
    from feed.services import dashboard_feeds_service, msg_get_data_service

    kwargs = {
        'page': 1,
        'page_size': 1000000,
        'type': '简讯订阅',
        'parent_id': '',
        'business_tags': business_tags,
        'skip_paging_flag': 1
    }

    if isinstance(business_tags, str):
        business_tags = set([s for s in business_tags.split(',') if s])
    rs = dashboard_feeds_service.get_dashboard_feeds_list(FeedsQueryModel(**kwargs)).get_result_dict()
    result = rs.get('items') or []
    for one in result:
        one['name'] = msg_get_data_service.clear_title_html(one.get('subject_email', ''))
    result = preformat_data(result, name)
    if not business_tags:
        return result
    else:
        # 取有交集的主题标签门户
        return [d for d in result if set([i.get('tag_id') for i in d.get('tag_list', [])]) & business_tags]


def preformat_data(data_list: list, name):
    """
    将要返回的数据提前进行格式处理
    """
    fields = [
        'id',
        'name',
        'platform',
        'level_code',
        'type',
        'parent_id',
        'created_on',
        'modified_on',
        'created_by',
        'modified_by',
        'application_type',
        'new_layout_type',
        'status',
        'tag_list',
    ]
    accounts = [d.get('created_by') for d in data_list] + [d.get('modified_by') for d in data_list]
    users_info = get_user_info(accounts)
    result = []
    for data in data_list:
        # 名字过滤搜索
        data_name = data.get('name') or ''
        new_data = {}
        if not name:
            for field in fields:
                if field in data:
                    new_data[field] = data[field]
            # 格式化人
            new_data['created_by'] = _format_account(new_data.get('created_by'), users_info)
            new_data['modified_by'] = _format_account(new_data.get('modified_by'), users_info)
            result.append(new_data)
        else:
            if name in data_name:
                for field in fields:
                    if field in data:
                        new_data[field] = data[field]
                # 格式化人
                new_data['created_by'] = _format_account(new_data.get('created_by'), users_info)
                new_data['modified_by'] = _format_account(new_data.get('modified_by'), users_info)
                result.append(new_data)
    return result


def get_all_report_tree(request, kwargs):
    """
    获取所有的报表的树形，直接构造完成
    """
    from user.services import user_service
    business_tags = kwargs.get('business_tags') or ''
    name = kwargs.get('name') or ''
    # TODO 开发者使用管理员
    group_ids = user_service.get_cur_user_group_ids() or []
    if not hasattr(g, 'group_ids'):
        g.group_ids = group_ids
    # 下面都是调用的是数见的列表接口
    # 为什么这么做，是为了数据权限过滤
    dashboard_tree = get_export_dashboard_tree(business_tags, name)
    large_screen_tree = get_export_big_screen_tree(business_tags, name)
    active_report_tree = get_export_activate_report_tree(request, business_tags, name)
    ppt_tree = get_export_ppt_tree(request, business_tags, name)
    application_data = get_export_application_data(business_tags, name)
    feeds_data = get_export_feeds_data(business_tags, name)

    data = [
        __build_mock_group_tree('仪表板', 'DASHBOARD', dashboard_tree),
        __build_mock_group_tree('酷炫大屏', 'LARGE_SCREEN', large_screen_tree),
        __build_mock_group_tree('数据报告', 'PPT', ppt_tree),
        __build_mock_group_tree('复杂报表', 'ACTIVATE_REPORT', active_report_tree),
        __build_mock_group_tree('数据门户', 'APPLICATION', application_data),
        __build_mock_group_tree('数据订阅', 'FEED', feeds_data),
    ]
    return data


def get_dashboard_ref_datasets(dashboard_ids: list):
    """
    获取报表所关联的数据集信息
    """
    dashboard_ids, large_screen_ids, ppt_ids, report_center_ids, application_ids, feed_ids = classify_dashboard_ids(
        dashboard_ids)
    # datasets = get_relate_dataset_info(dashboard_ids, large_screen_ids, ppt_ids, report_center_ids)

    datasource_ids = set()
    all_datasets = defaultdict(dict)
    # 根据报告获取数据集、数据源信息
    # 仪表板
    for dashboard_id in dashboard_ids:
        datasets = get_dmp_dashboard_dataset([dashboard_id], include_child=True)
        for dataset in datasets:
            datasource_id = _get_datasource_id(dataset)
            if datasource_id:
                datasource_ids.add(datasource_id)
        all_datasets[dashboard_id]['datasets'] = datasets

    # 大屏
    for dashboard_id in large_screen_ids:
        datasets = get_dmp_dashboard_dataset([dashboard_id], include_child=True)
        for dataset in datasets:
            datasource_id = _get_datasource_id(dataset)
            if datasource_id:
                datasource_ids.add(datasource_id)
        all_datasets[dashboard_id]['datasets'] = datasets

    # 数据报告
    for dashboard_id in ppt_ids:
        datasets = get_ppt_dataset([dashboard_id])
        for dataset in datasets:
            datasource_id = _get_datasource_id(dataset)
            if datasource_id:
                datasource_ids.add(datasource_id)
        all_datasets[dashboard_id]['datasets'] = datasets

    # 复杂报告
    for dashboard_id in report_center_ids:
        datasets = get_activate_report_dataset([dashboard_id])
        for dataset in datasets:
            datasource_id = _get_datasource_id(dataset)
            if datasource_id:
                datasource_ids.add(datasource_id)
        all_datasets[dashboard_id]['datasets'] = datasets

    # 简讯
    for feed_id in feed_ids:
        datasets = get_feeds_dataset([feed_id])
        for dataset in datasets:
            datasource_id = _get_datasource_id(dataset)
            if datasource_id:
                datasource_ids.add(datasource_id)
        all_datasets[feed_id]['datasets'] = datasets
    # TODO 门户的数据集

    datasources = _get_datasource_info(list(datasource_ids))
    datasources_map = {s.get('id'): s for s in datasources}

    result = _format_ref_dataset(all_datasets, datasources_map)

    return result


def _format_ref_dataset(all_datasets: dict, datasources_map):
    """
    格式化报表相关的返回数据集信息
    """
    dataset_fields = ['id', 'name', 'modified_by', 'modified_on', 'created_by', 'created_on']
    datasource_fields = ['id', 'name', 'modified_by', 'modified_on', 'created_by', 'created_on']
    for dashboard_id, data in all_datasets.items():
        datasets = data.get('datasets') or []
        new_datasets = []
        new_datasources = []
        shuxin15_models = []
        for dataset in datasets:
            datasource_id = _get_datasource_id(dataset)
            dataset_id = dataset.get('id')
            datasource = datasources_map.get(datasource_id) or {}
            new_dataset = {key: val for key, val in dataset.items() if key in dataset_fields}
            new_datasource = {key: val for key, val in datasource.items() if key in datasource_fields}
            if new_dataset:
                new_datasets.append(new_dataset)
            if new_datasource:
                new_datasources.append(new_datasource)
            datasource_type = datasource.get('type')
            if datasource_type == DataSourceType.MysoftShuXin15.value:
                models = repository.get_columns('dap_bi_dataset_used_table', conditions={'dataset_id': dataset_id},
                                                col_name='table_name')
                if models:
                    shuxin15_models.extend(models)

        # 去重
        all_datasets[dashboard_id]['datasets'] = __unique_list_by_id(new_datasets)
        all_datasets[dashboard_id]['datasources'] = __unique_list_by_id(new_datasources)
        all_datasets[dashboard_id]['models'] = shuxin15_models
    return all_datasets


def __unique_list_by_id(dict_list):
    unique_dict = {d.get('id'): d for d in dict_list}
    unique_list = list(unique_dict.values())
    return unique_list


def _get_datasource_id(dataset):
    try:
        content = json.loads(dataset.get('content'))
    except Exception as e:
        content = {}
    datasource_id = content.get('data_source_id')
    return datasource_id


def _get_datasource_info(ids):
    if not ids or not isinstance(ids, list):
        return []
    return repository.get_list('dap_m_data_source', conditions={'id': ids})


def _deal_data_to_tree(data):
    # 过滤未发布的报表
    # c_data = filter_dashboard_status(data, 0)
    # 构建树形
    tree = build_record_tree_v1(data)
    # 移除空文件夹
    tree = remove_empty_folders(tree)

    return tree


def __build_mock_group_tree(name, id, data_tree):
    data = {
        'id': id,
        'parent_id': '',
        'name': name,
        'type': 'FOLDER',
    }
    for row in data_tree:
        row['parent_id'] = id
    data['sub'] = data_tree
    return data


def remove_empty_folders(data):
    for i in range(len(data) - 1, -1, -1):
        sub = data[i].get('sub') or []
        if data[i]["type"] == "FOLDER" and len(sub) == 0:
            data.pop(i)
        elif len(sub) > 0:
            remove_empty_folders(sub)
            # 检查删除子文件夹后，当前文件夹是否为空
            if len(sub) == 0 and data[i]["type"] == "FOLDER":
                data.pop(i)
    return data


def get_relate_dataset_info(dashboard_ids, large_screen_ids, ppt_ids, activate_report_ids, feed_ids):
    """
    获取各种报表关联的数据集
    """
    result = {}
    result['ppt'] = get_ppt_dataset(ppt_ids)
    result['activate_report'] = get_activate_report_dataset(activate_report_ids)
    result['dashboard'] = get_dmp_dashboard_dataset(dashboard_ids, include_child=True)
    result['large_screen'] = get_dmp_dashboard_dataset(large_screen_ids, include_child=True)
    result['feed'] = get_feeds_dataset(feed_ids)
    return result


def duplicate_all_datasets(datasets):
    """
    去重数据集
    """
    dash_datasets = [d for d in datasets.get('dashboard', []) if d]
    large_datasets = [d for d in datasets.get('large_screen', []) if d]
    activate_datasets = [d for d in datasets.get('activate_report', []) if d]
    ppt_datasets = [d for d in datasets.get('ppt', []) if d]
    feed_datasets = [d for d in datasets.get('feed', []) if d]

    dup = set()
    result = []
    for dataset in (dash_datasets + large_datasets + activate_datasets + ppt_datasets + feed_datasets):
        did = dataset.get('id')
        if did in dup:
            continue
        result.append(dataset)
        dup.add(did)
    return result


def get_ppt_dataset(ids):
    """
    获复杂报表的关联数据集
    """
    if not ids or not isinstance(ids, list):
        return []
    from exports.api_route import get_biz_relation_dataset
    params = {"biz_type": "ppt", "ids": ids}
    _, _, ref_ids = get_biz_relation_dataset(**params)
    return get_dataset(ref_ids)


def get_feeds_dataset(ids):
    """
    获取简讯的关联数据集
    """
    if not ids or not isinstance(ids, list):
        return []
    data = repository.get_data('dap_bi_dashboard_email_subscribe', conditions={'id': ids}, multi_row=True) or []
    if not data:
        return []
    result_ids = []
    for f in data:
        try:
            dataset_ids = json.loads(f.get('dataset_ids'))
            result_ids.extend(dataset_ids)
        except:
            pass
    return get_dataset(result_ids)


def get_activate_report_dataset(ids):
    """
    获复杂报表的关联数据集
    """
    if not ids or not isinstance(ids, list):
        return []
    from exports.api_route import get_biz_relation_dataset

    all_ids = get_all_report_ids_include_child(ids)
    params = {"biz_type": "report_center", "ids": all_ids}
    _, _, ref_ids = get_biz_relation_dataset(**params)
    return get_dataset(ref_ids)


def get_all_report_ids_include_child(ids: list):
    from exports.services.export_service import get_all_dashboard_ids

    all_ids = get_all_dashboard_ids(ids)
    return all_ids


def get_dataset(ids):
    if not ids or not isinstance(ids, list):
        return []
    ids = [i for i in ids if i]
    data = repository.get_data('dap_bi_dataset', conditions={'id': ids}, multi_row=True) or []
    return data


def get_dmp_dashboard_dataset(ids, include_child=False):
    """
    获数见报表的关联数据集
    """
    if not ids or not isinstance(ids, list):
        return []
    if not include_child:
        if len(ids) == 1:
            p = str(tuple(ids)).replace(',)', ')')
        else:
            p = str(tuple(ids))
        sql = """
        select * from dap_bi_dataset where id in
        (SELECT DISTINCT source from dap_bi_dashboard_chart WHERE dashboard_id  in %s and source != '')
        """ % p
        return repository.get_data_by_sql(sql, params=()) or []
    else:
        from exports.services import export_service
        dataset_ids = export_service.get_dashboard_relation_dataset(ids)
        if not dataset_ids:
            return []
        return repository.get_data('dap_bi_dataset', conditions={'id': dataset_ids}, multi_row=True) or []


def get_export_status(export_id):
    """
    获导出的状态信息
    """
    data = repository.get_one('dap_bi_exports', conditions={'id': export_id}) or {}
    rsp = {
        "id": data['id'],
        "status": data['status'],
        "message": data['message'],
        "url": data['url'],
        "created_on": data['created_on'],
        "modified_on": data['modified_on'],
    }
    if data['status'] == '成功':
        # 给压缩包添加额外的一层文件夹
        fname = data['url'].split('/')[-1]
        tmp_folder = f'/tmp/{str(uuid.uuid4())}'
        dist_folder = f'{tmp_folder}/dist'
        os.makedirs(tmp_folder, exist_ok=True)
        old_zip = f'{tmp_folder}/old-{fname}'
        new_zip = f'{dist_folder}/{fname}'
        download_file(data['url'], old_zip)
        repackage_zip(dist_folder, old_zip, new_zip, 'mysql')
        with open(new_zip, "rb") as fd:
            new_file_name = f'{md5(fname.encode()).hexdigest()}.zip'
            oss_file_url = OSSFileProxy().upload(fd, file_name=new_file_name, root='pubserver-dist',
                                                 key=f'pubserver-dist/{new_file_name}')
            rsp['url'] = oss_file_url
        # 删除临时解压目录
        remove_files(tmp_folder)
    return rsp


def remove_files(f):
    try:
        # 删除临时解压目录
        shutil.rmtree(f)
    except:
        pass


def repackage_zip_base_dashboard_ids(task_id, oss_url, dashboard_ids, dashboard_datasource):
    """
    根据所选的dmp的报表id。找到相关联的资源，重新打包成一个新的zip包
       "dashboard_datasource": [
        {
            "op": "update",
            "from": {
                "id": "90c87348-51d3-11ed-98ba-b29cedae9795",
                "name": "等要今我速体"
            },
            "to": {
                "id": "11111",
                "name": "务员风头才它万"
            }
        }
    """
    from open_api.services.pubserver_import_parser_service import ImportParser
    from data_source.repositories.data_source_repository import get_data_source_by_id
    # 没有勾选任何报表，就是全选
    if not dashboard_ids:
        return oss_url

    parser = ImportParser(task_id, only_shuxin15_datasource=False)
    result = parser.parse_report_dependency(dashboard_ids)
    dashboard_ids = [i.get('id') for i in result.get('items') or [] if i.get('id')]
    dataset_ids = [
        d.get('id') for i in result.get('items') or []
        for d in i.get('dependencies') or [] if d.get('dependency_type') == '数据集'
    ]
    keywords = [f'{i}.json' for i in dashboard_ids + dataset_ids]
    # 处理数据源映射关系
    dashboard_datasource_mapping = {
        i.get('from', {}).get('id'): get_data_source_by_id(id_=i.get('to', {}).get('id')) or {}
        for i in dashboard_datasource
        if i.get('op', '').lower() == 'update'
    }
    loguru.logger.error(f"重新打包的数据：dashboard_ids：{dashboard_ids}, dataset_ids: {dataset_ids}, keywords: {keywords}, dashboard_datasource_mapping: {dashboard_datasource_mapping}")
    fname = os.path.basename(oss_url.split('?')[0])
    tmp_folder = f'/tmp/{str(uuid.uuid4())}'
    new_folder = f'{tmp_folder}/new'
    os.makedirs(tmp_folder, exist_ok=True)
    old_zip = f'{tmp_folder}/old-{fname}'
    new_zip = f'{tmp_folder}/new-{fname}'

    try:
        download_file(oss_url, old_zip)

        # 1. 解压现有的 ZIP 文件
        with zipfile.ZipFile(old_zip, 'r') as zip_ref:
            zip_ref.extractall(new_folder)

        # 2. 开始根据关联资源过滤过滤信息
        filter_file_base_ref(
            new_folder,
            target_folders=[
                'dashboards',
                'datasets',
                'large_screens',
                'applications',
                'data_reporting',
                'feeds',
                'ppt',
            ], keywords=keywords
        )

        # 2.1 处理数据集的内容，将数据集的数据源替换成要更新的数据源id
        datasets_folders = os.path.join(new_folder, 'datasets')
        if os.path.exists(datasets_folders):
            dataset_files = [i for i in os.listdir(datasets_folders) if i.endswith('.json')]
        else:
            dataset_files = []
        deal_dataset_file_datasource(datasets_folders, dataset_files, dashboard_datasource_mapping)

        # 3. 重新打包成zip
        compress_folder_to_zip(new_folder, new_zip)

        # 4. 上传oss
        with open(new_zip, "rb") as fd:
            new_file_name = f'{md5(fname.encode()).hexdigest()}.zip'
            oss_file_url = OSSFileProxy().upload(
                fd, file_name=new_file_name, root='pubserver-dist',
                key=f'pubserver-dist/{new_file_name}'
            )
            local_debugger(f"重新打包最后的oss_url：{oss_file_url}", level='error')
            return oss_file_url
    finally:
        # 删除临时解压目录
        remove_files(tmp_folder)


def deal_dataset_file_datasource(datasets_folders, dataset_files, dashboard_datasource_mapping):
    """
    处理数据集中数据的数据源
    """
    local_debugger(f"dashboard_datasource_mapping：{json.dumps(dashboard_datasource_mapping)}", level='error')
    for file in dataset_files:
        fp = os.path.join(datasets_folders, file)
        with open(fp, 'r') as f:
            datasets = [json.loads(f.read())]
        datasets, has_replace = prepare_datasets(datasets, dashboard_datasource_mapping, g.code)
        if has_replace:
            with open(fp, 'w') as f:
                f.write(json.dumps(datasets[0], ensure_ascii=False))
                local_debugger(f"重写了数据集文件：{fp}", level='error')


def filter_file_base_ref(root_directory, target_folders, keywords):
    """
    根据资源引用过滤包资源
    """
    local_debugger(f"资源文件保留关键字：{keywords}", level='error')
    for root, dirs, files in os.walk(root_directory):
        if any(target_folder in root for target_folder in target_folders):
            for file in files:
                file_path = os.path.join(root, file)
                if not any(keyword in file for keyword in keywords):
                    try:
                        os.remove(file_path)
                        local_debugger(f"删除了资源文件：{file_path}", level='error')
                    except Exception as e:
                        local_debugger(f"删除资源文件失败：{file_path}， 原因： {str(e)}", level='error')
                else:
                    local_debugger(f"保留了资源文件：{file_path}", level='error')


def prepare_datasets(datasets: list, data_source: dict, project_code):
    """
    复制admin的代码
    """
    if not datasets:
        return
    data_source = check_dataset_source(datasets, data_source, project_code)
    has_replace = False
    for dataset in datasets:
        try:
            if not dataset.get("data_source"):
                continue
            source_id = dataset.get('data_source', {}).get('id', None)
            if source_id and data_source.get(source_id, None):
                dataset['data_source'] = data_source.get(source_id)
            content = json.loads(dataset['dataset']['content']) if dataset.get('dataset', {}).get('content', None) \
                else dict()
            data_source_id = content.get('data_source_id', None)
            if data_source_id and data_source.get(data_source_id, None):
                new_data_source_id = data_source.get(data_source_id).get('id')
                content['data_source_id'] = new_data_source_id
                local_debugger(f"替换了数据源：{data_source_id} to {new_data_source_id}", level='error')
                dataset['dataset']['content'] = json.dumps(content)
                has_replace = True
        except Exception as e:
            loguru.logger.error(f"{traceback.format_exc()}")
            local_debugger(f"数据源处理失败：{str(e)}", level='error')
            continue
    return datasets, has_replace


def check_dataset_source(datasets, datasource, project_code):
    """
    复制admin的代码
    """
    source_type = 'MysoftNewERP'
    with get_db(project_code) as db:
        for dataset in datasets:
            data_source = dataset.get('data_source') or {}
            if data_source.get('type') == source_type:
                data_source_id = data_source.get('id')
                conn_str = data_source.get('conn_str')
                try:
                    app_code = json.loads(conn_str).get('AppLevelCode')
                except:
                    app_code = ''
                # 是否已经设置了数据源映射，找到了映射直接跳过
                if datasource.get(data_source_id):
                    continue
                # 查询导入的租户中是否含有对应类型的数据源
                result = db.query_one(
                    "select id from dap_m_data_source where id = %(id)s and type='MysoftNewERP' and conn_str like %(app_code)s",
                    {'id': data_source_id, 'app_code': f'%{app_code}%'}
                )
                if result:
                    continue
                result = db.query_one(
                    'select * from dap_m_data_source where conn_str like %(id)s and type = %(type)s',
                    {'id': f'%{app_code}%', 'type': source_type}
                )
                if result:
                    datasource[data_source_id] = result
    return datasource


def deal_import_tag_relation(task_id, dashboard_ids, subject):
    from open_api.services.pubserver_import_parser_service import ImportParser
    # 没有勾选任何报表，就是全选

    parser = ImportParser(task_id, only_shuxin15_datasource=False)
    result = parser.parse_report_dependency(dashboard_ids)

    items = result.get('items') or []
    update_mapping = {
        i.get('from', {}).get('subject_id'): i.get('to', {})
        for i in subject
        if i.get('op', '').lower() == 'update'
    }

    with get_db() as db:
        try:
            db.begin_transaction()
            has_update = False
            commit = False
            for item in items:
                tag_list = item.get('tag_list') or []
                for tag in tag_list:
                    tag_id = tag.get('tag_id') or ''
                    dashboard_id = tag.get('relation_id') or ''
                    type = tag.get('type') or ''
                    if tag_id in update_mapping and dashboard_id in dashboard_ids:
                        # 有报告的主题提交了更新
                        has_update = True
                        db.delete(
                            'dap_bi_tag_relation',
                            condition={'tag_id': tag_id, 'relation_id': dashboard_id, 'type': type},
                            commit=commit
                        )

                        update_subject = update_mapping[tag_id]
                        data = {
                            'tag_id': update_subject.get('subject_id') or '',
                            'tag_name': update_subject.get('subject_name') or '',
                            'relation_id': dashboard_id,
                            'type': type
                        }
                        db.insert('dap_bi_tag_relation', data=data, commit=commit)
            # raise
            if has_update:
                db.commit()
                return True, '更新标签关系成功'
            else:
                return True, '无需处理'
        except Exception as e:
            db.rollback()
            local_debugger({"更新标签关系": traceback.format_exc()}, level='error')
            return False, f"更新标签关系： {str(e)}"


def repackage_zip(tmp_folder, original_zip, new_zip, new_folder_name):
    # 创建临时解压目录
    temp_extract_dir = tmp_folder

    # 解压现有的 ZIP 文件
    with zipfile.ZipFile(original_zip, 'r') as zip_ref:
        zip_ref.extractall(temp_extract_dir)

    # 创建新的文件夹
    new_folder_path = os.path.join(temp_extract_dir, new_folder_name)
    os.makedirs(new_folder_path, exist_ok=True)

    # 将所有文件和子文件夹移动到新文件夹
    for item in os.listdir(temp_extract_dir):
        item_path = os.path.join(temp_extract_dir, item)
        if item_path != new_folder_path:  # 确保不移动新文件夹本身
            shutil.move(item_path, new_folder_path)

    # 打包新的 ZIP 文件
    with zipfile.ZipFile(new_zip, 'w', compression=zipfile.ZIP_DEFLATED) as zip_ref:
        for root, dirs, files in os.walk(new_folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, temp_extract_dir)
                zip_ref.write(file_path, arcname)


def download_file(url, local_path):
    url = url.split('?')[0]
    data = import_helper.read_file_from_oss(url)
    # oss = OSSFileProxy()
    # oss.get_object(url, is_url=True)
    # url = oss.get_sigh_url(url, is_url=True)
    # response = requests.get(url, stream=True, timeout=10)
    with open(local_path, 'wb') as file:
        file.write(data)


def classify_dashboard_ids(ids):
    """
    将报表的id分类，数见报表+大屏+复杂报表+ppt
    """

    c_ids = copy.deepcopy(ids)
    dashboard_ids, large_screen_ids, ppt_ids, report_center_ids, application_ids, feed_ids = [], [], [], [], [], []
    if not c_ids:
        return dashboard_ids, large_screen_ids, ppt_ids, report_center_ids

    exist_app_ids = repository.get_column('dap_bi_application', fields=['id'], conditions={'id': ids}) or []
    exist_feeds_ids = repository.get_column('dap_bi_dashboard_email_subscribe', fields=['id'],
                                            conditions={'id': ids}) or []

    for id in c_ids:
        # 查看是数见哪一种报表
        data = repository.get_one('dap_bi_dashboard', conditions={'id': id}) or {}
        if data:
            # continue
            if data.get('application_type') == ApplicationType.LargeScreen.value:
                large_screen_ids.append(id)
            elif data.get('application_type') == ApplicationType.DataReporting.value:
                ppt_ids.append(id)
            elif data.get('application_type') == ApplicationType.ActiveReport.value:
                # tmp_ids = get_all_dashboard_ids([id]) # 复杂报表带出子报告
                # report_center_ids.extend(tmp_ids)
                report_center_ids.append(id)
            elif id in exist_feeds_ids:
                feed_ids.append(id)
            elif id in exist_app_ids:
                application_ids.append(id)
            else:
                dashboard_ids.append(id)
        if id in exist_feeds_ids:
            feed_ids.append(id)
        if id in exist_app_ids:
            application_ids.append(id)
    local_debugger({
        "classify_dashboard_ids": {
            "dashboard_ids": dashboard_ids, "large_screen_ids": large_screen_ids,
            "ppt_ids": ppt_ids, "report_center_ids": report_center_ids, 'application_ids': application_ids
        }
    })
    return dashboard_ids, large_screen_ids, ppt_ids, report_center_ids, application_ids, feed_ids


# def get_distribution_data(dashboard_ids, large_screen_ids, ppt_ids, report_center_ids):
#     """
#     获取制品的一些信息
#     """
#     # 数据集信息
#     datasets_result = _get_datasets(dashboard_ids, large_screen_ids, ppt_ids, report_center_ids)
#     # 报表信息
#     dashboard_result = _get_dashboards(dashboard_ids) if dashboard_ids else []
#     # 大屏
#     large_screen_result = _get_dashboards(large_screen_ids) if large_screen_ids else []
#     # 在线报告
#     ppt_result = _get_ppts(ppt_ids) if ppt_ids else []
#     # 复杂报表
#     activate_result = _get_activate_reports(report_center_ids) if report_center_ids else []
#
#     return {
#         'datasets': datasets_result,
#         'dashboards': dashboard_result,
#         'large_screens': large_screen_result,
#         'ppts': ppt_result,
#         'activate_reports': activate_result,
#     }


# def get_ppt_list():
#     """
#     获取新的在线报告的所有报告
#     """
#     data = repository.get_data(
#         'dashboard',
#         conditions={'application_type': ApplicationType.DataReporting.value},
#         multi_row=True
#     ) or []
#     for row in data:
#         row['report_type'] = AddFuncType.Ppt.value
#     return data
#
#
# def get_activate_report_list():
#     """
#     获取复杂报告的所有报告列表
#     """
#     # 报告文件夹
#     folder_data = repository.get_data(
#         'dashboard',
#         conditions={'application_type': [ApplicationType.ActiveReport.value, ApplicationType.SimpleReport.value],
#                     'type': "FOLDER"},
#         multi_row=True
#     ) or []
#
#     # 报告信息
#     dashboard_data = repository.get_data(
#         'dashboard',
#         conditions={'application_type': ApplicationType.ActiveReport.value, 'type': "FILE"},
#         multi_row=True
#     ) or []
#
#     data = folder_data + dashboard_data
#     for row in data:
#         row['report_type'] = AddFuncType.ActiveReport.value
#     return data


def get_user_info(accounts: list):
    """
    根据user_account获取用户信息
    """
    accounts = list(set([a for a in accounts if a]))
    if not accounts:
        return {}
    users = repository.get_data(
        'dap_p_user',
        conditions={'account': accounts},
        multi_row=True
    ) or []
    return {user.get('account'): user for user in users}


def _format_account(account: str, user_info: dict):
    info = user_info.get(account, {})
    if not info:
        return account
    else:
        return f'{info.get("name", "")}({account})'


APPLICATION_TYPE_MAPPING = {
    0: '仪表板',
    5: '复杂报表',
    6: '复杂报表',
    8: '酷炫大屏',
    9: '数据报告',
}


def get_dashboard_with_ref_datasets(dataset_ids):
    dashboard_map = {}
    for dataset_id in dataset_ids:
        data = repository.get_data('dap_bi_dashboard_chart', {'source': dataset_id}, ['dashboard_id'], multi_row=True)
        if not data:
            continue
        dashboard_ids = [item.get('dashboard_id') for item in data]
        for dashboard_id in dashboard_ids:
            dashboard = dashboard_map.get(dashboard_id)
            if dashboard:
                rels = dashboard.get('dataset_ids') or []
                rels.append(dataset_id)
            else:
                dashboard = repository.get_data('dap_bi_dashboard',
                                                {'id': dashboard_id, 'application_type': [0, 5, 6, 8]},
                                                ['id', 'name', 'application_type'], multi_row=False)
                tag_data = repository.get_data('dap_bi_tag_relation', {'relation_id': dashboard.get('id')},
                                               ['tag_id', 'tag_name'],
                                               multi_row=False)
                if tag_data:
                    dashboard.update(tag_data)
                dashboard['dataset_ids'] = [dataset_id]
                dashboard_map[dashboard_id] = dashboard
    return list(dashboard_map.values())


def get_active_report_by_dataset(dataset_ids):
    sql = """
        select MyRptDetailId as `id` from dap_bi_myrptdetail_design where {conds};
    """
    conds = []
    for dataset_id in dataset_ids:
        conds.append(f" `dsdefine` like '%{dataset_id}%' or `rptvar` like '%{dataset_id}%' ")
    conds = 'or'.join(conds)
    sql = sql.format(conds=conds)
    data = repository.get_data_by_sql(sql, None)
    if not data:
        return []
    ids = [item.get('id') for item in data]
    return ids


def get_data_reporting_by_datset(dataset_ids):
    sql = """
        select report_ppt_id as `id` from dap_bi_t_report_ppt_shape where {conds}
    """
    conds = []
    for dataset_id in dataset_ids:
        conds.append(f" `properties` like '%{dataset_id}%' ")
    conds = 'or'.join(conds)
    sql = sql.format(conds=conds)
    data = repository.get_data_by_sql(sql, None) or []
    sql = """
        select report_id as `id` from dap_bi_t_global_filter_dataset where dataset_id in %(dataset_ids)s
    """
    data += repository.get_data_by_sql(sql, {'dataset_ids': dataset_ids})
    if not data:
        return []
    ids = [item.get('id') for item in data]
    return list(set(ids))


def get_daptable_dashboard_relation(table_names):
    res = {}
    for table_name in table_names:
        data = repository.get_data('dap_bi_dataset_used_table', {'table_name': table_name}, ['dataset_id'],
                                   multi_row=True)
        if not data:
            continue
        dataset_ids = [item.get('dataset_id') for item in data]
        dataset_ids = list(set(dataset_ids))
        data = repository.get_data('dap_bi_dashboard_chart', {'source': dataset_ids}, ['dashboard_id'], multi_row=True)
        dashboard_ids = []
        if data:
            dashboard_ids += [item.get('dashboard_id') for item in data]
        dashboard_ids += get_active_report_by_dataset(dataset_ids)
        dashboard_ids += get_data_reporting_by_datset(dataset_ids)
        if not dashboard_ids:
            continue
        data = repository.get_data('dap_bi_dashboard', {'id': dashboard_ids, 'application_type': [0, 5, 6, 8, 9]},
                                   ['id', 'name', 'application_type'], multi_row=True)
        if not data:
            continue
        refs = []
        for item in data:
            type = APPLICATION_TYPE_MAPPING.get(item.get('application_type'))
            refs.append({'id': item.get('id'), 'name': item.get('name'), 'type': type})
        data = repository.get_data('dap_bi_function', {'url': dashboard_ids}, ['application_id'], multi_row=True)
        if data:
            app_ids = [item.get('application_id') for item in data]
            data = repository.get_data('dap_bi_application', {'id': app_ids}, ['id', 'name'], multi_row=True)
            for item in data:
                refs.append({'id': item.get('id'), 'name': item.get('name'), 'type': '数据门户'})
        res[table_name] = refs
    return res


# def _get_ppts(ppt_ids):
#     """
#     获取制品的一些信息-在线报告的信息(新的在线报告)
#     """
#     if not ppt_ids:
#         return []
#     data = repository.get_data(
#         'dashboard',
#         conditions={'id': ppt_ids, 'application_type': ApplicationType.DataReporting.value},
#         multi_row=True
#     ) or []
#     dashboard_result = []
#     accounts = [d.get('created_by') for d in data] + [d.get('modified_by') for d in data]
#     users_info = get_user_info(accounts)
#     for d in data:
#         dashboard_result.append({
#             "id": d.get('id'),
#             "name": d.get('name'),
#             "status": '已发布' if d.get('status') == 1 else '未发布',
#             "created_by": _format_account(d.get('created_by'), users_info),
#             "created_on": d.get('created_on'),
#             "modified_on": d.get('modified_on'),
#             "modified_by": _format_account(d.get('modified_by'), users_info),
#         })
#     return dashboard_result


# def _get_activate_reports(report_center_ids):
#     """
#     根据报表id获取报表信息
#     """
#     if not report_center_ids:
#         return []
#     data = repository.get_data(
#         'dashboard',
#         conditions={'id': report_center_ids, 'application_type': ApplicationType.ActiveReport.value},
#         multi_row=True
#     ) or []
#     dashboard_result = []
#     accounts = [d.get('created_by') for d in data] + [d.get('modified_by') for d in data]
#     users_info = get_user_info(accounts)
#     for d in data:
#         dashboard_result.append({
#             "id": d.get('id'),
#             "name": d.get('name'),
#             "status": '已发布' if d.get('status') == 1 else '未发布',
#             "created_by": _format_account(d.get('created_by'), users_info),
#             "created_on": d.get('created_on'),
#             "modified_on": d.get('modified_on'),
#             "modified_by": _format_account(d.get('modified_by'), users_info),
#         })
#     return dashboard_result


# def _get_dashboards(dashboard_ids):
#     """
#     获取制品的一些信息-报表信息
#     """
#     if not dashboard_ids or not isinstance(dashboard_ids, list):
#         return []
#     data = repository.get_data('dap_bi_dashboard', conditions={'id': dashboard_ids}, multi_row=True) or []
#     dashboard_result = []
#     accounts = [d.get('created_by') for d in data] + [d.get('modified_by') for d in data]
#     users_info = get_user_info(accounts)
#     for d in data:
#         dashboard_result.append({
#             "id": d.get('id'),
#             "name": d.get('name'),
#             "status": '已发布' if d.get('status') == 1 else '未发布',
#             "created_by": _format_account(d.get('created_by'), users_info),
#             "created_on": d.get('created_on'),
#             "modified_on": d.get('modified_on'),
#             "modified_by": _format_account(d.get('modified_by'), users_info),
#         })
#     return dashboard_result


# def _get_datasets(dashboard_ids, large_screen_ids, ppt_ids, report_center_ids):
#     """
#     获取制品的一些信息-数据集
#     """
#     datasets = get_relate_dataset_info(dashboard_ids, large_screen_ids, ppt_ids, report_center_ids)
#     datasets = duplicate_all_datasets(datasets)
#
#     # 数据集信息
#     datasets_result = []
#     datasets_ids = set()
#     accounts = [d.get('created_by') for d in datasets] + [d.get('modified_by') for d in datasets]
#     users_info = get_user_info(accounts)
#     for d in datasets:
#         # 去重展示的数据集信息
#         if d.get('id') in datasets_ids:
#             continue
#         datasets_result.append({
#             "id": d.get('id'),
#             "name": d.get('name'),
#             "created_by": _format_account(d.get('created_by'), users_info),
#             "created_on": d.get('created_on'),
#             "modified_on": d.get('modified_on'),
#             "modified_by": _format_account(d.get('modified_by'), users_info),
#         })
#         datasets_ids.add(d.get('id'))
#     return datasets_result

#
# def save_mobile_application(application_id, application_data):
#     """
#     保存移动端的数据门户
# {
#     "application": {
#         "platform": "mobile_screen",
#         "nav_type": 1,
#         "name": "移动门户名字",
#         "description": "移动门户描述",
#         "theme": "浅色"
#     },
#     "functions": [
#         {
#             "id": "aea40da6-0684-493e-9da6-cec0057caa97",
#             "parent_id": "",
#             "name": "菜单1",
#             "link_type": "dashbaord",
#             "link": "fau79bfabfagjggj",
#             "description": "描述1",
#             "sub": []
#         },
#         {
#             "id": "31f5337b-1458-46fc-b521-8cb62ed1ec2f",
#             "parent_id": "",
#             "name": "菜单2",
#             "link_type": "",
#             "link": "",
#             "description": "描述1",
#             "sub": [
#                 {
#                     "id": "c377fe63-e8f1-483c-b03e-5b39ecfd1b12",
#                     "parent_id": "31f5337b-1458-46fc-b521-8cb62ed1ec2f",
#                     "name": "菜单2-1",
#                     "link_type": "url",
#                     "link": "baicu.com",
#                     "description": "描述2-1",
#                     "type": "func",
#                     "sub": []
#                 }
#             ]
#         }
#     ]
# }
#     """
#     from app_menu.services import application_service
#     from app_menu.models import ApplicationModel
#
#     # application_id = 'apaas000-2b92-4df3-a6c6-0d3d0c36de83'
#     with get_db() as db:
#         try:
#             db.begin_transaction()
#
#             # 删除门户数据
#             sql1 = "delete from `dap_bi_application` WHERE `id` = %r" % application_id
#             sql2 = "delete from `function` WHERE `application_id` = %r" % application_id
#             db.exec_sql(sql1)
#             db.exec_sql(sql2)
#
#             # 添加门户数据
#             application = application_data.get('application', {})
#             model = ApplicationModel(**application)
#             model.nav_type = 3
#             model.type_access_released = 3
#             model.enable = 1
#             application_service.add_application(model, app_id=application_id)
#
#             # 添加菜单数据
#             functions = application_data.get('functions', {})
#             recursion_add_function(application_id, functions)
#             # raise
#             db.commit()
#             return True, '保存门户成功！'
#         except Exception as e:
#             db.rollback()
#             local_debugger({"保存门户失败": traceback.format_exc()}, level='error')
#             return False, f"保存门户失败： {str(e)}"
#
#
# def recursion_add_function(application_id: str, function_data: list):
#     """
#     递归添加菜单信息
#     """
#     from app_menu.services.function_service import _generate_level_code
#
#     for function in function_data:
#         model = FunctionModel(**function)
#         model.application_id = application_id
#         model.level_code = _generate_level_code(model)
#
#         link_type = function.get('link_type') or ''
#         link = function.get('link') or ''
#
#         if link_type == 'dashboard':
#             model.report_type = 0
#             model.url = link
#         else:
#             model.report_type = 0
#             model.url = link
#
#         fields = [
#             'id', 'name', 'parent_id', 'level_code', 'icon', 'url', 'target', 'application_id', 'icon_url',
#             'report_type', 'description'
#         ]
#         repository.add_model('`dap_bi_function`', model, fields)
#
#         sub = function.get('sub') or []
#         if sub:
#             recursion_add_function(application_id, sub)


# def get_application_data(application_id):
#     from app_menu.services import application_service
#     app_model = application_service.get_application_and_sub_without_permission(application_id, True)
#     # 格式化
#     functions = app_model.function or []
#     application = {
#         "platform": app_model.platform,
#         "name": app_model.name,
#         "description": app_model.description,
#         "theme": app_model.theme,
#     }
#     result = {'application': application, 'functions': _format_func_data(functions)}
#     return result


# def _format_func_data(function_data: list):
#     """
#     递归处理格式化的菜单信息
#     """
#     result = []
#     for function in function_data:
#         func_data = {}
#         func_data['id'] = function.get('id') or ''
#         func_data['parent_id'] = function.get('parent_id') or ''
#         func_data['name'] = function.get('name') or ''
#         func_data['link_type'] = 'url' if 'http' in function.get('url', '') else 'dashboard'
#         func_data['link'] = function.get('url', '')
#         func_data['description'] = function.get('description', '')
#
#         sub = function.get('sub') or []
#         if sub:
#             func_data['sub'] = _format_func_data(sub)
#
#         result.append(func_data)
#     return result

@stale_cache(prefix='pubserver_import', expire=3600)
def parse_import_zip_file(file_url):
    return import_helper.parse_zip_file(file_url)
