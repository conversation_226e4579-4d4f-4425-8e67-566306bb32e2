#!/usr/local/bin python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
from collections import defaultdict
from hashlib import md5
import logging
import json

# ---------------- 业务模块 ----------------
from dmplib.redis import conn as redis_conn
from dmplib.hug import debugger
from dmplib.utils.errors import UserError
from dashboard_chart.repositories import external_dashboard_repository


logger = logging.getLogger(__name__)
_debugger = debugger.Debug(__name__)


def generate_session_id(auth_id):
    salt = 'sjdmp'
    obj = md5(auth_id.encode('utf-8'))
    obj.update(salt.encode('utf-8'))
    return obj.hexdigest()[8:-8]


def get_third_auth_cache_key(session_id):
    prefix = 'screen_dashboard_auths'
    return prefix + ':' + session_id


def set_third_auth_cache(session_id, auths):
    cache_key = get_third_auth_cache_key(session_id)
    return redis_conn().hmset(cache_key, auths)


def del_third_auth_cache(session_id):
    cache_key = get_third_auth_cache_key(session_id)
    return redis_conn().delete(cache_key)


def get_third_auth_cache(session_id, dashboard_id):
    cache_key = get_third_auth_cache_key(session_id)
    return redis_conn().hget(cache_key, dashboard_id)


def get_third_all_auth_cache(session_id):
    cache_key = get_third_auth_cache_key(session_id)
    return redis_conn().hgetall(cache_key)


def get_third_auth_cache_by_keys(session_id, dashboard_ids):
    cache_key = get_third_auth_cache_key(session_id)
    return redis_conn().hmget(cache_key, dashboard_ids)


def _parse_and_get_relation_dict(jump_configs: list):
    """
    解析jump内容，获取起跳和被跳报告
    :param str jump_configs:
    :return:
    """
    from base.enums import DashboardJumpTargetType
    result = defaultdict(list)
    if not jump_configs:
        return result
    for single_jump_config in jump_configs:
        try:
            decoded_single_jump_config = json.loads(single_jump_config)
        except:
            decoded_single_jump_config = []
        if not decoded_single_jump_config:
            continue
        for i in decoded_single_jump_config:
            if i and i.get('target_type') == DashboardJumpTargetType.Dashboard.value:
                result[i.get('target')].append(i.get('dashboard_id'))
    return result


def recursive_get_jump_dashboard_auths(op_dashboard_ids: list, dashboard_auths: dict):
    """
    递归获取跳转目标报告，并设置为起跳报告的权限
    :param str op_dashboard_ids:
    :param str dashboard_auths:
    :return:
    """
    jump_config_rows = external_dashboard_repository.batch_get_released_jump_config(op_dashboard_ids)
    if not jump_config_rows:
        return
    jump_configs = [i.get('jump') for i in jump_config_rows]
    jump_relation_dict = _parse_and_get_relation_dict(jump_configs)
    if not jump_relation_dict:
        return
    filtered_to_dashboard_ids = list(filter(lambda i: i not in dashboard_auths, jump_relation_dict.keys()))
    if not filtered_to_dashboard_ids:
        return
    for to_dashboard_id in filtered_to_dashboard_ids:
        from_dashboard_ids = jump_relation_dict.get(to_dashboard_id)
        if not from_dashboard_ids:
            continue
        from_dashboard_auths = [dashboard_auths.get(i, '') for i in from_dashboard_ids]
        from_dashboard_auths_set = [set(str(i).split(',')) for i in from_dashboard_auths if i]
        total_auths_set = set()
        for i in from_dashboard_auths_set:
            total_auths_set = total_auths_set.union(i)
        dashboard_auths[to_dashboard_id] = ','.join(total_auths_set)
    recursive_get_jump_dashboard_auths(filtered_to_dashboard_ids, dashboard_auths)


def upload_auth(auth_id, dashboard_auths):
    _debugger.log({'上传第三方报告权限': {'auth_id': auth_id, 'auths': json.dumps(dashboard_auths)}})
    biz_codes = [i.get('biz_code') for i in dashboard_auths if i.get('biz_code')]
    if len(biz_codes) != len(dashboard_auths):
        raise UserError(message='参数`biz_code`不能为空')
    dashboard_infos = external_dashboard_repository.get_info_by_biz_codes(biz_codes)
    if not dashboard_infos:
        raise UserError(message='获取报表信息异常')
    if len(dashboard_infos) != len(biz_codes):
        raise UserError(message='存在已失效报表')

    biz_code_dashboard_ids = {i.get('biz_code'): i.get('id') for i in dashboard_infos}
    cache_dashboard_auths = {}
    for i in dashboard_auths:
        dashboard_id = biz_code_dashboard_ids.get(i.get('biz_code'))
        cache_dashboard_auths[dashboard_id] = i.get('auth')

    # 递归获取所有被跳转的报告
    recursive_get_jump_dashboard_auths(list(cache_dashboard_auths.keys()), cache_dashboard_auths)
    try:
        session_id = generate_session_id(auth_id)
        del_third_auth_cache(session_id)
        set_third_auth_cache(session_id, cache_dashboard_auths)
    except Exception as e:
        logger.error(msg='设置第三方报告权限缓存异常，异常信息:{}'.format(str(e)))
        raise UserError(message='获取session_id异常')
    _debugger.log({'第三方报告权限生成session_id': session_id})
    return session_id


def check_third_permission(session_id, dashboard_id, action):
    flag = False
    auth_res = get_third_auth_cache(session_id, dashboard_id)
    if auth_res and auth_res.find(action) != -1:
        flag = True
    return flag
