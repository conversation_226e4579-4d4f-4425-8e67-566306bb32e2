#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import calendar
import datetime
import json
import time
import traceback
from typing import Optional
from urllib.parse import quote, urljoin

from loguru import logger

from base import repository
from base import dmp_constant
from dmplib.components import auth_util
from dmplib import config
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from dmplib.hug import g
from dmplib.utils.errors import UserError
from base.enums import (
    FlowType, FeedType, FlowInstanceStatus, SubscribeSendUserFrom,
    ThirdPartyAppServiceType,
    SubscribeDetailStatus
)
from dmplib.db.mysql_wrapper import get_db as get_master_db
from feed.repositories import dashboard_feeds_repository
from feed.repositories.dashboard_feeds_repository import get_subscribe_by_dashboard_id, feed_format_list
from feed.services import dashboard_feeds_service
from flow.models import FlowInstanceModel
from flow.services import flow_instance_service
from flow.repositories import flow_instance_repository
from flow.services import flow_service
from message.services.msg_notify_pusher import MsgNotifyPusher
from system import system_constant
from system.services import system_setting_service
from components.url import url_add_param
from components.qywx_robot import SubscribeRobot
from feed.services.yzs_service import YzsService
from feed.services.factory_feed_service import FactoryFeed
from feed.services import msg_get_data_service
from feed.services import feed_external_service
from feed.services.feeds_analysis_service import init_exec_analysis, feed_exec_analysis


def send_feed_message(
        project_code: object, feed_id: object, dmp_send_time, feed_detail_id: object = '', retry: object = 0,
        is_rundeck: object = 0
):
    """
    发送 订阅信息
    :param project_code:  项目编码
    :param feed_id: 订阅ID
    :param dmp_send_time: 简讯发送时间
    :param feed_detail_id: 订阅明细ID（默认为空，重发时需要订阅明细ID）
    :param retry: 重发（默认0：不重发，1：重发全部收件人，2：重发失败收件人）
    :param is_rundeck: 是否rundeck调度任务（默认0：否）
    :return:
    """
    feed_model = None
    try:
        # 1 start
        feed_exec_analysis('start')

        # 获取简讯信息
        feed_data = get_feed_data(feed_id)
        feed_model = dashboard_feeds_service.generate_dashboard_feed_model(feed_data)
        # 简讯发送时间
        feed_model.send_time = dmp_send_time
        # 云助手发送开关
        is_yzs, yzs_tenant_id = get_yzs_is_open(project_code)

        # 系统设置简讯订阅开关为 1 才执行
        weixin_feed_switch = system_setting_service.get_system_setting_item(
            system_constant.FEED, system_constant.WEIXIN_FEED_SWITCH
        )
        # 简讯订阅
        if feed_model.type == FeedType.Mobile.value and (
                weixin_feed_switch.get("value") == 1 or weixin_feed_switch.get("value") == '1'
        ):
            # 执行拍照
            snap_id = execute_snapshot_task(project_code, feed_model)
            # 当前拍照id
            if snap_id:
                feed_model.snap_id = snap_id
            logger.error("报告原始report_url：" + feed_model.report_real_release_url)
            # 原始报表url增加相关参数
            _set_feed_dashboard_url(project_code, feed_model, feed_data, is_yzs, yzs_tenant_id)

            # 重发类型
            feed_model.retry = retry
            # 简讯重试的逻辑处理，添加简讯明细日志
            feed_detail_id = _add_feed_detail(feed_model, feed_detail_id, retry, is_rundeck,
                                              feed_data.get('dashboard_name'))
            # 更新简讯运行状态
            update_flow_status(feed_id, feed_detail_id, FlowInstanceStatus.Running.value, startup_time=get_now_time())

            # 云助手消息发送逻辑分支（利用云助手消息发送能力）
            if is_yzs:
                yzs_service = YzsService(
                    project_code,
                    feed_id,
                    feed_detail_id,
                    config.get('Yzs.domain'),
                    config.get('Yzs.msg_app_key')
                )
                failure_recipients = yzs_service.send_enterprise_yzs(feed_model)
                res = {"data": failure_recipients, "code": 200, "msg": ""}
            else:
                # 2 准备完成
                feed_exec_analysis('ready')

                # 记录简讯消息发送是否为重试
                feed_data['retry'] = retry
                # 使用数见用户进行消息发送，包括企业微信、三云应用、云助手
                res = send_message_by_app(project_code, feed_detail_id, feed_data, feed_model, is_rundeck)
            # 更新状态和错误日志
            update_subscribe_detail_status(feed_id, feed_detail_id, feed_model, res)
            # 9 end 更新简讯发送日志
            feed_exec_analysis('end')
    except Exception as e:
        err_msg = "简讯发送错误，errs：" + str(e)
        logger.exception(err_msg)
    finally:
        # 简讯的周期天下次发送时间获取和更新flow
        calc_day_cycle_next_time(feed_model)


def calc_day_cycle_next_time(feed_model):
    """
    更新简讯周期天的下次执行时间
    :param feed_model:
    :return:
    """
    try:
        if not feed_model:
            raise UserError(message="简讯不存在")
        cron_exp = dashboard_feeds_service.update_day_cycle_feeds_flow(feed_model)
        if cron_exp:
            logger.error(f"【{feed_model.subject_email}】简讯(ID:{feed_model.id})下次执行时间cron[{cron_exp}]")
    except Exception as e:
        logger.exception(f"简讯更新下次执行时间错误，errs：{str(e)}")


def get_feed_data(feed_id):
    """
    获取简讯以及简讯的关联数据
    :param feed_id:
    :return:
    """
    feed_data = dashboard_feeds_repository.get_feeds_or_dashboard(feed_id)
    # TODO 待优化，简讯不存在，应用不存在记录日志中断程序
    # 解决删除简讯订阅没有删除rundeck的定时任务，导致出现错误数据问题。继续执行下面记录日志
    if not feed_data:
        err_msg = f"简讯内容不存在 feed_id: {feed_id}"
        logger.error(err_msg)
        raise UserError(message=err_msg)

    # 初始化，日志执行分析初始化简讯信息
    init_exec_analysis(feed_id, msg_get_data_service.clear_title_html(feed_data.get("subject_email")))

    # 简讯应用模式校验秘钥信息
    if feed_data.get("app_service_type") == ThirdPartyAppServiceType.APP.value \
            and (not feed_data.get("corp_id") or not feed_data.get("app_id") or not feed_data.get("app_secret")):
        # 应用信息不存在不中断程序，是因为在这里中断，没有记录任何日志
        feed_data_str = json.dumps(feed_data, ensure_ascii=False)
        logger.error(f"简讯关联的应用不存在，不会发送 feed_data: {feed_data_str}")

    # 简讯字段显示格式
    feed_data['display_format'] = repository.get_list(
        'dap_bi_dashboard_subscribe_display_format', {'subscribe_id': feed_data.get("id")}
    )
    # 简讯过滤条件列表
    feed_data['filters'] = dashboard_feeds_service.get_mobile_subscribe_filter_list(feed_data.get("id"))
    # 简讯的明细段落列表
    feed_data['chapters'] = dashboard_feeds_service.get_mobile_subscribe_chapters(feed_data.get("id"))
    # 简讯的企业关联的cloud_apps信息
    feed_data['cloud_app'] = dashboard_feeds_service.get_mobile_subscribe_cloud_app(feed_data.get("app_code"))
    return feed_data


def get_yzs_is_open(project_code):
    """
    简讯云助手发送开关
    :param project_code:
    :return:
    """
    yzs_config = repository.get_data('dap_bi_project_yzs_config', {'code': project_code}, from_config_db=True) or {}
    is_yzs = bool(yzs_config and yzs_config.get('tenant_id') and yzs_config.get('enable_yzs_message') == 1)
    tenant_id = yzs_config.get('tenant_id', '')
    return is_yzs, tenant_id


def _set_feed_dashboard_url(project_code, feed_model, feed_data, is_yzs, yzs_tenant_id):
    """
    设置简讯关联报告的url参数
    :param project_code:
    :param feed_model:
    :param feed_data:
    :param is_yzs:
    :param yzs_tenant_id:
    :return:
    """
    # 原始报表url增加相关参数
    feed_model.report_real_release_url = feed_external_service.get_report_params_url(
        feed_model.report_real_release_url, feed_model.send_time, feed_model.snap_id)
    if is_yzs:
        # 云助手报表的跳转地址调整
        feed_model.release_url = get_yzs_redirect_url(feed_model.release_url, yzs_tenant_id,
                                                      feed_model.send_time, feed_model.snap_id)
    else:
        # 报表打开的集成地址
        # 服务商模式
        if feed_data.get("app_service_type") == ThirdPartyAppServiceType.SERVICE.value:
            feed_model.release_url = feed_external_service.get_super_app_redirect_url(feed_model.dashboard_id,
                                                                                      feed_model.send_time,
                                                                                      feed_model.snap_id)
        else:
            # 应用模式
            feed_model.release_url = feed_external_service.get_redirect_url(project_code, feed_data,
                                                                            feed_model.release_url,
                                                                            feed_model.send_time,
                                                                            feed_model.snap_id)
    logger.error(f"消息集成后的报告report_url: {feed_model.release_url}")


def get_yzs_redirect_url(report_url, yzs_tenant_id, dmp_send_time, snap_id=None):
    report_url = url_add_param(report_url, {"dmp_send_date": time.strftime("%Y-%m-%d", time.localtime())})
    report_url = url_add_param(report_url, {"sent_time": dmp_send_time})
    if snap_id:
        report_url = url_add_param(report_url, {"snap_id": snap_id})
    redirect_uri =urljoin(AppHosts.get(SkylineApps.DP, False),f"api/user/yzs_msg_login?redirect_url={quote(report_url)}&tenant_id={yzs_tenant_id}")
    url = f"{config.get('Yzs.domain')}/api/tenant-open/sso?app_code={dmp_constant.YZS_MSG_APP_CODE}&channel_id=10&redirect_uri={redirect_uri}"
    return url


def _add_feed_detail(feed_model, feed_detail_id, retry, is_rundeck, dashboard_name):
    """
    简讯重试的逻辑处理，添加简讯明细日志
    :param feed_model:
    :param feed_detail_id:
    :param retry:
    :param is_rundeck:
    :param dashboard_name:
    :return:
    """
    if retry and (retry == 1 or retry == '1'):
        pass  # NOSONAR
    elif retry and (retry == 2 or retry == '2'):
        feed_detail_data = repository.get_data("dap_bi_dashboard_email_subscribe_detail", {"id": feed_detail_id})
        # pylint: disable=not-callable
        feed_model.recipients = feed_detail_data.get("failure_recipients")
    else:
        if is_rundeck and is_rundeck == 1:
            flow_instance = FlowInstanceModel(
                flow_id=feed_model.id, name=msg_get_data_service.clear_title_html(feed_model.subject_email),
                type=FlowType.Feeds.value
            )
            feed_detail_id = flow_instance_service.add_instance(flow_instance)

        feed_detail_data = {
            "id": feed_detail_id,
            "email_subscribe_id": feed_model.id,
            "relevancy_url": feed_model.release_url,
            "subject_email": msg_get_data_service.clear_title_html(feed_model.subject_email),
            "dashboard_id": feed_model.dashboard_id,
            "dashboard_name": dashboard_name,
            "status": SubscribeDetailStatus.Running.value,
        }
        repository.add_data('dap_bi_dashboard_email_subscribe_detail', feed_detail_data)
    return feed_detail_id


def send_message_by_app(project_code, feed_detail_id, feed_data, feed_model, is_rundeck):
    """
    使用数见用户的应用渠道消息发送
    :param project_code:
    :param feed_detail_id:
    :param feed_data:
    :param feed_model:
    :param is_rundeck:
    :return:
    """
    res = {"data": [], "code": 200, "msg": ''}
    try:
        # 简讯ID
        feed_id = feed_model.id
        # 日志记录
        feed_data_json = json.dumps(feed_data, ensure_ascii=False)
        logger.error(f"简讯发送准备feed_data: {feed_data_json}")

        # 简讯的角色发送对象转换为用户，用户如不存在则直接抛出异常
        convert_subscribe_role_user(feed_model)
        # 3 角色转换为用户
        feed_exec_analysis('role_to_user')

        # 简讯发送条件校验, 来自定时任务才会走
        if is_rundeck and is_rundeck == 1 and feed_model.msg_subscribe_config.enable_send_rule == 1:
            if not check_feed_is_send(feed_model):
                logger.error('简讯[%s]不能发送' % feed_model.id)
                raise Exception('简讯未满足条件发送')
            else:
                logger.error('简讯[%s]正常发送' % feed_model.id)

        # 4 发送规则校验
        feed_exec_analysis('feed_rule')

        # 发送渠道实例获取
        app_feed_service = FactoryFeed.get_instance(project_code, feed_id, feed_detail_id, feed_data)
        failure_recipients = app_feed_service.send_app_msg(feed_model)
        res['data'] = failure_recipients
    except Exception as e:
        logger.error('简讯发送异常： %s' % traceback.format_exc())
        err_msg = str(e)
        res['code'] = 500
        res['msg'] = err_msg

    return res


def convert_subscribe_role_user(feed_model):
    """
    获取简讯关联的角色用户信息
    会对feed_model.recipients 重新赋值
    :param feed_model:
    :return:
    """
    msg_subscribe_config = feed_model.msg_subscribe_config
    # 默认都是个人模式
    user_from = msg_subscribe_config.user_from \
        if msg_subscribe_config.user_from is not None else SubscribeSendUserFrom.PERSONAL.value

    # 只重发失败消息
    if feed_model.retry and (feed_model.retry == 2 or feed_model.retry == '2'):
        logger.error("只重发失败用户，不用获取角色中用户")
        return True
    if user_from == SubscribeSendUserFrom.PERSONAL.value:
        logger.error("简讯发送对象：个人模式")
        return True
    logger.error("简讯发送对象：角色模式")
    # 角色中的用户获取
    role_id_list = dashboard_feeds_service.get_mobile_subscribe_role_ids(feed_model.id)
    role_count = len(role_id_list) if role_id_list else 0
    logger.error(f"简讯关联角色个数：{role_count}")
    user_id_list = dashboard_feeds_service.get_subscribe_role_user_id_list(role_id_list)
    user_count = len(user_id_list) if user_id_list else 0
    logger.error(f"简讯关联角色中的用户数：{user_count}")
    if not user_id_list:
        err = '简讯关联角色中没有用户，不会发送'
        logger.error(err)
        raise Exception(err)
    # user_id查找用户
    user_list = dashboard_feeds_service.get_user_by_ids(user_id_list)
    feed_model.recipients = user_list
    return True


def check_feed_is_send(feed_model):
    # 拿到规则
    send_rules = dashboard_feeds_repository.get_mobile_subscribe_send_rules(feed_model.id) or []

    # 遍历校验规则
    for send_rule in send_rules:
        if not match_send_rule(send_rule):
            return False

    return True


def match_send_rule(send_rule):
    # {
    #     "email_subscribe_id": "",
    #     "dataset_id": "",
    #     "rule_info": {
    #         "rule": "早于",
    #         "time_node": "当周",
    #         "day_or_week": 1,
    #         "time_detail": "2020-09-09 09:09:09"
    #     }
    # }
    email_subscribe_id = send_rule.get('email_subscribe_id', '')
    dataset_id = send_rule.get('dataset_id')
    rule_info = send_rule.get('rule_info', {})
    if isinstance(rule_info, str):
        rule_info = json.loads(rule_info)
    rule = rule_info.get('rule')
    time_node = rule_info.get('time_node')
    day_or_week = rule_info.get('day_or_week')
    time_detail = rule_info.get('time_detail')

    if any([rule is None, time_node is None, time_detail is None, dataset_id is None]):
        logger.error(f'简讯[{email_subscribe_id}]发送条件send_rule错误：{send_rule}')
        return False

    instance_record = flow_instance_repository.get_last_success_instance(flow_id=dataset_id)
    clean_time = instance_record.get('end_time', '')
    if isinstance(clean_time, datetime.datetime):
        clean_time = clean_time.strftime('%Y-%m-%d %H:%M:%S')
    if not clean_time:
        logger.error(
            f'简讯[{email_subscribe_id}]发送条件查询不到清洗时间：{send_rule}, instance_record: {instance_record}')
        return False

    now = lambda: datetime.datetime.utcnow() + datetime.timedelta(hours=8)
    _now = now()

    if time_node == '当日':
        day_str = _now.strftime('%Y-%m-%d')
        time_str = f'{day_str} {time_detail}'
    elif time_node == '前一日':
        day_str = (_now - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        time_str = f'{day_str} {time_detail}'
    elif time_node == '当周':  # python中周是以0开始的，与前端的约定是从1开始
        calc_day = _now + datetime.timedelta(days=0 - (_now.weekday())) + datetime.timedelta(days=day_or_week - 1)
        day_str = calc_day.strftime('%Y-%m-%d')
        time_str = f'{day_str} {time_detail}'
    elif time_node == '前一周':
        calc_day = _now + datetime.timedelta(days=0 - (_now.weekday()) - 7) + datetime.timedelta(days=day_or_week - 1)
        day_str = calc_day.strftime('%Y-%m-%d')
        time_str = f'{day_str} {time_detail}'
    elif time_node == '当月':
        st1_day = datetime.datetime.strptime(_now.strftime('%Y-%m-01 00:00:00'), '%Y-%m-%d %H:%M:%S')
        month_days = calendar.monthrange(st1_day.year, st1_day.month)[1]  # (1, 31)
        m_day = day_or_week if day_or_week <= month_days else month_days
        calc_day = st1_day + datetime.timedelta(days=m_day - 1)
        day_str = calc_day.strftime('%Y-%m-%d')
        time_str = f'{day_str} {time_detail}'
    elif time_node == '前一月':
        st1_day = datetime.datetime.strptime(_now.strftime('%Y-%m-01 00:00:00'), '%Y-%m-%d %H:%M:%S')  # 当月最后一天
        last_st31_day = st1_day + datetime.timedelta(days=-1)  # 上月最后一天
        m_day = day_or_week if day_or_week <= last_st31_day.day else last_st31_day.day
        day_str = last_st31_day.strftime(f'%Y-%m-{m_day}')
        time_str = f'{day_str} {time_detail}'
    else:
        # 理论上不存在这种情况，因为数据库的值是经过严格校验的，但是这里还是做处理
        logger.error(f'简讯[{email_subscribe_id}]发送条件time_node错误：{send_rule}')
        return False

    logger.error(f'简讯[{email_subscribe_id}], clean_time: {clean_time}, rule: {rule}, time_str: {time_str}')
    if rule == '早于':
        if clean_time < time_str:
            return True
    elif rule == '晚于':
        if clean_time > time_str:
            return True
    else:
        # 理论上不存在这种情况，因为数据库的值是经过严格校验的，但是这里还是做处理
        logger.error(f'简讯[{email_subscribe_id}]发送条件rule错误：{send_rule}')
        return False

    logger.error(f'简讯[{email_subscribe_id}]达不到发送条件：{send_rule}, instance_record: {instance_record}')
    return False


def update_subscribe_detail_status(feed_id, feed_detail_id, feed_model, res):
    """
    更新简讯详情的状态和日志、更新flow状态
    :param feed_id:
    :param feed_detail_id:
    :param feed_model:
    :param res: 发送的结果字典 res = {"data": [], "code": 200, "msg": ''}
    :return:
    """

    code = res.get("code")
    failure_recipients = res.get("data")
    err_msg = res.get("msg")
    # 总发送人数
    recipients_num = len(feed_model.recipients)
    end_time = get_now_time()
    detail_status = SubscribeDetailStatus.Successful.value
    if code == 200:
        send_result = "成功{},失败{}"
        # 部分用户失败
        if failure_recipients:
            failure_recipients_num = len(failure_recipients)
            # 失败人数与全部人数相等，则为“已失败”否则为“部分失败”
            detail_status = SubscribeDetailStatus.Failed.value if failure_recipients_num == recipients_num \
                else SubscribeDetailStatus.FailedPart.value
            send_result_str = send_result.format(
                str(recipients_num - failure_recipients_num), str(failure_recipients_num)
            )
            update_subscribe_detail_failure(feed_model, feed_detail_id, failure_recipients, send_result_str,
                                            detail_status, end_time=end_time)
        else:
            # 全部成功
            update_flow_status(feed_id, feed_detail_id, FlowInstanceStatus.Successful.value, end_time=end_time)
            repository.update_data(
                "dap_bi_dashboard_email_subscribe_detail",
                {"failure_recipients": "", "send_result": send_result.format(str(len(feed_model.recipients)), '0'),
                 "status": SubscribeDetailStatus.Successful.value},
                {"id": feed_detail_id},
            )
    else:
        detail_status = SubscribeDetailStatus.Failed.value
        # 还未到用户发送阶段，发生异常失败
        send_result_str = '执行异常，发送失败'
        if err_msg:
            send_result_str += f' err：{err_msg}'
        update_subscribe_detail_failure(feed_model, feed_detail_id, failure_recipients, send_result_str, detail_status,
                                        end_time=end_time)
    try:
        from dmplib.components.fast_logger import FastLogger
        log_dict = {
            'flow_type': FastLogger.ModuleType.SUBSCRIBE,
            'flow_id': feed_id,
            'flow_name': msg_get_data_service.clear_title_html(feed_model.subject_email),
            'plan_start_time': '',
            'startup_time': feed_model.send_time,
            'end_time': end_time,
            'run_status': detail_status,
            'number1': recipients_num,
            'number2': len(failure_recipients) if failure_recipients else 0
        }
        FastLogger.FlowOperationsFastLogger(**log_dict).record()
    except Exception as e:
        logger.exception(f'简讯上传天眼报错：%s' % traceback.format_exc())


def update_subscribe_detail_failure(feed_model, feed_detail_id, failure_recipients, send_result_str, status,
                                    end_time=None):
    """
    更新简讯发送记录和flow的状态为失败
    :param feed_model:
    :param feed_detail_id:
    :param failure_recipients:
    :param send_result_str:
    :param status:
    :param end_time:
    :return:
    """
    feed_id = feed_model.id
    update_flow_status(feed_id, feed_detail_id, FlowInstanceStatus.Failed.value, end_time=end_time)
    repository.update_data(
        "dap_bi_dashboard_email_subscribe_detail",
        {
            "failure_recipients": json.dumps(failure_recipients),
            "send_result": send_result_str,
            "status": status,
        },
        {"id": feed_detail_id},
    )
    try:
        send_error_to_wechat(feed_id)
    except Exception as e:
        logger.error(f"发送企业微信机器人失败，err：{str(e)}")

    # 用户配置的自定义预警推送
    try:
        feed_failure_mag_notify(feed_model, end_time, send_result_str, failure_recipients)
    except Exception as e:
        logger.exception(e)
        logger.error(f"用户自定义的预警推送失败，err：{str(e)}")


def feed_failure_mag_notify(feed_model, end_time, send_result_str, failure_recipients):
    """
    用户定义的简讯的失败预警推送
    """
    pusher = MsgNotifyPusher(data_type='简讯')
    feed_name = msg_get_data_service.clear_title_html(feed_model.subject_email)
    dommain = AppHosts.get(SkylineApps.DP, False).strip('/')
    detail_url = "/".join([dommain, "bi/feeds/detail/wx", feed_model.id])
    failure_count = len(failure_recipients or [])
    total_count = len(feed_model.recipients or [])
    if total_count == failure_count:
        send_status = ['全部失败', '部分失败']
    elif failure_count >= 1:
        send_status = ['部分失败']
    else:
        send_status = ['全部成功']

    pusher.set_push_level(send_status)

    # 企业微信机器人
    pusher.set_qywx_content({
        "msgtype": "markdown",
        "markdown": {
            "content": """
简讯【%s】发送异常，请及时关注！：
>发送时间:  <font color="warning">%s</font>
>发送结果:  <font color="warning">%s</font>
>点击查看详情:  <font color="comment">[%s](%s)</font> """ % (
                feed_name, end_time, send_result_str, detail_url, detail_url
            ),
        }
    })
    # 邮件内容
    pusher.set_email_content({
        'subject': '简讯【%s】发送异常，请及时关注！' % feed_name,
        'body': """
您好：
<br/>
简讯【%s】发送异常，请及时关注！
<br/>
发送时间： %s
<br/>
发送结果：%s
<br/>
点击查看详情：%s
""" % (
            feed_name, end_time, send_result_str, detail_url
        ),
    })
    pusher.notify()


def update_detail_status(feed_detail_id, status):
    """
    更新报告邮件订阅明细状态
    :param feed_detail_id:
    :param status:
    :return:
    """
    if not feed_detail_id:
        return
    return repository.update_data(
        "dap_bi_dashboard_email_subscribe_detail",
        {
            "status": status,
        },
        {"id": feed_detail_id},
    )


def feed_snapshot(code, feed_model):
    """
    简讯拍照报告和数据集
    """
    logger.error(f"简讯开始拍照，feed_id: {feed_model.id}")
    from components.snapshot_service import start_snapshot
    from dashboard_chart.services.released_dashboard_service import get_all_dashboard_and_dataset_of_need_snapshot

    # 获取所有关联的dashboard_id和dataset_id
    dashboard_id = feed_model.dashboard_id
    dataset_id_str = feed_model.dataset_ids or "[]"
    dataset_ids = json.loads(dataset_id_str)
    dashboard_ids, dataset_ids = get_all_dashboard_and_dataset_of_need_snapshot(
        [dashboard_id],
        dataset_ids,
        [dashboard_id]
    )
    # 异步执行拍照任务
    snap_id = start_snapshot(code, dashboard_ids=dashboard_ids, dataset_ids=dataset_ids)
    return snap_id


def execute_snapshot_task(code, feed_model) -> str:
    """
    执行拍照
    :param code:
    :param feed_model:
    :return:
    """
    # 判断是否开启拍照
    if feed_model and feed_model.dashboard_id and feed_model.is_snap:
        snap_id = feed_snapshot(code, feed_model)
        return snap_id
    if feed_model and feed_model.dashboard_id and not feed_model.is_snap and feed_model.msg_subscribe_config.snap_type == 2:
        # https://www.tapd.cn/38229611/prong/stories/view/1138229611001662664?from=wxnotification&corpid=wxfe3aa6c1dd22f053&agentid=1000048&jump_count=1&qy_private_corpid=
        # 新功能（快照简讯），不立即拍照发送最新的拍照数据，而是发送上次拍照的数据
        logger.error(f"简讯不执行立即拍照，查询上一次的拍照数据，feed_id: {feed_model.id}")
        from dashboard_snapshot.services.dashboard_snapshot_service import get_dashboard_snapshot_record_list
        data = get_dashboard_snapshot_record_list(feed_model.dashboard_id) or []
        if data:
            # 取最近一次的拍照id
            snap_id = data[0].get('snap_id') or ''
        else:
            logger.error(f"快照简讯没有最近一版的拍照数据，立即进行拍照一份")
            # snap_id = feed_snapshot(code, feed_model)
            # 调用报告的拍照
            # from dashboard_snapshot.services import dashboard_snapshot_service
            # snap_id = dashboard_snapshot_service.just_execute_dashboard_snap(feed_model.dashboard_id)
            from components.snapshot_service import Snapshot
            from app_celery import dashboard_snapshot
            snap_id = Snapshot.generate_snap_record(code)
            dashboard_snapshot.apply_async(args=(code,),kwargs={"snap_id": snap_id, "data_id": feed_model.dashboard_id ,"code": code}, queue='celery-slow')
        return snap_id


def update_flow_status(flow_id, flow_instance_id, status, startup_time=None, end_time=None):
    """
    更新流程和实例状态
    :param flow_id:
    :param flow_instance_id:
    :param status:
    :param startup_time:
    :param end_time:
    :return:
    """
    if not flow_instance_id:
        return
    flow_instance_service.update_instance(flow_instance_id, status, startup_time=startup_time, end_time=end_time)
    flow_service.update_flow_status(flow_id, status, startup_time=startup_time, end_time=end_time)


def get_now_time():
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def send_error_to_wechat(feed_id):
    feed = dashboard_feeds_repository.get_dashboard_feeds(feed_id)
    if not feed:
        return
    subscribe_name = feed['subject_email']
    url = config.get("Subscribe.warning_url")
    dommain = AppHosts.get(SkylineApps.DP, False)
    detail_url = "/".join([dommain, "bi/feeds/detail/wx", feed_id])
    client = auth_util.get_env_name()
    tenant = g.code
    robot = SubscribeRobot(
        robot_url=url, client=client, tenant=tenant, subscribe_name=subscribe_name, detail_url=detail_url, sub_type="简讯"
    )
    robot.send_message()


def check_feed_dataset_and_field(dataset_ids: list, field_ids: list):
    data = {'dataset_error_id': [], 'fields_error_id': []}
    has_dataset_ids = has_field_ids = []
    if dataset_ids:
        has_dataset_ids = repository.get_column('dap_bi_dataset', {'id': dataset_ids}, ['id']) or []
    if field_ids:
        has_field_ids = repository.get_column('dap_bi_dataset_field', {'id': field_ids}, ['id']) or []
    data['dataset_error_id'] = list(set(dataset_ids).difference(set(has_dataset_ids)))
    data['fields_error_id'] = list(set(field_ids).difference(set(has_field_ids)))
    return data


def get_third_app_list_used_user(app_list):
    """
    获取每个应用简讯发送是否使用数见用户
    :param app_list:
    :return:
    """
    third_app_list = FactoryFeed.APP_SEND_MAP.keys()
    for app in app_list:
        app_code = app.get("app_code")
        msg_send_type = app.get("msg_send_type")
        is_shujian_user = 1
        if app_code in third_app_list and msg_send_type == 1:
            is_shujian_user = 0

        app["is_shujian_user"] = is_shujian_user
    return app_list


def feed_used_dataset_stat():
    logger.error("开始执行简讯使用调度数据集统计")
    # 首先删除以后的简讯调度数据集列表
    dashboard_feeds_service.delete_subscribe_dataset_redis()
    page = 1
    while True:
        code_list = _get_project_by_page(page)
        if not code_list:
            break
        _stat_subscribe_dataset(code_list)
        page += 1


def _get_project_by_page(page=1, page_size=100):
    skip = ((int(page) if page else 1) - 1) * (int(page_size) if page_size else 100)
    sql = 'SELECT `code` FROM `dap_p_tenant`'
    with get_master_db() as db:
        sql += ' LIMIT ' + str(skip) + ',' + str(page_size)
        return db.query_columns(sql)


def _stat_subscribe_dataset(code_list):
    from dmplib.saas.project import get_db
    for code in code_list:
        _stat_subscribe_dataset_by_code(code)
        # 关闭数据库连接
        with get_db(code) as db:
            db.end()


def _stat_subscribe_dataset_by_code(code):
    try:
        g.code = code
        g.account = "celery"
        dataset_ids_list = dashboard_feeds_repository.get_all_subscribe_dataset_ids(code) or []
        all_dataset_ids = []
        for dataset_ids in dataset_ids_list:
            try:
                dataset_ids = json.loads(dataset_ids)
            except Exception as _:
                dataset_ids = []
            if dataset_ids:
                all_dataset_ids.extend(dataset_ids)
        all_dataset_ids = list(set(all_dataset_ids))

        logger.error(f"{code}租户简讯全部引用数据集数量：{len(all_dataset_ids)}")
        schedule_dataset_num = dashboard_feeds_service.add_subscribe_used_schedule_dataset(code, all_dataset_ids)
        logger.error(f"{code}租户简讯全部调度数据集数量：{schedule_dataset_num}")
    except Exception as e:
        logger.error(f"{code}租户简讯调度数据集统计失败，errs:" + str(e))


def get_dashboard_ref_feeds(dashboard_id):
    """
    获取报告所有关联的简讯
    """
    if not dashboard_id:
        return []
    feeds = get_subscribe_by_dashboard_id(dashboard_id)
    for feed in feeds:
        subject_email = feed.pop('subject_email', None) or ''
        feed['name'] = msg_get_data_service.clear_title_html(subject_email)
    return feeds
