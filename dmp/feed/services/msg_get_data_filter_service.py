#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import traceback
from loguru import logger
from collections import defaultdict

from dashboard_chart.services import proxy_dataset_service
from dashboard_chart.models import ChartDataModel
from dashboard_chart.formatter.chart_filter_formatter import Chart<PERSON>ilterFormatter
from dashboard_chart.agent.agents import ConditionAgent
from base.query_builder.query import Query


def get_filter_query_struct_obj(replace_dataset_metas, filters):
    """
    获取组件正文中作用于数据集上的过滤条件query struct对象
    :param replace_dataset_metas:
    :param filters:
    :return:
    """
    dataset_filter_where = {}
    if not replace_dataset_metas or not filters:
        return dataset_filter_where

    # 简讯使用到的数据集id
    dataset_id_list = list(replace_dataset_metas.keys())
    return get_filter_struct_by_dataset_id(dataset_id_list, filters)


def get_chapter_filter_query_struct_obj(chapter_model_list, filters):
    """
    明细段落过滤条件解析 struct对象
    :param chapter_model_list:
    :param filters:
    :return:
    """
    dataset_filter_where = {}
    if not chapter_model_list or not filters:
        return dataset_filter_where

    # 明细段落使用到的数据集id
    dataset_id_list = []
    for chapter_model in chapter_model_list:
        dataset_id_list.append(chapter_model.dataset_id)
    dataset_id_list = list(set(dataset_id_list))

    return get_filter_struct_by_dataset_id(dataset_id_list, filters)


def get_filter_struct_by_dataset_id(dataset_id_list, filters):
    """
    按数据集id获取过滤条件query struct对象
    :param dataset_id_list:
    :param filters:
    :return:
    """
    dataset_filter_where = {}
    if not dataset_id_list or not filters:
        return dataset_filter_where
    try:
        dataset_id_list = list(set(dataset_id_list))
        dataset_filter_map = defaultdict(list)
        for filter_model in filters:
            dataset_filter_map[filter_model.dataset_id].append(filter_model.get_dict())
        dataset_filter_where = get_query_struct_filter_by_dataset(dataset_id_list, dataset_filter_map)
    except Exception as e:
        logger.error('获取简讯过滤条件对象异常，err:%s trace:%s' % (str(e), traceback.format_exc()))
    return dataset_filter_where


def get_query_struct_filter_by_dataset(dataset_id_list, dataset_filter_map):
    """
    获取每个数据集的过滤条件的query struct对象
    :param dataset_id_list:
    :param dataset_filter_map:
    :return:
    """
    dataset_filter_where = {}
    if not dataset_filter_map:
        return dataset_filter_where

    used_dataset_id_list = []
    for dataset_id, filter_list in dataset_filter_map.items():
        if dataset_id in dataset_id_list:
            logger.info("过滤条件解析-数据集(ID:%s)存在过滤条件" % dataset_id)
            used_dataset_id_list.append(dataset_id)

    if not used_dataset_id_list:
        return dataset_filter_where

    for dataset_id in used_dataset_id_list:
        dataset_filter_where[dataset_id] = get_query_struct_by_filter(dataset_id, dataset_filter_map.get(dataset_id))

    return dataset_filter_where


def get_query_struct_by_filter(dataset_id, filter_list):
    """
    获取指定数据集的过滤条件query struct对象
    :param dataset_id:
    :param filter_list:
    :return:
    """
    query_struct = None
    if not filter_list:
        return query_struct
    field_id_list = [item.get("dataset_field_id") for item in filter_list]
    field_id_list = list(set(field_id_list))
    # 批量获取数据集字段的表数据
    field_data_dict = proxy_dataset_service.batch_get_formatted_fields_by_field_id(field_id_list)
    # 注意！需要将数据集id设置到字段信息中，非常重要！
    for dataset_field_id, item in field_data_dict.items():
        field_data_dict[dataset_field_id]["id"] = dataset_field_id

    # 附加过滤条件的字段信息
    _match_field_info(filter_list, field_data_dict)
    return _convert_dataset_filter(dataset_id, filter_list, field_data_dict)


def _match_field_info(query_data, field_data_dict):
    """
    匹配对应数据集字段信息
    :param query_data:
    :param field_data_dict:
    :return:
    """
    for single_query_data in query_data:
        dataset_field_id = single_query_data.get("dataset_field_id", "")
        if not dataset_field_id:
            continue
        field_data = field_data_dict.get(dataset_field_id)
        if not field_data:
            continue
        for k, v in field_data.items():
            if k not in ['rank']:
                single_query_data.update({k: v})


def _convert_dataset_filter(dataset_id, filter_list, field_data_dict):
    """
    将过滤条件转换为query struct对象
    借鉴组件过滤器的转换处理逻辑，这里构造的有效数据只有where节点对象
    :param dataset_id:
    :param filter_list:
    :param field_data_dict:
    :return:
    """
    query_struct = {}
    model = ChartDataModel()
    # 数据集信息
    model.dataset = proxy_dataset_service.get_dataset(dataset_id)
    # 指定的数据集字段信息
    model.dataset_field_dict = field_data_dict
    # 过滤信息
    model.filters = filter_list
    # 构造条件对象 dashboard_chart.convertor.field_types.WhereField
    where = ChartFilterFormatter(model).conditions()
    # 条件对象转换为逻辑关系表达式对象，例如 base.query_builder.expressions.And
    if where and model.dataset_field_dict:
        where_agent = ConditionAgent()
        where_query = where_agent.convert(where, model.dataset_field_dict)
        # query 对象构造和转换 base.query_builder.Query
        query_model = Query()
        if where_query:
            query_model.where(where_query)
            # 按json格式转换为query struct结构（转换逻辑：base.query_builder.builders.json.Builder）
            query_struct = query_model.build(output_format="json")
    return query_struct


