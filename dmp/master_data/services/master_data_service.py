#!/usr/bin/env python
# -*- coding: utf-8 -*-

from dmplib import config
import json
import logging

import time
import traceback
from datetime import datetime

import requests
from hashlib import md5
from requests import RequestException

import app_celery
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib.redis import RedisCache
from master_data.models import CorporateKeyuser
from master_data.repositories import master_data_repository

from user.services.user_service import get_cur_role_id
from dmplib.constants import ADMIN_ROLE_ID

logger = logging.getLogger(__name__)

def corporate_list(**kwargs):
    """
    :主数据列表
    :param str name :
    :param create_type :
    :return list:
    """ 
    list_data = master_data_repository.corporate_list(**kwargs)
    return list_data

def corporate_binded_tenant_list():
    """
    :主数据列表
    :param str name :
    :param create_type :
    :return list:
    """ 
    list_data = master_data_repository.corporate_binded_tenant_list()
    return list_data 

def corporate_info(**kwargs):
    """
    :编辑主数据
    :param str id :
    :return list:
    """ 
    id = kwargs.get('id', "")
    corporate_info = master_data_repository.corporate_info(id)
    if corporate_info is None:
        return []
    corporate_tenant_info = master_data_repository.corporate_tenant_info(id)
    corporate_erp_ver_info = master_data_repository.corporate_erp_ver_info(id)
    corporate_info['corporate_tenant_info']=corporate_tenant_info
    corporate_info['corporate_erp_ver_info']=corporate_erp_ver_info
    return corporate_info

def corporate_save(**kwargs):
    """
    :保存主数据
    :param str id :
    :return list:
    """ 
    id = kwargs.get('id', "")
    corporate_obj={
        "id":id,
        "full_name":kwargs.get('full_name', ""),
        "name":kwargs.get('name', ""),
        "label":kwargs.get('label', ""),
        "remark":kwargs.get('remark', ""),
        "area":kwargs.get('area', ""),
        "area_type":kwargs.get('area_type', ""),
        "manager":kwargs.get('manager', "")
    }
    erp_sys_ver_list=kwargs.get('erp_sys_ver_list', [])
    corporate_tenant_list=kwargs.get('corporate_tenant_list', [])
    if  id:  #修改模式
        update_corporte(corporate_obj,erp_sys_ver_list,corporate_tenant_list)
        return id
    else:#新增模式 
        return add_corporte(corporate_obj,erp_sys_ver_list,corporate_tenant_list)

def update_corporte(corporate_obj,erp_sys_ver_list,corporate_tenant_list):
    id = corporate_obj['id']
    #先判断绑定关系      
    for tenant in corporate_tenant_list:
        repet_list=master_data_repository.corporate_tenant_repet_list(tenant)
        if len(repet_list)>0  and repet_list[0]["id"] != tenant["id"]:
            raise UserError(message='该产品code已经被其他客户绑定') 
    #删除本次删除的绑定关系
    old_corporate_tenant_list = master_data_repository.corporate_tenant_info(id)
    old_tenant_bing_id_list = [ i['id']  for i in old_corporate_tenant_list ]
    new_tenant_bing_id_list= [ i['id']  for i in corporate_tenant_list]
    for tenant_bing_id in old_tenant_bing_id_list:
        if tenant_bing_id not in new_tenant_bing_id_list:
            master_data_repository.corporate_tenant_delete(tenant_bing_id) 
    #更新或者插入新的绑定关系
    for tenant in corporate_tenant_list:
        if tenant["id"]:                
            master_data_repository.corporate_tenant_update(tenant) 
        else:
            del tenant['id']
            tenant['corp_id']=id
            master_data_repository.corporate_tenant_insert(tenant) 
    
        #删除本次删除的ERP版本
    old_corporate_erp_ver_info = master_data_repository.corporate_erp_ver_info(id)
    old_erp_ver_info_id_list = [ i['id']  for i in old_corporate_erp_ver_info ]
    new_erp_ver_info_id_list= [ i['id']  for i in erp_sys_ver_list]
    for ver_info_id_ in old_erp_ver_info_id_list:
        if ver_info_id_ not in new_erp_ver_info_id_list:
            master_data_repository.corporate_erp_ver_delete(ver_info_id_) 
    #ERP版本插入
    for erp_sys_ver in erp_sys_ver_list:
        if erp_sys_ver["id"]:                
            master_data_repository.corporate_erp_ver_update(erp_sys_ver) 
        else:
            del erp_sys_ver['id']
            erp_sys_ver['corp_id']=id
            master_data_repository.corporate_erp_ver_insert(erp_sys_ver)
    master_data_repository.corporate_update(corporate_obj) 
    return id

def add_corporte(corporate_obj,erp_sys_ver_list,corporate_tenant_list):
    id = seq_id()
    corporate_obj['id']=id
    for tenant in corporate_tenant_list:
        repet_list=master_data_repository.corporate_tenant_repet_list(tenant)  
        if len(repet_list)>0 :
            raise UserError(message='该产品code已经被其他客户绑定')      
    for tenant in corporate_tenant_list:
        tenant["source"]="手动添加"
        tenant["corp_id"]=id
        del tenant['id']
        master_data_repository.corporate_tenant_insert(tenant)  
    for erp_ver in erp_sys_ver_list:
        erp_ver["source"]="手动添加"
        erp_ver["corp_id"]=id
        del erp_ver['id']
        master_data_repository.corporate_erp_ver_insert(erp_ver)  
    master_data_repository.corporate_insert(corporate_obj) 
    return id

def add_corporte(corporate_obj,erp_sys_ver_list,corporate_tenant_list):
    id = seq_id()
    corporate_obj['id']=id
    for tenant in corporate_tenant_list:
        repet_list=master_data_repository.corporate_tenant_repet_list(tenant)  
        if len(repet_list)>0 :
            raise UserError(message='该产品code已经被其他客户绑定')      
    for tenant in corporate_tenant_list:
        tenant["source"]="手动添加"
        tenant["corp_id"]=id
        del tenant['id']
        master_data_repository.corporate_tenant_insert(tenant)  
    for erp_ver in erp_sys_ver_list:
        erp_ver["source"]="手动添加"
        erp_ver["corp_id"]=id
        del erp_ver['id']
        master_data_repository.corporate_erp_ver_insert(erp_ver)  
    master_data_repository.corporate_insert(corporate_obj) 
    return id

def corporate_delete(**kwargs):
    """
    :删除主数据
    :param str id : 
    """ 
    id = kwargs.get('id', "")
    master_data_repository.corporate_delete(id)    
    return True
def area_list():
    """
    :获取区域列表
    """
    area_list = master_data_repository.area_list()
    return area_list

def erp_sys_ver_list():
    """
    :获取ERP子系统的列表
    """
    erp_sys_ver_list = master_data_repository.erp_sys_ver_list()
    return erp_sys_ver_list

def product_list():
    """
    :获取产品的列表
    """
    product_list = master_data_repository.product_list()
    return product_list

def product_tenant_list(**kwargs):#todo:
    """
    :获取产品的对应的租户列表
    """
    prod_code = kwargs.get('prod_code', "")
    product_info = master_data_repository.product_info(prod_code) 
    if product_info['get_type']=='api':#api模式
        return api_get_prod_list(product_info['get_info'])
def api_get_prod_list(url_info):
    try:
        headers = {"Referer": "http://erpapi.fdccloud.com"}
        url_list=url_info.split(';')
        ret_list=[]
        for url in url_list:
            response =requests.get(url, params=[], timeout=60, headers=headers)
            res_obj = json.loads(response.text)
            if res_obj['success']:
                ret_list+= res_obj['data'] 
        return ret_list
    except:
        return  []
def corporate_keyuser_info(**kwargs):
    """
    :主数据客户列表
    :param str name :
    :param create_type :
    :return list:
    """ 
    list_data = master_data_repository.corporate_list(**kwargs)
    return list_data
def corporate_keyuser_list(**kwargs):
    """
    :主数据客户关键用户列表
    :param str name :
    :param create_type :
    :return list:
    """ 
    list_data = master_data_repository.corporate_keyuser_list(**kwargs)
    return list_data

def corporate_keyuser_info(**kwargs):
    """
    :编辑关键用户
    :param str id :
    :return list:
    """ 
    id = kwargs.get('id', "")
    corporate_info = master_data_repository.corporate_keyuser_info(id)
    return corporate_info

def corporate_keyuser_save(model: CorporateKeyuser):
    """
    :保存关键用户
    :param str id :
    :return list:
    """ 
    id = model.id     
    if  id:
        #model.id = seq_id()
        keyuser = model.get_dict() 
        #判断用户是否已经被添加了，防止重复添加，1、用户名不能重复；绑定的用户不能重复
        user_list=master_data_repository.corporate_keyuser_repet_list(keyuser)  
        for user in user_list:
            if user["id"]!=model.id:
                raise UserError(message='该用户姓名重复或者用户已经被绑定') 
        return master_data_repository.corporate_keyuser_update(keyuser)  
    else:#新增模式
        keyuser = model.get_dict(['corp_id', 'yk_user_code', 'yzs_user_code',
        'bi_user_code', 'user_name', 'position', 'positiondesc', 'is1w', 'remark'])
        user_list=master_data_repository.corporate_keyuser_repet_list(keyuser)  
        if len(user_list)>0 :
            raise UserError(message='该用户姓名重复或者用户已经被绑定')        
        keyuser['source']="手动新增"
        return master_data_repository.corporate_keyuser_insert(keyuser) 
        
def corporate_keyuser_delete(**kwargs): 
    #删除关键用户   
    id = kwargs.get('id', "")
    master_data_repository.corporate_keyuser_delete(id)    
    return True
    
