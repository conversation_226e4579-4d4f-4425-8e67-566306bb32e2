from dmplib.hug import APIWrapper
from base.enums import HdUpgradeModuleList
from dmplib.utils.errors import UserError
from master_data.models import CorporateKeyuser
from .services import master_data_service
import urllib.parse

api = APIWrapper(__name__)


@api.admin_route.get('/corporate_list')
def corporate_list(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {get} /api/corporate_list 主数据客户模块
    @apiGroup  客户主数据
    @apiParam query {string}  name 客户名称/绑定的code
    {
        "name": "cmsk",
        "area": "",
        "label": "",
    }
    @apiResponse 200  [
           {"id": "000FD8C8-6F85-4B8D-83DD-4D9A089BAEBA", "full_name": "重庆爱普地产(集团)有限公司", "name": "重庆爱普地产(集团)有限公司", "label": "拓新客户", "remark": "初始化运维", "scrm_customer_id": "000FD8C8-6F85-4B8D-83DD-4D9A089BAEBA", "scrm_top_customer_id": "000FD8C8-6F85-4B8D-83DD-4D9A089BAEBA", "yw_customer_id": "000fd8c8-6f85-4b8d-83dd-4d9a089baeba", "yw_top_customer_id": "000FD8C8-6F85-4B8D-83DD-4D9A089BAEBA", "area": "重庆区域", "area_type": "B", "manager": "朱丽", "created_on": "2022-03-21 23:13:28", "created_by": null, "modified_on": "2022-03-21 23:13:28", "modified_by": null, "source": "导入数据", "ykcode": null, "tenant_code": "APDC", "d.tenant_code": null}
    ]
    **/
    """
    return True, 'success', master_data_service.corporate_list(**kwargs)

@api.admin_route.get('/corporate_info')
def corporate_info(**kwargs):
    return True, 'success', master_data_service.corporate_info(**kwargs)

@api.admin_route.post('/corporate_save')
def corporate_save(**kwargs):
    return True, 'success' , master_data_service.corporate_save(**kwargs)

@api.admin_route.get('/corporate_delete')
def corporate_delete(**kwargs):
    return True, 'success' , master_data_service.corporate_delete(**kwargs)

@api.admin_route.get('/area_list')
def area_list():
    return True, 'success', master_data_service.area_list()

@api.admin_route.get('/product_list')
def product_list():
    return True, 'success',master_data_service.product_list()

@api.admin_route.get('/product_tenant_list')
def product_tenant_list(**kwargs):
    return True, 'success',master_data_service.product_tenant_list(**kwargs)

@api.admin_route.get('/erp_sys_ver_list')
def erp_sys_ver_list():
    return True, 'success', master_data_service.erp_sys_ver_list() 

@api.admin_route.get('/corporate_keyuser_list')
def corporate_keyuser_list(**kwargs):
    return True, 'success', master_data_service.corporate_keyuser_list(**kwargs)

@api.admin_route.get('/corporate_keyuser_info')
def corporate_keyuser_info(**kwargs):
    return True, 'success', master_data_service.corporate_keyuser_info(**kwargs)

@api.admin_route.get('/corporate_binded_tenant_list')
def corporate_binded_tenant_list():
    return True, 'success' , master_data_service.corporate_binded_tenant_list()

@api.admin_route.post('/corporate_keyuser_save')
def corporate_keyuser_save(**kwargs):
    model = CorporateKeyuser(**kwargs)
    return True, 'success' , master_data_service.corporate_keyuser_save(model)

@api.admin_route.get('/corporate_keyuser_delete')
def corporate_keyuser_delete(**kwargs):
    return True, 'success' , master_data_service.corporate_keyuser_delete(**kwargs)

