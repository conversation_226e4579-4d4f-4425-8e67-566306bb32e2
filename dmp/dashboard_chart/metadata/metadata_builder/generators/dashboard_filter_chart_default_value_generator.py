#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardFilterChartDefaultValueGenerator(MetadataBaseGenerator):
    """
    单图筛选默认值
    """

    def generator_name(self):
        return "dashboard_filter_chart_default_value"

    def build(self):
        """

        :return:
        """
        default_value_meta = []
        data_dict = self.storage.get_specific_table_data("dap_bi_dashboard_filter_chart_default_values")
        data_dict = filter(lambda i: i.get("chart_id") == self.op_chart_id, data_dict)
        for item in data_dict:
            default_value_meta.append(
                {
                    "id": item.get("id"),
                    "dataset_field_id": item.get("dataset_field_id"),
                    "operator": item.get("operator"),
                    "value": item.get("value"),
                    "select_all": item.get("select_all"),
                    "extend_data": item.get("extend_data"),
                }
            )
        return default_value_meta
