#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""

"""
from base.dmp_constant import SCHEMA_VERSION

# dashboard节点
dashboard_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": ["object", "string", "null"],
    "title": "dashboard_schema",
    "required": [
        "cover",
        "layout",
        "publish",
        "name",
        "id",
        "level_code",
        "biz_code",
        "scale_mode",
        "styles",
    ],
    "properties": {
        "cover": {
            "$id": "#/properties/cover",
            "type": ["string", "null"],
            "title": "The Cover Schema",
            "default": "",
        },
        "layout": {
            "$id": "#/properties/layout",
            "type": ["object", "string", "null"],
            "title": "The Layout Schema",
        },
        "publish": {
            "$id": "#/properties/publish",
            "type": ["object", "string", "null"],
            "title": "The Publish Schema",
            "required": ["share_secret_key", "status", "released_on"],
            "properties": {
                "share_secret_key": {
                    "$id": "#/properties/publish/properties/share_secret_key",
                    "type": ["string", "null"],
                    "title": "The Share_secret_key Schema",
                    "default": "",
                    "pattern": "^(.*)$",
                },
                "status": {
                    "$id": "#/properties/publish/properties/status",
                    "type": "integer",
                    "title": "The Status Schema",
                },
                "type_access_released": {
                    "$id": "#/properties/publish/properties/type_access_released",
                    "type": "integer",
                    "title": "The Type_access_released Schema",
                },
                "released_on": {
                    "$id": "#/properties/publish/properties/released_on",
                    "type": ["string", "null"],
                    "title": "The Released_on Schema",
                    "default": "",
                    "examples": ["2018-10-16T10:06:25"],
                    "pattern": "^(.*)$",
                },
            },
        },
        "name": {
            "$id": "#/properties/name",
            "type": ["string", "null"],
            "title": "The Name Schema",
            "pattern": "^(.+)$",
        },
        "id": {
            "$id": "#/properties/id",
            "type": "string",
            "title": "The Id Schema",
            "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
            "pattern": "^(.+)$",
        },
        "level_code": {
            "$id": "#/properties/level_code",
            "type": ["string", "null"],
            "title": "The Level_code Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        "type": {
            "$id": "#/properties/type",
            "type": ["string", "null"],
            "title": "The Type Schema",
            "default": "",
            "examples": ["FILE"],
            "pattern": "^(.*)$",
        },
        "refresh_rate": {
            "$id": "#/properties/refresh_rate",
            "type": ["string", "null"],
            "title": "The Refresh_rate Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        "biz_code": {
            "$id": "#/properties/biz_code",
            "type": ["string", "null"],
            "title": "The Biz_code Schema",
            "default": "",
            "examples": ["a6dcdd0f75514cf38dcaf5e13d827a89"],
            "pattern": "^(.*)$",
        },
        "data_type": {"$id": "#/properties/data_type", "type": "integer", "title": "The Data_type Schema"},
        "is_multiple_screen": {
            "$id": "#/properties/is_multiple_screen",
            "type": "integer",
            "title": "The Is_multiple_screen Schema",
            "default": 0,
        },
        "snapshot_id": {
            "$id": "#/properties/snapshot_id",
            "type": ["string", "null"],
            "title": "The Snapshot_id Schema",
            "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
            "pattern": "^(.*)$",
        },
        "scale_mode": {
            "$id": "#/properties/scale_mode",
            "type": "integer",
            "title": "The Scale_mode Schema",
            "default": 0,
        },
        "styles": {
            "$id": "#/properties/styles",
            "type": ["object", "string", "null"],
            "title": "The Styles Schema",
            "required": ["attrs", "theme"],
            "properties": {
                "background": {
                    "$id": "#/properties/styles/properties/background",
                    "type": ["object", "string", "null"],
                    "title": "The Background Schema",
                    "required": ["image", "size", "color"],
                    "properties": {
                        "image": {
                            "$id": "#/properties/styles/properties/background/properties/image",
                            "type": ["string", "null"],
                            "title": "The Image Schema",
                            "default": "",
                            "pattern": "^(.*)$",
                        },
                        "size": {
                            "$id": "#/properties/styles/properties/background/properties/size",
                            "type": ["string", "null"],
                            "title": "The Size Schema",
                            "default": "",
                            "examples": ["stretch"],
                            "pattern": "^(.*)$",
                        },
                        "color": {
                            "$id": "#/properties/styles/properties/background/properties/color",
                            "type": ["string", "null"],
                            "title": "The Color Schema",
                            "default": "",
                            "examples": ["RGBA(15,24,47,1)"],
                            "pattern": "^(.*)$",
                        },
                    },
                },
                "attrs": {
                    "$id": "#/properties/styles/properties/attrs",
                    "type": ["object", "string", "null"],
                    "title": "The Attrs Schema",
                },
                "theme": {
                    "$id": "#/properties/styles/properties/theme",
                    "type": ["string", "null"],
                    "title": "The Theme Schema",
                    "default": "",
                    "examples": ["tech_blue"],
                    "pattern": "^(.*)$",
                },
            },
        },
    },
}
