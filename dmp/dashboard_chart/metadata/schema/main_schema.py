#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""

"""
from base.dmp_constant import SCHEMA_VERSION


# 元数据主schema
main_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "object",
    "title": "main_schema",
    "required": ["dashboard", "first_report", "screens"],
    "properties": {
        "dashboard": {"$id": "#/properties/dashboard", "type": "object", "title": "dashboard"},
        "first_report": {"$id": "#/properties/first_report", "type": "object", "title": "first_report"},
        "screens": {"$id": "#/properties/screens", "type": "array", "title": "screens"},
        "installed_component": {
            "$id": "#/properties/installed_component",
            "type": "array",
            "title": "installed_component",
        },
    },
}
