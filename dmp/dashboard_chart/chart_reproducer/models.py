#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/2 14:44
# <AUTHOR> caoxl
# @File     : models.py
from base.models import BaseModel


class CopyTaskDataModel(BaseModel):
    __slots__ = [
        "source_dashboard_id",
        "source_chart_id",
        "target_dashboard_id",
        "source_dashboard_metadata",
        "target_dashboard_metadata",
        "userid",
    ]

    def __init__(self, **kwargs):
        """
        任务数据model
        :param kwargs:
        """
        self.source_dashboard_id = None
        self.source_chart_id = None
        self.target_dashboard_id = None
        self.source_dashboard_metadata = None
        self.userid = None
        self.account = None
        self.target_dashboard_metadata = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(
            (['source_dashboard_id', 'source_chart_id', 'target_dashboard_id', 'userid'], 'string', {'max': 36})
        )
        rules.append((['source_dashboard_metadata', 'target_dashboard_metadata'], 'string'))
        rules.append((['account'], 'string', {'required': False}))
        return rules


class ReproducerNodeDataModel(BaseModel):
    __slots__ = [
        "source_dashboard_id",
        "source_chart_id",
        "target_dashboard_id",
        "source_dashboard_metadata",
        "target_dashboard_metadata",
        "userid",
    ]

    def __init__(self, **kwargs):
        """
        复制各节点基础数据model
        :param kwargs:
        """
        self.source_dashboard_id = None
        self.source_chart_id = None
        self.target_dashboard_id = None
        self.source_dashboard_metadata = None
        self.userid = None
        self.account = None
        self.target_dashboard_metadata = None
        super().__init__(**kwargs)


class ReproducerChartNodeDataModel(ReproducerNodeDataModel):
    __slots__ = ["target_parent_chart_id", "z_index_step", "container_id"]

    def __init__(self, **kwargs):
        """
        单图节点数据model
        :param kwargs:
        """
        self.target_parent_chart_id = None
        self.container_id = None
        self.z_index_step = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['target_parent_chart_id'], 'string', {'max': 36}))
        rules.append((['z_index_step'], 'number'))
        return rules


class ChartLeafNodeDataModel(ReproducerChartNodeDataModel):
    __slots__ = ["target_chart_id"]

    def __init__(self, **kwargs):
        """
        单图个叶子节点数据model
        :param kwargs:
        """
        self.target_chart_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['target_chart_id'], 'string', {'max': 36}))
        return rules
