#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/6 11:05
# <AUTHOR> caoxl
# @File     : field_sort.py
from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class FieldSortNode(LeafBaseNode):
    """
    维度节点
    """

    def build(self, metadata: dict) -> dict:
        chart_metadata = self.get_chart_metadata(self.target_chart_id, metadata)
        field_sorts = self.get_data_by_path("data.indicator.field_sorts", chart_metadata) or []
        for field_sort in field_sorts:
            field_sort["dashboard_chart_id"] = self.target_chart_id
            field_sort["id"] = seq_id()
        return metadata
