#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/2 11:40
# <AUTHOR> caoxl
# @File     : dim_node.py
from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class DimNode(LeafBaseNode):
    """
    维度节点
    """

    def build(self, metadata: dict) -> dict:
        chart_metadata = self.get_chart_metadata(self.target_chart_id, metadata)
        dims = self.get_data_by_path("data.indicator.dims", chart_metadata) or []
        for dim in dims:
            dim["dashboard_chart_id"] = self.target_chart_id
            dim["id"] = seq_id()
        return metadata
