#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartFilterModel, DashboardChartFilterRelation


class DashboardChartFilter(Editor):
    def edit(self):
        errors = []
        models = []
        dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
        charts = self.metadata_storage.metadata_dashboard_charts()
        for chart in charts:
            chart_filters = self.metadata_storage.get_data_by_path('data.indicator.filters', chart)
            for chart_filter in chart_filters:
                model = DashboardChartFilterModel(
                    id=chart_filter.get('id'),
                    dashboard_id=dashboard_id,
                    dashboard_chart_id=chart_filter.get('dashboard_chart_id'),
                    col_name=chart_filter.get('col_name'),
                    dataset_field_id=chart_filter.get('dataset_field_id'),
                )
                operators = chart_filter.get('operators')
                for operator in operators:
                    op_model = DashboardChartFilterRelation(
                        id=operator.get('id'),
                        dashboard_id=dashboard_id,
                        dashboard_chart_id=chart_filter.get('dashboard_chart_id'),
                        dashboard_chart_filter_id=chart_filter.get('id'),
                        operator=operator.get('operator'),
                        col_value=operator.get('col_value'),
                    )
                    models.append(op_model)
                models.append(model)
        return models, errors

    def get_action_name(self):
        return '保存单图过滤器'
