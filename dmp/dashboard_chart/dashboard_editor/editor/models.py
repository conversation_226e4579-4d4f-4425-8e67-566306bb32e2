#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/23 11:29
# <AUTHOR> caoxl
# @File     : models.py
# pylint: skip-file

from abc import ABCMeta, abstractmethod
from collections import OrderedDict
from dashboard_chart.dashboard_editor.editor_utils import EditorUtils
from dmplib.utils.errors import UserError

from base.models import BaseModel
from base.enums import JumpConfigSourceType, ChartFilterInitiatorSouce


class EditorBaseModel(BaseModel, metaclass=ABCMeta):
    def __init__(self, default=None, **kwargs):
        """
        初始化赋值
        :param default: dict 默认的值
        :param kwargs:
        """
        default = default or {}
        for v in self.__slots__:
            attr_val = None
            if v in default:
                attr_val = default[v]
            setattr(self, v, attr_val)
        super().__init__(**kwargs)

    @abstractmethod
    def get_table_name(self):
        """
        获取模型对应的表名称
        :return: str
        """
        pass

    def get_ordered_dict(self):
        """
        获取经过排序的dict
        :return:
        """
        _dict = self.get_dict()
        _dict_keys = sorted(_dict.keys())
        ordered_dict = OrderedDict()
        for _dict_key in _dict_keys:
            ordered_dict[_dict_key] = _dict.get(_dict_key)
        return ordered_dict

    def get_where_field(self):
        return ['id']

    def get_where(self):
        """
        获取数据库操作的where条件
        :return:
        """
        order_dict = self.get_ordered_dict()
        where_field = self.get_where_field()
        if not where_field:
            return order_dict
        return {f: v for f, v in order_dict.items() if f in where_field}

    def get_pk(self):
        """获取表的主键字段"""
        if hasattr(self, 'id'):
            return 'id'
        return None

    def get_use_resource(self):
        """
        获取引用的外部资源
        dashboard_ids 报告id集合
        dateset_ids 数据集id集合
        dataset_field_ids 数据集字段id集合
        dashboard_filter_ids 报告过滤器的id集合
        :return: {"dashboard_ids":set(), "dateset_ids":set(), "dataset_field_ids":set(), "dashboard_filter_ids":set()}
        """
        return None

    def get_used_resource(self):
        """
        获取会被外部引用的资源
        dashboard_filter_ids 报告过滤器的id集合
        :return: {"dashboard_filter_ids":set()}
        """
        return None

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        return self.get_ordered_dict() == other.get_ordered_dict()

    def __hash__(self):
        row = self.get_ordered_dict()
        row['table_name'] = self.get_table_name()
        hash_touple = list()
        for key, value in row.items():
            hash_touple.append(tuple([key, EditorUtils.convert_to_db_value(value)]))
        return hash(tuple(hash_touple))


class DashboardChartJumpBaseModel(EditorBaseModel):

    def get_table_name(self):
        return None

    def target_dashboard_use_resource(self):
        """
        获取目标报告页的引用数据检测
        新增校验，1、目标报告页下拉筛选和字段是否存在；2、目标报告页作用变量是否存在
        :return:
        """
        resource = {}
        # if self.relation_field_type or self.target_filter_id or self.target_filter_field_id:
        #     # 关联的类型 1:全局筛选 2:页面内的筛选器 3:变量
        #     # 全局筛选校验规则原来已经添加
        #     if self.relation_field_type == 2:
        #         resource["chart_ids"] = {self.target_filter_id}
        #         resource["dataset_field_ids"] = {self.target_filter_field_id}
        #     elif self.relation_field_type == 3:
        #         resource["dataset_var_ids"] = {self.target_filter_field_id}
        return resource

    def merge_target_use_resource(self, current_resource):
        """
        将当前报告的引用资源和目标报告的资源合并，然后统一校验
        :param current_resource:
        :return:
        """
        target_resource = self.target_dashboard_use_resource()
        if not target_resource:
            return current_resource
        for action, val in current_resource.items():
            if val and action in target_resource:
                current_resource[action].update(target_resource[action])
                del target_resource[action]
        if target_resource:
            current_resource.update(target_resource)
        return current_resource


class DashboardFilterModel(EditorBaseModel):
    __slots__ = ["id", "dashboard_id", "main_dataset_field_id", "filter_relation"]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.main_dataset_field_id = None
        self.filter_relation = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_filter'

    def get_use_resource(self):
        return {'dataset_field_ids': {self.main_dataset_field_id}}

    def get_used_resource(self):
        pass


class DashboardFilterRelationModel(EditorBaseModel):
    __slots__ = ["id", "dashboard_id", "dashboard_filter_id", "operator", "col_value", "select_all_flag"]

    def __init__(self, **kwargs):
        """
        报告级筛选关系
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.dashboard_filter_id = None
        self.operator = None
        self.col_value = None
        self.select_all_flag = 0
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_filter_relation'

    def get_used_resource(self):
        pass


class DashboardDatasetFieldRelationModel(EditorBaseModel):
    __slots__ = ["id", "dashboard_id", "main_dataset_field_id", "related_dataset_field_id"]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.main_dataset_field_id = None
        self.related_dataset_field_id = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_dataset_field_relation'

    def get_use_resource(self):
        return {'dataset_field_ids': {self.main_dataset_field_id, self.related_dataset_field_id}}


class DashboardDatasetVarFieldRelationModel(EditorBaseModel):
    __slots__ = ["id", "dashboard_id", "var_name", "related_dataset_field_id", "related_dataset_id", "data_type"]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.var_name = None
        self.related_dataset_id = None
        self.related_dataset_field_id = None
        self.data_type = None

        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_filter_vars_relation_field'

    def get_use_resource(self):
        return {'dataset_field_ids': {self.related_dataset_field_id}}


class DashboardChartFilterModel(EditorBaseModel):
    __slots__ = ["id", "dashboard_id", "dashboard_chart_id", "col_name", "dataset_field_id"]

    def __init__(self, **kwargs):
        """
        单图筛选
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.col_name = None
        self.operator = None
        self.col_value = None
        self.dataset_field_id = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_filter'

    def get_use_resource(self):
        return {'dataset_field_ids': {self.dataset_field_id}}


class DashboardChartFilterRelation(EditorBaseModel):
    __slots__ = ['id', 'dashboard_chart_id', 'dashboard_chart_filter_id', 'operator', 'col_value', 'dashboard_id']

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_filter_relation'


class DashboardChartComparisonModel(EditorBaseModel):
    """
    单图对比数据模型类
    """

    __slots__ = [
        "id",
        "dashboard_id",
        "dashboard_chart_id",
        "dataset_field_id",
        "alias",
        "content",
        "formula_mode",
        "rank",
        "sort",
    ]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.alias = None
        self.content = None
        self.formula_mode = 0
        self.rank = 0
        self.sort = 0
        super().__init__(**kwargs)

    def get_table_name(self):
        return "dap_bi_dashboard_chart_comparison"

    def get_use_resource(self):
        return {'dataset_field_ids': {self.dataset_field_id}}


class DashboardChartPenetrateRelationModel(EditorBaseModel):
    """穿透字段的关联关系"""

    __slots__ = [
        'id',
        'dashboard_chart_id',
        'dashboard_id',
        'parent_chart_field_id',
        'child_chart_field_id',
        'type',
        'parent_chart_var_id',
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_penetrate_relation'

    def get_use_resource(self):
        return {'dataset_field_ids': {self.parent_chart_field_id, self.child_chart_field_id}}


class DashboardChartSelectorModel(EditorBaseModel):
    """联动"""

    __slots__ = ['id', 'type', 'dashboard_id', 'chart_initiator_id', 'chart_responder_id', 'is_same_dataset']

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_selector'


class DashboardChartSelectorFieldModel(EditorBaseModel):
    """联动字段"""

    __slots__ = ['id', 'selector_id', 'chart_id', 'field_initiator_id', 'field_responder_id', 'dashboard_id']

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_selector_field'

    def get_use_resource(self):
        return {'dataset_field_ids': {self.field_initiator_id, self.field_responder_id}}


class DashboardComponentFilterModel(EditorBaseModel):
    __slots__ = ['id', 'chart_initiator_id', 'chart_responder_id', 'dataset_id', 'is_same_dataset']

    def get_table_name(self):
        return 'dap_bi_dashboard_component_filter'

    def get_use_resource(self):
        return {'dataset_ids': {self.dataset_id}}


class DashboardComponentFilterFieldModel(EditorBaseModel):
    __slots__ = ['id', 'field_initiator_id', 'field_responder_id', 'chart_id', 'filter_id']

    def get_table_name(self):
        return 'dap_bi_dashboard_component_filter_field_relation'

    def get_use_resource(self):
        return {'dataset_field_ids': {self.field_initiator_id, self.field_responder_id}}


class DashboardChartParamsModel(EditorBaseModel):
    __slots__ = ['dashboard_id', 'dashboard_chart_id', 'dataset_field_id', 'alias', 'rank', 'param_id']

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_params'

    def get_pk(self):
        return 'param_id'

    def get_where_field(self):
        return ['param_id']

    def get_use_resource(self):
        return {'dataset_field_ids': {self.dataset_field_id}}


class DashboardChartParamsJumpModel(DashboardChartJumpBaseModel):
    __slots__ = [
        'dashboard_id',
        'dashboard_chart_id',
        'param_dataset_field_id',
        'source_id',
        'dashboard_filter_id',
        'rank',
        'relation_field_type',
        'target_filter_id',
        'target_filter_field_id',
        'global_params_id',
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_params_jump'

    def get_where_field(self):
        return [
            'dashboard_id',
            'dashboard_chart_id',
            'param_dataset_field_id',
            'source_id',
            'dashboard_filter_id',
            'rank',
            'filter_type',
            'relation_field_type',
            'target_filter_id',
            'target_filter_field_id',
            'global_params_id',
        ]

    def get_use_resource(self):
        resource = {"dashboard_filter_ids": {self.dashboard_filter_id},
                    "dataset_field_ids": {self.param_dataset_field_id}}
        # 目标报告页引用资源检查
        resource = self.merge_target_use_resource(resource)
        return resource


class DashboardChartTriggersModel(EditorBaseModel):
    """
    报告跳转配置
    """

    __slots__ = [
        'dashboard_id',
        'dashboard_chart_id',
        'id',
        'type',
        'actions',
        'conditions',
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_visible_triggers'


class DashboardJumpConfigModel(EditorBaseModel):
    """
    报告跳转配置
    """

    __slots__ = [
        'dashboard_id',
        'dashboard_chart_id',
        'source_id',
        'source_type',
        'target',
        'target_type',
        'open_way',
        'status',
        'has_token',
        'with_params',
        'title',
        'unbound_related_dims',
        'redirect_window_config',
        'id',
        'condition_jump',
        'is_default',
        'sort',
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_jump_config'

    def get_use_resource(self):
        ret = {}
        if self.source_type != JumpConfigSourceType.Title.value:
            ret['dataset_field_ids'] = {self.source_id}
        if self.target_type == 'dashboard':
            ret['dashboard_ids'] = {self.target}
        return ret


class DashboardJumpRelationModel(DashboardChartJumpBaseModel):
    """
    报告跳转-筛选关系model
    """

    __slots__ = [
        'jump_config_id',
        'dashboard_chart_id',
        'dashboard_id',
        'dashboard_filter_id',
        'dataset_field_id',
        'filter_type',
        'relation_field_type',
        'target_filter_id',
        'target_filter_field_id',
        'global_params_id',
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_jump_relation'

    def get_where_field(self):
        return [
        'jump_config_id',
        'dashboard_chart_id',
        'dashboard_id',
        'dashboard_filter_id',
        'dataset_field_id',
        'filter_type',
        'relation_field_type',
        'target_filter_id',
        'target_filter_field_id',
        'global_params_id',
    ]

    def get_use_resource(self):
        if self.filter_type == 1:
            resource = {
                "dashboard_var_filter_ids": {self.dashboard_filter_id},
                "dataset_field_ids": {self.dataset_field_id},
            }
        else:
            resource = {"dashboard_filter_ids": {self.dashboard_filter_id}, "dataset_field_ids": {self.dataset_field_id}}
        # 目标报告页引用资源检查
        resource = self.merge_target_use_resource(resource)
        return resource


class DashboardVarJumpRelationModel(DashboardChartJumpBaseModel):
    """
    报告变量跳转-筛选关系model
    """

    __slots__ = [
        "jump_config_id",
        "dashboard_id",
        "dashboard_chart_id",
        "dataset_field_id",
        "dataset_id",
        "var_id",
        "dashboard_filter_id",
        'filter_type',
        'relation_field_type',
        'target_filter_id',
        'target_filter_field_id',
        'global_params_id',
    ]

    def __init__(self, **kwargs):
        """
        单图筛选
        :param kwargs:
        """
        self.jump_config_id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.dataset_id = None
        self.var_id = None
        self.dashboard_filter_id = None
        self.filter_type = 0
        self.relation_field_type = None
        self.target_filter_id = ''
        self.target_filter_field_id = ''
        self.global_params_id = ''
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_vars_jump_relation'

    def get_where_field(self):
        return [
            "jump_config_id",
            "dashboard_id",
            "dashboard_chart_id",
            "dataset_field_id",
            "dataset_id",
            "var_id",
            "dashboard_filter_id",
            'filter_type',
            'relation_field_type',
            'target_filter_id',
            'target_filter_field_id',
            'global_params_id',
        ]

    def get_use_resource(self):
        if self.filter_type == 1:
            resource = {
                "dashboard_var_filter_ids": {self.dashboard_filter_id},
                "dataset_field_ids": {self.dataset_field_id},
            }
        else:
            resource = {
                'dataset_field_ids': {self.dataset_field_id},
                'dataset_ids': {self.dataset_id},
                'dashboard_filter_ids': {self.dashboard_filter_id},
                'dataset_var_ids': {self.var_id},
            }
        # 目标报告页引用资源检查
        resource = self.merge_target_use_resource(resource)
        return resource


class DashboardFixedVarJumpRelationModel(DashboardChartJumpBaseModel):
    """
    报告固定参数跳转-筛选关系model
    """

    __slots__ = [
        "jump_config_id",
        "dashboard_id",
        "dashboard_chart_id",
        "dataset_field_id",
        # "dataset_id",
        "var_name",
        "var_value",
        "dashboard_filter_id",
        'filter_type',
        'relation_field_type',
        'target_filter_id',
        'target_filter_field_id',
        'global_params_id'
    ]

    def __init__(self, **kwargs):
        """
        单图筛选
        :param kwargs:
        """
        self.jump_config_id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        # self.dataset_id = None
        self.var_name = None
        self.var_value = None
        self.dashboard_filter_id = None
        self.filter_type = 0
        self.relation_field_type = None
        self.target_filter_id = ''
        self.target_filter_field_id = ''
        self.global_params_id = ''
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_fixed_var_jump_relation'

    def get_where_field(self):
        return [
            "jump_config_id",
            "dashboard_id",
            "dashboard_chart_id",
            "dataset_field_id",
            # "dataset_id",
            "var_name",
            "var_value",
            "dashboard_filter_id",
            'filter_type',
            'relation_field_type',
            'target_filter_id',
            'target_filter_field_id',
            'global_params_id'
        ]

    def get_use_resource(self):
        if self.filter_type == 1:
            resource = {
                "dashboard_var_filter_ids": {self.dashboard_filter_id},
                "dataset_field_ids": {self.dataset_field_id},
            }
        else:
            resource = {
                'dataset_field_ids': {self.dataset_field_id},
                # 'dataset_ids': {self.dataset_id},
                'dashboard_filter_ids': {self.dashboard_filter_id},
            }
        # 目标报告页引用资源检查
        resource = self.merge_target_use_resource(resource)
        return resource

    def rules(self):
        rules = super().rules()
        if self.var_name and not self.var_value:
            raise UserError(message='固定值未填写')
        if self.var_value and not self.var_name:
            raise UserError(message='固定值名未填写')
        return rules


class DashboardFilterChartRelationModel(DashboardChartJumpBaseModel):
    """
    报告筛选器跳转-筛选关系model
    """

    __slots__ = [
        "jump_config_id",
        "dashboard_id",
        "dashboard_chart_id",
        "filter_chart_id",
        "dataset_field_id",
        "global_params_id",
        "date_filter_chart_flag",
        # "dashboard_filter_id",  # 以前的目标报告跳转id，这个筛选器的表是新表，就去掉这个字段
        # 'filter_type',
        # 'relation_field_type',
        # 'target_filter_id',
        # 'target_filter_field_id',
    ]

    def __init__(self, **kwargs):
        """
        单图筛选
        :param kwargs:
        """
        self.jump_config_id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.filter_chart_id = None
        self.global_params_id = ''
        self.date_filter_chart_flag = ''
        # self.dashboard_filter_id = None
        # self.filter_type = 0
        # self.relation_field_type = None
        # self.target_filter_id = ''
        # self.target_filter_field_id = ''
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_filter_chart_jump_relation'

    def get_where_field(self):
        return [
            "jump_config_id",
            "dashboard_id",
            "dashboard_chart_id",
            "filter_chart_id",
            "dataset_field_id",
            "global_params_id",
            "date_filter_chart_flag",
        ]

    def get_use_resource(self):
        resource = {
            'dataset_field_ids': {self.dataset_field_id},
            # 'dashboard_filter_ids': {self.filter_chart_id},
        }
        # 目标报告页引用资源检查
        resource = self.merge_target_use_resource(resource)
        return resource


class DashboardGlobalParamsRedirectRelationModel(DashboardChartJumpBaseModel):
    """
    跳转使用全局参数作为起跳-筛选关系model
    """

    __slots__ = [
        "jump_config_id",
        "dashboard_id",
        "dashboard_chart_id",
        "initiator_global_params_id",
        "global_params_id",
    ]

    def __init__(self, **kwargs):
        """
        单图筛选
        :param kwargs:
        """
        self.jump_config_id = ''
        self.dashboard_id = ''
        self.dashboard_chart_id = ''
        self.initiator_global_params_id = ''
        self.global_params_id = ''
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_global_params_jump_relation'

    def get_where_field(self):
        return [
            "jump_config_id",
            "dashboard_id",
            "dashboard_chart_id",
            "initiator_global_params_id",
            "global_params_id",
        ]


class DashboardGlobalParams(DashboardChartJumpBaseModel):
    """
    全局参数-数据集字段关联model
    """

    __slots__ = [
        "id",
        "dashboard_id",
        "name",
        "alias_name",
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_jump_global_params'


class GlobalParams2DatasetFieldRelation(DashboardChartJumpBaseModel):
    """
    全局参数-数据集字段关联model
    """

    __slots__ = [
        "global_params_id",
        "dashboard_id",
        "chart_id",
        "dataset_id",
        "dataset_field_id",
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_global_params_2_dataset_field_relation'

    def get_where_field(self):
        return [
            "global_params_id",
            "dashboard_id",
            "chart_id",
            "dataset_id",
            "dataset_field_id",
        ]

    def get_use_resource(self):
        resource = {
            'dataset_field_ids': {self.dataset_field_id},
        }
        # 目标报告页引用资源检查
        resource = self.merge_target_use_resource(resource)
        return resource


class GlobalParams2TabRelation(DashboardChartJumpBaseModel):
    """
    全局参数-tab关联model
    """

    __slots__ = [
        "global_params_id",
        "dashboard_id",
        "chart_id",
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_global_params_2_tab_relation'

    def get_where_field(self):
        return [
            "global_params_id",
            "dashboard_id",
            "chart_id",
        ]

    # 暂时没有实现组件的资源关联
    # def get_use_resource(self):
    #     resource = {
    #         'dataset_field_ids': {self.dataset_field_id},
    #     }
    #     # 目标报告页引用资源检查
    #     resource = self.merge_target_use_resource(resource)
    #     return resource


class GlobalParams2DatasetVarsRelation(DashboardChartJumpBaseModel):
    """
    全局参数-数据集变量关联model
    """

    __slots__ = [
        # "id",
        "global_params_id",
        "dashboard_id",
        "dataset_id",
        "var_id",
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_global_params_2_dataset_vars_relation'

    def get_where_field(self):
        return [
            "global_params_id",
            "dashboard_id",
            "dataset_id",
            "var_id",
        ]

    def get_use_resource(self):
        resource = {
            'dataset_field_ids': {self.dataset_id},
        }
        # 目标报告页引用资源检查
        resource = self.merge_target_use_resource(resource)
        return resource


class GlobalParams2FilterChartRelation(DashboardChartJumpBaseModel):
    """
    全局参数-筛选组件关联model
    """

    __slots__ = [
        "global_params_id",
        "dashboard_id",
        "filter_chart_id",
        "dataset_field_id",
        "date_filter_chart_flag",
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_global_params_2_filter_chart_relation'

    def get_where_field(self):
        return [
            "global_params_id",
            "dashboard_id",
            "filter_chart_id",
            "dataset_field_id",
            "date_filter_chart_flag",
        ]

    def get_use_resource(self):
        resource = {
            'dashboard_filter_ids': {self.filter_chart_id},
        }
        # 目标报告页引用资源检查
        resource = self.merge_target_use_resource(resource)
        return resource


class DashboardModel(EditorBaseModel):
    """
    报告数据模型类
    """

    __slots__ = [
        "id",
        "type",
        "platform",
        "is_multiple_screen",
        "cover",
        "description",
        "layout_type",
        "layout",
        "scale_mode",
        "background",
        "rank",
        "biz_code",
        "refresh_rate",
        "grid_padding",
        "theme",
        "create_type",
        "is_show_mark_img",
        "new_layout_type",
        "distribute_type",
        "application_type",
        "main_external_subject_id",
        "external_subject_ids",
        "line_height",
        "smart_beauty_status",
        "dataset_id",
        "analysis_type",
        "external_url"
    ]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        # 在数据库设置的时候没有做好，这里做一个字符串到数字的一个映射
        new_layout_type_map = {'free': 0, 'grid': 1}
        new_layout_type = kwargs.get('new_layout_type')
        if new_layout_type in new_layout_type_map:
            kwargs['new_layout_type'] = new_layout_type_map[new_layout_type]
        super().__init__({"is_multiple_screen": 0, "status": 0, "rank": 0, "application_type": 0}, **kwargs)

    def get_table_name(self):
        return "dap_bi_dashboard"


class DashboardChartModel(EditorBaseModel):
    """
    单图数据模型类
    """

    __slots__ = [
        "id",
        "dashboard_id",
        "name",
        "chart_code",
        "filter_config",
        "page_size",
        "data_logic_type_code",
        "chart_type",
        "content",
        "source",
        "data_modified_on",
        "position",
        "sort_method",
        "penetrate",
        "parent_id",
        "level_code",
        "refresh_rate",
        "display_item",
        "desired_value",
        "percentage",
        "style_type",
        "default_value",
        "layout",
        "layout_extend",
        "config",
        "filter_relation",
        "layers",
        "column_order",
        "enable_subtotal",
        "enable_summary",
        "enable_subtotal_col",
        "enable_subtotal_col_summary",
        "enable_subtotal_row",
        "enable_subtotal_row_summary",
        "subtotal_row_summary_formula_mode",
        "reset_field_sort",
        "aggregation",
        "pre_comparison",
        "export_type",
        "parent_chart_id",
        "child_rank",
        "fixed_data_mode",
        "fixed_manual_value",
        "is_highdata",
        "close_detail_mode",
        "chart_visible",
    ]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.name = None
        self.chart_code = None
        self.filter_config = None
        self.page_size = 100
        self.data_logic_type_code = None
        self.chart_type = None
        self.content = None
        self.source = None
        self.data_modified_on = None
        self.position = None
        self.sort_method = None
        self.penetrate = None
        self.parent_id = None
        self.level_code = None
        self.refresh_rate = None
        self.display_item = None
        self.desired_value = None
        self.percentage = None
        self.style_type = None
        self.default_value = None
        self.layout = None
        self.layout_extend = None
        self.config = None
        self.filter_relation = 0
        self.layers = None
        self.column_order = None
        self.enable_subtotal = 0
        self.enable_summary = 0
        self.enable_subtotal_col = 0
        self.enable_subtotal_col_summary = 0
        self.enable_subtotal_row = 0
        self.enable_subtotal_row_summary = 0
        self.subtotal_row_summary_formula_mode = None
        self.aggregation = 1
        self.pre_comparison = 1
        self.export_type = ""
        self.reset_field_sort = 1
        self.parent_chart_id = ''
        self.child_rank = 0
        self.external_subject_id = None
        self.fixed_data_mode = None
        self.fixed_manual_value = None
        self.is_highdata = None
        self.close_detail_mode = 0
        self.asset_id = 0
        self.chart_visible = 1
        super().__init__(**kwargs)

    def get_table_name(self):
        return "dap_bi_dashboard_chart"

    def get_use_resource(self):
        if self.source:
            return {'dataset_ids': {self.source}}
        return None


class DashboardChartDimModel(EditorBaseModel):
    """
    单图维度数据模型类
    """

    __slots__ = [
        "id",
        "dashboard_id",
        "dashboard_chart_id",
        "dim",
        "alias",
        "content",
        "formula_mode",
        "dashboard_jump_config",
        "rank",
        "sort",
        "note",
        "is_subtotal_cate",
        "dim_type",
        "display_format",
        "parent_id",
        "relation_fields"
    ]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dim = None
        self.alias = None
        self.content = None
        self.formula_mode = None
        self.dashboard_jump_config = None
        self.rank = None
        self.sort = None
        self.note = None
        self.is_subtotal_cate = 0
        self.dim_type = 0
        self.display_format = ""
        self.sub_type_code = ""
        self.parent_id = None
        self.relation_fields = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return "dap_bi_dashboard_chart_dim"

    def get_use_resource(self):
        return {'dataset_field_ids': {self.dim}}


class DashboardChartNumModel(EditorBaseModel):
    """
    单图度量数据模型类
    """

    __slots__ = [
        "id",
        "dashboard_id",
        "dashboard_chart_id",
        "num",
        "alias",
        "formula_mode",
        "display_format",
        "axis_type",
        "chart_code",
        "dashboard_jump_config",
        "rank",
        "sort",
        "note",
        "calc_null",
        "subtotal_formula_mode",
        "subtotal_col_formula_mode",
        "subtotal_row_formula_mode",
        "same_ring_ratio_config",
        "hidden",
        "relation_fields"
    ]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.num = None
        self.alias = None
        self.formula_mode = None
        self.display_format = None
        self.axis_type = None
        self.chart_code = None
        self.dashboard_jump_config = None
        self.rank = None
        self.sort = None
        self.note = None
        self.calc_null = 0
        self.subtotal_formula_mode = None
        self.subtotal_col_formula_mode = None
        self.subtotal_row_formula_mode = None
        self.same_ring_ratio_config = None
        self.sub_type_code = ''
        self.hidden = 0
        self.subtotal_col_formula_expression = None
        self.relation_fields = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return "dap_bi_dashboard_chart_num"

    def get_use_resource(self):
        return {'dataset_field_ids': {self.num}}


class DashboardChartDesireModel(EditorBaseModel):
    """
    目标值模型
    """

    __slots__ = [
        'id',
        'dashboard_id',
        'dashboard_chart_id',
        'dataset_field_id',
        'mode',
        'value',
        'alias',
        'formula_mode',
        'rank',
        'display_format',
        'sort',
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_desire'

    def get_use_resource(self):
        if not self.dataset_field_id:
            return None
        return {"dataset_field_ids": {self.dataset_field_id}}


class DashboardChartMarklineModel(EditorBaseModel):
    """
    单图辅助线模型
    """

    __slots__ = [
        'id',
        'dashboard_id',
        'dashboard_chart_id',
        'name',
        'mode',
        'num',
        'formula_mode',
        'value',
        'rank',
        'axis_type',
        'color',
        'config',
        'percentile',
    ]

    def get_table_name(self):
        return 'dap_bi_dashboard_chart_markline'

    def get_use_resource(self):
        return {'dataset_field_ids': {self.num}}


class ChartFitlerModel(EditorBaseModel):
    '''筛选单图'''

    __slots__ = ["id", "dashboard_id", "chart_id", "dataset_field_id", "dataset_id", "filter_type", "available", "indicator_dim_obj"]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.chart_id = None
        self.dataset_field_id = None
        self.dataset_id = None
        self.dashboard_id = None
        self.filter_type = None
        self.available = None
        self.initiator_source = None
        self.indicator_dim_obj = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_filter_chart'

    def get_use_resource(self):
        if self.initiator_source == ChartFilterInitiatorSouce.Fixedvalue.value:
            return
        return {'dataset_field_ids': {self.dataset_field_id}, 'dataset_ids': {self.dataset_id}}


class ChartFitlerFixedValueModel(EditorBaseModel):
    '''筛选单图固定值'''

    __slots__ = ["id", "dashboard_id", "chart_id", "name", "value_type", "identifier", "extra_data"]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.chart_id = None
        self.name = None
        self.value_type = None
        self.identifier = None
        self.extra_data = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_filter_chart_fixed_value'


class ChartFilterRelation(EditorBaseModel):
    '''单图之间的筛选关系'''

    __slots__ = ['id', 'filter_id', 'chart_responder_id', 'field_responder_id', 'dataset_responder_id', "dashboard_id"]

    def __init__(self, **kwargs):
        """
        :param kwargs:
        """
        self.id = None
        self.filter_id = None
        self.chart_responder_id = None
        self.field_responder_id = None
        self.dataset_responder_id = None
        self.dashboard_id = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_filter_chart_relation'


class ChartLinkageModel(EditorBaseModel):
    '''单图联动'''

    __slots__ = ["id", "dashboard_id", "chart_id", "dataset_field_id", "dataset_id"]

    def __init__(self, **kwargs):
        """
        单图筛选
        :param kwargs:
        """
        self.id = None
        self.chart_id = None
        self.dataset_field_id = None
        self.dataset_id = None
        self.dashboard_id = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dap_bi_dashboard_linkage'


class ChartLinkageRelation(EditorBaseModel):
    '''单图之间的联动关系'''

    __slots__ = ['id', 'link_id', 'chart_responder_id', 'field_responder_id', 'dataset_responder_id', "dashboard_id"]

    def __init__(self, **kwargs):
        self.link_id = None
        self.id = None
        self.chart_responder_id = None
        self.field_responder_id = None
        self.dataset_responder_id = None
        self.dashboard_id = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return "dap_bi_dashboard_linkage_relation"


class ChartDefaultValueModel(EditorBaseModel):
    __slots__ = [
        "id",
        "dashboard_id",
        "chart_id",
        "dataset_id",
        "dataset_field_id",
        "operator",
        "value",
        "select_all",
        "extend_data",
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.dashboard_id = None
        self.chart_id = None
        self.dataset_id = None
        self.dataset_field_id = None
        self.operator = None
        self.value = None
        self.select_all = None
        self.extend_data = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return "dap_bi_dashboard_filter_chart_default_values"


class VarRelationsModel(EditorBaseModel):
    __slots__ = [
        "id",
        "chart_initiator_id",
        "field_initiator_id",
        "dashboard_id",
        "var_id",
        "var_dataset_id",
        "initiator_type",
        "var_dim_obj",
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.chart_initiator_id = None
        self.field_initiator_id = None
        self.dashboard_id = None
        self.var_id = None
        self.var_dataset_id = None
        self.initiator_type = None
        self.var_dim_obj = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(
            (
                ['chart_initiator_id', 'dashboard_id', 'var_id', 'var_dataset_id'],
                'string',
                {'max': 36, 'required': True},
            )
        )
        rules.append((['id'], 'string', {'max': 38, 'required': True}))
        return rules

    def get_table_name(self):
        return "dap_bi_dashboard_dataset_vars_relation"


class DashboardChartFieldSortModel(EditorBaseModel):
    """
    单图字段排序数据模型类
    """

    __slots__ = [
        "id",
        "dashboard_id",
        "dashboard_chart_id",
        "dataset_field_id",
        "field_source",
        "sort",
        "content",
        "weight",
    ]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.field_source = None
        self.sort = None
        self.content = None
        self.weight = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return "dap_bi_dashboard_chart_field_sort"

    def get_use_resource(self):
        return {'dataset_field_ids': set([self.dataset_field_id])}


class DashboardValueSourceModel(EditorBaseModel):
    """
    变量取值来源
    """

    __slots__ = ["id", "dashboard_id", "value_source_name", "value_source", "value_identifier"]

    def __init__(self, **kwargs):
        self.id = None
        self.dashboard_id = None
        self.value_source_name = None
        self.value_source = None
        self.value_identifier = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['id', 'dashboard_id'], 'string', {'max': 36, 'required': True}))
        return rules

    def get_table_name(self):
        return "dap_bi_dashboard_value_source"


class DashboardVarsValueSourceRelationModel(EditorBaseModel):
    """
    变量取值来源绑定关系
    """

    __slots__ = ["dashboard_id", "var_id", "value_source_id"]

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.var_id = None
        self.value_source_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['dashboard_id', 'var_id', 'value_source_id'], 'string', {'max': 36, 'required': True}))
        return rules

    def get_table_name(self):
        return "dap_bi_dashboard_vars_value_source_relation"

    def get_where_field(self):
        return ["dashboard_id", "var_id", "value_source_id"]
