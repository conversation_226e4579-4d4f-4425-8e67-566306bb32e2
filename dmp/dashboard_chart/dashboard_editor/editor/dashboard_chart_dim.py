#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by yangzy02 on 2018/11/23
import json

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartDimModel


class DashboardChartDim(Editor):
    def edit(self):
        errors = []
        editor_models = []
        dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
        dashboard_charts = self.metadata_storage.metadata_dashboard_charts()
        if len(dashboard_charts) < 1:
            return editor_models, errors
        for chart in dashboard_charts:
            dim_list = self.metadata_storage.get_data_by_path("data.indicator.dims", chart)
            for dim in dim_list:
                content = json.dumps(dim.get("content")) if dim.get("content") else ''
                display_format = dim.get("display_format") or ""
                if display_format:
                    display_format = json.dumps(display_format)
                args = {
                    "id": dim.get("id"),
                    "dashboard_id": dashboard_id,
                    "dashboard_chart_id": chart.get("id"),
                    "dim": dim.get("dim") if dim.get("dim") else dim.get("dataset_field_id"),
                    "alias": dim.get("alias"),
                    "content": content,
                    "formula_mode": dim.get("formula_mode"),
                    "rank": dim.get("rank", 0),
                    "sort": dim.get("sort", ''),
                    "note": dim.get("note"),
                    "is_subtotal_cate": dim.get("is_subtotal_cate", 0),
                    "dim_type": dim.get("dim_type", 0),
                    "display_format": display_format,
                    "parent_id": dim.get("parent_id", ''),
                    "sub_type_code": dim.get("sub_type_code", ''),
                    "relation_fields": dim.get("relation_fields")
                }
                chart_model = DashboardChartDimModel(**args)
                editor_models.append(chart_model)
        return editor_models, errors

    def get_action_name(self):
        """
        获取操作名称
        :return str:
        """
        return "单图维度元数据保存"
