import json

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartDesireModel


from typing import Any, List, Tuple


class DashboardChartDesire(Editor):
    """目标值解析"""

    def edit(self) -> Tuple[List[Any], List[Any]]:
        errors = []
        models = []
        dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
        charts = self.metadata_storage.metadata_dashboard_charts()
        if charts and isinstance(charts, list):
            for chart in charts:
                desires = self.metadata_storage.get_data_by_path('data.indicator.desires', chart)
                if desires and isinstance(desires, list):
                    for desire in desires:
                        model = DashboardChartDesireModel(
                            id=desire.get('id'),
                            dashboard_id=dashboard_id,
                            dashboard_chart_id=desire.get('dashboard_chart_id'),
                            dataset_field_id=desire.get('dataset_field_id'),
                            mode=desire.get('mode', 0),
                            value=desire.get('value'),
                            alias=desire.get('alias'),
                            formula_mode=desire.get('formula_mode'),
                            rank=desire.get('rank'),
                            display_format=json.dumps(desire.get('display_format')),
                            sort=desire.get('sort'),
                            axis_type=desire.get('axis_type'),
                        )
                        models.append(model)
        return models, errors

    def get_action_name(self):
        return '保存目标值'
