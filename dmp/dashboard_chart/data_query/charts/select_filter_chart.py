#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint:disable=W0613
"""
    <NAME_EMAIL> 2018/9/8
"""
from typing import Tuple, List, Dict, Union

import pandas as pd
from dashboard_chart.data_query.charts.assist_chart import AssistChart


class SelectFilterChart(AssistChart):
    def __init__(self, model):
        super().__init__(model)
        self.dataset_field = None

    def restructure_chart_data(self, data: pd.DataFrame, original_data: List[Dict]) -> Union[List, Dict]:
        """
        重新组织data数据满足各个单图类型数据结构
        :param pd.DataFrame data: 查询data库后返回的数据
        :return:
        """
        col_name = self.dataset_field.get("alias") or self.dataset_field.get("col_name")
        select_filter_data = {col_name: original_data}
        if select_filter_data:
            return [select_filter_data]
        return []

    def get_chart_data(self, dataset_version: str = None):
        """
        通过数据集获取单图数据
        :return:
        """
        # 维度排序
        self.chart_data_model.dims.sort(key=lambda k: k.get("rank"))
        if self.chart_data_model.penetrate_conditions and len(self.chart_data_model.penetrate_conditions) > 0:
            if len(self.chart_data_model.penetrate_conditions) < len(self.chart_data_model.dims):
                self.dataset_field = self.chart_data_model.dataset_field_dict.get(
                    self.chart_data_model.dims[len(self.chart_data_model.penetrate_conditions)].get("dim")
                )
                self.chart_data_model.dims = [
                    self.chart_data_model.dims[len(self.chart_data_model.penetrate_conditions)]
                ]
        else:
            dim_0 = self.chart_data_model.dims[0] if self.chart_data_model.dims else dict()
            self.dataset_field = self.chart_data_model.dataset_field_dict.get(dim_0.get("dim", ""))
            self.chart_data_model.dims = [dim_0]

        if self.dataset_field:
            # 1. 验证字段是否正确
            self.validation_dataset_fields(self.get_need_validation_fields())

            # 2. 查询数据
            result = self.query_data(dataset_version=dataset_version)

            # 3. 设置data数据
            data = self.get_data(result)
            return data

        return {}

    def get_variable_chart_data(self):
        """
        通过数据集获取单图数据
        :return:
        """
        # 维度排序
        self.chart_data_model.dims.sort(key=lambda k: k.get("rank"))
        if self.chart_data_model.penetrate_conditions and len(self.chart_data_model.penetrate_conditions) > 0:
            if len(self.chart_data_model.penetrate_conditions) < len(self.chart_data_model.dims):
                self.dataset_field = self.chart_data_model.dataset_field_dict.get(
                    self.chart_data_model.dims[len(self.chart_data_model.penetrate_conditions)].get("dim")
                )
                self.chart_data_model.dims = [
                    self.chart_data_model.dims[len(self.chart_data_model.penetrate_conditions)]
                ]
        else:
            self.dataset_field = self.chart_data_model.dataset_field_dict.get(self.chart_data_model.dims[0].get("dim"))
            self.chart_data_model.dims = [self.chart_data_model.dims[0]]

        if self.dataset_field:
            # 1. 验证字段是否正确
            self.validation_dataset_fields(self.get_need_validation_fields())

            # 2. 查询数据
            result = self.query_data()

            # 3. 设置data数据
            data = self.get_variable_data(result)
            return data

        return {}

    def apply_funcs(self, db_data: list):
        """
        该组件无其他辅助功能(如透视)
        :param db_data:
        :return:
        """
        return pd.DataFrame(db_data), db_data

    def get_marklines_for_data(self, data: pd.DataFrame, original_data: List[Dict]):
        """
        该组件无辅助线
        :return:
        """
        return []

    def get_subtotal_for_struct(self):
        """
        该组件无小计
        :return:
        """
        return {}

    def get_subtotal_for_data(
        self, data_frame: pd.DataFrame, next_item_frame: pd.DataFrame, original_data, original_next_item,
        prev_item_frame, original_prev_item
    ) -> Tuple[Dict, pd.DataFrame, List[Dict]]:
        """
        该组件无小计
        :param data:
        :param next_item:
        :return:
        """
        return {}, data_frame, original_data
