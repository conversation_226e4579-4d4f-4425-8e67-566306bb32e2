#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/8
"""
from dashboard_chart.data_query.charts.assist_chart import AssistChart
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.data_query.charts.comparision_line_chart import ComparisionLineChart
from dashboard_chart.data_query.charts.comparision_line_chart_v2 import ComparisionLineChartV2
from dashboard_chart.data_query.charts.excel_table_chart import ExcelTableChart
from dashboard_chart.data_query.charts.interval_chart import IntervalChart
from dashboard_chart.data_query.charts.nondataset_chart import NondatasetChart
from dashboard_chart.data_query.charts.select_filter_chart import SelectFilterChart
from dashboard_chart.data_query.charts.label_filter_chart import LabelFilterChart
from dashboard_chart.data_query.charts.pagination_table_chart import PaginationTableChart
from dashboard_chart.data_query.charts.pagination_table_yk_chart import PaginationTableYkChart
from dashboard_chart.data_query.charts.column_chart import ColumnChart
from dashboard_chart.data_query.charts.statistic_table_chart import StatisticTableChart
from dashboard_chart.repositories import dashboard_repository
from dmplib.utils.errors import UserError
from dmplib.locale import trans_msg


class ChartFactory:
    # 组件标准化用户自定义组件通用类字典
    # 注意：如果有特殊需求需要按照chart_code实例化chart类，则key跟chart_code保持一致即可，然后再实现对应的chart类并继承自ChartQuery
    chart_class_dict = {
        # chart_code类
        "excel_table": ExcelTableChart,
        "select_filter": SelectFilterChart,
        "pagination_table": PaginationTableChart,
        "pagination_table_yk": PaginationTableYkChart,
        "label_filter": LabelFilterChart,
        "comparison_line": ComparisionLineChart,
        "cluster_column": ComparisionLineChartV2,  # 柱状图添加对比维度的缺失值（直接使用对比折线图的实现）
        "stack_bar": ComparisionLineChartV2,
        "area": ComparisionLineChartV2,
        "statistic_table": StatisticTableChart,
        # data_logic_type_code类型
        "default": CommonChart,
        "assist": AssistChart,
        "interval": IntervalChart,
        "nondataset": NondatasetChart,
        "column": ColumnChart,
    }

    @classmethod
    def create_chart(cls, chart_id, model):
        """
        创建单图实例
        :param str chart_id: 单图id
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        # 先通过chart_code进行实例化，如果实例化不成功再进行data_logic_type_code的实例化
        if model.chart_code:
            chart_class = ChartFactory.create_chart_by_code(model.chart_code)
            if chart_class:
                return chart_class(model)
        if model.data_logic_type_code:
            chart_class = ChartFactory.create_chart_by_code(model.data_logic_type_code)
            if chart_class:
                return chart_class(model)

        if not chart_id:
            raise UserError(message=trans_msg("实例化单图类时单图id为空"))

        codes = dashboard_repository.get_chart_code_and_type_code_by_chart_id(chart_id)
        if not codes:
            raise UserError(message=trans_msg("无法找到单图信息!"))
        # 1. 通过chart_code进行实例化
        chart_class = ChartFactory.create_chart_by_code(codes.get("chart_code"))
        if chart_class:
            return chart_class(model)
        # 2. 通过data_logic_type_code进行实例化
        chart_class = ChartFactory.create_chart_by_code(codes.get("data_logic_type_code"))
        if not chart_class:
            raise UserError(message=trans_msg("实例化单图类失败"))
        return chart_class(model)

    @classmethod
    def create_chart_by_code(cls, code):
        """
        根据chart_code或者data_logic_type_code进行实例化单图类
        :param code:
        :return:
        """
        return ChartFactory.chart_class_dict.get(code)
