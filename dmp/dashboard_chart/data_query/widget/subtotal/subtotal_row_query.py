#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/2 17:23
# <AUTHOR> caoxl(cao)
# @File     : subtotal_row_agg_query.py
# pylint: disable=R0201

import copy
import pandas as pd
import numpy as np
from typing import Tuple, List
from collections import Iterable
from base.enums import ColTypes, ChartNumSubtotalFormulaMode, SqlWhereOperator, DataQueryMode
from dmplib.locale import trans_msg
from dashboard_chart.models import (
    ChartDataModel,
    SubtotalRowModel,
    SubtotalColModel,
    SubtotalHeaderModel,
    SubtotalRowColumnModel,
    SubtotalColColOfRowModel,
)
from dashboard_chart.convertor.select.subtotal_row_select import SubtotalRowSelect
from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.convertor.group.dim_group import DimGroup
from dashboard_chart.convertor.select.dim_select import DimSelect
from dashboard_chart.convertor.where.where import Where
from dashboard_chart.convertor.select.select import Select
from dashboard_chart.data_query import utils as DataQueryUtil
from dashboard_chart.data_query.widget import utils as WidgetUtil
from base.errors import ChartQueryExceptionError
from loguru import logger
from components.analysis_time import AnalysisTimeUtils


class SubtotalRowQuery:
    def __init__(self, chart_data_model: ChartDataModel, orgin_where_fields: list, query_vars: List, user_id: str):
        self._chart_data_model = chart_data_model
        self._origin_where_fields = orgin_where_fields
        self._query_vars = query_vars
        self._user_id = user_id
        self._subtotal_row_select_obj = SubtotalRowSelect().get_select_obj(chart_data_model)
        self._chart_alias_obj = DataQueryUtil.ChartDisplayAlias(chart_data_model)
        self.curr_query_mode = self.get_curr_query_mode()

    def get_curr_query_mode(self):
        from dashboard_chart.data_query.charts.common_chart import get_curr_mode
        return get_curr_mode()

    def _get_real_column(self, column_item):
        if len(column_item) > 1 and column_item[1] == '':
            return column_item[0]
        return column_item

    def get_data(
            self, data: pd.DataFrame, subtotal_col_result: SubtotalColModel
    ) -> Tuple[list, pd.DataFrame, SubtotalColModel]:
        # 如果取数模式是直接算列总计，那么直接跳过行小计计算
        if self.curr_query_mode == DataQueryMode.SUMMARY.value:  # NOSONAR
            return [], data, subtotal_col_result

        # 没有开启行小计则不返回
        if not self._chart_data_model.enable_subtotal_row or len(data) == 0:
            return [], data, subtotal_col_result
        # 1. 标准化原始行加入行小计字段
        # data = self._format_subtotal_row_fields(data)
        data = self._format_subtotal_row_fields_v2(data)

        # 2. 获取普通列及总计列
        subtotal_row_cols = self._get_normal_cols()
        subtotal_summary_col = self._get_summary_col()

        # 3. 获取行小计数据
        data, columns, subtotal_col_result = self.get_columns(
            subtotal_row_cols, subtotal_summary_col, data, subtotal_col_result
        )
        return columns, data, subtotal_col_result

    def _get_normal_cols(self):
        """
        获取普通列
        :return:
        """
        cols = []
        subtotal_row_fields = self._get_subtotal_row_fields()
        for subtotal_row_field in subtotal_row_fields:
            field_id = subtotal_row_field.field_ref
            field_info = self._chart_data_model.dataset_field_dict.get(field_id)
            subtotal_row = SubtotalRowModel()
            subtotal_row.header = [
                SubtotalHeaderModel(
                    **{
                        'col_name': field_info.get("col_name"),
                        'alias_name': subtotal_row_field.alias or subtotal_row_field.field,
                        'col_value': None,
                        'dataset_id': self._chart_data_model.dataset_id,
                        'field_id': field_id,
                        'alias': self._chart_alias_obj.get_alias(field_id, subtotal_row_field.logic_source)
                                 or field_info.get("alias_name"),
                        'col_type': ColTypes.Num.value,
                    }
                )
            ]
            cols.append(subtotal_row)
        return cols

    def _get_summary_col(self):
        """
        获取总计列
        :return:
        """
        return SubtotalRowModel()

    def get_columns(
            self,
            subtotal_row_cols: List[SubtotalRowModel],
            subtotal_summary_col: SubtotalRowModel,
            data: pd.DataFrame,
            subtotal_col_result: SubtotalColModel,
    ) -> Tuple[pd.DataFrame, List, SubtotalColModel]:
        """
        获取所有列
        :param subtotal_row_cols:  行小计列
        :param subtotal_summary_col:  行总计列
        :param data:  原始数据
        :param subtotal_col_result: 列小计结果
        :return:
        """
        AnalysisTimeUtils.recode_time_node('开始行小计疑似耗时代码计算-2')
        # 1. 获取普通数据的行小计
        data, subtotal_row_cols = self._get_columns_for_data(data, subtotal_row_cols)
        AnalysisTimeUtils.recode_time_node('结束行小计疑似耗时代码计算-2')
        # 2. 获取普通数据的行总计
        if self._chart_data_model.subtotal_row_summary_formula_mode:
            self._get_summary_columns_for_data(data, subtotal_summary_col)
        # 3. 获取列小计的行小计
        self._get_columns_for_subtotal_col(subtotal_col_result, subtotal_row_cols)
        # 4. 获取列小计的行总计
        if self._chart_data_model.subtotal_row_summary_formula_mode:
            self._get_summary_columns_for_subtotal_col(subtotal_col_result, subtotal_summary_col)
        # 5. 获取列总计的行小计
        self._get_columns_for_subtotal_col_summary(subtotal_col_result, subtotal_row_cols)
        # 6. 获取列总计的行总计
        if self._chart_data_model.subtotal_row_summary_formula_mode:
            self._get_summary_columns_for_subtotal_col_summary(subtotal_col_result, subtotal_summary_col)
        # 7. 返回最终数据
        row_cols = subtotal_row_cols
        if self._chart_data_model.subtotal_row_summary_formula_mode:
            row_cols.append(subtotal_summary_col)
        return data, row_cols, subtotal_col_result

    def _get_subtotal_row_fields(self):
        """
        获取行小计字段
        :return:
        """
        return self._subtotal_row_select_obj.get_subtotal_select_fields(self._chart_data_model)

    def _get_subtotal_row_dict(self):
        """
        获取行小计field_id 为键名的字典
        :return:
        """
        if not hasattr(self, '_subtotal_row_dict'):
            fields = self._get_subtotal_row_fields()
            self._subtotal_row_dict = {field.field_ref: field for field in fields}
        return self._subtotal_row_dict

    def _get_subtotal_summary_fields(self):
        """
        获取行总计字段
        :return:
        """
        return self._subtotal_row_select_obj.get_summary_select_fields(self._chart_data_model)

    def _get_subtotal_alias_dict(self):
        """
        获取所有小计的字段别名的映射(行小计&行总计字段)
        :return:
        """
        if not hasattr(self, '_subtotal_alias_dict'):
            fields = self._subtotal_row_select_obj.get_select_fields(self._chart_data_model)
            self._subtotal_alias_dict = {field.alias or field.field: field for field in fields}
        return self._subtotal_alias_dict

    # def _get_columns_for_data(self, data: pd.DataFrame, subtotal_row_cols: List[SubtotalRowModel]):
    #     """
    #     获取原始数据的行小计
    #     :param data:
    #     :return:
    #     """
    #     drop_names = set()
    #     for subtotal_row_col in subtotal_row_cols:
    #         field_id = subtotal_row_col.header[0].field_id
    #         sobtotal_row_field = self._get_subtotal_row_dict().get(field_id)
    #         if not sobtotal_row_field:
    #             continue
    #         subtotal_row_field_name = sobtotal_row_field.alias or sobtotal_row_field.field
    #         drop_names.add(subtotal_row_field_name)
    #         for i in range(0, len(data)):
    #             item = data.iloc[i: i + 1, :]
    #             col = SubtotalRowColumnModel(
    #                 **{
    #                     'col_value': WidgetUtil.convert_to_py_data_type(item[subtotal_row_field_name].values[0]),
    #                     'siblings': self._get_source_data_siblings(item),
    #                 }
    #             )
    #             data.iloc[i: i + 1, :].drop(columns=[subtotal_row_field_name], axis=1)
    #             subtotal_row_col.cols.append(col)
    #     data.drop(list(drop_names), axis=1, inplace=True)

    @DataQueryUtil.chart_query_cache(timeout=30 * 60)
    def _get_columns_for_data(self, data: pd.DataFrame, subtotal_row_cols: List[SubtotalRowModel]):
        """
        获取原始数据的行小计
        :param data:
        :return:
        """
        drop_names = set()
        for subtotal_row_col in subtotal_row_cols:
            field_id = subtotal_row_col.header[0].field_id
            sobtotal_row_field = self._get_subtotal_row_dict().get(field_id)
            if not sobtotal_row_field:
                continue
            subtotal_row_field_name = sobtotal_row_field.alias or sobtotal_row_field.field
            drop_names.add(subtotal_row_field_name)
            for i in range(0, len(data)):
                item = data.iloc[i: i + 1, :]
                # d2 = item.ix[0, subtotal_row_field_name][0]
                val = item[subtotal_row_field_name].values[0]
                col_value = WidgetUtil.convert_to_py_data_type(val)
                siblings = self._get_source_data_siblings(item)
                col = SubtotalRowColumnModel(
                    **{
                        'col_value': col_value,
                        'siblings': siblings,
                    }
                )
                # 2023.1.9 此处不需要删除这一列的数据，在外面统一删除
                # data.iloc[i: i + 1, :].drop(columns=[subtotal_row_field_name], axis=1)
                subtotal_row_col.cols.append(col)

        # 2023.1.9 此处的删除列有性能问题， 这里换成另一种写法
        data.drop(list(drop_names), axis=1, inplace=True)
        # remain = [f for f in data.columns.tolist() if not (drop_names & set(f))]
        # new_data = pd.DataFrame(data, columns=remain)
        return data, subtotal_row_cols

    def _get_summary_columns_for_data(self, data: pd.DataFrame, subtotal_summary_col: SubtotalRowModel):
        """
        获取元数据的行总计
        :param data:
        :return:
        """
        summary_columns = [
            summary_field.alias or summary_field.field for summary_field in self._get_subtotal_summary_fields()
        ]
        for i in range(0, len(data)):
            item = data.iloc[i: i + 1, :]
            col_val = self._compute_summary_value(
                [item[summary_column].values[0] for summary_column in summary_columns],
                self._chart_data_model.subtotal_row_summary_formula_mode,
            )
            col = SubtotalRowColumnModel(
                **{
                    'col_value': WidgetUtil.convert_to_py_data_type(col_val),
                    'siblings': self._get_source_data_siblings(item),
                }
            )
            subtotal_summary_col.cols.append(col)
        data.drop(summary_columns, axis=1, inplace=True)

    def _get_source_data_subling_columns(self):
        if not hasattr(self, '_source_data_subling_columns'):
            select_obj = self._get_dim_select_obj() if self._chart_data_model.aggregation else self._get_select_obj()
            subling_fields = select_obj.get_select_fields(self._chart_data_model)
            subtotal_alias_dict = self._get_subtotal_alias_dict()
            # 有对比维度时从index中取 否则从columns中取
            # 行小计字段不能作为兄弟节点
            self._source_data_subling_columns = []
            for subling_field in subling_fields:
                data_key = subling_field.alias or subling_field.field
                if data_key in subtotal_alias_dict:
                    continue
                # 过滤掉隐藏字段
                if self._check_not_visible_field(subling_field):
                    continue
                self._source_data_subling_columns.append(
                    {'field': subling_field, 'type': 'index' if self._chart_data_model.comparisons else 'columns'}
                )
        return self._source_data_subling_columns

    def _check_not_visible_field(self, subling_field):
        if self._chart_data_model.nums:
            for item in self._chart_data_model.nums:
                if subling_field.field_ref == item.get('dataset_field_id') and item.get('visible') == 0:
                    return True
        return False

    def _get_source_data_siblings(self, item: pd.DataFrame) -> List:
        sublings = []
        subling_columns = self._get_source_data_subling_columns()
        item_index_dict = {name: idx for idx, name in enumerate(item.index.names)}
        for subling_column_dict in subling_columns:
            loc_type = subling_column_dict.get('type')
            subling_field = subling_column_dict.get('field')
            field_id = subling_field.field_ref
            field_info = self._chart_data_model.dataset_field_dict.get(field_id)
            alias_name = subling_field.alias or subling_field.field
            header = [
                SubtotalHeaderModel(
                    **{
                        'col_name': field_info.get('col_name'),
                        'alias_name': alias_name,
                        'col_value': None,
                        'dataset_id': self._chart_data_model.dataset_id,
                        'field_id': field_id,
                        'alias': self._chart_alias_obj.get_alias(field_id, subling_field.logic_source)
                                 or field_info.get("alias_name"),
                        'col_type': subling_field.logic_source,
                    }
                )
            ]
            col_value = None
            if loc_type == 'index':
                if isinstance(item.index, pd.MultiIndex):
                    idx = item_index_dict.get(alias_name)
                    if idx is None:
                        continue
                    col_value = copy.deepcopy(item.index[0][idx])
                else:
                    col_value = copy.deepcopy(item.index[0])
            if loc_type == 'columns':
                col_value = copy.deepcopy(item[alias_name].values[0])
            subling = SubtotalColColOfRowModel(
                **{"header": header, "col_value": WidgetUtil.convert_to_py_data_type(col_value)}
            )
            sublings.append(subling)
        return sublings

    def _filter_none_data(self, values: List[pd.Series]) -> List:
        result = []
        for val in values:
            if val is not None:
                result.append(val)
        return result

    def _compute_summary_value(self, values: List[pd.Series], summary_formula_mode):
        if summary_formula_mode == ChartNumSubtotalFormulaMode.Sum.value:
            return sum(self._filter_none_data(values))
        if summary_formula_mode == ChartNumSubtotalFormulaMode.Avg.value:
            return sum(self._filter_none_data(values)) / len(values)
        if self._chart_data_model.subtotal_row_summary_formula_mode == ChartNumSubtotalFormulaMode.Count.value:
            return sum(self._filter_none_data(values))
        if self._chart_data_model.subtotal_row_summary_formula_mode == ChartNumSubtotalFormulaMode.Distinct.value:
            return sum(self._filter_none_data(values))
        if self._chart_data_model.subtotal_row_summary_formula_mode == ChartNumSubtotalFormulaMode.Max.value:
            return max(self._filter_none_data(values))
        if self._chart_data_model.subtotal_row_summary_formula_mode == ChartNumSubtotalFormulaMode.Min.value:
            return min(self._filter_none_data(values))
        return None

    def _get_columns_for_subtotal_col(
            self, subtotal_col_result: SubtotalColModel, subtotal_row_cols: List[SubtotalRowModel]
    ):
        """
        获取列小计的行小计
        :param subtotal_col_result:
        :param subtotal_row_cols:
        :return:
        """
        if not subtotal_col_result.rows:
            return
        for subtotal_row_col in subtotal_row_cols:
            header = subtotal_row_col.header[0]
            field_id = header.field_id
            subtotal_row_field_name = self._get_subtotal_row_dict().get(field_id)
            if not subtotal_row_field_name:
                continue
            for subtotal_col_row in subtotal_col_result.rows:
                for idx, col in enumerate(subtotal_col_row.cols):
                    if (
                            len(col.header) == len(subtotal_row_col.header)
                            and col.header[0].alias_name == header.alias_name
                    ):
                        col = SubtotalRowColumnModel(
                            **{
                                'col_value': WidgetUtil.convert_to_py_data_type(col.col_value),
                                'siblings': [
                                    item
                                    for item in subtotal_col_row.cols
                                    if len(item.header) == 1 and item.header[0].col_type == ColTypes.Dim.value
                                ],
                            }
                        )
                        subtotal_row_col.subtotal_cols.append(col)
                        # 删除行小计字段
                        del subtotal_col_row.cols[idx]

    def _get_summary_columns_for_subtotal_col(
            self, subtotal_col_result: SubtotalColModel, subtotal_summary_col: SubtotalRowModel
    ):
        """
        获取小计的行总计
        :param subtotal_col_result:
        :param subtotal_summary_col:
        :return:
        """
        if not subtotal_col_result.rows:
            return
        summary_columns = [
            summary_field.alias or summary_field.field for summary_field in self._get_subtotal_summary_fields()
        ]
        field_values = []
        for subtotal_col_row in subtotal_col_result.rows:
            for idx, col in enumerate(subtotal_col_row.cols):
                if len(col.header) == 1 and col.header[0].alias_name in summary_columns:
                    field_values.append(WidgetUtil.convert_to_py_data_type(col.col_value))
                    # 删除行总计字段
                    del subtotal_col_row.cols[idx]
            field_value = self._compute_summary_value(
                field_values, self._chart_data_model.subtotal_row_summary_formula_mode
            )
            col = SubtotalRowColumnModel(
                **{
                    'col_value': WidgetUtil.convert_to_py_data_type(field_value),
                    'siblings': [
                        item
                        for item in subtotal_col_row.cols
                        if len(item.header) == 1 and item.header[0].col_type == ColTypes.Dim.value
                    ],
                }
            )
            subtotal_summary_col.subtotal_cols.append(col)

    def _get_columns_for_subtotal_col_summary(
            self, subtotal_col_result: SubtotalColModel, subtotal_row_cols: List[SubtotalRowModel]
    ):
        """
        获取列总计的行小计
        :param subtotal_col_result:
        :param subtotal_row_cols:
        :return:
        """
        if not subtotal_col_result.summary:
            return
        for subtotal_row_col in subtotal_row_cols:
            header = subtotal_row_col.header[0]
            field_id = header.field_id
            subtotal_row_field_name = self._get_subtotal_row_dict().get(field_id)
            if not subtotal_row_field_name:
                continue
            for idx, col in enumerate(subtotal_col_result.summary.cols):
                if len(col.header) == len(subtotal_row_col.header) and col.header[0].alias_name == header.alias_name:
                    col = SubtotalRowColumnModel(
                        **{'col_value': WidgetUtil.convert_to_py_data_type(col.col_value), 'siblings': []}
                    )
                    subtotal_row_col.subtotal_summary_cols.append(col)
                    # 删除行小计字段
                    del subtotal_col_result.summary.cols[idx]

    def _get_summary_columns_for_subtotal_col_summary(
            self, subtotal_col_result: SubtotalColModel, subtotal_summary_col: SubtotalRowModel
    ):
        """
        获取列总计的行总计
        :param subtotal_col_result:
        :param subtotal_summary_col:
        :return:
        """
        if not subtotal_col_result.summary:
            return
        summary_columns = [
            summary_field.alias or summary_field.field for summary_field in self._get_subtotal_summary_fields()
        ]
        field_values = []
        for idx, col in enumerate(subtotal_col_result.summary.cols):
            if len(col.header) == 1 and col.header[0].alias_name in summary_columns:
                field_values.append(WidgetUtil.convert_to_py_data_type(col.col_value))
                # 删除行总计字段
                del subtotal_col_result.summary.cols[idx]
        field_value = self._compute_summary_value(
            field_values, self._chart_data_model.subtotal_row_summary_formula_mode
        )
        col = SubtotalRowColumnModel(**{'col_value': WidgetUtil.convert_to_py_data_type(field_value), 'siblings': []})
        subtotal_summary_col.subtotal_summary_cols.append(col)

    def _format_subtotal_row_fields(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        格式化行小计字段
        对于没有对比维度的 原始字段中已经存在行小计及总计字段
        对于有对比维度的 原始字段无行小计及总计字段 需要在此处进行拼接
        :param data:
        :return:
        """
        if not self._chart_data_model.comparisons:
            return data
        select_fields = self._subtotal_row_select_obj.get_select_fields(self._chart_data_model)
        dim_select_fields = self._get_dim_select_obj().get_select_fields(self._chart_data_model)
        where_fields = copy.deepcopy(self._origin_where_fields)
        where_fields.extend(self._get_comparison_where_fields(data))
        group_fields = DimGroup().get_group_fields(self._chart_data_model)
        query_agent = QueryAgent()
        json_struct = query_agent.convert(
            self._get_query_agent_convert_params(
                {"select": [*select_fields, *dim_select_fields], "where": where_fields, "group": group_fields}
            )
        )
        query_params = {
            "user_id": self._user_id,
            "dataset_id": self._chart_data_model.dataset_id,
            "chart_id": self._chart_data_model.id,
            "json_struct": json_struct,
            "external_subject_ids": self._chart_data_model.external_subject_ids,
        }
        result = QueryAgent.query(**query_params)
        if result and result.get("code") != 200:
            raise ChartQueryExceptionError(message=trans_msg("单图取数异常: {}").format(result.get('msg')))
        subtotal_dataframe = pd.DataFrame(result.get("data")) if result.get("code") == 200 else pd.DataFrame()
        subtotal_dataframe.fillna(value=np.nan, inplace=True)
        subtotal_dataframe.set_index(keys=list(data.index.names), inplace=True)
        # pandas concta 遇到nan数据认为不一致 因此参数nan为索引的数据会有异常
        result_df = pd.concat([data, subtotal_dataframe], axis=1, join='outer', sort=False)
        for select_field in select_fields:
            field_name = select_field.alias or select_field.field
            column_values = result_df[field_name]
            data[field_name] = column_values
        return data

    def _format_subtotal_row_fields_v2(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        格式化行小计字段
        对于没有对比维度的 原始字段中已经存在行小计及总计字段
        对于有对比维度的 原始字段无行小计及总计字段 需要在此处进行拼接
        :param data:
        :return:
        """
        if not self._chart_data_model.comparisons:
            return data
        select_fields = self._subtotal_row_select_obj.get_select_fields(self._chart_data_model)
        dim_select_fields = self._get_dim_select_obj().get_select_fields(self._chart_data_model)
        where_fields = copy.deepcopy(self._origin_where_fields)
        where_fields.extend(self._get_comparison_where_fields(data))
        group_fields = DimGroup().get_group_fields(self._chart_data_model)
        query_agent = QueryAgent()
        json_struct = query_agent.convert(
            self._get_query_agent_convert_params(
                {"select": [*select_fields, *dim_select_fields], "where": where_fields, "group": group_fields}
            )
        )
        query_params = {
            "user_id": self._user_id,
            "dataset_id": self._chart_data_model.dataset_id,
            "chart_id": self._chart_data_model.id,
            "json_struct": json_struct,
            "external_subject_ids": self._chart_data_model.external_subject_ids,
        }
        result = QueryAgent.query(**query_params)
        if result and result.get("code") != 200:
            raise ChartQueryExceptionError(message=trans_msg("单图取数异常: {}").format(result.get('msg')))
        subtotal_dataframe = pd.DataFrame(result.get("data")) if result.get("code") == 200 else pd.DataFrame()
        subtotal_dataframe.fillna(value=np.nan, inplace=True)
        subtotal_dataframe.set_index(keys=list(data.index.names), inplace=True)
        # 为每行字段赋值
        field_values_dict = {select_field.alias or select_field.field: [] for select_field in select_fields}
        subtotal_df_dict = {}
        for idx, tuple_key in enumerate(subtotal_dataframe.index.values.tolist()):
            transform_key = self._get_tuple_key(tuple_key)
            subtotal_df_dict[transform_key] = subtotal_dataframe.iloc[idx]
        log_tmp_list = []
        for idx, index in enumerate(data.index.values.tolist()):
            transform_key = self._get_tuple_key(index)
            subtotal_df_line = subtotal_df_dict.get(transform_key, pd.Series())
            for select_field in select_fields:
                field_name = select_field.alias or select_field.field
                field_val = None
                if len(subtotal_df_line) and (field_name in subtotal_df_line):
                    field_val = subtotal_df_line[field_name]
                if field_val is None:
                    log_tmp_list.append({'field_name': field_name, 'transform_key': transform_key})
                field_values_dict[field_name].append(field_val)
        if log_tmp_list:
            logger.error(f'存在行小计取数计算的值为None, 行小计取数SQL返回的值有问题, 详细日志需打开App.enable_profiling=1, '
                         f'以及加上__stack__=1(或者根据性能医生的行小计SQL, 加上__sql__=1)重新请求输出查看行小计取数SQL')

        AnalysisTimeUtils.recode_time_node('开始行小计疑似耗时代码计算-1')
        # 增加字段
        # 2023.1.9 此处代码使用切片操作panda.DataFrame有性能问题
        # 使用替代方案
        # for select_field in select_fields:
        #     field_name = select_field.alias or select_field.field
        #     data[field_name] = field_values_dict.get(field_name)
        for select_field in select_fields:
            field_name = select_field.alias or select_field.field
            data.insert(data.shape[1], field_name, field_values_dict.get(field_name))
        AnalysisTimeUtils.recode_time_node('结束行小计疑似耗时代码计算-1')
        return data

    def _get_tuple_key(self, val):
        if isinstance(val, Iterable):
            return tuple([WidgetUtil.convert_to_py_data_type(item) for item in val])
        return tuple([WidgetUtil.convert_to_py_data_type(val)])

    def _get_locate_conditions(self, data_index_name_dict, item, locate_condition_names, locate_conditions):
        for locate_condition_name in locate_condition_names:
            if isinstance(item.index, pd.MultiIndex):
                idx = data_index_name_dict.get(locate_condition_name)
                if idx is None:
                    continue
                locate_conditions[locate_condition_name] = item.index[0][idx]
            else:
                locate_conditions[locate_condition_name] = item.index[0]

    def _get_dim_select_obj(self):
        if not hasattr(self, '_dim_select_obj'):
            self._dim_select_obj = DimSelect()
        return self._dim_select_obj

    def _get_comparison_where_fields(self, data: pd.DataFrame) -> List[object]:
        where_fields = []
        where_conditions = {}
        for dim_select_field in self._get_dim_select_obj().get_select_fields(self._chart_data_model):
            where_field = copy.deepcopy(dim_select_field)
            where_field.alias = ''
            where_conditions[dim_select_field.alias or dim_select_field.field] = {
                "where_field": where_field,
                "contions": [],
            }
        index_name_dict = {name: idx for idx, name in enumerate(data.index.names)}
        for name, condition in where_conditions.items():
            idx = index_name_dict.get(name)
            if idx is None:
                continue
            _conditions = {
                index_value[idx] if isinstance(index_value, tuple) else index_value
                for index_value in data.index.values.tolist()
            }
            condition['contions'].extend(list(_conditions))
            conditions = []
            is_null = self._convert_conditions_and_judge_null(condition, conditions)
            field = condition["where_field"]
            original_dim_dict = self._chart_alias_obj.get_original_dim_dict()
            original_dim = original_dim_dict.get(field.field_ref, {})
            where_field_params = {
                'alias_name': '',
                'col_name': field.field,
                'dataset_field_id': field.field_ref,
                'data_type': field.field_type,
                'formula_mode': original_dim.get('formula_mode', ''),
            }
            if conditions:
                where_field = self.dim2where(where_field_params, SqlWhereOperator.In.value, conditions)
                self._op_where_field_with_null(is_null, where_field, where_field_params)
            else:
                if not is_null:
                    continue
                where_field = self.dim2where(where_field_params, SqlWhereOperator.IsNull.value, None)
            where_fields.append(where_field)
        return where_fields

    def _convert_conditions_and_judge_null(self, condition, conditions):
        is_null = False
        for item in condition['contions']:
            val = WidgetUtil.convert_to_py_data_type(item)
            if val is None:
                is_null = True
                continue
            conditions.append(val)
        return is_null

    def _op_where_field_with_null(self, is_null, where_field, where_field_params):
        if is_null:
            where_field.complex_logic = 'OR'
            where_field.complex = [self.dim2where(where_field_params, SqlWhereOperator.IsNull.value, None)]

    def _get_query_agent_convert_params(self, params: dict):
        params["vars"] = self._get_subtotal_query_vars()
        params["dataset_field_dict"] = self._chart_data_model.dataset_field_dict
        params["external_subject_ids"] = self._chart_data_model.external_subject_ids
        return params

    def _get_subtotal_query_vars(self):
        return self._query_vars

    def _get_select_obj(self):
        if not hasattr(self, '_select_obj'):
            self._select_obj = Select()
        return self._select_obj

    def dim2where(self, field, operator, value):
        return Where.field2where(field, operator, value, self._chart_data_model)
