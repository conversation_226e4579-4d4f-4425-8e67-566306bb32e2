#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/2 17:22
# <AUTHOR> caoxl
# @File     : subtotal_col_query.py
# pylint:disable=R0201,W0613,C0123
import logging
from datetime import datetime
from operator import itemgetter
import copy
from typing import Dict
from typing import List, Tuple

import json
import pandas as pd
from collections import defaultdict

from base.dmp_constant import EXTERNAL_CUBE_SUBJECT_SUBTOTAL_ALIAS_SUFFIX, FUNCTION_SUBTOTAL_QUERY_METHOD
from dashboard_chart.data_query.widget import utils as WidgetUtil
from base.enums import SqlWhereOperator, ColTypes, ChartNumSubtotalFormulaMode, DatasetConnectType, DatasetType
from dmplib.locale import trans_msg
from dashboard_chart.models import (
    SubtotalColCateFieldModel,
    SubtotalColModel,
    SubtotalColSummaryModel,
    SubtotalHeaderModel,
)
from dashboard_chart.models import FieldExtDataModel
from dashboard_chart.convertor.group.dim_group import DimGroup
from dashboard_chart.convertor.where.where import Where
from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.convertor.select.agg.subtotal_row_select import SubtotalRowSelectForAgg
from dashboard_chart.convertor.select.comparison_select import ComparisonSelect
from dashboard_chart.convertor.select.dim_select import DimSelect
from dashboard_chart.convertor.select.num_select import NumSelect
from dmplib.utils.errors import UserError
from dashboard_chart.convertor import utils as ConvertorUtils
from dashboard_chart.data_query import utils as DataQueryUtil
from base.errors import ChartQueryExceptionError
from base.enums import DataQueryMode
from dashboard_chart.data_query.widget.subtotal.subtotal_col_struct import SubtotalStruct, SubtotalSummaryStruct
import functools
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dmplib.hug.context import DBContext
from dmplib import config
from dmplib.hug import g
from components.query_models import Select
from dashboard_chart.data_query.widget.utils import convert_to_py_data_type
from components.analysis_time import AnalysisTimeUtils

logger = logging.getLogger(__name__)


def handle_g(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread_local_g = _AppCtxGlobals()
        thread_local_g.code = kwargs.pop('code', None)
        thread_local_g.account = kwargs.pop('account', None)  # getattr(parent_thread_g, 'account', None)
        thread_local_g.userid = kwargs.pop('userid', None)  # getattr(parent_thread_g, 'userid', None)
        thread_local_g.cookie = kwargs.pop('cookie', None)  # getattr(parent_thread_g, 'cookie', None)
        thread_local_g.external_params = kwargs.pop('external_params', None)
        _app_ctx_stack.push(thread_local_g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(thread_local_g)
        try:
            return func(*args, **kwargs)
        finally:
            db_ctx.close_all()
            _app_ctx_stack.pop()

    return wrapper


from gevent.pool import Pool

pool = Pool(500)


class SubtotalColQuery:
    def __init__(self, **kwargs):
        self._chart_data_model = kwargs.get('chart_data_model')
        self._user_id = kwargs.get('user_id')
        self._origin_where_fields = kwargs.get('origin_where_fields')
        self._origin_select_fields = kwargs.get('origin_select_fields')
        self._origin_select_fields_headers = copy.deepcopy(self._origin_select_fields)
        self._origin_group_fields = kwargs.get('origin_group_fields')
        self._origin_order_fields = kwargs.get('origin_order_fields')
        self._query_vars = kwargs.get('query_vars')
        self._dim_select_obj = DimSelect()
        self._num_select_obj = NumSelect()
        self._comparison_select_obj = ComparisonSelect()
        self._chart_alias_obj = DataQueryUtil.ChartDisplayAlias(self._chart_data_model)
        self._filter_group_concat_select_fields()
        self.curr_query_mode = self.get_curr_query_mode()

    def get_curr_query_mode(self):
        from dashboard_chart.data_query.charts.common_chart import get_curr_mode
        return get_curr_mode()

    def get_g_property(self) -> dict:
        from dashboard_chart.services.chart_service import get_g_property
        return get_g_property()

    def set_g_property(self, g_params: dict) -> None:
        ignores = ['DBContext', 'cache', 'get', 'pop', 'redis_conns', 'storage']
        for key, val in g_params.items():
            if key in ignores:
                continue
            setattr(g, key , val)

    def _filter_group_concat_select_fields(self):
        self._origin_select_fields = [
            item for item in self._origin_select_fields if item.field_func != "group_concat"
        ]

    def get_subtotal_groups(self):
        subtotal_groups = []
        sorted_dims = copy.deepcopy(self._chart_data_model.dims)
        uncompute_subtotal_fields = self._get_uncompute_subtotal_fields_v2()
        for idx, dim in enumerate(sorted_dims):
            dim['dataset_field_id'] = dim.get('dim')
            if dim.get("is_subtotal_cate") == 1 and dim.get("dim") not in uncompute_subtotal_fields:
                items = sorted_dims[0 : idx + 1]
                subtotal_groups.append(items)
        return subtotal_groups

    def get_subtotal_groups_v2(self):
        """
        新版获取小计汇总分组
        1. 获取所有排序字段，去除非维度字段
        2. 获取所有不需要计算的字段
        3. 遍历处理好的排序列表，排出不需要展示的字段，获取最终需要展示的分类汇总分组
        :return:
        """
        subtotal_groups = []
        # 1. 获取所有排序字段，去除非维度字段
        dim_map = {dim.get("dim"): dim for dim in self._chart_data_model.dims}
        order_dims = [
            dim_map[order.field_ref]
            for order in copy.deepcopy(self._origin_order_fields)
            if order.logic_source == ColTypes.Dim.value and order.field_ref in dim_map
        ]
        # 2.获取所有不需要计算的字段
        uncompute_subtotal_fields = self._get_uncompute_subtotal_fields_v3()
        for idx, dim in enumerate(order_dims):
            dim['dataset_field_id'] = dim.get('dim')
            if dim.get("is_subtotal_cate") == 1 and dim.get("dim") not in uncompute_subtotal_fields:
                items = order_dims[0 : idx + 1]
                subtotal_groups.append(items)
        return subtotal_groups

    def _get_uncompute_subtotal_fields_v3(self):
        """
        1. 获取列展示顺序种所有字段的权重
        2. 将列展示顺序按照权重排序进行遍历，若分分类汇总相关数值字段不在分类汇总维度字段后方(即分类汇总相关数值字段展示权重须小于分类汇总字段展示权重)则不显示小计，反之显示小计
        3. 遍历排序列表，若是已勾选分类汇总自动，将当前元素前方和后方的分别取出
        4. 查看当前元素是否隐藏，若隐藏则该小计不计算，否则执行后续步骤
        5. 遍历前方元素，只要有任意一个元素展示列权重小于当前元素展示列权重，则该值不计算小计
        6. 遍历后方元素，只要有任意一个元素展示列权重大于当前元素展示列权重，则该值不计算小计
        :return:
        """
        uncompute_subtotal_fields = []
        # 获取所有字段权重和是否展示
        column_display_map = self._get_column_display_map()
        subtotal_field_ids = [
            dim.get("dataset_field_id") for dim in self._chart_data_model.dims if dim.get("is_subtotal_cate") == 1
        ]
        for idx, order_field in enumerate(self._origin_order_fields):
            field_id = order_field.field_ref
            if order_field.logic_source != ColTypes.Dim.value or field_id not in subtotal_field_ids:
                continue
            column_display_map_key = tuple([field_id, ColTypes.Dim.value])
            # 隐藏列则不计算小计
            if not column_display_map.get(column_display_map_key, {}).get("is_show"):
                uncompute_subtotal_fields.append(field_id)
                continue
            current_weight = column_display_map.get(column_display_map_key, {}).get("order", 0)
            # 判断小计相关数值字段展示权重是否比当前分类汇总字段展示权重低
            if self._compare_subtotal_dim_and_nums_display_weight(column_display_map, field_id):
                uncompute_subtotal_fields.append(field_id)
                continue
            # 前方序列
            front_list = self._origin_order_fields[0:idx]
            # 后方序列
            back_list = self._origin_order_fields[idx + 1 :]
            # 显示规则为 当前排序字段前面没有比自己展示序列优先级低的，后面没有比自己展示序列优先级高的
            # 注意order值小的优先级高，反之则优先级低
            self._get_front_upcompute_subtotal_fields(
                column_display_map, current_weight, field_id, front_list, uncompute_subtotal_fields
            )
            self._get_back_upcompute_subtotal_fields(
                back_list, column_display_map, current_weight, field_id, uncompute_subtotal_fields
            )
        return uncompute_subtotal_fields

    def _compare_subtotal_dim_and_nums_display_weight(self, column_display_map, subtotal_dim_field_id):
        """
        对比小计分类汇总字段和相关数值字段展示权重
        若分类汇总字段展示权重小计任意一个相关数值字段展示权重则返回1，反之返回0
        :param column_display_map:
        :param subtotal_dim_field_id:
        :return:
        """
        dim_display_weight = column_display_map.get(tuple([subtotal_dim_field_id, ColTypes.Dim.value]), {}).get(
            "order", 0
        )
        for subtotal_num in self._get_subtotal_nums:
            map_key = tuple([subtotal_num.get("num"), ColTypes.Num.value])
            num_display_weight = column_display_map.get(map_key, {}).get("order", 0)
            if num_display_weight < dim_display_weight:
                return 1
        return 0

    def _get_back_upcompute_subtotal_fields(
        self, back_list, column_display_map, current_weight, field_id, uncompute_subtotal_fields
    ):
        for item in back_list:
            # 聚合模式下的对比维度 不需要对比 (此时前端column_display有可能不会传递对比维度)
            if self._chart_data_model.aggregation and item.logic_source == ColTypes.Comparison.value:
                continue
            item_field_id = item.field_ref
            item_weight = column_display_map.get(tuple([item_field_id, item.logic_source]), {}).get("order", 0)
            if item_weight < current_weight:
                uncompute_subtotal_fields.append(field_id)

    def _get_front_upcompute_subtotal_fields(
        self, column_display_map, current_weight, field_id, front_list, uncompute_subtotal_fields
    ):
        for item in front_list:
            # 聚合模式下的对比维度 不需要对比 (此时前端column_display有可能不会传递对比维度)
            if self._chart_data_model.aggregation and item.logic_source == ColTypes.Comparison.value:
                continue
            item_field_id = item.field_ref
            item_weight = column_display_map.get(tuple([item_field_id, item.logic_source]), {}).get("order", 0)
            if item_weight > current_weight:
                uncompute_subtotal_fields.append(field_id)

    def _get_column_display_map(self):
        """
        获取列展示字典
        存在两种场景： 1. 列展示顺序显示传递； 2. 列展示顺序未传递
        算法： 对于场景1 直接取列展示顺序， 对于场景 2 按照 dim > comparison > num 这种规则构建展示顺序
        :return:
        """
        column_display_map = {}
        if self._chart_data_model.column_display:
            column_display_map = {
                tuple([column.get("dataset_field_id"), column.get("col_type")]): {
                    "order": column.get("order", 0),
                    "is_show": column.get("is_show", 1),
                }
                for column in self._chart_data_model.column_display
            }
        else:
            dim_display_map, order = self._get_column_display_map_by_col_type(col_type=ColTypes.Dim.value)
            comparison_diplay_map, order = self._get_column_display_map_by_col_type(
                col_type=ColTypes.Comparison.value, order=order
            )
            num_diplay_map, _ = self._get_column_display_map_by_col_type(col_type=ColTypes.Num.value, order=order)
            column_display_map.update(dim_display_map)
            column_display_map.update(comparison_diplay_map)
            column_display_map.update(num_diplay_map)
        return column_display_map

    def _get_column_display_map_by_col_type(self, col_type: str, order: int = None):
        order = order if order is not None else 0
        lists = []
        field_id_key = None
        column_display_map = {}
        if col_type == ColTypes.Dim.value:
            lists = self._chart_data_model.dims or []
            field_id_key = "dim"
        elif col_type == ColTypes.Comparison.value:
            lists = self._chart_data_model.comparisons or []
            field_id_key = "dataset_field_id"
        elif col_type == ColTypes.Num.value:
            lists = self._chart_data_model.nums or []
            field_id_key = "num"
        for item in lists:
            column_display_map[tuple([item.get(field_id_key), col_type])] = {"order": order, "is_show": 1}
            order += 1
        return column_display_map, order

    def _get_uncompute_subtotal_fields_v2(self):
        """
        获取不需要计算小计的字段 (column_display 中不会传入对比维度 因此不需要考虑对比维度)
        :return:
        """
        uncompute_subtotal_fields = []
        # 此处使用原始的dim和num 原因是对比维度转明细后 前端不会传递该对比维度 但是后端已经将对比维度作为维度处理
        # 此时column_display元素会和dim和num组成的数据有差异
        sorted_dims = sorted(self._chart_data_model.original_dims, key=itemgetter("rank"))
        sorted_nums = sorted(self._chart_data_model.original_nums, key=itemgetter("rank"))
        sorted_dim_and_num = []
        subtotal_field_ids = [
            dim.get("dataset_field_id") for dim in self._chart_data_model.dims if dim.get("is_subtotal_cate") == 1
        ]
        for sorted_dim in sorted_dims:
            sorted_dim_and_num.append(tuple([sorted_dim.get("dim"), ColTypes.Dim.value]))
        for sorted_num in sorted_nums:
            sorted_dim_and_num.append(tuple([sorted_num.get("num"), ColTypes.Num.value]))
        column_display = sorted(self._chart_data_model.column_display, key=lambda item: item.get("order", 0))
        sorted_columns = [tuple([column.get("dataset_field_id"), column.get("col_type")]) for column in column_display]
        sorted_dim_and_num_max_idx = len(sorted_dim_and_num) - 1
        for idx, item in enumerate(sorted_columns):
            column_display_item = column_display[idx]
            column_col_type = column_display_item.get("col_type")
            column_dataset_field_id = column_display_item.get("dataset_field_id")
            # 不是维度 或者 字段不是小计计算字段 跳过
            if column_col_type != ColTypes.Dim.value or column_dataset_field_id not in subtotal_field_ids:
                continue
            # 若维度不显示，小计则不显示
            if not column_display_item.get("is_show"):
                uncompute_subtotal_fields.append(column_dataset_field_id)
                continue
            # 若对应位置上的值不一致 则不显示 (或者值没有)
            if idx > sorted_dim_and_num_max_idx or sorted_dim_and_num[idx] != item:
                uncompute_subtotal_fields.append(column_dataset_field_id)
                continue
            # 从第二个开始 若前面的顺序不一致则该小计不显示
            if (
                idx > 0
                and sorted_dim_and_num[idx] == item
                and tuple(sorted_dim_and_num[0:idx]) != tuple(sorted_columns[0:idx])
            ):
                uncompute_subtotal_fields.append(column_dataset_field_id)
        return uncompute_subtotal_fields

    def _get_subtotal_query_vars(self):
        return self._query_vars

    def get_data(self, data: pd.DataFrame, next_item: pd.DataFrame, prev_item: pd.DataFrame) -> SubtotalColModel:
        # 1. 只有先开启小计才有小计汇总
        # 2. 未开启小计直接返回
        subtotal_col_result = SubtotalColModel()
        if self.curr_query_mode == DataQueryMode.SUMMARY.value: # NOSONAR
            # 如果是直接获取总计，data为[], 放开这个限制
            if not self._chart_data_model.enable_subtotal_col: # NOSONAR
                return subtotal_col_result
        else: # NOSONAR
            # 原来的判断逻辑
            if not self._chart_data_model.enable_subtotal_col or len(data) == 0: # NOSONAR
                return subtotal_col_result

        subtotal_base_num_fields = self.get_subtotal_base_num_fields()
        subtotal_base_comparsion_fields = self.get_subtotal_base_comparison_field()
        num_headers, comparsion_dim_headers = [], []
        for subtotal_base_num_field in subtotal_base_num_fields:
            dataset_field_info = self._chart_data_model.dataset_field_dict.get(subtotal_base_num_field.field_ref, {})
            num_headers.append(
                [
                    SubtotalHeaderModel(
                        **{
                            'col_name': dataset_field_info.get('col_name'),
                            'alias_name': subtotal_base_num_field.alias or subtotal_base_num_field.field,
                            'col_value': None,
                            'field_id': subtotal_base_num_field.field_ref,
                            'alias': self._chart_alias_obj.get_alias(
                                subtotal_base_num_field.field_ref, ColTypes.Num.value
                            ),
                            'col_type': ColTypes.Num.value,
                            'dataset_id': self._chart_data_model.dataset_id,
                        }
                    )
                ]
            )
        for subtotal_base_comparsion_field in subtotal_base_comparsion_fields:
            dataset_field_info = self._chart_data_model.dataset_field_dict.get(
                subtotal_base_comparsion_field.field_ref, {}
            )
            comparsion_dim_headers.append(
                [
                    SubtotalHeaderModel(
                        **{
                            'col_name': dataset_field_info.get('col_name'),
                            'alias_name': subtotal_base_comparsion_field.alias or subtotal_base_comparsion_field.field,
                            'col_value': None,
                            'field_id': subtotal_base_comparsion_field.field_ref,
                            'alias': self._chart_alias_obj.get_alias(
                                subtotal_base_comparsion_field.field_ref, ColTypes.Comparison.value
                            ),
                            'col_type': ColTypes.Comparison.value,
                            'dataset_id': self._chart_data_model.dataset_id,
                        }
                    )
                ]
            )
        base_select_fields = [*subtotal_base_num_fields, *subtotal_base_comparsion_fields]
        base_group_fields = self.get_subtotal_base_group_fields()
        subtotal_row_fields = self.get_subtotal_row_fields()
        if self.curr_query_mode == DataQueryMode.SUMMARY.value:
            data = self.mock_header_data(self._origin_select_fields)
        data_num2subtotal_header_dict = self._get_data_num2subtotal_header_dict(data)
        subtotal_row_headers = self.get_subtotal_row_headers()
        data_num_headers = self._get_data_num_headers(data)
        query_agent = QueryAgent()
        header_data = {
            'data_num_headers': data_num_headers,
            'data_index_headers': [],
            'subtotal_row_headers': subtotal_row_headers,
            'data_num2subtotal_header_dict': data_num2subtotal_header_dict,
            'comparsion_dim_headers': comparsion_dim_headers,
            'num_headers': num_headers,
        }
        subtotal_col_rows, subtotal_col_summary = self._query_subtotal_and_summary(**{
            'data': data,
            'next_item': next_item,
            'prev_item': prev_item,
            'query_agent': query_agent,
            'base_select_fields': base_select_fields,
            'base_group_fields': base_group_fields,
            'subtotal_row_fields': subtotal_row_fields,
            'header_data': header_data,
        })
        subtotal_col_result.rows = subtotal_col_rows
        subtotal_col_result.summary = subtotal_col_summary

        return subtotal_col_result

    def _query_subtotal_and_summary(self, **kwargs):
        method = FUNCTION_SUBTOTAL_QUERY_METHOD
        start_datetime = datetime.now()
        if method == 'serial':
            rv = self._serial_query_subtotal_and_summary(**kwargs)
        else:
            rv = self._parallel_query_subtotal_and_summary(**kwargs)
        end_datetime = datetime.now()
        # pylint: disable=W1203
        logger.debug(
            f"报告<{self._chart_data_model.dashboard_id}>, "
            f"取总计和小计方式: {method}, 开始时间: {start_datetime}, 结束时间: {end_datetime}, "
            f"用时: {(end_datetime - start_datetime).total_seconds()}"
        )

        return rv

    def _serial_query_subtotal_and_summary(self, **kwargs):
        data = kwargs.get('data')
        next_item = kwargs.get('next_item')
        prev_item = kwargs.get('prev_item')
        query_agent = kwargs.get('query_agent')
        base_select_fields = kwargs.get('base_select_fields')
        base_group_fields = kwargs.get('base_group_fields')
        subtotal_row_fields = kwargs.get('subtotal_row_fields')
        header_data = kwargs.get('header_data')

        subtotal_col_rows = self._query_subtotal_col_data(
            data,
            next_item,
            prev_item,
            query_agent,
            base_select_fields=base_select_fields,
            base_group_fields=base_group_fields,
            subtotal_row_fields=subtotal_row_fields,
            header_data=header_data,
        )

        subtotal_col_summary = self._query_subtotal_col_summary(**{
            'query_agent': query_agent,
            'data': data,
            'base_select_fields': base_select_fields,
            'base_group_fields': base_group_fields,
            'subtotal_row_fields': subtotal_row_fields,
            'header_data': header_data
        })
        return subtotal_col_rows, subtotal_col_summary

    def _parallel_query_subtotal_and_summary(self, **kwargs):
        data = kwargs.get('data')
        next_item = kwargs.get('next_item')
        prev_item = kwargs.get('prev_item')
        query_agent = kwargs.get('query_agent')

        # 并发取小计
        @handle_g
        def single_query_data(ins: SubtotalColQuery, params, subtotal_col_rows):
            # 处理请求线程到协程的g传值问题
            # setattr(g, "snap_id", params.get("snap_id"))
            # setattr(g, "query_mode", params.get("query_mode"))
            # setattr(g, "dataset_id", params.get("dataset_id"))
            # setattr(g, "dataset_version_id", params.get("dataset_version_id"))
            #
            # del params["snap_id"]
            # del params["query_mode"]
            # del params['dataset_id']
            # del params['dataset_version_id']
            g_params = params.pop('g_params', {})
            self.set_g_property(g_params)
            new_subtotal_col_rows = ins._single_query_subtotal_col_data(
                params.get('data'),
                params.get('next_item'),
                params.get('prev_item'),
                params.get('query_agent'),
                params.get('group_dims'),
                **params.get('kwargs'),
            )
            subtotal_col_rows.extend(new_subtotal_col_rows)

        subtotal_col_rows = []
        subtotal_cate_groups = self.get_subtotal_groups_v2()
        for group_dims in subtotal_cate_groups:
            params = {
                'data': data,
                'next_item': next_item,
                'prev_item': prev_item,
                'query_agent': query_agent,
                'group_dims': group_dims,
                'kwargs': {
                    'base_select_fields': kwargs.get('base_select_fields'),
                    'base_group_fields': kwargs.get('base_group_fields'),
                    'subtotal_row_fields': kwargs.get('subtotal_row_fields'),
                    'header_data': kwargs.get('header_data'),
                },
                # 'snap_id': getattr(g, "snap_id", None),
                # 'query_mode': getattr(g, "query_mode", None),
                # 'dataset_id': getattr(g, "dataset_id", None),
                # 'dataset_version_id': getattr(g, 'dataset_version_id', None)
            }
            g_params = self.get_g_property()
            params['g_params'] = g_params

            external_params = getattr(g, 'external_params', None)
            cookie = getattr(g, 'cookie', None)
            pool.spawn(
                single_query_data,
                self,
                params,
                subtotal_col_rows,
                code=g.code,
                account=g.account,
                userid=g.userid,
                cookie=cookie,
                external_params=external_params,
            )

        # 并发取总计
        @handle_g
        def query_summary(ins: SubtotalColQuery, params, rv):
            # 处理请求线程到协程的g传值问题
            # setattr(g, "snap_id", params.get("snap_id"))
            # setattr(g, "query_mode", params.get("query_mode"))
            # setattr(g, "dataset_id", params.get("dataset_id"))
            # setattr(g, "dataset_version_id", params.get("dataset_version_id"))
            # del params["snap_id"]
            # del params["query_mode"]
            # del params['dataset_id']
            # del params['dataset_version_id']
            g_params = params.pop('g_params', {})
            self.set_g_property(g_params)

            subtotal_col_summary = ins._query_subtotal_col_summary(**params.get('kwargs'))
            rv['subtotal_col_summary'] = subtotal_col_summary

        rv = {}

        # 并行小计和总计
        external_params = getattr(g, 'external_params', None)
        cookie = getattr(g, 'cookie', None)
        summary_params = {
            'kwargs': kwargs,
            # 'snap_id': getattr(g, "snap_id", None),
            # 'query_mode': getattr(g, "query_mode", None),
            # 'dataset_id': getattr(g, "dataset_id", None),
            # 'dataset_version_id': getattr(g, 'dataset_version_id', None)
        }
        g_params = self.get_g_property()
        summary_params['g_params'] = g_params
        # summary_params.update(g_params)
        pool.spawn(
            query_summary,
            self,
            summary_params,
            rv,
            code=g.code,
            account=g.account,
            userid=g.userid,
            cookie=cookie,
            external_params=external_params
        )
        pool.join()

        return subtotal_col_rows, rv.get('subtotal_col_summary')

    def mock_summary(self):
        subtotal_col_summary = SubtotalColSummaryModel()
        subtotal_summary_struct_obj = SubtotalSummaryStruct(
            subtotal_col_data=[],
            subtotal_row_data=[],
            header_data={},
            chart_data_model=self._chart_data_model,
        )
        subtotal_col_summary.cols = subtotal_summary_struct_obj.build()
        return subtotal_col_summary

    def _query_subtotal_col_summary(self, **kwargs):
        # 明细模式不查询列总计
        _start_time_ = AnalysisTimeUtils.now()
        if self.curr_query_mode == DataQueryMode.DETAIL.value:
            return self.mock_summary()

        query_agent = kwargs.get('query_agent')
        data = kwargs.get('data')
        base_select_fields = kwargs.get('base_select_fields')
        base_group_fields = kwargs.get('base_group_fields')
        subtotal_row_fields = kwargs.get('subtotal_row_fields')
        header_data = kwargs.get('header_data')

        subtotal_col_summary = SubtotalColSummaryModel()
        summary_select_fields = copy.deepcopy(base_select_fields)
        # 获取行总计字段加入查询
        summary_select_fields.extend(self.get_subtotal_row_fields())
        # 没有任何查询字段不进行查询
        if not (self._chart_data_model.enable_subtotal_col_summary and summary_select_fields):
            return subtotal_col_summary

        # 多维模型下透传原始select字段
        if self._chart_data_model.external_subject_ids:
            for field in summary_select_fields:
                setattr(field, 'alias', f"{getattr(field, 'alias', '')}{EXTERNAL_CUBE_SUBJECT_SUBTOTAL_ALIAS_SUFFIX}")
            summary_select_fields = [*summary_select_fields, *self._origin_select_fields]
            base_group_fields = self._origin_group_fields
        # 模拟维度
        json_struct = query_agent.convert(
            self.get_query_agent_convert_params(
                {"select": summary_select_fields, "where": self._origin_where_fields, "group": base_group_fields}
            )
        )
        json_struct = self._replace_subtotal_col_formula_expression(json_struct, summary_select_fields)
        query_params = {
            "user_id": self._user_id,
            "dataset_id": self._chart_data_model.dataset_id,
            "chart_id": self._chart_data_model.id,
            "json_struct": json_struct,
            "external_subject_ids": self._chart_data_model.external_subject_ids,
            "chart_data_model": copy.copy(self._chart_data_model)
        }
        # 修复调度时并发取数问题，调度类型并且非数芯数据源才会传递表名
        self._use_cache_table_name(query_params)
        is_comparisons = True if self._chart_data_model.comparisons else False
        query_params['chart_data_model'].is_subtotal_col_query = is_comparisons
        query_params['chart_data_model'].is_subtotal_query = not is_comparisons
        query_params['chart_data_model'].origin_select_fields = self._origin_select_fields
        result = QueryAgent.query(**query_params)
        query_params['chart_data_model'].origin_select_fields = None
        query_params['chart_data_model'].is_subtotal_query = False
        query_params['chart_data_model'].is_subtotal_col_query = False
        logger.debug('列总计二次取数： %s' % result)
        self._judge_data_result(result)

        self.deal_same_ring_calc_summary_result(data, result)

        # 若有对比维度 则行小计字段需要单独求取
        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.summary_compare_process.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            subtotal_row_summary_data = self._get_subtotal_row_summary_data(
                query_agent, subtotal_row_fields, result.get("data")
            )  # 对结果进行处理

        subtotal_summary_struct_obj = SubtotalSummaryStruct(
            subtotal_col_data=result.get("data"),
            subtotal_row_data=subtotal_row_summary_data,
            header_data=header_data,
            chart_data_model=self._chart_data_model,
        )
        subtotal_col_summary.cols = subtotal_summary_struct_obj.build()
        AnalysisTimeUtils.record(
            step=AnalysisTimeUtils.step_type.subtotal_summary_process.value, sql=None, db_type=None,
            start_time=_start_time_, extra={}, need_type_inference=False
        )
        return subtotal_col_summary

    def _use_cache_table_name(self, query_params):
        # 修复调度时并发取数问题，调度类型并且非数芯数据源才会传递表名
        if self._chart_data_model.dataset.get('connect_type') != DatasetConnectType.Directly.value and self._chart_data_model.dataset.get('type') != DatasetType.Indicator.value:
            query_params['table_name'] = self._chart_data_model.dataset.get('table_name')


    def _get_same_ring_nums(self):
        return {
            num['col_name']: num
            for num in self._chart_data_model.nums if num['formula_mode'] in ['same_ratio', 'ring_ratio']
        }

    def deal_same_ring_calc_summary_result(self, data: pd.DataFrame, query_result):
        # 处理同环比计算的列汇总值
        query_data = query_result.get('data', [])

        # 1. 先找到同环比计算的列
        nums = self._get_same_ring_nums()
        if not nums:
            return

        data_same_ring_col_names = {
            num['col_name']: f"{num['formula_mode']}_{num['col_name']}"
            for num in nums.values()
        } # same_ratio_MBRGTS_15764154403
        result_same_ring_col_names = {
            num['col_name']: f"{num['subtotal_col_formula_mode']}_{num['col_name']}"
            for num in nums.values()
        } # sum_MBRGTS_15764154403

        # 2. 直接计算列的汇总值
        for line in query_data:
            for col_name, data_same_ring_col_name in data_same_ring_col_names.items():
                value = self._same_ring_col_calc(
                    data[data_same_ring_col_name], nums[col_name]
                )
                line[result_same_ring_col_names[col_name]] = convert_to_py_data_type(value)

    def _get_alias_to_prop_raw_map(self, select_files: [Select]):
        # 获取expression条件下的 alias->表达式的map
        if not hasattr(self, '_alias_to_prop_raw_map'):
            expression_dict = {
                num['num']: json.loads(num.get('subtotal_col_formula_expression') or '{}').get('exp', '')
                for num in self._chart_data_model.nums
            }
            result = {}
            for select in select_files:
                if select.field_func == 'expression':
                    result[select.alias] = expression_dict[select.field_ref]
            setattr(self, '_alias_to_prop_raw_map', result)

        return getattr(self, '_alias_to_prop_raw_map', {})

    def _replace_subtotal_col_formula_expression(self, json_struct: str, select_files):
        # 修改表达式的structure， 替换成高级计算形式
        try:
            json_struct_dict = json.loads(json_struct)
        except:
            json_struct_dict = {}
        selects = json_struct_dict.get('select', [])
        prop_raw_map = self._get_alias_to_prop_raw_map(select_files)

        for select in selects:
            alias = select.get('alias')
            if alias in prop_raw_map:
                select['prop_raw'] = f"({prop_raw_map[alias]})"
                select['props'] = []
                select['func'] = ''

        return json.dumps(json_struct_dict, ensure_ascii=False) if json_struct else json_struct


    def _parallel_query_subtotal_col_data(
        self, data: pd.DataFrame, next_item: pd.DataFrame, prev_item: pd.DataFrame, query_agent: QueryAgent, **kwargs
    ):

        subtotal_col_rows = []
        subtotal_cate_groups = self.get_subtotal_groups_v2()

        @handle_g
        def single_query_data(ins: SubtotalColQuery, params, subtotal_col_rows):
            # 处理请求线程到协程的g传值问题
            # setattr(g, "snap_id", params.get("snap_id"))
            # setattr(g, "query_mode", params.get("query_mode"))
            # setattr(g, "dataset_id", params.get("dataset_id"))
            # setattr(g, "dataset_version_id", params.get("dataset_version_id"))
            # del params["snap_id"]
            # del params["query_mode"]
            # del params['dataset_id']
            # del params['dataset_version_id']
            g_params = params.pop('g_params', {})
            self.set_g_property(g_params)
            new_subtotal_col_rows = ins._single_query_subtotal_col_data(
                params.get('data'),
                params.get('next_item'),
                params.get('prev_item'),
                params.get('query_agent'),
                params.get('group_dims'),
                **params.get('kwargs'),
            )
            subtotal_col_rows.extend(new_subtotal_col_rows)

        for group_dims in subtotal_cate_groups:
            params = {
                'data': data,
                'next_item': next_item,
                'prev_item': prev_item,
                'query_agent': query_agent,
                'group_dims': group_dims,
                'kwargs': kwargs,
                # 'snap_id': getattr(g, "snap_id", None),
                # 'query_mode': getattr(g, "query_mode", None),
                # 'dataset_id': getattr(g, "dataset_id", None),
                # 'dataset_version_id': getattr(g, 'dataset_version_id', None)
            }
            g_params = self.get_g_property()
            params['g_params'] = g_params

            external_params = getattr(g, 'external_params', None)
            cookie = getattr(g, 'cookie', None)
            pool.spawn(
                single_query_data,
                self,
                params,
                subtotal_col_rows,
                code=g.code,
                account=g.account,
                userid=g.userid,
                cookie=cookie,
                external_params=external_params,
            )
        pool.join()
        return subtotal_col_rows

    def _single_query_subtotal_col_data(
        self, data: pd.DataFrame, next_item: pd.DataFrame, prev_item: pd.DataFrame, query_agent: QueryAgent, group_dims, **kwargs
    ):
        _start_time_ = AnalysisTimeUtils.now()
        base_select_fields = kwargs.get('base_select_fields')
        base_group_fields = kwargs.get('base_group_fields')
        subtotal_row_fields = kwargs.get('subtotal_row_fields')
        origin_header_data = kwargs.get('header_data')

        group_fields, subtotal_cate_fields, contain_keys, index_headers = [], [], [], []
        subtotal_row_locate_fields = []
        # 获取基础查询字段
        select_fields = copy.deepcopy(base_select_fields)
        header_data = copy.deepcopy(origin_header_data)
        # 若无对比维度则将行小计字段加入查询（有对比维度时 需要单独查询并将字段拼接至小计透视后的结果行中）
        if not self._chart_data_model.comparisons:
            select_fields.extend(subtotal_row_fields)
        # 将原始where字段加入条件
        where_fields = copy.deepcopy(self._origin_where_fields)
        where_fields_dict = dict()
        current_where_field_values = defaultdict(set)
        # 将分类汇总字段加入查询字段及group字段
        for group_dim in group_dims:
            group_field = self._dim2group(group_dim)
            group_fields.append(group_field)
            select_field = self._dim2select(group_dim)
            subtotal_row_locate_fields.append(select_field)
            select_fields.append(select_field)
            col_name = self._get_data_col_name(select_field)
            subtotal_cate_fields.append(
                SubtotalColCateFieldModel(
                    **{
                        "dataset_id": group_dim.get("dataset_id"),
                        "field_id": group_dim.get("dataset_field_id"),
                        "rank": group_dim.get("rank"),
                        "col_name": col_name,
                        "detail_col_name": col_name,
                    }
                )
            )
            # 数据类型
            group_dim["data_type"] = self._chart_data_model.dataset_field_dict.get(group_dim.get("dim")).get(
                "data_type"
            )
            # 数据字段索引
            group_dim['data_key'] = select_field.alias or select_field.field
            contain_keys.append(group_dim.get("data_key"))
            current_where_field_values = self._get_where_values(data, group_dim, current_where_field_values)
            self._assign_where_fields_dict(where_fields_dict, group_dim, current_where_field_values)
            # index（group）header 获取
            dataset_field_info = self._chart_data_model.dataset_field_dict.get(group_dim.get('dim'), {})
            index_headers.append(
                [
                    SubtotalHeaderModel(
                        **{
                            'col_name': dataset_field_info.get('col_name') or group_dim.get('col_name'),
                            'alias_name': group_dim.get('data_key'),
                            'col_value': None,
                            'field_id': group_dim.get('dim'),
                            'alias': self._chart_alias_obj.get_alias(group_dim.get('dim'), ColTypes.Dim.value)
                            or group_dim.get("alias"),
                            'col_type': ColTypes.Dim.value,
                            'dataset_id': self._chart_data_model.dataset_id,
                        }
                    )
                ]
            )
        # 将当前数据加入到where条件 缩小查询范围
        for _, where_field in where_fields_dict.items():
            where_fields.append(where_field)
        # 将特殊排序字段加入到group by 头部 （在外排序）
        self._insert_special_group_fields(group_fields)
        subtotal_row_group_fields = copy.deepcopy(group_fields)
        # 加入基础 group 字段 (如对比维度)
        group_fields.extend(base_group_fields)
        # 多维模型下透传原始select字段
        if self._chart_data_model.external_subject_ids:
            for field in select_fields:
                setattr(field, 'alias', f"{getattr(field, 'alias', '')}{EXTERNAL_CUBE_SUBJECT_SUBTOTAL_ALIAS_SUFFIX}")
            select_fields = [*select_fields, *self._origin_select_fields]
            group_fields = self._origin_group_fields

        # 加入变量获取完整查询结构
        json_struct = query_agent.convert(
            self.get_query_agent_convert_params({"select": select_fields, "group": group_fields, "where": where_fields})
        )
        json_struct = self._replace_subtotal_col_formula_expression(json_struct, select_files=select_fields)

        query_params = {
            "user_id": self._user_id,
            "dataset_id": self._chart_data_model.dataset_id,
            "chart_id": self._chart_data_model.id,
            "json_struct": json_struct,
            "external_subject_ids": self._chart_data_model.external_subject_ids,
            "chart_data_model": copy.copy(self._chart_data_model),
            "other_params":{}
        }
        # 修复调度时并发取数问题，调度类型并且非数芯数据源才会传递表名
        self._use_cache_table_name(query_params)
        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.single_col_build.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            query_params['chart_data_model'].is_subtotal_query = False
            query_params['chart_data_model'].is_subtotal_col_query = True
            query_params['chart_data_model'].origin_select_fields = self._origin_select_fields
            result = QueryAgent.query(**query_params)
            query_params['chart_data_model'].origin_select_fields = None
            query_params['chart_data_model'].is_subtotal_col_query = False
        logger.debug('列小计二次取数： %s' % result)

        self._judge_data_result(result)

        self.deal_same_ring_calc_result(data, result, contain_keys)

        # 若有对比维度 则行小计字段需要单独求取
        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.single_col_compare_process.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            subtotal_row_data = self._get_subtotal_row_data(
                query_agent,
                [*subtotal_row_locate_fields, *subtotal_row_fields],
                subtotal_row_group_fields,
                where_fields,
                result.get("data"),
            )
        header_data['data_index_headers'] = index_headers
        subtotal_struct_obj = SubtotalStruct(
            subtotal_col_data=result.get("data"),
            subtotal_row_data=subtotal_row_data,
            header_data=header_data,
            chart_data_model=self._chart_data_model,
            subtotal_cate_fields=subtotal_cate_fields,
            next_item=next_item,
            prev_item=prev_item,
        )
        subtotal_struct_obj_data = subtotal_struct_obj.build()
        AnalysisTimeUtils.record(
            step=AnalysisTimeUtils.step_type.single_subtotal_col_process.value, sql=None, db_type=None,
            start_time=_start_time_, extra={}, need_type_inference=False
        )
        return subtotal_struct_obj_data

    def deal_same_ring_calc_result(self, data: pd.DataFrame, query_result, contain_keys):
        # 处理同环比计算的列小计值
        # contain_keys group key ['SF_8656113452', 'day_RQ_8486901298']
        query_data = query_result.get('data', [])

        # 1. 先找到同环比计算的列
        nums = self._get_same_ring_nums()
        if not nums:
            return

        data_same_ring_col_names = {
            num['col_name']: f"{num['formula_mode']}_{num['col_name']}"
            for num in nums.values()
        } # same_ratio_MBRGTS_15764154403
        result_same_ring_col_names = {
            num['col_name']: f"{num['subtotal_col_formula_mode']}_{num['col_name']}"
            for num in nums.values()
        } # sum_MBRGTS_15764154403

        # 2. 根据contain_keys进行同环比值聚合计算
        for line in query_data:
            for col_name, data_same_ring_col_name in data_same_ring_col_names.items():
                qs = ' and '.join(['%s==%r' % (key, line[key]) for key in contain_keys])
                value = self._same_ring_col_calc(
                    data.query(qs)[data_same_ring_col_name], nums[col_name]
                )
                line[result_same_ring_col_names[col_name]] = convert_to_py_data_type(value)

    def _same_ring_col_calc(self, col_pd: pd.DataFrame, num):
        # auto avg count distinct expression max min ratio sum
        subtotal_col_formula_mode = num['subtotal_col_formula_mode']
        if subtotal_col_formula_mode in ('max', 'min', 'count', 'sum'):
            return getattr(col_pd, subtotal_col_formula_mode)()
        elif subtotal_col_formula_mode == 'distinct': # 去重计数
            return col_pd.drop_duplicates().count()
        elif subtotal_col_formula_mode == 'avg': # 平均值
            return col_pd.mean()
        else: # expression / ratio
            return None

    def _serial_query_subtotal_col_data(
        self, data: pd.DataFrame, next_item: pd.DataFrame, prev_item: pd.DataFrame, query_agent: QueryAgent, **kwargs
    ):
        subtotal_col_rows = []
        subtotal_cate_groups = self.get_subtotal_groups_v2()
        for group_dims in subtotal_cate_groups:
            new_subtotal_col_rows = self._single_query_subtotal_col_data(
                data, next_item, prev_item, query_agent, group_dims, **kwargs
            )
            subtotal_col_rows = [*subtotal_col_rows, *new_subtotal_col_rows]
        return subtotal_col_rows

    def _query_subtotal_col_data(
        self, data: pd.DataFrame, next_item: pd.DataFrame, prev_item: pd.DataFrame,
        query_agent: QueryAgent, **kwargs
    ):
        method = FUNCTION_SUBTOTAL_QUERY_METHOD
        start_datetime = datetime.now()
        if method == 'serial':
            rv = self._serial_query_subtotal_col_data(data, next_item, prev_item, query_agent, **kwargs)
        else:
            rv = self._parallel_query_subtotal_col_data(data, next_item, prev_item, query_agent, **kwargs)
        end_datetime = datetime.now()
        # pylint: disable=W1203
        logger.debug(
            f"报告<{self._chart_data_model.dashboard_id}>, "
            f"取小计方式: {method}, 开始时间: {start_datetime}, 结束时间: {end_datetime}, "
            f"用时: {(end_datetime - start_datetime).total_seconds()}"
        )

        return rv

    def _judge_data_result(self, result: dict):
        if result and result.get("code") != 200:
            raise ChartQueryExceptionError(message=trans_msg("单图取数异常: {}").format(result.get('msg')))

    def _insert_special_group_fields(self, group_fields):
        for idx1, field in enumerate(self.get_special_order_for_group_fields()):
            group_fields.insert(idx1, field)

    def _get_subtotal_row_summary_data(self, query_agent, subtotal_row_fields: List, subtotal_col_summary_data: List):
        """
        获取行总计数据
        有对比维度时单独获取
        无对比维度时从原始数据中获取
        :param query_agent:
        :param subtotal_row_fields:
        :param subtotal_col_summary_data:
        :return:
        """
        subtotal_row_summary_data = []
        if not self._chart_data_model.enable_subtotal_row or not subtotal_row_fields:
            return subtotal_row_summary_data
        if self._chart_data_model.comparisons:
            subtotal_row_summary_json_struct = query_agent.convert(
                self.get_query_agent_convert_params({"select": subtotal_row_fields, "where": self._origin_where_fields})
            )
            query_params = {
                "user_id": self._user_id,
                "dataset_id": self._chart_data_model.dataset_id,
                "chart_id": self._chart_data_model.id,
                "json_struct": subtotal_row_summary_json_struct,
                "external_subject_ids": self._chart_data_model.external_subject_ids,
                "chart_data_model": copy.copy(self._chart_data_model)
            }
            query_params['chart_data_model'].is_subtotal_col_query = False
            query_params['chart_data_model'].is_subtotal_query = True
            query_params['chart_data_model'].origin_select_fields = self._origin_select_fields
            subtotal_row_summary_result = QueryAgent.query(**query_params)
            query_params['chart_data_model'] = None
            self._judge_data_result(subtotal_row_summary_result)
            subtotal_row_summary_data = subtotal_row_summary_result.get('data')
        else:
            subtotal_field_keys = [
                subtotal_row_field.alias or subtotal_row_field.field for subtotal_row_field in subtotal_row_fields
            ]
            for each in subtotal_col_summary_data:
                item = {}
                for subtotal_field_key in subtotal_field_keys:
                    item[subtotal_field_key] = each[subtotal_field_key]
                subtotal_row_summary_data.append(item)
        return subtotal_row_summary_data

    def _get_subtotal_row_data(
        self,
        query_agent,
        subtotal_row_fields: List,
        subtotal_row_group_fields: List,
        where_fields: List,
        subtotal_col_data: List,
    ):
        """
        获取列小计的行小计 数据
        有对比维度时单独获取
        无对比维度时从原始数据获取
        :param query_agent:
        :param subtotal_row_fields:
        :param subtotal_row_group_fields:
        :param where_fields:
        :param subtotal_col_data:
        :return:
        """
        subtotal_row_data = []
        if not self._chart_data_model.enable_subtotal_row or not subtotal_row_fields:
            return subtotal_row_data
        if self._chart_data_model.comparisons:
            subtotal_row_json_struct = query_agent.convert(
                self.get_query_agent_convert_params(
                    {"select": subtotal_row_fields, "group": subtotal_row_group_fields, "where": where_fields}
                )
            )
            query_params = {
                "user_id": self._user_id,
                "dataset_id": self._chart_data_model.dataset_id,
                "chart_id": self._chart_data_model.id,
                "json_struct": subtotal_row_json_struct,
                "external_subject_ids": self._chart_data_model.external_subject_ids,
            }
            subtotal_row_result = QueryAgent.query(**query_params)
            self._judge_data_result(subtotal_row_result)
            subtotal_row_data = subtotal_row_result.get('data')
        else:
            subtotal_field_keys = [
                subtotal_row_field.alias or subtotal_row_field.field for subtotal_row_field in subtotal_row_fields
            ]
            for subtotal_row_field in subtotal_row_fields:
                key = subtotal_row_field.alias or subtotal_row_field.field
                subtotal_field_keys.append(key)
            for each in subtotal_col_data:
                item = {}
                for subtotal_field_key in subtotal_field_keys:
                    item[subtotal_field_key] = each[subtotal_field_key]
                subtotal_row_data.append(item)
        return subtotal_row_data

    def _get_num_field_dict(self):
        if not hasattr(self, '_data_num_field_dict'):
            num_select_fields = self._num_select_obj.get_select_fields(self._chart_data_model)
            self._data_num_field_dict = {
                num_select_field.alias or num_select_field.field: num_select_field.field_ref
                for num_select_field in num_select_fields
            }
        return self._data_num_field_dict

    def _get_dim_headers(self):
        if not hasattr(self, '_dim_headers'):
            dim_fields = self._dim_select_obj.get_select_fields(self._chart_data_model)
            self._dim_headers = [tuple([dim_field.alias or dim_field.field]) for dim_field in dim_fields]
        return self._dim_headers

    def _is_dim_header(self, col_header: Tuple) -> bool:
        return col_header in self._get_dim_headers()

    def _convert_data_num2subtotal_header(self, data_num_header) -> List[SubtotalHeaderModel]:
        """
        将data num 的头 转换为 小计的 头
        :param data_num_header:
        :return:
        """
        subtotal_num_fieldid_dict = self._get_subtotal_num_fieldid_dict()
        subtotal_header = []
        for item in data_num_header:
            subtotal_header_item = copy.deepcopy(item)
            # 小计头和数据头不一样的地方只有num字段，对比维度是一致的，无需转换
            if subtotal_header_item.col_type == ColTypes.Num.value:
                subtotal_num_field = subtotal_num_fieldid_dict.get(item.field_id)
                if subtotal_num_field:
                    subtotal_header_item.alias_name = subtotal_num_field.alias or subtotal_num_field.field
            subtotal_header.append(subtotal_header_item)
        return subtotal_header

    def _get_data_index_headers(self, data: pd.DataFrame) -> List:
        headers = []
        for index_name in data.index.names:
            header = tuple(index_name) if not isinstance(index_name, tuple) else index_name
            headers.append([header])
        return headers

    def _get_header_from_multi(self, col_header, col_names, dataset_alias_dict) -> List:
        multi_header = []
        for idx, header in enumerate(col_header):
            col_value = None
            if col_names[idx] is not None:
                col_name = col_names[idx]
                col_value = header
            else:
                col_name = header
            field = copy.deepcopy(dataset_alias_dict.get(col_name))
            field.col_value = WidgetUtil.convert_to_py_data_type(col_value)
            if not field:
                raise UserError(message=trans_msg("展示名为 {alias} 的字段不存在!".format(alias=col_name)))
            multi_header.append(field)
        return multi_header

    def _sort_multi_header(self, multi_header: List) -> List:
        # 如果有对比维度且对比维度前置 则需要对表头顺序进行排序 对比维度放在前面其他放在后面
        if self._chart_data_model.pre_comparison and self._chart_data_model.comparisons:
            multi_header = sorted(
                multi_header, key=lambda item: int(item.col_type == ColTypes.Comparison.value), reverse=True
            )
        return multi_header

    def _get_header_from_simple(self, col_header: str, dataset_alias_dict: dict) -> List:
        header = []
        field = copy.deepcopy(dataset_alias_dict.get(col_header))
        if not field:
            raise UserError(message=trans_msg("展示名为 {alias} 的字段不存在!".format(alias=col_header)))
        header.append(field)
        return header

    def _get_data_num_headers(self, data: pd.DataFrame) -> List:
        data_num_headers = []
        col_headers = data.columns.values
        col_names = data.columns.names
        dataset_alias_dict = self.get_dataset_alias_dict()
        subtotal_row_names = [field.alias or field.field for field in self.get_subtotal_row_fields()]
        for col_header in col_headers:
            if isinstance(col_header, (list, tuple)):
                data_num_headers.append(self._get_header_from_multi(col_header, col_names, dataset_alias_dict))
            else:
                # 小计字段排除在外
                if col_header in subtotal_row_names:
                    continue
                header = self._get_header_from_simple(col_header, dataset_alias_dict)
                # 维度排除
                if len(header) == 1 and header[0].col_type == ColTypes.Dim.value:
                    continue
                data_num_headers.append(self._get_header_from_simple(col_header, dataset_alias_dict))
        return data_num_headers

    def get_dataset_alias_dict(self) -> dict:
        """
        :return:
        """
        select_fields = self._origin_select_fields_headers
        dataset_alias_dict = {}
        for select_field in select_fields:
            dict_key = select_field.alias or select_field.field
            field_id = select_field.field_ref
            # 解决自助分析flag字段没有数据集字段id问题
            if field_id is None:
                continue
            display_name = select_field.field
            field_info = self._chart_data_model.dataset_field_dict.get(field_id)
            if field_info:
                display_name = self._chart_alias_obj.get_alias(field_id, select_field.logic_source) or field_info.get(
                    "alias_name"
                )
            dataset_alias_dict[dict_key] = SubtotalHeaderModel(
                **{
                    "col_name": field_info.get("col_name"),
                    "alias_name": select_field.alias,
                    "alias": display_name,
                    "dataset_id": self._chart_data_model.dataset.get("id"),
                    "field_id": field_id,
                    "col_type": self.get_select_field_col_type(select_field.alias or select_field.field),
                }
            )
        return dataset_alias_dict

    def get_select_field_col_type(self, field_alias: str):
        col_type_dict = self._get_select_fields_col_type_dict()
        col_type = col_type_dict.get(field_alias)
        return col_type

    def _get_select_fields_col_type_dict(self) -> Dict:
        if not hasattr(self, '_select_fields_col_type_dict'):
            self._select_fields_col_type_dict = ConvertorUtils.get_alias_logic_source_dict(self._origin_select_fields)
        return self._select_fields_col_type_dict

    def _get_data_num2subtotal_header_dict(self, data: pd.DataFrame):
        header_dict = {}
        if not len(data):
            return header_dict
        col_headers = self._get_data_num_headers(data)
        if not col_headers:
            return header_dict
        for col_header in col_headers:
            # 替换为小计头
            subtotal_header = self._convert_data_num2subtotal_header(col_header)
            header_dict[self._get_header_dict_key(col_header)] = subtotal_header
        return header_dict

    def mock_header_data(self, origin_select_fields):
        # nums_selects = [select for select in origin_select_fields if select.logic_source == ColTypes.Num.value]
        nums_selects = origin_select_fields
        mock_data_alias = [select.alias for select in nums_selects]
        length = len(mock_data_alias)
        mock_data = pd.DataFrame([[0 for _ in range(length)] for _ in range(length)])
        mock_data.columns = mock_data_alias
        return mock_data

    def _get_header_dict_key(self, col_header: List[SubtotalHeaderModel], has_levels=1):
        if has_levels:
            return tuple([header_model.col_value or header_model.alias_name for header_model in col_header])
        return col_header[0].alias_name

    def _convert_origin_data_header2model(self, origin_data_header) -> List:
        header = []
        origin_data_header = tuple([origin_data_header]) if isinstance(origin_data_header, str) else origin_data_header
        for item in origin_data_header:
            header.append(item)
        return self._sort_multi_header(header)

    def get_special_order_for_group_fields(self) -> list:
        """
        获取特殊排序的gruop by 字段
        组外排序需要将排序的第一个字段放入到group by 的第一个字段
        :return:
        """
        return []

    def get_special_order_for_group_dims(self) -> list:
        """
        获取特殊排序的gruop by 字段
        组外排序需要将排序的第一个字段放入到group by 的第一个字段
        :return:
        """
        return []

    @staticmethod
    def _get_first_item(loopitems):
        return loopitems[0] if loopitems else []

    def _compare_range_index(self, df1: pd.DataFrame, df2: pd.DataFrame, keys: List) -> bool:
        for key in keys:
            if df1[key].values[0] != df2[key].values[0]:
                return False
        return True

    def _compare_index(self, df1: pd.DataFrame, df2: pd.DataFrame, keys: List) -> bool:
        index_dict = {name: idx for idx, name in enumerate(df1.index.names)}
        for key in keys:
            idx = index_dict.get(key)
            val1 = df1.index[0][idx] if isinstance(df1.index, pd.MultiIndex) else df1.index[idx]
            val2 = df2.index[0][idx] if isinstance(df2.index, pd.MultiIndex) else df2.index[idx]
            if val1 != val2:
                return False
        return True

    def get_query_agent_convert_params(self, params: dict) -> dict:
        params["vars"] = self._get_subtotal_query_vars()
        params["dataset_field_dict"] = self._chart_data_model.dataset_field_dict
        params["external_subject_ids"] = self._chart_data_model.external_subject_ids
        return params

    def _assign_where_fields_dict(self, where_fields_dict: dict, group_dim: dict, current_where_field_values: dict):
        where_value = current_where_field_values.get(group_dim.get("data_key"))
        if where_value:
            is_null = False
            contions = []
            for val in where_value:
                if pd.isna(val) or val is None:
                    is_null = True
                    continue
                contions.append(val)
            # 若contions中无值，跳出
            if not contions:
                return
            where_field = self._dim2where(group_dim, SqlWhereOperator.In.value, contions)
            if is_null:
                where_field.complex_logic = 'OR'
                where_field.complex = [self._dim2where(group_dim, SqlWhereOperator.IsNull.value, None)]
            where_fields_dict[group_dim.get("data_key")] = where_field

    def _dim2where(self, field, operator, value):
        return Where.field2where(field, operator, value, self._chart_data_model)

    def _get_where_values(self, data: pd.DataFrame, group_dim: dict, current_where_field_values: dict) -> dict:
        data_key = group_dim.get('data_key')
        if not data_key:
            return current_where_field_values
        if self._chart_data_model.comparisons:
            self._get_comparison_where_field_values(current_where_field_values, data, data_key)
        else:
            self._get_normal_where_field_values(current_where_field_values, data, data_key, group_dim)
        return current_where_field_values

    def _get_normal_where_field_values(self, current_where_field_values, data, data_key, group_dim):
        if not len(data):
            return
        values = data[data_key]
        if values is not None:
            for val in values:
                current_where_field_values[group_dim.get("data_key")].add(val)

    def _get_comparison_where_field_values(self, current_where_field_values, data, data_key):
        index2level = {name: level for level, name in enumerate(list(data.index.names))}
        if index2level.get(data_key) is not None:
            if isinstance(data.index, pd.MultiIndex):
                values = [item[index2level[data_key]] for item in data.index.values.tolist()]
            else:
                values = list(data.index)
            for val in values:
                current_where_field_values[data_key].add(val)

    def _dim2group(self, dim):
        dim_group = DimGroup()
        _, group_field = dim_group.dim2group(dim, self._chart_data_model.dataset_field_dict, dim.get("formula_mode"))
        return group_field

    def _dim2select(self, dim):
        dim_select = DimSelect()
        _, select_field = dim_select.dim2select(dim, self._chart_data_model.dataset_field_dict)
        return select_field

    def get_subtotal_row_fields(self) -> list:
        """
        获取小计行字段用于获取对应的列小计的行小计部分
        对于列小计来说 行小计都是汇总类型
        :return:
        """
        if not hasattr(self, '_subtotal_row_fields'):
            subtotal_row_select = SubtotalRowSelectForAgg()
            self._subtotal_row_fields = subtotal_row_select.get_select_fields(self._chart_data_model)
        return self._subtotal_row_fields

    def get_subtotal_row_headers(self) -> List[SubtotalHeaderModel]:
        row_fields = self.get_subtotal_row_fields()
        headers = []
        for row_field in row_fields:
            field_id = row_field.field_ref
            field_info = self._chart_data_model.dataset_field_dict.get(field_id)
            display_name = ''
            if field_info:
                display_name = self._chart_alias_obj.get_alias(field_id, row_field.logic_source) or field_info.get(
                    "alias_name"
                )
            alias_name = row_field.alias or row_field.field
            headers.append(
                [
                    SubtotalHeaderModel(
                        **{
                            "col_name": field_info.get("col_name"),
                            "alias_name": alias_name,
                            "alias": display_name,
                            "dataset_id": self._chart_data_model.dataset.get("id"),
                            "field_id": field_id,
                            "col_type": self.get_select_field_col_type(alias_name),
                        }
                    )
                ]
            )
        return headers

    def get_subtotal_base_select_fields(self):
        # 1. 获取num对应的字段
        base_fields = self._assign_num_to_base_fields([])
        # 2. 获取对比维度对应的字段
        base_fields.extend(self._get_comparison_fields())
        return base_fields

    def get_subtotal_base_num_fields(self):
        return self._assign_num_to_base_fields([])

    def get_subtotal_base_comparison_field(self):
        return self._get_comparison_fields()

    def get_subtotal_base_group_fields(self):
        """
        获取小计基础group字段
        在有对比维度时 需要将对比维度加入到gruop by 中
        :return:
        """
        fields = []
        for field in self._get_comparison_fields():
            _temp = copy.deepcopy(field)
            _temp.alias = ''
            fields.append(_temp)
        return fields

    def get_subtotal_base_group_dims(self):
        return self._chart_data_model.comparisons

    def _get_comparison_fields(self):
        """
        获取对比维度group 字段
        :return:
        """
        fields = []
        if not self._chart_data_model.comparisons:
            return fields
        for comparison in self._chart_data_model.comparisons:
            fields.append(
                WidgetUtil.comparison_to_field(
                    comparison_select_obj=self._comparison_select_obj,
                    field_id=comparison.get("dataset_field_id"),
                    dataset_field_dict=self._chart_data_model.dataset_field_dict,
                    formula_mode=comparison.get("formula_mode"),
                )
            )
        return fields

    def get_subtotal_base_select_fields_dict(self):
        if not hasattr(self, '_subtotal_base_select_fields_dict'):
            self._subtotal_base_select_fields_dict = {}
            for field in self.get_subtotal_base_select_fields():
                self._subtotal_base_select_fields_dict[field.ext_data.get('col_name')] = field.ext_data
        return self._subtotal_base_select_fields_dict

    @property
    def _get_subtotal_nums(self):
        subtotal_nums = []
        # TODO mark 小计转换
        for num in self._chart_data_model.nums:
            if num.get("subtotal_col_formula_mode"):
                subtotal_num = copy.deepcopy(num)
                # auto 类型代表使用字段自身计算方法
                subtotal_num["formula_mode"] = (
                    subtotal_num.get("subtotal_col_formula_mode")
                    if subtotal_num.get("subtotal_col_formula_mode") != ChartNumSubtotalFormulaMode.Auto.value
                    else ''
                )
                subtotal_nums.append(subtotal_num)
        return subtotal_nums

    def _assign_num_to_base_fields(self, base_select_fields: list):
        base_select_fields.extend(self._get_subtotal_num_fields())
        return base_select_fields

    def _get_subtotal_num_fieldid_dict(self):
        subtotal_num_fields = self._get_subtotal_num_fields()
        fieldid_dict = {subtotal_num_field.field_ref: subtotal_num_field for subtotal_num_field in subtotal_num_fields}
        return fieldid_dict

    def _get_subtotal_num_fields(self):
        if not hasattr(self, '_subtotal_num_fields'):
            subtotal_num_fields = []
            subtotal_nums = self._get_subtotal_nums
            for subtotal_num in subtotal_nums:
                # TODO 是否需要改成一致
                original_num_field = self._num2select(subtotal_num)
                subtotal_num_field = self._num2select(subtotal_num)
                self._assign_field_extdata(
                    subtotal_num_field,
                    FieldExtDataModel(
                        **{
                            "detail_col_name": self._get_data_col_name(original_num_field),
                            "dataset_id": subtotal_num.get("dataset_id"),
                            "col_name": self._get_data_col_name(subtotal_num_field),
                            "dataset_field_id": subtotal_num.get("dataset_field_id"),
                        }
                    ),
                )
                subtotal_num_fields.append(subtotal_num_field)
            self._subtotal_num_fields = subtotal_num_fields
        return self._subtotal_num_fields

    @staticmethod
    def _get_data_col_name(field):
        return field.alias or field.field

    @staticmethod
    def _assign_field_extdata(field, ext_data_model: FieldExtDataModel):
        field.ext_data = ext_data_model.get_dict()

    def _num2select(self, num):
        num_select = NumSelect()
        _, select_field = num_select.num2select(num, self._chart_data_model.dataset_field_dict)
        return select_field
