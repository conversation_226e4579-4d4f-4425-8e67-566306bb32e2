#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/7/9 11:32
# <AUTHOR> caoxl
# @File     : dataset_query.py
from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.convertor.group.group import Group
from dashboard_chart.convertor.select.select import Select
from dashboard_chart.data_query import utils as data_query_utils
from dashboard_chart.convertor.query_var.query_var import QueryVar


class DatasetQuery:
    def __init__(self, chart_data_model, user_id):
        self._chart_data_model = chart_data_model
        self._user_id = user_id

    def get_item_list(self, limt_fields: list):
        """
        获取
        :return:
        """
        # 1. 查询数据
        # 如果只有字段数据而没有单图信息，则获取select等数据从dataset_field信息中获取
        struct = {
            "select": Select.get_select_fields_for_dataset_field(self._chart_data_model),
            "group": Group.get_group_fields_for_dataset_field(self._chart_data_model),
            "limit": limt_fields,
            "vars": QueryVar.get_dataset_vars(self._chart_data_model),
            "dataset_field_dict": self._chart_data_model.dataset_field_dict,
            "external_subject_ids": self._chart_data_model.external_subject_ids,
        }
        query_agent = QueryAgent()
        json_struct = query_agent.convert(struct)
        query_params = {
            "user_id": self._user_id,
            "dataset_id": self._chart_data_model.dataset_id,
            "chart_id": self._chart_data_model.id,
            "json_struct": json_struct,
            "external_subject_ids": self._chart_data_model.external_subject_ids,
            "chart_data_model": self._chart_data_model
        }
        result = QueryAgent.query(**query_params)

        # 2. 设置data数据
        data = {
            "data": result.get("data"),
            "msg": data_query_utils.get_data_msg(result),
            "msg_code": data_query_utils.get_msg_code(result),
            "execute_status": result.get("code"),
            "sql": result.get("sql"),
            "sql_execute_time": result.get("sql_execute_time"),
        }
        return data
