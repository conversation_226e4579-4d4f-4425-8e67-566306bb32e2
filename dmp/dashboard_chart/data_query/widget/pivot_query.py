#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/7/22 17:11
# <AUTHOR> caoxl (xx)
# @File     : pivot_query.py
from abc import ABCMeta, abstractmethod
from typing import List, Dict, <PERSON>ple

import copy
import pandas as pd

from dashboard_chart.convertor.field_types import Order<PERSON>ustom<PERSON>ield
from dashboard_chart.models import FieldSortModel
from base.enums import <PERSON>ck<PERSON>ield, ColTypes, OrderType, ChartDesireMode
from dashboard_chart.convertor.select.comparison_select import ComparisonSelect
from dashboard_chart.convertor.select.dim_select import DimSelect
from dashboard_chart.convertor.select.num_select import NumSelect
from dashboard_chart.convertor.select.desire_select import DesireSelect
from dashboard_chart.data_query.widget import utils as WidgetUtil
from dashboard_chart.data_query import utils as DataQueryUtil


class PivotQuery:
    def __init__(self, chart_data_model):
        self._chart_data_model = chart_data_model
        self._dim_select_obj = DimSelect()
        self._num_select_obj = NumSelect()
        self._desire_select_obj = DesireSelect()
        self._comparison_select_obj = ComparisonSelect()

    def pivot(self, data: List[Dict], order_fields: list = None) -> pd.DataFrame:
        """
        进行透视操作
        :param data:
        :return:
        """
        df = self.pivot_data(
            **{
                "dim_select_obj": self._dim_select_obj,
                "dims": self._chart_data_model.dims,
                "num_select_obj": self._num_select_obj,
                "nums": self._chart_data_model.nums,
                "comparison_select_obj": self._comparison_select_obj,
                "comparisons": self._chart_data_model.comparisons,
                "dataset_field_dict": self._chart_data_model.dataset_field_dict,
                "data": data,
                "order_fields": order_fields,
                "desire_select_obj": self._desire_select_obj,
                "desires": self._chart_data_model.desires,
            }
        )
        return df

    def pivot_data(self, **kwargs) -> pd.DataFrame:
        """
        透视操作
        :param kwargs:
        dim_select_obj： DimSelect
        dims： List
        num_select_obj： NumSelect
        nums: List
        omparison_select_obj: ComparisonSelect
        comparisons: List
        dataset_field_dict: Dict
        data: List[Dict]
        :return:
        """
        data = kwargs.get('data')
        columns, column_field_sort_data, indexes, index_field_sort_data, levels = self.compute_pivot_data_v2(kwargs)
        df = pd.DataFrame(data, columns=columns)
        df.set_index(indexes, inplace=True)
        # 行转列操作
        df = df.unstack(level=levels)
        # 对索引进行排序及对比维度 （pandas若不加排序，则自动按照升序进行排列）
        index_group_sort = IndexGroupSort(df, index_field_sort_data)
        df = index_group_sort.sort()
        if not self._chart_data_model.pre_comparison and self._chart_data_model.comparisons:
            # 当对比维度后置，模拟度量列为一个自定义排序字段
            num_field_sort = FieldSortModel(items=columns[len(indexes):], sort='')
            column_field_sort_data.insert(0, (num_field_sort, 0))
        column_group_sort = ColumnGroupSort(df, column_field_sort_data)
        df = column_group_sort.sort()
        return df

    def compute_pivot_data_v1(self, kwargs):
        data = kwargs.get("data")
        dim_select_obj = kwargs.get('dim_select_obj')
        dims = kwargs.get('dims')
        num_select_obj = kwargs.get('num_select_obj')
        nums = kwargs.get('nums')
        omparison_select_obj = kwargs.get('comparison_select_obj')
        comparisons = kwargs.get('comparisons')
        dataset_field_dict = kwargs.get('dataset_field_dict')
        order_fields = kwargs.get('order_fields', [])
        desires = kwargs.get("desires", [])
        desire_select_obj = kwargs.get("desire_select_obj")
        order_fields_with_cate = DataQueryUtil.get_order_fields_with_cate(order_fields)
        indexes = []
        column_level_sorts = []
        mock_dim = None
        # 对于没有dim 透视会将其转换为series 不符合统一规范数据
        # 此处处理没有dim的数据 模拟一行dim实现
        if not dims:
            mock_dim = MockField.Dim.value
            indexes.append(mock_dim)
            for each in data:
                each[mock_dim] = mock_dim
        index_field_sort_data = []
        for idx, dim in enumerate(dims):
            dim_field = WidgetUtil.dim_to_field(
                dim_select_obj, dim.get("dataset_field_id"), dataset_field_dict, dim.get("formula_mode")
            )
            if dim.get("dim") in order_fields_with_cate[ColTypes.Dim.value]:
                sort_field = order_fields_with_cate[ColTypes.Dim.value][dim.get("dim")]
                index_field_sort_data.append((self._get_dim_field_sort_model(dim, dim_field, sort_field), idx))
            indexes.append(dim_field.alias or dim.field)
        levels = []
        index_len = len(indexes)
        column_field_sort_data = []
        for idx, comparison in enumerate(comparisons):
            comparison_field = WidgetUtil.comparison_to_field(
                omparison_select_obj,
                comparison.get("dataset_field_id"),
                dataset_field_dict,
                comparison.get("formula_mode", ""),
            )
            column_level_sorts.append(idx + 1)
            field_sort_model = PivotQuery._get_comparison_field_sort_model(
                comparison, comparison_field, order_fields_with_cate
            )
            column_field_sort_data.append((field_sort_model, idx + 1))
            indexes.append(comparison_field.alias)
            levels.append(index_len + idx)
        columns = copy.deepcopy(indexes)
        for num in nums:
            num_field = WidgetUtil.num_to_field(
                num_select_obj, num.get("dataset_field_id"), dataset_field_dict, num.get("formula_mode")
            )
            columns.append(num_field.alias)
        for desire in desires:
            if desire.get("mode") == ChartDesireMode.CalculatorValue.value:
                desire_field = WidgetUtil.desire_to_field(
                    desire_select_obj, desire.get("dataset_field_id"), dataset_field_dict, desire.get("formula_mode")
                )
                columns.append(desire_field.alias)
        return columns, column_field_sort_data, indexes, index_field_sort_data, levels

    def compute_pivot_data_v2(self, kwargs):
        data = kwargs.get("data")
        dim_select_obj = kwargs.get('dim_select_obj')
        dims = kwargs.get('dims')
        num_select_obj = kwargs.get('num_select_obj')
        nums = kwargs.get('nums')
        omparison_select_obj = kwargs.get('comparison_select_obj')
        comparisons = kwargs.get('comparisons')
        dataset_field_dict = kwargs.get('dataset_field_dict')
        order_fields = kwargs.get('order_fields', [])
        desires = kwargs.get("desires", [])
        desire_select_obj = kwargs.get("desire_select_obj")
        indexes = []
        column_level_sorts = []
        mock_dim = None
        # 对于没有dim 透视会将其转换为series 不符合统一规范数据
        # 此处处理没有dim的数据 模拟一行dim实现
        if not dims:
            mock_dim = MockField.Dim.value
            indexes.append(mock_dim)
            for each in data:
                each[mock_dim] = mock_dim
        dim_map, comparison_map = {}, {}
        for idx, dim in enumerate(dims):
            dim_field = WidgetUtil.dim_to_field(
                dim_select_obj, dim.get("dataset_field_id"), dataset_field_dict, dim.get("formula_mode")
            )
            dim_map[dim.get("dim")] = (dim, dim_field, idx)
            indexes.append(dim_field.alias or dim.field)
        index_field_sort_data = self._get_index_field_sort_data(order_fields, dim_map)
        levels = []
        index_len = len(indexes)
        for idx, comparison in enumerate(comparisons):
            comparison_field = WidgetUtil.comparison_to_field(
                omparison_select_obj,
                comparison.get("dataset_field_id"),
                dataset_field_dict,
                comparison.get("formula_mode", ""),
            )
            column_level_sorts.append(idx + 1)
            comparison_map[comparison.get("dataset_field_id")] = (comparison, idx + 1)
            indexes.append(comparison_field.alias)
            levels.append(index_len + idx)
        column_field_sort_data = self._get_column_field_sort_data(order_fields, comparison_map)
        columns = copy.deepcopy(indexes)
        for num in nums:
            num_field = WidgetUtil.num_to_field(
                num_select_obj, num.get("dataset_field_id"), dataset_field_dict, num.get("formula_mode")
            )
            columns.append(num_field.alias)
        for desire in desires:
            if desire.get("mode") == ChartDesireMode.CalculatorValue.value:
                desire_field = WidgetUtil.desire_to_field(
                    desire_select_obj, desire.get("dataset_field_id"), dataset_field_dict, desire.get("formula_mode")
                )
                columns.append(desire_field.alias)
        return columns, column_field_sort_data, indexes, index_field_sort_data, levels

    def _get_index_field_sort_data(self, order_fields, dim_map):
        index_field_sort_data = []
        for order_field in order_fields:
            if order_field.logic_source != ColTypes.Dim.value:
                continue
            dim_data = dim_map.get(order_field.field_ref)
            if not dim_data:
                continue
            index_field_sort_data.append(
                (self._get_dim_field_sort_model(dim_data[0], dim_data[1], order_field), dim_data[2])
            )
        return index_field_sort_data

    def _get_column_field_sort_data(self, order_fields, comparison_map):
        column_field_sort_data = []
        for order_field in order_fields:
            if order_field.logic_source != ColTypes.Comparison.value:
                continue
            comparison_data = comparison_map.get(order_field.field_ref)
            if not comparison_data:
                continue
            field_sort_model = self._get_comparison_field_sort_model_v2(comparison_data[0], order_field)
            column_field_sort_data.append((field_sort_model, comparison_data[1]))
        return column_field_sort_data

    @staticmethod
    def _get_dim_field_sort_model(dim, dim_field, sort_field):
        dim_field_sort_model = FieldSortModel(
            **{
                'dataset_field_id': dim.get("dim"),
                'name': dim_field.alias or dim_field.field,
                'sort': sort_field.field_sort,
                'items': sort_field.items if isinstance(sort_field, OrderCustomField) else [],
            }
        )
        return dim_field_sort_model

    @staticmethod
    def _get_comparison_field_sort_model_v2(comparison, order_field):
        return FieldSortModel(
            **{
                'dataset_field_id': comparison.get("dataset_field_id"),
                'name': order_field.alias or order_field.field,
                'sort': order_field.field_sort,
                'items': order_field.items if isinstance(order_field, OrderCustomField) else [],
            }
        )

    @staticmethod
    def _get_comparison_field_sort_model(comparison, comparison_field, order_fields_with_cate):
        if comparison.get("dataset_field_id") in order_fields_with_cate[ColTypes.Comparison.value]:
            sort_field = order_fields_with_cate[ColTypes.Comparison.value][comparison.get("dataset_field_id")]
            field_sort_model = FieldSortModel(
                **{
                    'dataset_field_id': comparison.get("dataset_field_id"),
                    'name': sort_field.alias or sort_field.field,
                    'sort': sort_field.field_sort,
                    'items': sort_field.items if isinstance(sort_field, OrderCustomField) else [],
                }
            )
        else:
            field_sort_model = FieldSortModel(
                **{
                    'dataset_field_id': comparison.get("dataset_field_id"),
                    'name': comparison_field.alias or comparison_field.field,
                    'sort': OrderType.Asc.value,
                    'items': [],
                }
            )
        return field_sort_model


class FieldGroup(metaclass=ABCMeta):
    """
    字段分组
    分为两组： 1. 普通排序字段     2. 用户自定义排序字段
    """

    def __init__(self):
        self._fields = []
        self._sort_levels = []

    @abstractmethod
    def append(self, sort_field: FieldSortModel, level: int):
        pass

    def get_sort_levels(self):
        return self._sort_levels


class NormalFieldGroup(FieldGroup):
    def __init__(self):
        self._levels_ascending = []
        super().__init__()

    def get_levels_ascending(self):
        return self._levels_ascending

    def append(self, sort_field, level: int):
        self._fields.append(sort_field)
        self._sort_levels.append(level)
        self._levels_ascending.append(bool(sort_field.sort.upper() == OrderType.Asc.value))


class CustomerFieldGroup(FieldGroup):
    def __init__(self):
        self._levels_items = []
        super().__init__()

    def get_levels_items(self):
        return self._levels_items

    def append(self, sort_field, level: int):
        self._fields.append(sort_field)
        self._sort_levels.append(level)
        self._levels_items.append(sort_field.items)


class PivotSort(metaclass=ABCMeta):
    """
    透视排序
    """

    def __init__(self, data_frame: pd.DataFrame, order_fields_data: List[Tuple]):
        self._data_frame = data_frame
        self._groups = []
        self._level_sort_dict = {}
        self._sort_levels = []
        self._has_customer_sort = False
        for item in order_fields_data:
            self._append_field(item[0], item[1])

    def _append_field(self, sort_field: FieldSortModel, level: int):
        """
        添加字段
        :param order_field:
        :return:
        """
        if sort_field.sort.upper() in [e.value for e in OrderType.__members__.values()]:
            group_type = NormalFieldGroup
        else:
            group_type = CustomerFieldGroup
        if not self._groups or not isinstance(self._groups[len(self._groups) - 1], group_type):
            current_group = group_type()
            self._groups.append(current_group)
        else:
            current_group = self._groups[len(self._groups) - 1]
        if isinstance(current_group, CustomerFieldGroup):
            self._has_customer_sort = True
        if level not in self._sort_levels:
            self._sort_levels.append(level)
        current_group.append(sort_field, level)

    def __iter__(self):
        return iter(self._groups)

    def sort(self) -> pd.DataFrame:
        """
        排序
        有自定义排序时使用sort_has_customer_data进行排序
        无自定义排序时使用sort_no_customer_data进行排序
        :return:
        """
        if not self._groups:
            return self._data_frame
        if self._has_customer_sort:
            self.sort_has_customer_data()
        else:
            self.sort_no_customer_data()
        return self._data_frame

    @abstractmethod
    def sort_has_customer_data(self):
        """
        排序有包含自定义排序的数据
        :return:
        """

    @abstractmethod
    def sort_no_customer_data(self):
        """
        排序不包含自定义排序的数据
        :return:
        """

    def get_customer_level_sort_dict(self, group: CustomerFieldGroup):
        levels_items = group.get_levels_items()
        sort_levels = group.get_sort_levels()
        for idx, level_items in enumerate(levels_items):
            self._level_sort_dict[sort_levels[idx]] = dict(zip(level_items, range(0, len(level_items) - 1)))

    @abstractmethod
    def get_normal_level_sort_dict(self, group, sort_levels):
        pass


class IndexGroupSort(PivotSort):
    """
    index类型排序
    """

    def sort_has_customer_data(self):
        for group in self._groups:
            sort_levels = group.get_sort_levels()
            if isinstance(group, NormalFieldGroup):
                # 普通字段排序
                self.get_normal_level_sort_dict(group, sort_levels)
            elif isinstance(group, CustomerFieldGroup):
                # 自定义排序
                self.get_customer_level_sort_dict(group)
        _level_sort_dict = self._level_sort_dict
        _sort_levels = self._sort_levels

        def sort_index(index):
            sort_num = []
            for idx, sort_level in enumerate(_sort_levels):
                sort_num_dict = _level_sort_dict[sort_level]
                index_value = index[sort_level] if isinstance(index, tuple) else index
                index_value = WidgetUtil.convert_to_py_data_type(index_value)
                num = sort_num_dict[index_value] if index_value in sort_num_dict else len(sort_num_dict) + 1
                sort_num.append(num)
            return tuple(sort_num)

        self._data_frame = self._data_frame.reindex(sorted(self._data_frame.index, key=sort_index), axis=0)
        return self._data_frame

    def sort_no_customer_data(self):
        sort_levels = []
        levels_ascending = []
        for group in self._groups:
            sort_levels = [*sort_levels, *group.get_sort_levels()]
            levels_ascending = [*levels_ascending, *group.get_levels_ascending()]
        na_position = 'first' if sort_levels[0] else 'last'
        if isinstance(self._data_frame.index, pd.MultiIndex):
            # mutilindex 使用sort_index 排序
            self._data_frame.sort_index(
                axis=0, level=sort_levels, ascending=levels_ascending, inplace=True, na_position=na_position
            )
        else:
            # 普通 index 需要使用sort_values 进行排序
            bys = [self._data_frame.index.names[idx] for idx in sort_levels]
            self._data_frame.sort_values(
                axis=0, by=bys, ascending=levels_ascending, na_position=na_position, inplace=True
            )

    def get_normal_level_sort_dict(self, group, sort_levels):
        levels_ascending = group.get_levels_ascending()
        na_position = 'first' if sort_levels[0] else 'last'
        for idx, sort_level in enumerate(sort_levels):
            if isinstance(self._data_frame.index, pd.MultiIndex):
                # mutilindex 使用sort_index 排序
                sorted_df = self._data_frame.sort_index(
                    axis=0, level=sort_level, ascending=levels_ascending[idx], inplace=False, na_position=na_position
                )
            else:
                # 普通 index 需要使用sort_values 进行排序
                sorted_df = self._data_frame.sort_values(
                    axis=0,
                    by=self._data_frame.index.names[sort_level],
                    ascending=levels_ascending[idx],
                    na_position=na_position,
                    inplace=False,
                )
            sorted_df_level_values = [
                WidgetUtil.convert_to_py_data_type(val[sort_level]) for val in sorted_df.index.values
            ]
            self._level_sort_dict[sort_level] = dict(zip(sorted_df_level_values, range(0, len(sorted_df_level_values))))


class ColumnGroupSort(PivotSort):
    """
    column类型排序
    """

    def sort_has_customer_data(self):
        for group in self._groups:
            sort_levels = group.get_sort_levels()
            if isinstance(group, NormalFieldGroup):
                # 普通字段排序
                self.get_normal_level_sort_dict(group, sort_levels)
            elif isinstance(group, CustomerFieldGroup):
                # 自定义排序
                self.get_customer_level_sort_dict(group)
        _level_sort_dict = self._level_sort_dict
        _sort_levels = self._sort_levels

        def sort_column(column):
            sort_num = []
            for idx, sort_level in enumerate(_sort_levels):
                sort_num_dict = _level_sort_dict[sort_level]
                column_value = column[sort_level]
                num = sort_num_dict[column_value] if column_value in sort_num_dict else len(sort_num_dict) + 1
                sort_num.append(num)
            return tuple(sort_num)

        self._data_frame = self._data_frame.reindex(sorted(self._data_frame.columns, key=sort_column), axis=1)
        return self._data_frame

    def sort_no_customer_data(self):
        sort_levels = []
        levels_ascending = []
        for group in self._groups:
            sort_levels = [*sort_levels, *group.get_sort_levels()]
            levels_ascending = [*levels_ascending, *group.get_levels_ascending()]
        self._data_frame.sort_index(
            axis=1, level=sort_levels, ascending=levels_ascending, inplace=True, na_position='first'
        )

    def get_normal_level_sort_dict(self, group, sort_levels):
        levels_ascending = group.get_levels_ascending()
        na_position = 'first' if sort_levels[0] else 'last'
        for idx, sort_level in enumerate(sort_levels):
            # mutilindex 使用sort_index 排序
            sorted_df = self._data_frame.sort_index(
                axis=1, level=sort_level, ascending=levels_ascending[idx], inplace=False, na_position=na_position
            )
            sorted_df_level_values = [val[sort_level] for val in sorted_df.columns.values]
            self._level_sort_dict[sort_level] = dict(zip(sorted_df_level_values, range(0, len(sorted_df_level_values))))
