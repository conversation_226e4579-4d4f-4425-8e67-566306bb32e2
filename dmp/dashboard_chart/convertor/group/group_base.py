#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/14
"""
from abc import ABCMeta, abstractmethod
from dashboard_chart.convertor.field_types import <PERSON>Field
from dashboard_chart.convertor.field_base import FieldBase


class GroupBase(FieldBase):
    __metaclass__ = ABCMeta

    @abstractmethod
    def get_group_fields(self, model):
        """
        获取group数据模块
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        raise NotImplementedError("Please Implement this method")

    def field2base(self, field_id, dataset_field_dict, formula_mode, label_dataset_key=False):
        dataset_field = self.get_dataset_field(field_id, dataset_field_dict, formula_mode, label_dataset_key)
        return dataset_field, GroupField(**dataset_field)
