#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/14
"""
from base.dmp_constant import EXCEL_TABLE_QUERY_LIMIT, CHART_QUERY_DEFAULT_LIMIT
from dashboard_chart.convertor.field_types import LimitField
from dashboard_chart.convertor.limit.default_limit import DefaultLimit
from dashboard_chart.convertor.limit.page_limit import PageLimit
from dashboard_chart.models import ChartDataModel


class Limit:
    @staticmethod
    def get_limit_field(model: ChartDataModel):
        """
        获取limit数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        # 如果有对比维度则查询所有数据
        if model.comparisons:
            return {}
        if model.pagination.page:
            return Limit.get_limit_field_for_page(model)
        else:
            return Limit.get_limit_field_for_default(model)

    @staticmethod
    def get_limit_field_for_page(model: ChartDataModel):
        limit_model = PageLimit()
        return limit_model.get_limit_field(model)

    @staticmethod
    def get_limit_field_for_common(model: ChartDataModel):
        limit_model = PageLimit()
        return limit_model.get_common_limit_field(model)

    @staticmethod
    def get_limit_field_for_default(model: ChartDataModel):
        limit_model = DefaultLimit()
        return limit_model.get_limit_field(model)

    @staticmethod
    def get_limit_field_for_excel_table():
        """
        为透视表获取LimitField对象
        :return:
        """
        limit_field = LimitField()
        limit_field.limit = EXCEL_TABLE_QUERY_LIMIT
        return limit_field

    @staticmethod
    def get_limit_field_for_interval():
        """
        为区间获取LimitField对象
        :return:
        """
        limit_field = LimitField()
        limit_field.limit = CHART_QUERY_DEFAULT_LIMIT
        return limit_field
