#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/6 18:51
# <AUTHOR> caoxl
# @File     : subtotal_row_select.py
# pylint:disable=R0201
from abc import ABCMeta, abstractmethod
from dashboard_chart.convertor.select.select_base import SelectBase
from dashboard_chart.models import ChartDataModel
from dashboard_chart.convertor.select.num_select import NumSelect


class SubtotalRowSelect(SelectBase):
    __metaclass__ = ABCMeta

    def get_subtotal_row_select_for_agg(self):
        from ..select.agg.subtotal_row_select import SubtotalRowSelectForAgg

        return SubtotalRowSelectForAgg()

    def get_subtotal_row_select_for_noagg(self):
        from ..select.noagg.subtotal_row_select import SubtotalRowSelectForNoAgg

        return SubtotalRowSelectForNoAgg()

    def get_select_obj(self, model):
        select_obj = (
            self.get_subtotal_row_select_for_agg() if model.aggregation else self.get_subtotal_row_select_for_noagg()
        )
        return select_obj

    def get_select_fields(self, model):
        select_obj = self.get_select_obj(model)
        return [*select_obj.get_subtotal_select_fields(model), *select_obj.get_summary_select_fields(model)]

    @abstractmethod
    def get_summary_select_fields(self, model):
        raise NotImplementedError("Please Implement this method")

    @abstractmethod
    def get_subtotal_select_fields(self, model):
        raise NotImplementedError("Please Implement this method")

    def num2select(self, num: dict, model: ChartDataModel, alias_prefix: str) -> object:
        num_select = NumSelect()
        _, select_field = num_select.num2select(num, model.dataset_field_dict)
        select_field.alias = alias_prefix + '_' + select_field.field
        return select_field
