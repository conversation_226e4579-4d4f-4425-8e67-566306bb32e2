#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/6 18:55
# <AUTHOR> caoxl
# @File     : subtotal_row_select.py
import copy
from dashboard_chart.convertor.select.subtotal_row_select import SubtotalRowSelect
from base.enums import ChartNumSubtotalFormulaMode, SubtotalRowAliasPrefix, ColTypes


class SubtotalRowSelectForNoAgg(SubtotalRowSelect):
    def get_summary_select_fields(self, model):
        """
        获取小计汇总字段
        :param model:
        :return:
       """
        fields = []
        if not model.enable_subtotal_row_summary or not model.subtotal_row_summary_formula_mode:
            return fields
        # 小计行汇总 则必须将单图所有num获取到
        for num in model.nums:
            # 过滤掉隐藏字段
            if num.get("formula_mode") == ChartNumSubtotalFormulaMode.GroupConcat.value:
                continue
            summary_num = copy.deepcopy(num)
            alias_prefix = SubtotalRowAliasPrefix.Summary.value + '_' + model.subtotal_row_summary_formula_mode.lower()
            # 对于明细来说 行总计原始字段无计算方法 都为原始值
            summary_num.formula_mode = ''
            field = self.num2select(summary_num, model, alias_prefix)
            field.logic_source = ColTypes.Num.value
            fields.append(field)
        return fields

    def get_subtotal_select_fields(self, model):
        """
        获取小计字段
        :param model:
        :return:
        """
        fields = []
        if not model.enable_subtotal_row:
            return fields
        for num in model.nums:
            # 过滤掉隐藏字段
            if num.get("formula_mode") == ChartNumSubtotalFormulaMode.GroupConcat.value or num.get('visible') == 0:
                continue
            subtotal_row_formula_mode = num.get("subtotal_row_formula_mode")
            if subtotal_row_formula_mode == ChartNumSubtotalFormulaMode.No.value:
                continue
            subtotal_num = copy.deepcopy(num)
            subtotal_num['formula_mode'] = ''
            alias_prefix = SubtotalRowAliasPrefix.Row.value
            field = self.num2select(subtotal_num, model, alias_prefix)
            field.logic_source = ColTypes.Num.value
            fields.append(field)
        return fields
