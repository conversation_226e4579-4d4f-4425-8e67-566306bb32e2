#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/5 17:42
# <AUTHOR> caoxl
# @File     : num_select.py
from base.enums import DatasetFieldDataType, ColTypes
from dashboard_chart.convertor.select.num_select import NumSelect
from dashboard_chart.convertor import utils as ConvertorUtils


class NumSelectForAgg(NumSelect):
    def get_select_fields(self, model):
        """
        度量获取select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_fields = []
        for field in model.nums:
            dataset_field, select_field = self.num2select(
                field, model.dataset_field_dict, model.dataset.get(self.label_dataset_key)
            )
            select_field.logic_source = ColTypes.Num.value
            select_fields.append(select_field)
            # 地址字段返回7个细分字段
            if field.get("data_type") == DatasetFieldDataType.Address.value:
                address_fields = self.get_address_num_select_fields(
                    field, dataset_field, model.dataset.get(self.label_dataset_key)
                )
                select_fields.extend(ConvertorUtils.assign_logic_source(address_fields, ColTypes.Num.value))
        return select_fields
