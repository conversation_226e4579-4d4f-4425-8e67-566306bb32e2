#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
dashboard extra service
"""

# ---------------- 标准模块 ----------------
import time

# ---------------- 业务模块 ----------------
from base.enums import DashboardType
from dashboard_chart.repositories import dashboard_extra_repository, dashboard_released_design_repository
from base import repository

from datetime import datetime


from typing import Dict, List, Optional, Union

from user import external_user_service

def batch_get_dashboard_extra_data(
    dashboard_list: List[Dict[str, Union[str, None, int, datetime]]], application_type: Union[str, int]=0,
) -> List[Dict[str, Union[str, None, int, datetime]]]:
    """
    批量获取报告额外数据
    :param dashboard_list:
    :param application_type:
    :return:
    """
    if not dashboard_list:
        return dashboard_list
    query_dashboard_id_list = [
        i.get("id") for i in dashboard_list if i.get("id") and i.get("type") == DashboardType.File.value
    ]
    accounts = list({d['created_by'] for d in dashboard_list})
    query_dashboard_id_list = list({dashboard_id for dashboard_id in query_dashboard_id_list})
    account_name_map = get_account_name_map(accounts)
    query_extra_data = dashboard_extra_repository.batch_get_dashboard_extra_data(query_dashboard_id_list)
    dashboard_extra_dict = {i.get("dashboard_id"): i for i in query_extra_data} if query_extra_data else dict()
    for single_dashboard in dashboard_list:
        single_extra_info = dashboard_extra_dict.get(single_dashboard.get("id"))
        edit_on, released_on = None, None
        if single_extra_info:
            # 获取报告最新编辑时间
            edit_on = single_extra_info.get("edit_on") if single_extra_info.get("edit_on") else None
            # 获取报告发布时间
            released_on = single_extra_info.get("released_on") if single_extra_info.get("released_on") else None
        single_dashboard["edit_on"] = edit_on
        single_dashboard["released_on"] = released_on
        single_dashboard["edit_status"] = _get_dashboard_edit_status(edit_on, released_on)
        single_dashboard["user_name"] = (
            account_name_map[single_dashboard["created_by"]]
            if single_dashboard['created_by'] in account_name_map
            else single_dashboard['created_by']
        )
    if application_type in [1, '1']:
        subjects = repository.get_dict('dap_bi_dataset_external_subject', {}, ['id', 'name']) or {}
        datasets = repository.get_dict('dap_bi_dataset', {}, ['id', 'name']) or {}
        for single_dashboard in dashboard_list:
            main_external_subject_id = single_dashboard['main_external_subject_id']
            dataset_id = single_dashboard['dataset_id']
            single_dashboard['external_subject_name'] = subjects.get(main_external_subject_id, None) if main_external_subject_id else datasets.get(dataset_id, None)
            # 适配历史数据
            if main_external_subject_id:
                external_subject_ids = single_dashboard['external_subject_ids']
                if main_external_subject_id and not external_subject_ids:
                    external_subject_ids = main_external_subject_id
                single_dashboard['external_subject_ids'] = external_subject_ids.split(',')

    return dashboard_list


def get_dashboard_extra_data(dashboard_id: str) -> Dict[str, Union[datetime, None, int]]:
    """
    获取报告额外数据
    :param dashboard_id:
    :return:
    """
    extra_info = dict()
    if not dashboard_id:
        return extra_info

    query_data = dashboard_extra_repository.get_dashboard_extra_data(dashboard_id) or {}
    # 获取报告最新编辑时间
    edit_on = query_data.get("edit_on") or None
    # 获取报告发布时间
    released_on = query_data.get("released_on") or None
    root_dash_id = query_data.get("root_dash_id") or None
    copy_dash_id = query_data.get("copy_dash_id") or None
    first_released = query_data.get("first_released") or None

    extra_info["edit_on"] = edit_on
    extra_info["released_on"] = released_on
    extra_info["root_dash_id"] = root_dash_id
    extra_info["copy_dash_id"] = copy_dash_id
    extra_info["first_released"] = first_released
    extra_info["edit_status"] = _get_dashboard_edit_status(edit_on, released_on)
    return extra_info


def _get_dashboard_edit_status(edit_on: datetime, released_on: Optional[datetime]) -> int:
    """

    :param edit_on:
    :param released_on:
    :return: edit_status 0 未有编辑未发布数据 1有编辑未发布数据
    """
    edit_status = 0
    if not edit_on or not released_on:
        return edit_status
    if int(time.mktime(edit_on.timetuple())) > int(time.mktime(released_on.timetuple())):
        edit_status = 1
    return edit_status


def update_dashboard_edit_on(dashboard_id: str, edit_on="") -> int:
    """
    更新报告编辑时间
    :param dashboard_id:
    :param edit_on: 指定更新时间
    :return:
    """
    if not dashboard_id:
        return False
    return dashboard_extra_repository.update_edit_on(dashboard_id, edit_on)


def update_dashboard_released_on(dashboard_id: str) -> int:
    """
    更新报告发布时间
    :param dashboard_id:
    :return:
    """
    if not dashboard_id:
        return False
    dashboard_extra = dashboard_extra_repository.get_dashboard_extra_data(dashboard_id=dashboard_id) or {}
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    if not dashboard_extra:
        # 记录不存在，首次发布
        return dashboard_extra_repository.update_dashboard_extra_data(dashboard_id, released_on=now, first_released=now)
    else:
        first_released = dashboard_extra.get('first_released')
        if first_released:
            # 已经有初次发布时间了
            return dashboard_extra_repository.update_dashboard_extra_data(dashboard_id, released_on=now)
        else:
            # 没有初次发布时间
            return dashboard_extra_repository.update_dashboard_extra_data(
                dashboard_id, released_on=now,first_released=now
            )


def update_dashboard_extra_data(
        dashboard_id: str, edit_on="", released_on="",
        copy_dash_id="", root_dash_id="", is_copy_sys=0, first_released=""
):
    """
    更新报告的额外信息
    :param dashboard_id: 报告id
    :param edit_on:      最近编辑时间
    :param released_on:  最后发布时间
    :param copy_dash_id: 从哪个报告(id)复制
    :param root_dash_id: 复制的源头报告id
    :param is_copy_sys:  复制的时候，是否从系统级报告复制
    :return:
    """
    if not dashboard_id:
        return False
    if copy_dash_id:
        # 往上找是否存在报告是系统级的
        is_copy_sys = 1 if check_copy_dash_is_sys(copy_dash_id) else 0
    return dashboard_extra_repository.update_dashboard_extra_data(
        dashboard_id, edit_on, released_on,
        copy_dash_id, root_dash_id, is_copy_sys, first_released
    )


def check_copy_dash_is_sys(copy_dash_id):
    """
    检查复制关系上面的所有报表是否有系统级的
    判断报表是否是从系统级复制过来的
    """
    if not copy_dash_id:
        return False

    result = [copy_dash_id]
    find_all_top_copy_dashboard(copy_dash_id, result)

    data = repository.get_data(
        'dap_bi_dashboard', conditions={'id': result}, fields=['distribute_type'], multi_row=True  # noqa
    ) or []
    return any([d.get('distribute_type') for d in data])


def find_all_top_copy_dashboard(copy_dash_id, result: list):
    """
    往上找到所有的复制链路上的报告
    """
    dashboard_extra = dashboard_extra_repository.get_dashboard_extra_data(dashboard_id=copy_dash_id)
    if dashboard_extra:
        pre_dash_id = dashboard_extra.get('copy_dash_id')
        if pre_dash_id:
            result.append(pre_dash_id)
            find_all_top_copy_dashboard(pre_dash_id, result)


def reset_edit_or_released_on(dashboard_id: str, key: str = "edit_on") -> int:
    """
    重置报告的编辑或发布时间
    :param dashboard_id:
    :param key:
    :return:
    """
    return dashboard_extra_repository.reset_edit_on_or_released_on(key, dashboard_id)


def reset_edit_and_released_on(dashboard_id):
    """
    重置报告的编辑和发布时间
    :param dashboard_id:
    :return:
    """
    return dashboard_extra_repository.reset_edit_on_and_released_on(dashboard_id)


def get_account_name_map(accounts):
    account_name_map = {}
    users = external_user_service.get_users({'account': accounts}, fields=['name', 'account'])
    if users:
        account_name_map = {user['account']: user['name'] for user in users}
    return account_name_map


def update_root_dashboard_status(level_code):
    dashboard_root_info = dashboard_released_design_repository.get_root_dashboard_by_level_code(level_code)
    if dashboard_root_info:
        top_dashboard_id = dashboard_root_info.get('id')
        update_dashboard_edit_on(top_dashboard_id)
        repository.update_data('dap_bi_dashboard', {'modified_on': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}, {'id': top_dashboard_id})