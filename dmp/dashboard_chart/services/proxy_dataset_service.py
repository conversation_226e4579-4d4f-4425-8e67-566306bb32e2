#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/10
"""
import json
import logging
from copy import deepcopy

from base.enums import DatasetFieldOperator
from dmplib.utils.errors import UserError
from dmplib.hug import g
from dataset import external_query_service
from collections import defaultdict
from typing import Any, Dict, List, Optional, Union
from dashboard_chart.repositories import dashboard_repository

logger = logging.getLogger(__name__)


def get_dataset_field(dataset_id: str, dataset_version_id: str = None) -> List[Dict[str, Union[str, int, None]]]:
    """
    获取数据集字段
    :param dataset_id:
    :return:
    """
    return external_query_service.get_dataset_fields(dataset_id=dataset_id, dataset_version_id=dataset_version_id)


def get_external_subjects_dataset_field_dict(external_subject_ids: List[str]) -> dict:
    """
    获取多个外部主题字段字典
    :param external_subject_id:
    :return:
    """
    dataset_field_dict = {}
    if not external_subject_ids:
        return dataset_field_dict
    for external_subject_id in external_subject_ids:
        data_dict = get_external_subject_dataset_field_dict(external_subject_id)
        dataset_field_dict.update(data_dict)
    return dataset_field_dict


def get_external_subject_dataset_field_dict(external_subject_id: str) -> dict:
    """
    获取外部主题字段字典
    :param external_subject_id:
    :return:
    """
    dataset_field_dict = {}
    if not external_subject_id:
        return dataset_field_dict
    external_subject_dataset_fields = external_query_service.get_external_subject_dataset_fields(external_subject_id)
    dataset_field_dict = {
        external_subject_dataset_field.get("id"): external_subject_dataset_field
        for external_subject_dataset_field in external_subject_dataset_fields
    }
    return dataset_field_dict


def get_dataset_field_dict_with_field_id(
    dataset_id: str, dataset_version_id: str = None
) -> Dict[str, Dict[str, Union[str, int, None]]]:
    """
    以字段id为key获取数据集下所有字段dict
    :param str dataset_id: 数据集id
    :return:
    """
    # 添加拍照数据查询支持
    if not dataset_version_id and hasattr(g, "dataset_version_id"):
        dataset_version_id = g.dataset_version_id
    dataset_field_dict = dict()
    if not dataset_id:
        return dataset_field_dict
    query_dataset_data = get_dataset_field(dataset_id, dataset_version_id)
    if not query_dataset_data:
        return dataset_field_dict
    dataset_field_dict = {dataset_field.get("id"): dataset_field for dataset_field in query_dataset_data}
    return dataset_field_dict


def _set_expression_field_dict(
    fields: Dict[str, Dict[str, Union[str, int, None]]],
    field_dict: Optional[Dict[str, Dict[str, Union[str, int, None]]]] = None,
    ignore_error: bool = False,
) -> None:
    """
    对高级字段进行内容解析
    :param list|dict field:
    :return:
    """
    for dataset_field_id, item in fields.items():
        if item.get("expression"):
            expression_dict = _get_expression_field_colname(
                json.loads(item.get("expression")), item.get("alias_name"), field_dict, ignore_error
            )
            item["expressions"] = expression_dict


def set_expression_single_field(field_dict):
    """
    selector中对高级字段进行内容解析
    :param dict field_dict:
    :return:
    """
    if not field_dict:
        return field_dict
    field_id = field_dict.get('id')
    dataset_id = field_dict.get('dataset_id')
    expression = field_dict.get('expression')
    alias_name = field_dict.get('alias_name')
    if not expression or not field_id:
        return field_dict
    dataset_field_dict = {dataset_field.get("id"): dataset_field for dataset_field in get_dataset_field(dataset_id)}
    expression_dict = _get_expression_field_colname(json.loads(expression), alias_name, dataset_field_dict)
    field_dict['expressions'] = expression_dict
    return field_dict


def _parse_expression_op_operator(expression):
    operator = expression["op"].upper()
    if operator == DatasetFieldOperator.Input.value:
        expression["data"] = expression["value"]
        return expression
    else:
        expression["data"] = operator
    expression["op"] = operator
    expression["is_col"] = False
    return expression


def _parse_expression_id_operator(expression, dataset_field_dict, ignore_error, alias_name):
    dataset_field = dataset_field_dict.get(expression.get("id"))
    if not dataset_field:
        if not ignore_error:
            raise UserError(message=alias_name + "该高级字段的引用字段被删除，请重新编辑该高级字段。")
        else:
            return expression
    expression["data"] = expression["col_name"] = dataset_field.get("col_name")
    expression["table"] = dataset_field.get("table_name")
    expression["is_col"] = True
    return expression


def _get_expression_field_colname(expression_dict, alias_name, dataset_field_dict, ignore_error=False):
    """
    根据字段表达式字典获取字段的表达式字段名，顺带查询数据是否是计算高级字段
    例如将：
    [{"op": "sum" },
    {"op": "(" },
    {"id": "39e02d3b-cb98-3dd2-a37f-c5eefbf1d6c3"},
    {"op":"+"},
    {"id":"39e02d3b-cb98-3dd2-a37f-c5eefbf1d6c3"},
    {"op": ")"}]
    转换成：
    sum(`col2`+`col2`)
    :param expression_dict:字典类型的表达式
    :return:
    """
    for item in expression_dict:
        if not isinstance(item, dict):
            continue

        # 操作符
        if "op" in item:
            _parse_expression_op_operator(item)
        elif "id" in item:
            _parse_expression_id_operator(item, dataset_field_dict, ignore_error, alias_name)
    return expression_dict


def get_dataset(dataset_id: str, dataset_version_id: str = None) -> Dict[str, Union[str, None, bool]]:
    """
    获取数据集
    :param dataset_id:
    :return:
    """
    if not dataset_id:
        return dict()
    return external_query_service.get_dataset(dataset_id=dataset_id, dataset_version_id=dataset_version_id)


def get_formatted_fields_by_field_id(field_id, field_for_change_dict=None):
    """
    获取格式化数据集字段
    :param field_id:
    :param field_for_change_dict: 需要更换的字段 {'old_key': 'new_key'}
    :return:
    """
    field_data = dict()
    if not field_id:
        return field_data
    query_field_data_list = external_query_service.get_multi_dataset_fields([field_id])
    if not len(query_field_data_list):
        return field_data
    query_field_data = query_field_data_list[0]
    for k, v in query_field_data.items():
        if field_for_change_dict and k in field_for_change_dict:
            k = field_for_change_dict[k]
        field_data.update({k: v})
    return field_data


def batch_get_formatted_fields_by_field_id(
    field_id_list: List[str], field_for_change_dict: None = None
) -> Dict[str, Dict[str, Union[str, int, None]]]:
    """
    批量获取格式化数据集字段
    :param field_id_list:
    :param field_for_change_dict: 需要更换的字段 {'old_key': 'new_key'}
    :return:
    """
    field_data = dict()
    field_data_dict = dict()

    # 批量查询
    if not field_id_list:
        return field_data_dict
    query_field_data = external_query_service.get_multi_dataset_fields(field_id_list)
    if not query_field_data:
        return field_data

    # 组装到dict
    for single_query_field_data in query_field_data:
        dataset_field_id = single_query_field_data.get("id")
        if dataset_field_id not in field_data_dict:
            field_data_dict.update({dataset_field_id: dict()})
        # 需要更换的字段名
        for k, v in single_query_field_data.items():
            if field_for_change_dict and k in field_for_change_dict:
                k = field_for_change_dict[k]
            if k == "id":
                continue
            field_data_dict[dataset_field_id].update({k: v})
    return field_data_dict


def get_formatted_dataset_field(orig_dict_data, main_dataset_field_id):
    """
    获取格式化数据集字段数据
    :param orig_dict_data:
    :param main_dataset_field_id:
    :return:
    """
    dataset_field_data = get_formatted_fields_by_field_id(main_dataset_field_id)
    for k, v in dataset_field_data.items():
        orig_dict_data.update({k: v})
        if k == "col_name":
            orig_dict_data.update({"main_dataset_col_name": v})
        if k == "alias_name":
            orig_dict_data.update({"main_dataset_alias_name": v})
    return orig_dict_data


def get_dataset_field_by_field_id(fields: List[str], field_id: str) -> Dict[str, Union[str, int, None]]:
    """
    获取数据集字段数据
    :param fields:
    :param field_id:
    :return:
    """
    result_field_data = dict()
    if not fields or not field_id:
        return result_field_data
    query_field_data_list = external_query_service.get_multi_dataset_fields([field_id])
    if not query_field_data_list:
        return list()
    query_field_data = query_field_data_list[0]
    if not query_field_data:
        return result_field_data
    for single_field in fields:
        result_field_data.update({single_field: query_field_data.get(single_field)})
    return result_field_data


def get_dataset_data_by_id(fields: List[str], dataset_id: Optional[str]) -> Dict[str, str]:
    """

    :param fields:
    :param dataset_id:
    :return:
    """
    result_field_data = dict()
    if not fields or not dataset_id:
        return result_field_data
    query_dataset_data = external_query_service.get_dataset(dataset_id)
    if not query_dataset_data:
        return result_field_data
    for single_field in fields:
        result_field_data.update({single_field: query_dataset_data.get(single_field)})
    return result_field_data


def get_fields_by_field_id(
    fields: List[str], field_id: str, field_for_change_dict: None = None
) -> Dict[str, Union[str, int, None]]:
    """
    获取结构化的数据集字段
    :param fields:
    :param field_id:
    :param field_for_change_dict:
    :return:
    """
    field_data = dict()
    for single_field in fields:
        if field_for_change_dict and single_field in field_for_change_dict:
            single_field = field_for_change_dict[single_field]
        field_data.update({single_field: None})
    query_field_data = get_dataset_field_by_field_id(fields, field_id)
    if not query_field_data:
        return field_data
    for k, v in query_field_data.items():
        if field_for_change_dict and k in field_for_change_dict:
            k = field_for_change_dict[k]
        field_data.update({k: v})
    return field_data


def get_initiator_alias(field_initiator_id):
    """
    获取initiator_alias
    :param field_initiator_id:
    :return:
    """
    initiator_alias = ''
    if field_initiator_id:
        field_data = get_dataset_field_by_field_id(['alias_name'], field_initiator_id)
        initiator_alias = field_data['alias_name'] if field_data else ''
    return initiator_alias


def _get_dashboard_filter_id_list(dashboard_filters, dashboard_filter_relations):
    query_field_id_list = list()
    for item in dashboard_filters:
        if item.get('main_dataset_field_id'):
            query_field_id_list.append(item.get('main_dataset_field_id'))
    if dashboard_filter_relations:
        for item in dashboard_filter_relations:
            if item.get('related_dataset_field_id'):
                query_field_id_list.append(item.get('related_dataset_field_id'))
    return query_field_id_list


def get_dashboard_var_filters(var_filter_relations):
    """
    获取报告级参数筛选
    """
    if not var_filter_relations:
        return
    res = []
    # 获取参数筛选逻辑
    for var_filter_relation in var_filter_relations:
        var_filter = dict(
            id=var_filter_relation.get("id"),
            dashboard_id=var_filter_relation.get("dashboard_id"),
            data_type=var_filter_relation.get("data_type"),
            filter_relations=[]
        )
        var_name = var_filter_relation.get("var_name")
        related_dataset_field_id = var_filter_relation.get("related_dataset_field_id", "")
        if related_dataset_field_id:
            field = dashboard_repository.get_dataset_field_by_id(related_dataset_field_id)
            filter_relation = dict(
                id=var_filter_relation.get("id"),
                related_dataset_id=var_filter_relation.get("related_dataset_id", ""),
                related_dataset_field_id=var_filter_relation.get("related_dataset_field_id", ""),
                alias_name=field.get("alias_name"),
                col_name=field.get("col_name")
            )
        else:
            filter_relation = {}
        if not res:
            var_filter['var_name'] = var_name
            var_filter['filter_relations'] = [filter_relation]
            res.append(var_filter)
        else:
            res_copy = deepcopy(res)
            for item in res_copy:
                if var_name not in item.keys():
                    var_filter['var_name'] = var_name
                    var_filter['filter_relations'] = [filter_relation]
                    res.append(var_filter)
                else:
                    item['filter_relations'].append(filter_relation)
    return res


def get_dashboard_filters(dashboard_filters, dashboard_filter_relations):
    """
    获取报告级筛选
    :return:
    """
    if not dashboard_filters:
        return dashboard_filters

    query_field_id_dict = dict()
    # 收集数据集字段id
    query_field_id_list = _get_dashboard_filter_id_list(dashboard_filters, dashboard_filter_relations)

    # 批量获取字段id的数据集数据
    query_field_data_list = external_query_service.get_multi_dataset_fields(list(set(query_field_id_list)))
    for single_query_field_data in query_field_data_list:
        single_query_field_data = switch_to_alias_name(single_query_field_data)
        field_id = single_query_field_data.get('id')
        if field_id:
            query_field_id_dict[field_id] = single_query_field_data

    # 获取related_dataset_field_id对应数据集id
    dashboard_fitler_relation_dict = defaultdict(list)
    for dashboard_filter_relation in dashboard_filter_relations:
        related_dataset_field_id = dashboard_filter_relation.get('related_dataset_field_id')
        main_dataset_field_id = dashboard_filter_relation.get('main_dataset_field_id')
        # 调用数据集服务获取数据集数据
        related_dataset_data = query_field_id_dict.get(related_dataset_field_id, {})
        dashboard_filter_relation['related_dataset_id'] = related_dataset_data.get('dataset_id', '')
        # 同一个主数据集字段id下的关联关系
        dashboard_fitler_relation_dict[main_dataset_field_id].append(dashboard_filter_relation)

    # 拼装需要的报告级筛选数据
    formatted_filter_data = list()
    for single_dashboard_filter in dashboard_filters:
        main_dataset_field_id = single_dashboard_filter.get('main_dataset_field_id', '')
        query_field_data = query_field_id_dict.get(main_dataset_field_id, {})
        # 如果字段已不存在，筛选条件则无法生效，直接过滤掉
        if not query_field_data:
            # logger.exception(msg='报告筛选条件引用数据集字段不存在，字段ID：{}'.format(main_dataset_field_id))
            continue
        item = {
            'main_dataset_id': query_field_data.get('dataset_id'),
            'alias_name': query_field_data.get('alias_name'),
            'col_name': query_field_data.get('col_name'),
            'data_type': query_field_data.get('data_type'),
            'field_group': query_field_data.get('field_group'),
            'type': query_field_data.get('type'),
            'expression': query_field_data.get('expression'),
            'format': query_field_data.get('format'),
        }
        item['filter_relations'] = dashboard_fitler_relation_dict.get(main_dataset_field_id, [])
        single_dashboard_filter.update(item)
        formatted_filter_data.append(single_dashboard_filter)
    return formatted_filter_data


def get_advanced_field_by_ids(fields, field_id_list):
    """
    获取高级字段对应数据，普通字段也需要处理
    :param fields:
    :param field_id_list:
    :return:
    """
    result_field_data = list()
    if not field_id_list:
        return result_field_data
    query_field_data_list = external_query_service.get_multi_dataset_fields(field_id_list)
    if not query_field_data_list:
        return result_field_data
    for single_data in query_field_data_list:
        # 优先取alias，赋值给col_name
        if single_data.get('alias'):
            single_data['col_name'] = single_data.get('alias')
        need_field_dict = dict()
        for need_field in fields:
            need_field_dict[need_field] = single_data.get(need_field)
        result_field_data.append(need_field_dict)
    return result_field_data


def fillup_dashboard_filter_related_dataset(dashboard_filters: List[Any]) -> List[Any]:
    """
    补充报告级筛选关联数据集缺少的数据
    :param dashboard_filters:
    :return:
    """
    if not dashboard_filters:
        return dashboard_filters
    for single_dashboard_filter in dashboard_filters:
        filter_relations = single_dashboard_filter.get('filter_relations')
        if not filter_relations:
            continue
        for single_filter_relation in filter_relations:
            related_dataset_field_id = single_filter_relation.get('related_dataset_field_id')
            related_dataset_id = single_filter_relation.get('related_dataset_id')
            if related_dataset_id or not related_dataset_field_id:
                continue
            # 调用数据集服务获取数据集数据
            related_dataset_data = external_query_service.get_multi_dataset_fields([related_dataset_field_id])
            if related_dataset_data:
                single_filter_relation['related_dataset_id'] = related_dataset_data[0].get('dataset_id')
    return dashboard_filters


def switch_to_alias_name(single_filed_data: Dict[str, Union[str, int, None]]) -> Dict[str, Union[str, int, None]]:
    """
    兼容直连api数据集表同名情况，优先将alias赋值给col_name
    :param single_filed_data:
    :return:
    """
    if not single_filed_data or not isinstance(single_filed_data, dict):
        return single_filed_data
    if single_filed_data.get('alias'):
        single_filed_data['col_name'] = single_filed_data.get('alias')

    # 数据集使用alias与前端的alias重叠，需要删除
    if 'alias' in single_filed_data.keys():
        single_filed_data.pop('alias')
    return single_filed_data
