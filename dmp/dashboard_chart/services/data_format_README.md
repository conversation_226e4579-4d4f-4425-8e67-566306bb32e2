# 数据格式化服务使用说明

## 概述

数据格式化服务用于根据 `dap_bi_dashboard_chart_num` 表中的 `display_format` 配置，对查询结果中的数值进行格式化处理。

## 功能特性

### 1. 支持的格式化选项

根据 `display_format` JSON 配置支持以下格式化选项：

- **千分位分隔符** (`thousand_point_separator`): 是否添加千分位分隔符
- **小数位数** (`fixed_decimal_places`): 固定小数位数
- **显示模式** (`display_mode`): 数值显示模式（num/percentage）
- **单位转换** (`unit`, `column_unit_name`): 万、亿等单位转换
- **单位显示** (`hidden_unit`): 是否隐藏单位
- **特殊值处理**:
  - `show_zero_way`: 零值显示方式
  - `show_null_way`: 空值显示方式
  - `show_empty_way`: 空字符串显示方式
- **智能格式化** (`smart_num_format`): 根据数值大小自动选择格式

### 2. 支持的数据格式

- **字符串列表**: `["来访数（LFS_10909121737）：10458.0组，复访数（FFS_10187173489）：522.0套"]`
- **字典列表**: `[{"col1": "项目A", "col2": 1234.56}, {"col1": "项目B", "col2": 7890.12}]`

## 使用方法

### 1. 在 API 中使用

数据格式化功能已集成到 `dashboard_chart_get_data` API 中：

```python
# 默认启用格式化
result = dashboard_chart_get_data(request, **kwargs)

# 禁用格式化
kwargs['enable_number_format'] = False
result = dashboard_chart_get_data(request, **kwargs)
```

### 2. 直接调用格式化服务

```python
from dashboard_chart.services.data_format_service import format_query_result_data

# 格式化查询结果
formatted_data = format_query_result_data(query_result, dashboard_chart_id)
```

### 3. 格式化特定数值

```python
from dashboard_chart.services.data_format_service import format_number_value

display_format = {
    "display_mode": "num",
    "thousand_point_separator": 1,
    "fixed_decimal_places": 2,
    "unit": "万",
    "hidden_unit": 0
}

formatted_value = format_number_value(1234567.89, display_format)
# 结果: "123.46万"
```

## 配置示例

### 1. 基本数值格式化

```json
{
    "display_mode": "num",
    "thousand_point_separator": 1,
    "fixed_decimal_places": 2,
    "unit": "无",
    "hidden_unit": 0
}
```

输入: `1234567.89` → 输出: `1,234,567.89`

### 2. 百分比格式化

```json
{
    "display_mode": "percentage",
    "thousand_point_separator": 0,
    "fixed_decimal_places": 2,
    "unit": "无",
    "hidden_unit": 0
}
```

输入: `0.1234` → 输出: `12.34%`

### 3. 万元单位格式化

```json
{
    "display_mode": "num",
    "thousand_point_separator": 1,
    "fixed_decimal_places": 2,
    "unit": "万",
    "hidden_unit": 0
}
```

输入: `12345678.9` → 输出: `1,234.57万`

### 4. 智能格式化

```json
{
    "display_mode": "num",
    "smart_num_format": 1,
    "fixed_decimal_places": 2
}
```

- `123456789` → `1亿`
- `12345678` → `1234万`
- `1234` → `1234.00`

### 5. 特殊值处理

```json
{
    "display_mode": "num",
    "show_zero_way": "0.00",
    "show_null_way": "--",
    "show_empty_way": "N/A",
    "fixed_decimal_places": 2
}
```

- `0` → `0.00`
- `null` → `--`
- `""` → `N/A`

## 错误处理

- 格式化失败时会记录错误日志，但不影响 API 正常返回
- 无效的数值会保持原样返回
- JSON 解析失败时使用默认格式

## 性能考虑

- 格式化配置会从数据库查询并缓存
- 大量数据格式化时建议分批处理
- 可通过 `enable_number_format=False` 禁用格式化以提升性能

## 测试

运行测试用例：

```bash
python -m pytest dmp/dashboard_chart/tests/test_data_format_service.py
```

## 注意事项

1. 确保 `dap_bi_dashboard_chart_num` 表中的 `display_format` 字段包含有效的 JSON 配置
2. 字段 ID 映射需要正确，确保能通过 `num` 字段关联到对应的数据集字段
3. 国际化场景下会自动应用本地化的数值格式
4. 格式化仅对数值类型字段生效，非数值字段保持原样
