import json

from dashboard_chart.repositories.dashboard_repository import get_dashboard
from datetime import datetime
from base import repository
from base.enums import FlowType, FlowNodeType
from dashboard_chart.services import dashboard_service
from dashboard_chart.services.released_dashboard_service import get_dashboard_release_url
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from dmplib.hug import g
from dmplib.saas.project import set_correct_project_code
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from flow.models import FlowModel, FlowNodeModel
from flow.services import flow_service
from message.models import MessageModel
from message.services import message_service
from dashboard_chart.models import PdfDownloadModel, PdfExportModel
from dashboard_chart.services.download import download_utils
from dmplib import config
from dmplib.locale import set_language, trans_msg

queue_name = config.get('RabbitMQ.queue_name_flow_feeds', 'dmp_flow_feeds')


def generate_message_title(msg_type, filename):
    """
    组装消息title
    """
    title = ''
    if msg_type == 'generate':
        title = f'正在生成数据【{filename}】，请稍候...'
    elif msg_type == 'successful':
        title = f'成功生成数据【{filename}】，可点击下方按钮再次下载数据'
    elif msg_type == 'fail':
        title = f'异常错误，数据【{filename}】生成失败，请联系管理员'
    return title


def generate_pdf_task(token, model: PdfExportModel, request_data):
    # 检查token有效性
    check_flag, errmsg, data = dashboard_service.check_token(token)
    if not check_flag:
        return check_flag, errmsg, None

    g.cookie = {'token': token}
    g.account = data.get("account")
    g.userid = data.get("id")
    code = data.get('tenant_code') or data.get('code') or request_data.get('code')

    set_language(code)

    set_correct_project_code(code)
    if not g.code:
        return False, trans_msg('租户代码不能为空'), None

    # 如果是在列表页导出(前端未传dashboard_url), 判断报告是否是发布状态
    dashboard = get_dashboard(model.dashboard_id)
    if not model.dashboard_url and dashboard and dashboard.get('status') != 1:
        raise UserError(message=trans_msg('未发布报告不能生成pdf'))

    # 报告截图导出任务是否已存在运行中的
    get_active_screenshot_task_data(model)

    # 创建flow和instance
    flow_id = seq_id()

    flow = FlowModel(id=flow_id, name='pdf导出', type=FlowType.PdfExport.value)
    node = FlowNodeModel(
        name=flow.name,
        type=FlowNodeType.PdfExport.value,
    )
    flow.nodes = [node]
    flow_service.add_flow(flow)

    # 获取dashboard_name，生成文件名
    dashboard_data = repository.get_data('dap_bi_dashboard', {'id': model.dashboard_id}, ['name'])
    if not dashboard_data:
        raise UserError(message=trans_msg("查询不到对应报告数据，报告id：{id}").format(id=model.dashboard_id))
    dashboard_name = dashboard_data.get('name', '报告')
    filename = download_utils.get_valid_filename(dashboard_name)

    # 消息提醒
    _message = {
        'source_id': filename + '_' + datetime.now().strftime("%Y%m%d%H%M%S"),
        'user_id': g.userid,
        'source': '通知',
        'type': '个人消息',
        'title': generate_message_title(msg_type='generate', filename=filename),
        'url': '/flow/ops',
    }
    message_service.message_add(MessageModel(**_message))

    # 在列表页, 前端不传url, 使用发布页url; 在报告中, 使用前端传的url
    release_url = (
        f'{get_pure_domain(AppHosts.get(SkylineApps.DP, False).strip("/"))}{model.dashboard_url}'
        if model.dashboard_url
        else get_dashboard_release_url(model.dashboard_id)
    )

    # 报告截图导出参数处理
    format_external_conditions(model)

    # 创建任务
    download_id = seq_id()
    download_model = PdfDownloadModel(
        download_id=download_id,
        dashboard_id=model.dashboard_id,
        flow_id=flow_id,
        user_token=token,
        release_url=release_url,
        download_url='',
        external_conditions=json.dumps(model.external_conditions) if model.external_conditions else None,
        export_type=model.export_type
    )

    data = download_model.get_dict(
        [
            'download_id',
            'dashboard_id',
            'user_token',
            'flow_id',
            'status',
            'download_url',
            'release_url',
            'external_conditions',
            'export_type',
        ]
    )
    repository.add_data('dap_bi_dashboard_pdf_export_task', data)

    # 执行流程
    flow_service.run_flow(flow_id, queue_name=queue_name)

    # 更新任务状态
    repository.update_data('dap_bi_dashboard_pdf_export_task', {'status': 1}, {'download_id': download_id})

    return True, '', {'download_id': download_id}


def get_pure_domain(url):
    from urllib.parse import urlparse
    parsed_url = urlparse(url)
    return f"{parsed_url.scheme}://{parsed_url.netloc}"


def format_external_conditions(model):
    """
    报告截图导出，url拼接域名
    :param model:
    :return:
    """
    if model.external_conditions and isinstance(model.external_conditions, list) and model.export_type == 1:
        for item in model.external_conditions:
            if item.get("url"):
                url = item.get("url")
                item['url'] = f'{AppHosts.get(SkylineApps.DP, False).strip("/")}/bi-visual/{url}'


def get_export_message(token, request_data):
    """
    获取下载结果
    """
    # 检查token有效性
    check_flag, errmsg, data = dashboard_service.check_token(token)
    if not check_flag:
        return check_flag, errmsg, None

    g.cookie = {'token': token}
    g.account = data.get("account")
    g.userid = data.get("id")
    code = data.get('tenant_code') or data.get('code') or request_data.get('code')

    set_language(code)

    set_correct_project_code(code)
    if not g.code:
        return False, trans_msg('租户代码不能为空'), None

    download_model = PdfDownloadModel(**request_data)
    msg, result = get_download_task_data(download_model)
    return True, msg, result


def get_download_task_data(download_model):
    """
    获取下载进度
    """
    result_data = repository.get_data(
        'dap_bi_dashboard_pdf_export_task', {'download_id': download_model.download_id}, ['status', 'download_url']
    )
    if not result_data:
        msg = trans_msg("下载任务不存在:{}").format(download_model.download_id)
        raise UserError(message=msg)
    download_status = result_data.get('status', 0)
    msg = ''
    if download_status == 0:
        msg = '下载异常，请稍后重试'
    elif download_status == 1:
        msg = 'pdf生成中，完成后会提示您下载'
    elif download_status == 2:
        msg = 'pdf下载文件已生成，点击下载'
    elif download_status == 3:
        msg = 'pdf下载文件生成失败，请重试'

    return trans_msg(msg), result_data


def get_active_screenshot_task_data(model: PdfExportModel):
    """
    按报告id获取正在进行报告截图导出任务
    一个报告只能有一个正在进行的的报告截图导出任务
    :param model:
    :return:
    """
    result_data = {}
    if model.export_type == 1:
        dashboard_id = model.dashboard_id
        result_data = repository.get_data(
            'dap_bi_dashboard_pdf_export_task', {'dashboard_id': dashboard_id, "status": [0, 1], "export_type": 1},
            ['download_id', 'status', 'download_url']
        )
        if result_data:
            msg = trans_msg('下载任务进行中，请稍等 dashboard_id:{}').format(dashboard_id)
            raise UserError(message=msg)
    return result_data


def clear_screenshot(dashboard_id, clear_type):
    """
    清除报告截图存储到租户的表数据
    :param dashboard_id:
    :param clear_type:
    :return:
    """
    # 全部数据清除
    if clear_type == 'all':
        count = repository.get_data_scalar_by_sql("select count(1) from dap_bi_dashboard_screenshot_export_record", {})
        clear_sql = "truncate table dap_bi_dashboard_screenshot_export_record"
        repository.exec_sql(clear_sql)
    else:
        # 删除指定报告数据
        if not dashboard_id:
            raise UserError(message="报告id不能为空")
        count = repository.delete_data('dap_bi_dashboard_screenshot_export_record', {"dashboard_id": dashboard_id})
    return True, '', {'count': count}
