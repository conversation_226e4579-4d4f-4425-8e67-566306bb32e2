#!/usr/local/bin python3
# -*- coding:  utf-8 -*-
from urllib.parse import urljoin

from app_menu.services.application_openapi_service import _get_mobile_screen_portal_url
from base import repository
from base.enums import SubjectDataSourceType, AddFuncType, ApplicationType
from dashboard_chart.repositories import external_dashboard_repository, chart_repository
from dashboard_chart.models import DashboardOpenAPIQueryModel
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from rbac.repositories import data_permission_repository
from user.repositories import user_repository
from user.services.user_service import get_cur_role_id
from dmplib.constants import ADMIN_ROLE_ID
from dmplib import config
from dmplib.hug import g
from dmplib.locale import trans_msg
from base.errors import UserError
from app_celery import async_run_subject_full_inspection_dashboard_node, async_run_subject_full_inspection_storage


def get_report_list(d_type='', **kwargs):  # NOSONAR
    from ppt import external_service

    user_account = kwargs.get('user_account', None) or kwargs.get('userCode', None)
    if user_account is not None:
        g.account = user_account

    platform = kwargs.get('platform')
    data = []
    if d_type == AddFuncType.Ppt.value:
        if platform != 'mobile':
            data = external_service.get_ppt_list(**kwargs)
    elif d_type == AddFuncType.ActiveReport.value:
        if platform != 'mobile':
            data = external_service.get_active_report_list(**kwargs)
    elif d_type == AddFuncType.Dashboard.value:
        data = get_dashboard_list_for_openapi(d_type, **kwargs)
    elif d_type == 'large_screen':
        if platform != 'mobile':
            data = get_dashboard_list_for_openapi(d_type, **kwargs)
    else:
        dashboard = get_dashboard_list_for_openapi('dashboard',**kwargs)
        large_screen = []
        active_report = []
        if platform != 'mobile':
            large_screen = get_dashboard_list_for_openapi('large_screen',**kwargs)
            # ppt = external_service.get_ppt_list(**kwargs)
            active_report = external_service.get_active_report_list(**kwargs)
        data = dashboard + large_screen + active_report
    if kwargs.get('include_folder') == 1:
        set_parent_folder(data)
    return data

def set_parent_folder(data):
    ids = [item.get('id') for item in data]
    folders = repository.get_list('dap_bi_dashboard', {'type': 'folder'})
    if not folders:
        return data
    for item in folders:
        if item.get('id') not in ids:
            data.append(item)
    return data


def get_dashboard_list_for_openapi(d_type='dashboard', **kwargs):
    user_account = kwargs.get('user_account', None)
    if user_account is not None:

        g.account = user_account
        has_role, role_ids = get_user_role_ids(user_account)
        if not has_role:
            return []
        else:
            if ADMIN_ROLE_ID not in role_ids and not data_permission_repository.get_permission_by_role_ids(
                    role_ids, 'dashboard', 'view'
            ):
                allow_ids = data_permission_repository.get_allow_ids(role_ids, 'dashboard', 'view') or []
                own_ids = external_dashboard_repository.get_user_create_dashboard_ids(user_account)
                allow_ids.extend(own_ids or [])
                if not allow_ids:
                    return []
                kwargs['dashboard_ids'] = list(set(allow_ids))

    query_model = DashboardOpenAPIQueryModel(**kwargs)
    data = external_dashboard_repository.get_dashboard_list_by_query_model(query_model, d_type)
    for row in data:
        if row.get('platform') == 'mobile':
            row['edit_url'] = '/dataview-mobile/design/{}'.format(row.get('id'))
            row['preview_url'] = '/dataview-mobile/preview/{}'.format(row.get('id'))
            row['super_app_integrate_url'] = _get_mobile_screen_portal_url(row.get('id'), AddFuncType.Dashboard.value)
        else:
            row['edit_url'] = '/dataview/design/{}'.format(row.get('id'))
            row['preview_url'] = '/dataview/preview/{}'.format(row.get('id'))
        row['super_portal_integrate_url'] =urljoin(AppHosts.get(SkylineApps.DP, False),f"api/user/superportal/dashboard?report_id={row.get('id')}")
        row['report_type'] = AddFuncType.Dashboard.value
        row['name'] = _format_folder_name(row)
    return data


def _format_folder_name(row):
    """
    格式化openapi对外展示的酷炫大屏的文件夹名字
    """
    level_code = row.get('level_code') or ''
    ftype = row.get('type')
    application_type = row.get('application_type')
    if (
            level_code.count('-') == 1
            and ftype == 'FOLDER'
            and application_type == ApplicationType.LargeScreen.value
    ):
        return f"{trans_msg(row.get('name') or '')}({trans_msg('酷炫大屏')})"
    else:
        return trans_msg(row.get('name') or '')


def get_user_role_ids(user_account):
    user_id = user_repository.get_cur_user_id()
    if not user_id:
        return False, []

    role_ids = get_cur_role_id(user_id=user_id, account=user_account)
    if not role_ids:
        return False, []
    return True, role_ids


def run_dataset_subject_inspection(data: dict):
    if not data.get('subject_id'):
        raise UserError(message='缺少subject_id')
    if not data.get('project_code'):
        raise UserError(message='缺少project_code')
    if not data.get('data_source_type'):
        raise UserError(message='缺少data_source_type')

    # api 数据源的报告巡检暂不支持
    if data["data_source_type"] != SubjectDataSourceType.Api.value:
        async_run_subject_full_inspection_dashboard_node.delay(
            data['project_code'], data['subject_id'], data["data_source_type"]
        )
    async_run_subject_full_inspection_storage.delay(data['project_code'], data['subject_id'], data["data_source_type"])
    return True
