#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/8
"""
from dmplib.saas.project import get_db
import json
from base import repository


def get_chart_info(chart_id):
    sql = """SELECT `c`.`id`, `c`.`dashboard_id`, `c`.`name`, `c`.`chart_code`, `c`.`page_size`, `c`.`data_logic_type_code`, `c`.`chart_type`, `c`.`source`,
       `c`.`position`, `c`.`parent_id`, `c`.`level_code`, `c`.`display_item`, `c`.`refresh_rate`, `c`.`aggregation`, `c`.`enable_subtotal_col_summary`,
       `c`.`enable_subtotal_col`, `c`.`enable_subtotal_row`, `c`.`enable_subtotal_row_summary`, `c`.`subtotal_row_summary_formula_mode`,
       `d`.`name` AS `dashboard_name`, `d`.`type` AS `dashboard_type`, `d`.`platform`, `d`.`terminal_type`, `d`.`is_multiple_screen`, `d`.`status`,
       `d`.`layout_type`, `d`.`type_access_released`
        FROM `dap_bi_dashboard_chart` AS `c`
        LEFT JOIN `dap_bi_dashboard` AS `d`
        ON `c`.`dashboard_id` = `d`.`id`
        WHERE `c`.`id` = %(chart_id)s
             """
    params = {'chart_id': chart_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dataset_markline_by_chart_id(chart_id):
    """
    获取单图的markline（标线）数据（数据集）
    :param str chart_id:
    :return:
    """
    sql = """SELECT `cm`.`dashboard_chart_id`, `cm`.`name`, `cm`.`mode`, `cm`.`num`, `cm`.`formula_mode`, `cm`.`value`, `cm`.`axis_type`, `cm`.`percentile`
        FROM `dap_bi_dashboard_chart_markline` AS `cm`
        WHERE `cm`.`dashboard_chart_id` = %(chart_id)s
        ORDER BY `cm`.`rank`
             """
    params = {'chart_id': chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_nums_by_chart_id(chart_id):
    """
    :param chart_id:
    :return:
    """
    sql = """
          SELECT `dcn`.`dashboard_chart_id`, `dcn`.`alias`, `dcn`.`axis_type`, `dcn`.`chart_code`, `dcn`.`calc_null`,
               `dcn`.`display_format`, `dcn`.`formula_mode`, `dcn`.`rank`, `dcn`.`sort`, `dcn`.`num`, `dcn`.`dashboard_jump_config`, `dcn`.`note`,
               `dcn`.`subtotal_formula_mode`, `dcn`.`subtotal_col_formula_mode`, `dcn`.`subtotal_row_formula_mode`,
               `dcn`.`same_ring_ratio_config`, `dcn`.`subtotal_col_formula_expression`, `dcn`.`hidden`
        FROM `dap_bi_dashboard_chart_num` AS `dcn`
        WHERE `dcn`.`dashboard_chart_id` = %(chart_id)s
        ORDER BY `dcn`.`rank` ASC
          """
    params = {'chart_id': chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_num_by_field_id(field_id, dashboard_chart_id):
    """
    根据field_id获取num数据
    :param str field_id:
    :param str dashboard_chart_id:
    :return:
    """
    sql = """SELECT `num` AS `id`, `formula_mode`, `dashboard_chart_id`, `alias`
        FROM `dap_bi_dashboard_chart_num`
        WHERE `num` = %(field_id)s AND `dashboard_chart_id` = %(chart_id)s
             """
    params = {"field_id": field_id, "chart_id": dashboard_chart_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dims_by_chart_id(chart_id):
    """

    :param chart_id:
    :return:
    """
    sql = """
          SELECT
            `dcd`.`dashboard_chart_id`, `dcd`.`dashboard_jump_config`, `dcd`.`alias`, `dcd`.`content`, `dcd`.`formula_mode`, `dcd`.`rank`, `dcd`.`sort`,
            `dcd`.`dim`, `dcd`.`note`, `dcd`.`is_subtotal_cate`
        FROM `dap_bi_dashboard_chart_dim` AS `dcd`
        WHERE `dcd`.`dashboard_chart_id` = %(chart_id)s
        ORDER BY `dcd`.`rank` ASC
          """
    params = {'chart_id': chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_comparisons_by_chart_id(chart_id):
    """

    :param chart_id:
    :return:
    """
    sql = """SELECT
            `dcc`.`alias`, `dcc`.`content`, `dcc`.`dashboard_chart_id`,
            `dcc`.`formula_mode`, `dcc`.`rank`, `dcc`.`sort`, `dcc`.`dataset_field_id`
        FROM `dap_bi_dashboard_chart_comparison` AS `dcc`
        WHERE `dcc`.`dashboard_chart_id` = %(chart_id)s
        ORDER BY `rank` ASC
          """
    params = {'chart_id': chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_desires_by_chart_id(chart_id):
    """

    :param chart_id:
    :return:
    """
    sql = """SELECT
            `dcd`.`alias`, `dcd`.`dashboard_chart_id`, `dcd`.`dataset_field_id`,
            `dcd`.`mode`, `dcd`.`rank`, `dcd`.`sort`, `dcd`.`formula_mode`, `dcd`.`value`, `dcd`.`id`, `dcd`.`display_format`
        FROM `dap_bi_dashboard_chart_desire` AS `dcd`
        WHERE `dcd`.`dashboard_chart_id` = %(chart_id)s
          """
    params = {'chart_id': chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_selector(chart_initiator_id, chart_responder_id):
    """
    通过单图id获取关联单图关系的数据
    :param chart_initiator_id:
    :param chart_responder_id:
    :return:
    """
    sql = """SELECT
    `cs`.`id`, `cs`.`chart_initiator_id`, `cs`.`chart_responder_id`, `cs`.`dashboard_id`, `cs`.`dataset_id`,
    `cs`.`is_same_dataset`, `csf`.`field_initiator_id`, `csf`.`field_responder_id`
FROM `dap_bi_dashboard_chart_selector` AS `cs`
LEFT JOIN `dap_bi_dashboard_chart_selector_field` AS `csf` ON `cs`.`id` = `csf`.`selector_id`
WHERE `chart_initiator_id` = %(iid)s AND `chart_responder_id` = %(rid)s AND `cs`.`type` = 0
             """
    params = {'iid': chart_initiator_id, 'rid': chart_responder_id}
    with get_db() as db:
        return db.query(sql, params)


def get_new_chart_linkage(chart_initiator_id, chart_responder_id):
    sql = '''
    SELECT
    `cdl`.`id`,
    `cdl`.`chart_id` AS `chart_initiator_id`,
    `cdl`.`dataset_field_id` AS `field_initiator_id`,
    `cdl`.`dataset_id` AS `initiator_dataset_id`,
    `cdl`.`dashboard_id`,
    `cdlf`.`chart_responder_id`,
    `cdlf`.`dataset_responder_id` AS `dataset_id`,
    `cdlf`.`field_responder_id`
    FROM `dap_bi_dashboard_linkage` AS `cdl`
    LEFT JOIN `dap_bi_dashboard_linkage_relation` AS `cdlf` ON `cdl`.`id` = `cdlf`.`link_id`
    WHERE `cdl`.`chart_id` = %(chart_initiator_id)s
    AND `chart_responder_id` = %(chart_responder_id)s
    '''

    params = {"chart_initiator_id": chart_initiator_id, "chart_responder_id": chart_responder_id}
    with get_db() as db:
        return db.query(sql, params)


def get_new_chart_filter(chart_initiator_id, chart_responder_id):
    # 获取新版的单图筛选数据

    sql = '''
    SELECT
    `cdl`.`id`,
    `cdl`.`chart_id` AS `chart_initiator_id`,
    `cdl`.`dataset_field_id` AS `field_initiator_id`,
    `cdl`.`dataset_id` AS `initiator_dataset_id`,
    `cdl`.`dashboard_id`,
    `cdlf`.`chart_responder_id`,
    `cdlf`.`dataset_responder_id` AS `dataset_id`,
    `cdlf`.`field_responder_id`
    FROM `dap_bi_dashboard_filter_chart` AS `cdl`
    LEFT JOIN `dap_bi_dashboard_filter_chart_relation` AS `cdlf` ON `cdl`.`id` = `cdlf`.`filter_id`
    WHERE `cdl`.`chart_id` = %(chart_initiator_id)s
    AND `chart_responder_id` = %(chart_responder_id)s

    '''

    params = {"chart_initiator_id": chart_initiator_id, "chart_responder_id": chart_responder_id}
    with get_db() as db:
        return db.query(sql, params)


def get_available_new_chart_filter(chart_initiator_id, chart_responder_id):
    # 获取新版的单图筛选数据

    sql = '''
    SELECT
    `cdl`.`id`,
    `cdl`.`chart_id` AS `chart_initiator_id`,
    `cdl`.`dataset_field_id` AS `field_initiator_id`,
    `cdl`.`dataset_id` AS `initiator_dataset_id`,
    `cdl`.`dashboard_id`,
    `cdlf`.`chart_responder_id`,
    `cdlf`.`dataset_responder_id` AS `dataset_id`,
    `cdlf`.`field_responder_id`
    FROM `dap_bi_dashboard_filter_chart` AS `cdl`
    LEFT JOIN `dap_bi_dashboard_filter_chart_relation` AS `cdlf` ON `cdl`.`id` = `cdlf`.`filter_id`
    WHERE `cdl`.`chart_id` = %(chart_initiator_id)s
    AND `cdl`.`available` = 1
    AND `chart_responder_id` = %(chart_responder_id)s
    '''

    params = {"chart_initiator_id": chart_initiator_id, "chart_responder_id": chart_responder_id}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_filter_by_chart_id(chart_id):
    """
    获取单图的过滤数据（数据集）
    :param str chart_id:
    :return:
    """
    sql = """SELECT
    `dcf`.`dashboard_chart_id`, `dcf`.`col_name`, `dcfr`.`operator`, `dcfr`.`col_value`, `dcf`.`dataset_field_id`,
    `dc`.`source` AS `dataset_id`, `dc`.`filter_relation`
FROM `dap_bi_dashboard_chart_filter` AS `dcf`
INNER JOIN `dap_bi_dashboard_chart` AS `dc` ON `dc`.`id` = `dcf`.`dashboard_chart_id` AND `dc`.`id` = %(chart_id)s
INNER JOIN `dap_bi_dashboard_chart_filter_relation` AS `dcfr` ON `dcfr`.`dashboard_chart_filter_id` = `dcf`.`id`
          """
    params = {'chart_id': chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_penetrate_relation_by_chart_id(chart_id):
    """
    获取单图的穿透字段关联关系
    :param str chart_id:
    :return:
    """
    sql = """
        SELECT
    `parent_chart_field_id`, `child_chart_field_id`, `type`, `parent_chart_var_id`
FROM `dap_bi_dashboard_chart_penetrate_relation`
WHERE `dashboard_chart_id` = %(chart_id)s

    """
    return get_db().query(sql, {'chart_id': chart_id})


def get_rank_dims(chart_id):
    """
    获取排序后的dim数据
    :param str chart_id:
    :return:
    """
    sql = """
        SELECT
            `dim` AS `field_id`, `formula_mode`, `rank`, `alias`
        FROM `dap_bi_dashboard_chart_dim`
        WHERE `dashboard_chart_id` = %(chart_id)s
        ORDER BY `rank`

    """
    return get_db().query(sql, {'chart_id': chart_id})


def get_rank_nums(chart_id):
    """
    获取排序后的num数据
    :param str chart_id:
    :return:
    """
    sql = """
        SELECT
            `num` AS `field_id`, `formula_mode`, `rank`, `alias`
        FROM `dap_bi_dashboard_chart_num`
        WHERE `dashboard_chart_id` = %(chart_id)s
        ORDER BY `rank`
    """
    return get_db().query(sql, {'chart_id': chart_id})


def get_dashboard_chart_filter_relation(dashboard_chart_filter_id):
    """
    获取单图筛选关联表数据
    :param dashboard_chart_filter_id:
    :return:
    """
    sql = """
    SELECT `id`,`operator`,`col_value`
    FROM `dap_bi_dashboard_chart_filter_relation` WHERE `dashboard_chart_filter_id`=%(dashboard_chart_filter_id)s
    """
    params = {'dashboard_chart_filter_id': dashboard_chart_filter_id}
    with get_db() as db:
        return db.query(sql, params)


def get_penetrate_relation_by_chart_ids(chart_ids):
    sql = """
        select * from `dap_bi_dashboard_chart_penetrate_relation`
        where `dashboard_chart_id` in %(chart_ids)s
    """
    return get_db().query(sql, {"chart_ids": chart_ids})


def get_chart_by_chart_id(chart_id):
    sql = "select * from `dap_bi_dashboard_chart` where `id` = %(chart_id)s"
    with get_db() as db:
        return db.query_one(sql, {'chart_id': chart_id})


def get_chart_by_chart_ids(chart_ids):
    sql = "select * from `dap_bi_dashboard_chart` where `id` in %(chart_ids)s"
    params = {"chart_ids": chart_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_list_by_dashboard_id(dashboard_id):
    """
    获取报告的所有单图数据
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT * FROM `dap_bi_dashboard_chart` WHERE `dashboard_id`=%(dashboard_id)s
    """
    params = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def batch_get_chart_params_jump(chart_ids):
    """
    批量获取单图参数跳转配置
    :param chart_ids:
    :return:
    """
    chart_placeholder = []
    params = {}
    count = 0
    for chart_id in chart_ids:
        holder = 'chart_' + str(count)
        chart_placeholder.append('%(' + holder + ')s')
        params[holder] = chart_id
        count += 1
    sql = (
        "SELECT `dashboard_id`,`dashboard_chart_id`,`param_dataset_field_id`, "
        "`source_id`, `dashboard_filter_id`, `rank`, "
        "`relation_field_type`,`target_filter_field_id`,`target_filter_id` "
        "FROM `dap_bi_dashboard_chart_params_jump` WHERE `dashboard_chart_id` IN ({chart_placeholder})".format(
            chart_placeholder=','.join(chart_placeholder)
        )
    )
    with get_db() as db:
        return db.query(sql, params)


def batch_get_chart_params(chart_ids):
    """
    批量获取单图参数跳转配置
    :param chart_ids:
    :return:
    """
    chart_placeholder = []
    params = {}
    count = 0
    for chart_id in chart_ids:
        holder = 'chart_' + str(count)
        chart_placeholder.append('%(' + holder + ')s')
        params[holder] = chart_id
        count += 1
    sql = (
        "SELECT "
        "`param_id`,`dashboard_id`,`dashboard_chart_id`,`dataset_field_id`,`alias`, `rank` "
        "FROM "
        "`dap_bi_dashboard_chart_params` WHERE `dashboard_chart_id` IN ({chart_placeholder}) ".format(
            chart_placeholder=','.join(chart_placeholder)
        )
    )
    with get_db() as db:
        return db.query(sql, params)


def get_chart_id_list(dashboard_id):
    """
    获取报告下所有单图ID
    :param dashboard_id:
    :return:
    """
    sql = (
        'SELECT `c`.`id` '
        'FROM `dap_bi_dashboard_chart` AS `c` '
        'WHERE `c`.`dashboard_id`=%(dashboard_id)s AND (`parent_id` = \'\' or `parent_id` is null) '
    )
    params = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_list(dashboard_id):
    """
    获取看板下所有单图
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT
        `c`.`id`, `c`.`dashboard_id`, `c`.`name`, `c`.`chart_code`, `c`.`chart_type`, `c`.`content`, `c`.`filter_config`, `c`.`level_code`,
        `c`.`source`, `d`.`type` AS `source_type`, `d`.`content` AS `source_content`, `c`.`data_modified_on`, `c`.`position`,
        `c`.`sort_method`, `c`.`refresh_rate`, `c`.`display_item`, `c`.`desired_value`, `c`.`percentage`, `c`.`data_logic_type_code`,
        `c`.`penetrate`, `c`.`parent_id`, `c`.`config`, `c`.`style_type`, `c`.`default_value`, `c`.`layout`, `c`.`layout_extend`, `c`.`column_order`,
        `c`.`page_size`, `c`.`filter_relation`, `c`.`enable_subtotal`, `c`.`enable_subtotal_col`, `c`.`asset_id`,
        `c`.`enable_summary`, `c`.`enable_subtotal_col_summary`, `c`.`enable_subtotal_row`,
        `c`.`enable_subtotal_row_summary`, `c`.`subtotal_row_summary_formula_mode`, `c`.`aggregation`,
        `c`.`pre_comparison`, `c`.`export_type`, `c`.`reset_field_sort`, `c`.`chart_visible`,
        `c`.`parent_chart_id`, `c`.`child_rank`, `c`.`fixed_data_mode`, `c`.`fixed_manual_value`, `c`.`is_highdata`, `c`.`close_detail_mode`,
        `l`.`label_id`, `l`.`tmpl_id` AS `label_tmpl_id`, `l`.`org_id` AS `label_org_id`, `f`.`name` AS `label_name`
    FROM `dap_bi_dashboard_chart` AS `c`
    LEFT JOIN `dap_bi_label` AS `l` ON `c`.`source` = `l`.`label_id`
    LEFT JOIN `dap_bi_flow` AS `f` ON `l`.`label_id` = `f`.`id`
    LEFT JOIN `dap_bi_dataset` AS `d` ON `c`.`source` = `d`.`id`
    WHERE `c`.`dashboard_id` = %(dashboard_id)s
    """
    params = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_dims_by_chart_ids(chart_ids):
    """
    批量获取单图的维度指标（数据集）
    :param str chart_ids:
    :return:
    """
    sql = """
    SELECT
        `cd`.`dashboard_chart_id`, `cd`.`dim`, `cd`.`dashboard_jump_config`, `cd`.`formula_mode`, `cd`.`sub_type_code`,
        `cd`.`alias`, `cd`.`content`, `cd`.`rank`, `cd`.`note`, `cd`.`sort`, `cd`.`is_subtotal_cate`, `cd`.`dim_type`,
        `cd`.`display_format`, `cd`.`parent_id`
    FROM `dap_bi_dashboard_chart_dim` AS `cd`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `cd`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
    ORDER BY `cd`.`dashboard_chart_id`, `cd`.`rank`

    """

    with get_db() as db:
        rows = db.query(sql, {'chart_ids': chart_ids})
    for row in rows:
        if row["content"]:
            row["content"] = json.loads(row["content"])
        if row["display_format"]:
            row["display_format"] = json.loads(row["display_format"])
    return rows


def get_dashboard_chart_values(dashboard_id):
    sql = "select * from  `dap_bi_dashboard_filter_chart_default_values` where `dashboard_id` = :dashboard_id"
    with get_db() as db:
        return db.query(sql, {'dashboard_id': dashboard_id})


def get_dataset_comparisons_by_chart_ids(chart_ids):
    """
    批量获取单图的对比指标（数据集）
    :param str chart_ids:
    :return:
    """
    sql = """
    SELECT
        `cc`.`dashboard_chart_id`, `cc`.`dataset_field_id`, `cc`.`formula_mode`, `cc`.`alias`, `cc`.`content`, `cc`.`rank`
    FROM `dap_bi_dashboard_chart_comparison` AS `cc`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `cc`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
    ORDER BY `cc`.`dashboard_chart_id`, `cc`.`rank`
    """

    with get_db() as db:
        rows = db.query(sql, {'chart_ids': chart_ids})
    for row in rows:
        if row["content"]:
            row["content"] = json.loads(row["content"])
    return rows


def get_dataset_layers_by_chart_ids(chart_ids):
    """
    获取单图穿透的维度指标（数据集）
    :param str chart_ids:
    :return:
    """
    sql = """
    SELECT
        `cd`.`dashboard_chart_id`, `cd`.`dim`, `cd`.`formula_mode`, `cd`.`alias`, `cd`.`content`, `cd`.`rank`
    FROM `dap_bi_dashboard_chart_layers` AS `cd`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `cd`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
    ORDER BY `cd`.`dashboard_chart_id`, `cd`.`rank`
    """
    with get_db() as db:
        return db.query(sql, {'chart_ids': chart_ids})


def get_dataset_nums_by_chart_ids(chart_ids):
    """
    获取数据集单图的数值指标（包含计数维度）（数据集）
    :param str chart_ids:
    :return:
    """
    sql = """
    SELECT
        `cn`.`dashboard_chart_id`, `cn`.`num`, `cn`.`alias`, `cn`.`formula_mode`, `cn`.`rank`, `cn`.`sort`, `cn`.`note`,
        `cn`.`display_format`, `cn`.`axis_type`, `cn`.`chart_code`, `cn`.`dashboard_jump_config`, `cn`.`calc_null`,
        `cn`.`subtotal_formula_mode`, `cn`.`subtotal_col_formula_mode`, `cn`.`sub_type_code`,
        `cn`.`subtotal_row_formula_mode`, `cn`.`same_ring_ratio_config`, `cn`.`subtotal_col_formula_expression`, `cn`.`hidden`
    FROM `dap_bi_dashboard_chart_num` AS `cn`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `cn`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
    ORDER BY `cn`.`dashboard_chart_id`, `cn`.`rank`

    """
    with get_db() as db:
        return db.query(sql, {'chart_ids': chart_ids})


def get_chart_desires_by_chart_ids(chart_ids):
    """
    获取数据集单图的目标值数据列表（包含计数维度）（数据集）
    :param str chart_ids:
    :return:
    """
    sql = """
    SELECT
        `cd`.`dashboard_chart_id`, `cd`.`dataset_field_id`, `cd`.`mode`, `cd`.`value`, `cd`.`alias`, `cd`.`formula_mode`,
        `cd`.`rank`, `cd`.`sort`, `cd`.`display_format`, `cd`.`id`
    FROM `dap_bi_dashboard_chart_desire` AS `cd`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `cd`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
    ORDER BY `cd`.`dashboard_chart_id`, `cd`.`rank`
    """
    with get_db() as db:
        return db.query(sql, {'chart_ids': chart_ids})


def get_map_by_chart_ids(chart_ids):
    """
    获取单图地图配置
    :param chart_ids:
    :return:
    """
    sql = """
    SELECT
        `cm`.`dashboard_chart_id`, `cm`.`address`, `cm`.`alias`, `cm`.`sub_chart_code`, `cm`.`content`
    FROM `dap_bi_dashboard_chart_map` AS `cm`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `cm`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
    """
    with get_db() as db:
        return db.query(sql, {'chart_ids': chart_ids})


def get_dataset_markline_by_chart_ids(chart_ids):
    """
    获取单图的markline（标线）数据（数据集）
    :param str chart_ids:
    :return:
    """
    sql = """
    SELECT
        `cm`.`dashboard_chart_id`, `cm`.`name`, `cm`.`mode`, `cm`.`num`, `cm`.`formula_mode`, `cm`.`value`, `cm`.`axis_type`,
        `cm`.`config`, `cm`.`percentile`
    FROM `dap_bi_dashboard_chart_markline` AS `cm`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `cm`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
    ORDER BY `cm`.`rank`
    """
    with get_db() as db:
        return db.query(sql, {'chart_ids': chart_ids})


def get_dataset_field_sorts_by_chart_ids(chart_ids):
    """
    获取单图的field_sorts数据
    :param str chart_ids:
    :return:
    """
    sql = '''SELECT
        `dcfs`.`id`, `dcfs`.`dashboard_id`, `dcfs`.`dashboard_chart_id`, `dcfs`.`dataset_field_id`,
        `dcfs`.`field_source`, `dcfs`.`sort`, `dcfs`.`content`, `dcfs`.`weight`
    FROM `dap_bi_dashboard_chart_field_sort` AS `dcfs`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `dcfs`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
'''
    params = {"chart_ids": chart_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_colour_by_chart_ids(chart_ids):
    """
    获取单图的颜色数据（数据集）
    :param str chart_ids:
    :return:
    """
    sql = """
    SELECT
        `cc`.`dashboard_chart_id`, `cc`.`colour_content`, `cc`.`num`
    FROM `dap_bi_dashboard_chart_colour` AS `cc`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `cc`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
    """
    with get_db() as db:
        return db.query(sql, {'chart_ids': chart_ids})


def get_dataset_filter_by_chart_ids(chart_ids):
    """
    获取单图的过滤数据（数据集）
    :param str chart_id:
    :return:
    """
    sql = """
    SELECT
        `cf`.`dashboard_chart_id`, `cf`.`operator`, `cf`.`col_value`, `cf`.`dataset_field_id`, `c`.`source` AS `dataset_id`,
        `c`.`filter_relation`, `cf`.`id` AS `filter_id`
    FROM `dap_bi_dashboard_chart_filter` AS `cf`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `cf`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN (:chart_ids)
    """
    with get_db() as db:
        return db.query(sql, {'chart_ids': chart_ids})


def get_var_relation_by_chart_ids(chart_id_list):
    """
    根据单图id获取变量关系数据
    :param chart_id_list:
    :return:
    """
    if not chart_id_list:
        return []
    sql = """
    SELECT
        `id`, `chart_initiator_id`, `field_initiator_id`, `dashboard_id`, `var_id`, `var_dataset_id` AS `dataset_id`,
        `initiator_type`, `var_dim_obj`
    FROM `dap_bi_dashboard_dataset_vars_relation`
    WHERE `chart_initiator_id` IN (:chart_id_list)
    """
    params = {"chart_id_list": chart_id_list}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_new_linkage(chart_id):
    '''
    获取新版单图联动关系
    '''
    sql = '''
    SELECT
        `id`,
        `chart_id` AS `chart_initiator_id`,
        `dataset_id`,
        `dataset_field_id` AS `field_initiator_id`
    FROM
        `dap_bi_dashboard_linkage`
    WHERE
        `chart_id` = %(chart_id)s
    '''
    params = {"chart_id": chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_new_linkage_relation(linkid):
    '''
    获取新版单图详细的联动关系
    '''

    sql = '''
    SELECT
        `id`,
        `chart_responder_id`,
        `field_responder_id`,
        `dataset_responder_id` AS `related_dataset_id`
    FROM
        `dap_bi_dashboard_linkage_relation`
    WHERE
        `link_id` = %(linkid)s
    '''
    params = {"linkid": linkid}

    with get_db() as db:
        return db.query(sql, params)


def get_chart_new_filter(chart_id):
    '''
    获取新版单图间筛选
    '''
    sql = '''
    SELECT
        `id`,
        `chart_id` AS `chart_initiator_id`,
        `dataset_id`,
        `dataset_field_id` AS `field_initiator_id`,
        `indicator_dim_obj`,
        `filter_type`,
        `available`,
        `initiator_source`
    FROM
        `dap_bi_dashboard_filter_chart`
    WHERE
        `chart_id` = %(chart_id)s
    '''

    params = {"chart_id": chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_new_filter_relation(filter_id):
    '''
    获取新版单图详细的筛选关系
    '''

    sql = """
    SELECT
        `id`,
        `chart_responder_id`,
        `field_responder_id`,
        `dataset_responder_id` AS `related_dataset_id`
    FROM
        `dap_bi_dashboard_filter_chart_relation`
    WHERE
        `filter_id` = %(filter_id)s
    """
    params = {"filter_id": filter_id}

    with get_db() as db:
        return db.query(sql, params)


def get_chart_params(chart_id):
    """
    获取单图参数
    :param chart_id:
    :return:
    """
    sql = (
        "SELECT "
        "`param_id`,`dashboard_id`,`dashboard_chart_id`,`dataset_field_id`,`alias`, `rank` "
        "FROM "
        "`dap_bi_dashboard_chart_params` WHERE `dashboard_chart_id`=%(chart_id)s "
    )
    params = {'chart_id': chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_chart_params(chart_ids):
    """
    获取单图参数
    :param chart_ids:
    :return:
    """
    sql = """
    SELECT
        `dcp`.`param_id`, `dcp`.`dashboard_chart_id`, `dcp`.`dataset_field_id`, `dcp`.`alias`, `dcp`.`rank`
    FROM `dap_bi_dashboard_chart_params` AS `dcp`
    INNER JOIN `dap_bi_dashboard_chart` AS `c` ON `dcp`.`dashboard_chart_id` = `c`.`id` AND `c`.`id` IN %(chart_ids)s
    ORDER BY `dcp`.`rank`
    """
    params = {"chart_ids": chart_ids}
    with get_db() as db:
        return db.query(sql, params)


def update_dashboard_modified_time(dashboard_id):
    """
    更新dashboard的最后修改时间为当前时间，同时也会修改上一级的时间
    :param dashboard_id:
    :return:
    """
    with get_db() as db:
        level_code = db.query_one(
            "select `level_code` from `dap_bi_dashboard` where `id` = %(dashboard_id)s", {"dashboard_id": dashboard_id}
        )

        if level_code and level_code["level_code"]:
            level_code_s = level_code["level_code"].split("-")[:-1]

            _tmp_level_code = ""
            all_level_code = []
            for lc in level_code_s:
                _tmp_level_code += lc + "-"
                all_level_code.append(_tmp_level_code)

            sql = "update `dap_bi_dashboard` set `modified_on` = CURRENT_TIMESTAMP() where `level_code` in %(all_level_code)s"
            db.exec_sql(sql, {'all_level_code': all_level_code})


def get_penetrate_by_chart_id(level_code):
    """
    获取单图的穿透单图
    :param str level_code:
    :return:
    """
    # pylint: disable=W1404
    sql = 'SELECT `c`.`id` ' 'FROM `dap_bi_dashboard_chart` AS `c` ' 'WHERE `level_code`  like %(level_code_path)s ORDER BY level_code '

    params = {'level_code_path': level_code + '%'}
    with get_db() as db:
        return db.query(sql, params)


def delete_selector_chart(chart_id):
    """
    删除关联单图关系
    :param chart_id:
    :return:
    """
    result = 0
    for col in ('chart_initiator_id', 'chart_responder_id'):
        result += repository.delete_data('dap_bi_dashboard_chart_selector', {col: chart_id})
    return result


def get_chart_selector_list_by_chart_id(chart_id):
    """
    通过单图id获取关联单图和被关联单图关系记录
    :param chart_id:
    :return:
    """
    sql = (
        'select `id`, `chart_initiator_id`, `chart_responder_id`, `dashboard_id`, `dataset_id`, `is_same_dataset` '
        'from `dap_bi_dashboard_chart_selector` where `chart_initiator_id`=%(id)s or `chart_responder_id`=%(id)s'
    )
    params = {"id": chart_id}
    with get_db() as db:
        return db.query(sql, params)


def delete_chart_selector_field(selector_id_list):
    """
    删除关联单图字符关系
    :param list selector_id_list: dashboard_chart_selector表id列表
    :return:
    """
    sql = 'delete from `dap_bi_dashboard_chart_selector_field` where `selector_id` in (:selector_id_list)'
    with get_db() as db:
        return db.exec_sql(sql, {'selector_id_list': selector_id_list})


def delete_chart_selector_field_new(selector_id_list):
    """
    删除关联单图字段关系
    :param list selector_id_list: dashboard_chart_selector表id列表
    :return:
    """
    sql = 'delete from `dap_bi_dashboard_chart_selector_field` where `selector_id` in %(selector_id_list)s'
    with get_db() as db:
        return db.exec_sql(sql, params={"selector_id_list": selector_id_list})


def delete_component_filter_chart(chart_id):
    """
    删除筛选组件配置关联关系
    :param chart_id:
    :return:
    """
    result = 0
    for col in ('chart_initiator_id', 'chart_responder_id'):
        result += repository.delete_data('dap_bi_dashboard_component_filter', {col: chart_id})
    return result


def delete_component_filter_field(chart_id):
    """
    删除筛选组件配置字段关联关系
    :param chart_id:
    :return:
    """
    sql = 'delete from `dap_bi_dashboard_component_filter_field_relation` where `chart_id`=%(id)s'
    params = {"id": chart_id}
    with get_db() as db:
        return db.exec_sql(sql, params)


def get_chart_by_table(table_name, dashboard_ids):
    """
    获取报告下所有单图
    :param dashboard_id:
    :return:
    """
    sql = 'SELECT * ' 'FROM `' + table_name + '` WHERE `dashboard_chart_id` in ' + dashboard_ids
    with get_db() as db:
        return db.query(sql)


def get_dashboard_chart_selector_by_dashboard_id(dashboard_id):
    """
    根据dashboard_id获取dashboard_chart_selector表数据
    :param dashboard_id:
    :return:
    """
    sql = 'SELECT * FROM `dap_bi_dashboard_chart_selector` WHERE `dashboard_id` = %(dashboard_id)s '
    params = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_id_list_by_dataset_id(dataset_id):
    """
    根据dataset_id获取chart_id的列表
    :param dataset_id: dataset_id
    :return:
    """
    sql = 'select `id` from `dap_bi_dashboard_chart` where `source` =%(id)s'
    with get_db() as db:
        chart_id_list = db.query(sql, {'id': dataset_id})
    return chart_id_list


def get_component_filter_by_chart_ids(chart_ids: list):
    """
    component_filter表数据
    :param chart_ids:
    :return:
    """
    if not chart_ids:
        return []
    sql = '''select * from `dap_bi_dashboard_component_filter` where `chart_initiator_id` in %(chart_ids)s'''
    params = {'chart_ids': chart_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_component_filter_field_relation_by_chart_ids(chart_ids: list):
    """
    component_filter_field_relation表数据
    :param chart_ids:
    :return:
    """
    if not chart_ids:
        return []
    sql = '''select * from `dap_bi_dashboard_component_filter_field_relation` where `chart_id` in %(chart_ids)s'''
    params = {'chart_ids': chart_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_selector_field_list_by_dashboard_ids(dashboard_ids):
    """
    通过报告ids批量获取单图联动关系
    :param dashboard_ids: [dashboard_id, ...]
    :return:
    """
    sql = '''select `id`, `selector_id`, `chart_id`, `dashboard_id`, `field_initiator_id`, `field_responder_id`
    from `dap_bi_dashboard_chart_selector_field` where `dashboard_id` in %(dashboard_ids)s
    '''
    params = {'dashboard_ids': dashboard_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_field_sorts_by_chart_id(dashboard_chart_id):
    """
    根据单图id获取字段排序配置数据
    :param dashboard_chart_id:
    :return:
    """
    sql = """
          SELECT
          `dashboard_chart_id`, `dataset_field_id`, `field_source`, `sort`, `content`, `weight`
          FROM `dap_bi_dashboard_chart_field_sort` WHERE `dashboard_chart_id`=%(dashboard_chart_id)s order by `weight` desc
          """
    params = {'dashboard_chart_id': dashboard_chart_id}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_visible_triggers_by_chart_id(dashboard_chart_id):
    """
    :param dashboard_id:
    :return:
    """
    if not dashboard_chart_id:
        return []
    sql = """select `id`,`dashboard_id`,`dashboard_chart_id` as `chart_id`,`actions`,`conditions`, `type`
        from `dap_bi_dashboard_chart_visible_triggers` where `dashboard_chart_id`=%(dashboard_chart_id)s """
    params = {"dashboard_chart_id": dashboard_chart_id}
    with get_db() as db:
        return db.query(sql, params)