#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/26 19:14
# <AUTHOR> caoxl
# @File     : editor_repository.py
import json
import logging
import traceback

from base import repository
from base.models import BaseModelEncoder

from dmplib.saas.project import get_db
from dmplib.hug import g

logger = logging.getLogger(__name__)


def batch_operate_editor_modes(del_models, insert_models, update_models, meta=None):
    """
    批量操作editor model 的删除和插入操作 (同一个事务)
    :param del_models:
    :param insert_models:
    :param update_models:
    :param meta: 最新的元数据
    :return:
    """
    _record_metadata_change(del_models, insert_models, update_models)
    with get_db() as db:
        errors = []
        try:
            db.begin_transaction()
            batch_operate_editor_modes_no_commit(db, del_models, insert_models, update_models, meta)
            db.commit()
            return True, errors
        except Exception as e:
            msg = str(e)
            errors.append(msg)
            logger.exception(msg)
            db.rollback()
            return False, errors


def _record_metadata_change(del_models, list_insert_table2dict, update_models):
    g.del_models = del_models
    g.update_models = update_models
    g.list_insert_table2dict = list_insert_table2dict


def batch_operate_editor_modes_no_commit(db, del_models, insert_models, update_models, meta=None):
    """
    批量操作editor model 的删除和插入操作 (同一个事务)
    :param del_models:
    :param insert_models:
    :param update_models:
    :param meta: 最新的元数据
    :return:
    """
    del_model = None
    update_model = None
    table = ''
    try:
        commit = False
        for del_model in del_models:
            db.delete(table=del_model.get_table_name(), condition=del_model.get_where(), commit=commit)
        for insert_model in insert_models:
            exist = repository.get_data(insert_model.get_table_name(), insert_model.get_where())
            if not exist:
                db.insert(insert_model.get_table_name(), data=insert_model.get_dict(), commit=commit)
        for update_model in update_models:
            db.update(
                table=update_model.get_table_name(),
                data=update_model.get_dict(),
                condition=update_model.get_where(),
                commit=commit,
                with_none=True
            )
        if meta:
            db.delete('dap_bi_dashboard_metadata_history', condition={'dashboard_id': meta['dashboard_id']})
            db.insert('dap_bi_dashboard_metadata_history', data=meta, commit=commit)
            # db.replace_multi_data(table='dashboard_metadata_history', list_data=[meta], fields=list(meta.keys()),
            #                       commit=commit)
    except Exception as e:
        logger.error(f'编辑元数据SQL执行错误：{traceback.format_exc()}')
        msg = str(e)
        if update_model:
            msg = f'更新 {update_model.get_table_name()} 失败，原因：{str(e)}'
        elif table:
            msg = f'插入数据表 {table} 失败，原因：{str(e)}'
        elif del_model:
            msg = f'删除 {del_model.get_table_name()} 数据失败，原因：{str(e)}'
        raise Exception(msg)


def batch_operate_update_editor_models(db, insert_models):
    """
    批量操作editor model 的删除和插入操作 (同一个事务)
    :param db:
    :param insert_models:
    :return:
    """
    table = ''
    try:
        commit = False
        for insert_model in insert_models:
            exist = repository.get_data(insert_model.get_table_name(), insert_model.get_where())
            if not exist:
                db.insert(insert_model.get_table_name(), data=insert_model.get_dict(), commit=commit)
    except Exception as e:
        logger.error(f'编辑元数据SQL执行错误：{traceback.format_exc()}')
        msg = str(e)
        if table:
            msg = f'插入数据表 {table} 失败，原因：{str(e)}'
        raise Exception(msg)


def deal_pymysql_dict_value(one_data):
    for key, val in one_data.items():
        if isinstance(val, (list, tuple, dict)):
            one_data[key] = json.dumps(val, cls=BaseModelEncoder)


def get_dashboard_pre_metadata(dashboard_id):
    """
    获取指定报告的前一条元数据
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT `dashboard_id`, `metadata`, `version`
    FROM
    `dap_bi_dashboard_metadata_history`
    WHERE `dashboard_id`=%(dashboard_id)s
    """
    params = {'dashboard_id': dashboard_id}
    with get_db() as db:
        result = db.query_one(sql, params)
        if result:
            return json.loads(result.get('metadata'))
        return False
