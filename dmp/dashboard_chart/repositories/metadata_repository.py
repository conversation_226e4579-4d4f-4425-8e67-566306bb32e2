#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/22

"""
元数据表数据查询
"""

# ---------------- 标准模块 ----------------
from base import repository

# ---------------- 业务模块 ----------------
from base.dmp_constant import component_table_map
from dmplib.conf_constants import IS_GRAY_ENV
from dmplib import config
from dmplib.saas.project import get_db


def get_dashboard_data_by_id(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT `id`,`type`,`theme`,`name`,`description`,`share_secret_key`,`layout`,`type_access_released`,
          `background`,`status`,`cover`,`scale_mode`,`platform`,`rank`,
          `grid_padding`, `biz_code`, `level_code`, `create_type`, `new_layout_type`, `parent_id`, `is_show_mark_img`,
          `terminal_type`, `main_external_subject_id`, `application_type`, `external_subject_ids`,
          `line_height`,`smart_beauty_status`,`dataset_id`, `analysis_type`,`external_url`, `auto_play`
          FROM dap_bi_dashboard
          WHERE id=%(dashboard_id)s
    """
    with get_db() as db:
        return db.query_one(sql, {"dashboard_id": dashboard_id})


def get_dashboard_name_by_id(dashboard_id):
    sql = "select `name` from dap_bi_dashboard where id=%(dashboard_id)s"
    with get_db() as db:
        return db.query_one(sql, {"dashboard_id": dashboard_id})

def get_external_subject_name_by_id(external_subject_id):
    sql = "select `name` from dap_bi_dataset_external_subject where id=%(external_subject_id)s"
    with get_db() as db:
        return db.query_one(sql, {'external_subject_id': external_subject_id})

def get_snapshot_name_by_id(dashboard_id):
    sql = "select `name` from dap_bi_dashboard_released_snapshot_dashboard where id=%(dashboard_id)s"
    with get_db() as db:
        return db.query_one(sql, {"dashboard_id": dashboard_id})

def batch_get_dashboard_data(dashboard_id_list):
    """

    :param dashboard_id_list:
    :return:
    """
    sql = """SELECT `id`,`theme`,`name`,`description`,`share_secret_key`,`layout`,`type_access_released`,
          `background`,`status`,`cover`,`scale_mode`,`platform` FROM dap_bi_dashboard WHERE id in %(dashboard_id_list)s
          """
    params = {"dashboard_id_list": dashboard_id_list}
    with get_db() as db:
        return db.query(sql, params)


def batch_get_dashboard_name(dashboard_id_list):
    """

    :param dashboard_id_list:
    :return:
    """
    sql = """SELECT `id`,`name` FROM dap_bi_dashboard WHERE id in %(dashboard_id_list)s
          """
    params = {"dashboard_id_list": dashboard_id_list}
    with get_db() as db:
        return db.query(sql, params)


def batch_get_dims(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT
        dcd.`id`,
        dcd.`dashboard_chart_id`,
        dcd.`alias`,
        dcd.`content`,
        dcd.`formula_mode`,
        dcd.`rank`,
        dcd.`sort`,
        dcd.`dim`,
        dcd.`note`,
        dcd.`is_subtotal_cate`,
        dcd.`dim_type`,
        dcd.`display_format`,
        dcd.`parent_id`,
        dcd.`sub_type_code`,
        dcd.`relation_fields`
        FROM
        dap_bi_dashboard_chart_dim AS `dcd`
        INNER JOIN dap_bi_dashboard_chart AS dc ON dcd.`dashboard_chart_id` = dc.`id`
        AND dc.`dashboard_id` = '{dashboard_id}' ORDER BY dcd.`rank`
          """.format(
        dashboard_id=dashboard_id
    )
    params = dict()
    with get_db() as db:
        return db.query(sql, params)


def batch_get_comparisons(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT dcc.`id`,dcc.`alias`,dcc.`content`,dcc.`dashboard_chart_id`,dcc.`formula_mode`,dcc.`rank`,dcc.`sort`,
          dcc.`dataset_field_id` FROM dap_bi_dashboard_chart_comparison AS dcc
          INNER JOIN dap_bi_dashboard_chart  AS dc ON dcc.`dashboard_chart_id`=dc.`id` AND dc.`dashboard_id`='{dashboard_id}'
          ORDER BY dcc.`rank`
          """.format(
        dashboard_id=dashboard_id
    )
    params = dict()
    with get_db() as db:
        return db.query(sql, params)


def batch_get_filters(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """
          SELECT dcf.`id`,dcf.`col_name`,dcf.`col_value`,dcf.`operator`,dcf.`dashboard_chart_id`,dcf.`dataset_field_id`,
          dcf.`id` as filter_id FROM dap_bi_dashboard_chart_filter AS dcf
          INNER JOIN dap_bi_dashboard_chart  AS dc ON dcf.dashboard_chart_id=dc.id AND dc.dashboard_id='{dashboard_id}'
          ORDER BY dcf.id
          """.format(
        dashboard_id=dashboard_id
    )
    params = dict()
    with get_db() as db:
        return db.query(sql, params)


def batch_get_nums_and_zaxis(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT cn.`id`,cn.`dashboard_chart_id`,cn.`num`,cn.`alias`,cn.`formula_mode`,cn.`rank`,cn.`sort`,cn.`note`,cn.`calc_null`,
          cn.`display_format`,cn.`axis_type`,cn.`chart_code`,cn.`dashboard_jump_config`,cn.`subtotal_formula_mode`,
          cn.`subtotal_col_formula_mode`, cn.`subtotal_row_formula_mode`, cn.`same_ring_ratio_config`, cn.`hidden`,
          cn.`subtotal_col_formula_expression`, `relation_fields`, cn.`sub_type_code`
          FROM dap_bi_dashboard_chart_num AS cn
          INNER JOIN dap_bi_dashboard_chart  AS c ON cn.`dashboard_chart_id`=c.`id`  AND c.`dashboard_id`='{dashboard_id}'
          ORDER BY cn.`dashboard_chart_id`,cn.`rank`""".format(
        dashboard_id=dashboard_id
    )
    params = dict()
    with get_db() as db:
        return db.query(sql, params)


def get_params_by_chart_id(chart_id):
    """

    :param chart_id:
    :return:
    """
    sql = """SELECT dcp.`alias`,dcp.`dashboard_chart_id`,dcp.`dashboard_id`,dcp.`dataset_field_id`,
          dcp.`param_id` as id,dcp.`rank`
          FROM dap_bi_dashboard_chart_params AS dcp
          INNER JOIN dap_bi_dashboard_chart  AS dc ON dcp.`dashboard_chart_id`=dc.`id` AND dc.`id`='{chart_id}'
          """.format(
        chart_id=chart_id
    )
    params = dict()
    with get_db() as db:
        return db.query(sql, params)


def batch_get_params(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT dcp.`alias`,dcp.`dashboard_chart_id`,dcp.`dashboard_id`,dcp.`dataset_field_id`,
          dcp.`param_id` as id,dcp.`rank`
          FROM dap_bi_dashboard_chart_params AS dcp
          INNER JOIN dap_bi_dashboard_chart  AS dc ON dcp.`dashboard_chart_id`=dc.`id` AND dc.`dashboard_id`='{dashboard_id}'
          ORDER BY dcp.`rank`
          """.format(
        dashboard_id=dashboard_id
    )
    params = dict()
    with get_db() as db:
        return db.query(sql, params)


def batch_get_desires(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT dcd.`id`,dcd.`alias`,dcd.`dashboard_chart_id`,dcd.`dataset_field_id`,
          dcd.`mode`,dcd.`rank`,dcd.`sort`,dcd.`formula_mode`,dcd.`value`,dcd.`display_format`
          FROM dap_bi_dashboard_chart_desire AS dcd
          INNER JOIN dap_bi_dashboard_chart  AS dc ON dcd.`dashboard_chart_id`=dc.`id` AND dc.`dashboard_id`='{dashboard_id}'
          ORDER BY dcd.`rank`
          """.format(
        dashboard_id=dashboard_id
    )
    params = dict()
    with get_db() as db:
        return db.query(sql, params)


def batch_get_marklines(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT dcm.`id`,dcm.`dashboard_chart_id`,dcm.`formula_mode`,dcm.`mode`,dcm.`name`,dcm.`value`,dcm.`axis_type`,dcm.`num`,
                    dcm.`config`, dcm.`percentile`
          FROM dap_bi_dashboard_chart_markline AS dcm
          INNER JOIN dap_bi_dashboard_chart  AS dc ON dcm.`dashboard_chart_id`=dc.`id` AND dc.`dashboard_id`='{dashboard_id}'
          ORDER BY dcm.`rank`
          """.format(
        dashboard_id=dashboard_id
    )
    params = dict()
    with get_db() as db:
        return db.query(sql, params)


def batch_get_field_sorts(dashboard_id):
    """
    批量获取字段排序数据
    :param dashboard_id:
    :return:
    """
    sql = """SELECT dcfs.`id`,dcfs.`dashboard_chart_id`,dcfs.`dataset_field_id`,
          dcfs.`field_source`,dcfs.`sort`,dcfs.`content`,dcfs.`weight`
          FROM dap_bi_dashboard_chart_field_sort AS dcfs
          INNER JOIN dap_bi_dashboard_chart  AS dc ON dcfs.`dashboard_chart_id`=dc.`id` AND dc.`dashboard_id`='{dashboard_id}'
          """.format(
        dashboard_id=dashboard_id
    )
    params = dict()
    with get_db() as db:
        return db.query(sql, params)


def get_filters_by_dashboard_id(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT dcf.`chart_initiator_id`,dcf.`dataset_id`,dcf.`chart_responder_id`,
          dcffr.`field_initiator_id`,dcffr.`field_responder_id`,dcf.`is_same_dataset`,
          dcf.`id` as filter_id, dcffr.`id` as filter_relation_id
          FROM dap_bi_dashboard_chart AS dc
          INNER JOIN dap_bi_dashboard_component_filter  AS dcf
          ON dcf.`chart_initiator_id`=dc.`id` AND dc.`dashboard_id`=%(dashboard_id)s
          LEFT JOIN dap_bi_dashboard_component_filter_field_relation AS dcffr ON dcf.`id`=dcffr.`filter_id`
          WHERE dcf.`dataset_id` IS NOT NULL
          """
    with get_db() as db:
        return db.query(sql, {"dashboard_id": dashboard_id})


def get_linkages_by_dashboard_id(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT dcs.`chart_initiator_id`,dcs.`dataset_id`,dcs.`chart_responder_id`,
          dcsf.`field_initiator_id`,dcsf.`field_responder_id`,dcs.`is_same_dataset`,
          dcs.`id` as selector_id, dcsf.`id` as selector_field_id
          FROM dap_bi_dashboard_chart AS dc
          INNER JOIN dap_bi_dashboard_chart_selector  AS dcs ON dcs.`chart_initiator_id`=dc.`id` AND dc.`dashboard_id`=%(dashboard_id)s
          LEFT JOIN dap_bi_dashboard_chart_selector_field AS dcsf ON dcs.`id`=dcsf.`selector_id`
          """
    with get_db() as db:
        return db.query(sql, {"dashboard_id": dashboard_id})


def get_penetrates_by_chart_id(dashboard_id):
    return repository.get_list("dap_bi_dashboard_chart", {"dashboard_id": dashboard_id}, "id as chart_id,parent_id")


def get_new_linkage_by_dashboard_id(dashboard_id):
    """
    新编辑器的联动关系查询
    """

    sql = """
    SELECT
	cdl.`id` AS chart_linkage_id,
	cdl.`chart_id` AS chart_initiator_id,
	cdl.`dataset_id` AS chart_initiator_dataset_id,
	cdl.`dataset_field_id` AS chart_initiator_filed_id,
	cdlf.`id` AS chart_linkage_relation_id,
	cdlf.`chart_responder_id` AS chart_responder_id,
	cdlf.`field_responder_id` AS chart_responder_field_id,
	cdlf.`dataset_responder_id` AS chart_responder_dataset_id
    FROM
	dap_bi_dashboard_chart AS dc
	INNER JOIN dap_bi_dashboard_linkage AS cdl ON cdl.`chart_id` = dc.`id`
	AND dc.`dashboard_id` = '{dashboard_id}'
	left JOIN dap_bi_dashboard_linkage_relation AS cdlf ON cdl.`id` = cdlf.`link_id`
    """.format(
        dashboard_id=dashboard_id
    )

    with get_db() as db:
        return db.query(sql, {})


def get_new_filter_by_dashboard_id(dashboard_id):
    """
    新编辑器的联动关系查询
    """

    sql = """
    SELECT
	cdl.id AS chart_linkage_id,
	cdl.chart_id AS chart_initiator_id,
	cdl.dataset_id AS chart_initiator_dataset_id,
	cdl.dataset_field_id AS chart_initiator_filed_id,
	cdl.filter_type AS filter_type,
	cdl.indicator_dim_obj AS indicator_dim_obj,
	cdl.available,
	cdl.initiator_source,
	cdlf.id AS chart_linkage_relation_id,
	cdlf.chart_responder_id AS chart_responder_id,
	cdlf.field_responder_id AS chart_responder_field_id,
	cdlf.dataset_responder_id AS chart_responder_dataset_id
    FROM
	dap_bi_dashboard_chart AS dc
	INNER JOIN dap_bi_dashboard_filter_chart AS cdl ON cdl.chart_id = dc.id
	AND dc.dashboard_id = '{dashboard_id}'
	left JOIN dap_bi_dashboard_filter_chart_relation AS cdlf ON cdl.id = cdlf.filter_id
    """.format(
        dashboard_id=dashboard_id
    )
    with get_db() as db:
        return db.query(sql, {"dashboard_id": dashboard_id})


def get_penetrates_by_dashboard_id(dashboard_id):
    """

    :param dashboard_id:
    :return:
    """
    sql = """SELECT id as chart_id,parent_id
          FROM dap_bi_dashboard_chart AS dc
          WHERE dc.dashboard_id=%(dashboard_id)s
          AND (parent_id != '' OR dc.id IN
          (SELECT parent_id FROM dap_bi_dashboard_chart WHERE dashboard_id=%(dashboard_id)s))
          """
    params = {"dashboard_id": dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_jump_relaitons(dashboard_chart_id, jump_config_id):
    """
    :param dashboard_chart_id:
    :param jump_config_id:
    :return:
    """
    return repository.get_list(
        "dap_bi_dashboard_jump_relation",
        {"dashboard_chart_id": dashboard_chart_id, "jump_config_id": jump_config_id},
        ("dashboard_filter_id,dataset_field_id,dashboard_filter_id, filter_type, " # noqa
         "relation_field_type, target_filter_id, target_filter_field_id,global_params_id"), # noqa
    )


def get_var_jump_relaitons(jump_config_id):
    """

    :param jump_config_id:
    :return:
    """
    return repository.get_list(
        "dap_bi_dashboard_vars_jump_relation",
        {"jump_config_id": jump_config_id},
        ("dashboard_chart_id,dataset_id,dataset_field_id,dashboard_filter_id,var_id," # noqa
        "relation_field_type, target_filter_id, target_filter_field_id,global_params_id"), # noqa
    )


def get_fixed_var_jump_relaitons(dashboard_chart_id, jump_config_id):
    """

    :param jump_config_id:
    :return:
    """
    return repository.get_list(
        "dap_bi_dashboard_fixed_var_jump_relation",
        {"dashboard_chart_id": dashboard_chart_id, "jump_config_id": jump_config_id},
        ("dashboard_chart_id,dataset_field_id,dashboard_filter_id,var_name,var_value," # noqa
         "relation_field_type, target_filter_id,target_filter_field_id,global_params_id"), # noqa
    )


def get_filter_chart_jump_relaitons(dashboard_chart_id, jump_config_id):
    """

    :param jump_config_id:
    :return:
    """
    return repository.get_list(
        "dap_bi_dashboard_filter_chart_jump_relation",
        {"dashboard_chart_id": dashboard_chart_id, "jump_config_id": jump_config_id},
        ("dashboard_chart_id,dataset_field_id,filter_chart_id,global_params_id,date_filter_chart_flag"), # noqa
    )


def get_global_params_jump_relaitons(dashboard_chart_id, jump_config_id):
    """

    :param jump_config_id:
    :return:
    """
    return repository.get_list(
        "dap_bi_dashboard_global_params_jump_relation",
        {"dashboard_chart_id": dashboard_chart_id, "jump_config_id": jump_config_id},
        ("dashboard_chart_id,initiator_global_params_id,global_params_id"), # noqa
    )


def get_param_jump_relaitons(dashboard_chart_id, source_id):
    """

    :param dashboard_chart_id:
    :param source_id:
    :return:
    """
    return repository.get_list(
        "dap_bi_dashboard_chart_params_jump",
        {"dashboard_chart_id": dashboard_chart_id, "source_id": source_id},
        ("dashboard_chart_id,dashboard_filter_id,param_dataset_field_id," # noqa
         "relation_field_type, target_filter_id, target_filter_field_id,global_params_id"), # noqa
    )


def batch_get_component_data():
    component_table = "dap_bi_component"
    if IS_GRAY_ENV:
        component_table = component_table_map.get("component")
    return repository.get_list(component_table, {}, ["package", "data_logic_type_code"], from_config_db=True)


def batch_get_chart_code_by_snapshot_id(snapshot_id):
    return repository.get_list(
        "dap_bi_dashboard_released_snapshot_chart", {"snapshot_id": snapshot_id}, "distinct(chart_code) as chart_code"
    )


def batch_get_global_params(global_params_ids):
    if not global_params_ids:
        return []
    return repository.get_list(
        "dap_bi_dashboard_jump_global_params", {"id": global_params_ids}, fields=['*']
    )


def batch_get_released_dashbaoard_data_with_conn(fields, snapshot_id, conn):
    """

    :param fields:
    :param snapshot_id:
    :param conn:
    :return:
    """
    sql = """select {fields} from dap_bi_dashboard_released_snapshot_dashboard where snapshot_id='{snapshot_id}'
             """.format(
        fields=",".join(fields), snapshot_id=snapshot_id
    )
    return conn.query(sql)


def get_released_chart_data_with_conn(fields, snapshot_id, screen_id, conn):
    """
    获取已发布单图配置数据
    :param snapshot_id:
    :param screen_id:
    :param conn:
    :return:
    """
    sql = """select {fields} from dap_bi_dashboard_released_snapshot_chart where snapshot_id='{snapshot_id}' and
          dashboard_id='{dashboard_id}'
          """.format(
        fields=",".join(fields), snapshot_id=snapshot_id, dashboard_id=screen_id
    )
    return conn.query(sql)


def get_released_dashboard_data_with_conn(fields, snapshot_id, screen_id, conn):
    """

    :param snapshot_id:
    :param screen_id:
    :param conn:
    :return:
    """
    sql = """select {fields} from dap_bi_dashboard_released_snapshot_dashboard where snapshot_id='{snapshot_id}' and
          id='{dashboard_id}'
          """.format(
        fields=",".join(fields), snapshot_id=snapshot_id, dashboard_id=screen_id
    )
    return conn.query_one(sql)


def batch_get_dashboard_filter_config_info(dashboard_filter_ids):
    """
    批量获取报告级筛选配置信息
    :param dashboard_filter_ids:
    :return:
    """
    sql = """select df.id,df.dashboard_id,df.main_dataset_field_id,df2.dataset_id,df2.alias_name,df2.col_name,
          df2.origin_col_name,d.name as dataset_name
          from dap_bi_dashboard_filter df
          inner join dap_bi_dataset_field df2 on df.main_dataset_field_id=df2.id
          inner join dap_bi_dataset d on df2.dataset_id=d.id
          where df.id in %(dashboard_filter_ids)s"""
    params = {"dashboard_filter_ids": dashboard_filter_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_visible_triggers_by_dashboard_id(dashboard_id):
    """
    :param dashboard_id:
    :return:
    """
    if not dashboard_id:
        return []
    sql = """select `id`,`dashboard_id`,`dashboard_chart_id` as `chart_id`,`actions`,`conditions`, `type`
        from dap_bi_dashboard_chart_visible_triggers where dashboard_id=%(dashboard_id)s """
    params = {"dashboard_id": dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_var_relation_by_dashboard_id(dashboard_id):
    """
    根据报告id获取变量关系数据
    :param dashboard_id:
    :return:
    """
    if not dashboard_id:
        return []
    sql = """select id,chart_initiator_id,field_initiator_id,dashboard_id,var_id,var_dataset_id as dataset_id,
          initiator_type,var_dim_obj from dap_bi_dashboard_dataset_vars_relation where dashboard_id=%(dashboard_id)s """
    params = {"dashboard_id": dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_var_relation_by_var_ids(var_ids, dashboard_id):
    """
    获取变量相关联的单图关系
    :param var_ids:
    :param dashboard_id:
    :return:
    """
    if not var_ids:
        return []
    sql = """select chart_initiator_id,var_id from dap_bi_dashboard_dataset_vars_relation
          where var_id in %(var_ids)s and dashboard_id=%(dashboard_id)s """
    params = {"var_ids": var_ids, "dashboard_id": dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_filter_chart_fixed_values(fixed_value_ids: list):
    sql = """
    SELECT `id`, `chart_id`, `name`, `value_type`, `identifier`, `extra_data`
    FROM `dap_bi_dashboard_filter_chart_fixed_value`
    WHERE id IN %(fixed_value_ids)s
    """
    params = {"fixed_value_ids": fixed_value_ids}
    with get_db() as db:
        return db.query(sql, params)
