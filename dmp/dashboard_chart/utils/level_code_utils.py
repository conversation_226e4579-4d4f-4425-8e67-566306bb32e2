import logging
import traceback

from base import repository
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from level_sequence.models import DataSetLevelSequenceModel
from level_sequence.services import level_sequence_service


def substr_parent_level_code(level_code):
    arr = level_code.split('-')
    s = '-'.join(arr[:len(arr) - 2])
    return s + '-' if s else s

def generate_level_code(parent_id=None):
    return level_sequence_service.generate_level_code(DataSetLevelSequenceModel(level_id=parent_id))


def generate_dashboard_level_code(parent_id=None):
    return level_sequence_service.generate_dashboard_level_code(parent_id)


supported_tables = {'dap_bi_dashboard':generate_dashboard_level_code, 'dap_bi_dataset':generate_level_code}



class FixLevelCode(object):
    def __init__(self, data_id, table = 'dap_bi_dashboard', extra_params = {}):
        if not supported_tables.get(table):
            raise UserError(message = f'不支持的表:{table}')
        if not data_id:
            raise UserError(message='请指定dataId')
        self.data_id = data_id
        self.table = table
        self.extra_params = extra_params
        self.generate_level_code_funcs = supported_tables.get(table)


    def get_data(self):
        if self.data_id != 'ROOT':
            params = {"id": self.data_id}
            if self.extra_params:
                params.update(self.extra_params)
            return repository.get_data(self.table, params, multi_row=True)
        else:
            sql = """select * from {table} where parent_id = '' or LENGTH(level_code) - LENGTH(REPLACE(level_code,'-','')) = 1"""
            sql = sql.format(table = self.table)
            return repository.get_data_by_sql(sql, params = None)


    def fix(self):
        data = self.get_data()
        if not data:
            return
        for r in data:
            if r.get('level_code') == '0000-':
                # 内置模板数据集文件夹忽略
                continue

            data_id = r.get('id')
            data_name = r.get('name')
            self.__do_fix(r)
            logging.info(f'修复[{data_name}({data_id})]子目录完成')
        if self.data_id == 'ROOT':
            logging.info(f'开始修复所有结构异常数据')
            self.__update_illegal_level()
        logging.info(f'修复目录结构结束')



    def __update_illegal_level(self):
        data = self.__get_not_exists_parent_data()
        if not data:
            return
        for row in data:
            data_id = row['id']
            data_name = row['name']
            try:
                self.__do_fix(row)
                logging.info(f'修复[{data_name}({data_id})]子目录完成')
            except Exception as e:
                logging.error(f'修复[{data_name}({data_id})]子目录异常:{str(e)}')
                logging.error(traceback.format_exc())



    def __get_not_exists_parent_data(self):
        # 首先parentId==id的先统一修复
        sql = """
                        update `{table}` set parent_id = '' where id = parent_id
                     """
        sql = sql.format(table=self.table)
        repository.exec_sql(sql)
        # 检查parentId是否正确
        all_pid = repository.get_columns(self.table, {}, 'parent_id')
        if not all_pid:
            return
        sql = """
                    select t2.* from {table} t1 right join
                    ({rt}) t2 on t1.id = t2.id
                    where t1.id is null
                """
        all_pid = list(set(all_pid))
        r_table = []
        for pid in all_pid:
            if pid == '':
                continue
            r_table.append(f"select '{pid}' as id")
        r_table = " union all ".join(r_table)
        sql = sql.format(table=self.table, rt=r_table)
        not_exists_pid = repository.get_data_by_sql(sql, params=None)
        not_exists_pid = [row['id'] for row in not_exists_pid]
        if not not_exists_pid:
            return
        sql = """
                    select id,name,parent_id,level_code from {table} where parent_id in %(parent_ids)s
                """
        sql = sql.format(table=self.table)
        return repository.get_data_by_sql(sql, {'parent_ids': not_exists_pid})


    def __do_fix(self, dashboard):
        # 如果以当前报告为根，或当前报告通过父级id找不到父级，重新生成当前报告level_code,parent_id置为空
        parent_id = dashboard.get('parent_id', '')
        dashboard_id = dashboard.get('id')
        update = False
        #1.parent_id == id
        if parent_id == dashboard_id:
            parent_id = ''
            update = True
        level_code = dashboard.get('level_code', '')
        if parent_id or not level_code or (not parent_id and level_code.count('-') > 1):
            parent_dashboard = repository.get_data(self.table, {"id": parent_id})
            #2.parent_id not exists
            if not parent_dashboard or not level_code:
                if not parent_dashboard:
                    parent_id = ''
                level_code = self.generate_level_code_funcs(parent_id)
                update = True
        elif level_code == '9000-':
            parent_id = ''
            update = True
        if update:
            repository.update(self.table, {'level_code': level_code, 'parent_id': parent_id}, {"id": dashboard_id})
        all_dashboard = []
        # 通过父级id递归获取报告，如果存在循环依赖，报异常
        all_dashboard = self.__recursive_get_dashboard([dashboard_id], all_dashboard)
        all_dashboard.append({'id': dashboard_id, 'parent_id': parent_id, 'level_code': level_code})

        # 计算所有报告level_sequence
        dashboards_dict, level_sequences = self.__get_level_sequence(all_dashboard)

        # 递归计算所有报告level_code
        self.__recursive_set_dashboard_level_code(dashboards_dict, dashboard_id, level_code)
        with get_db() as db:
            try:
                db.begin_transaction()
                for dashboard in all_dashboard:
                    db.update(self.table,
                              {'parent_id': dashboard.get('parent_id'), 'level_code':dashboard.get('level_code')},
                              {"id": dashboard.get('id')}, commit=False)
                for level_sequence in level_sequences:
                    db.delete('dap_bi_level_sequence',
                              {'table_name': self.table, 'level_id': level_sequence.get('level_id')},
                              commit=False)
                    db.insert('dap_bi_level_sequence', level_sequence, commit=False)
                db.commit()
            except:
                db.rollback()
        return dashboards_dict


    def __recursive_get_dashboard(self, parent_ids: list, result: list):
        """
        通过父级id递归获取报告，如果存在循环依赖，报异常
        :param parent_ids:父级id
        :param result:查询结果
        :return:
        """
        child_dashboards = repository.get_list(self.table, {"parent_id": parent_ids}, ['id', 'parent_id'],
                                               order_by='level_code asc')
        if child_dashboards:
            child_dashboard_ids = []
            exist_parent_ids = [item.get('id') for item in result]
            for child in child_dashboards:
                if child.get('id') in exist_parent_ids:
                    raise UserError(message=f"报告id={child.get('id')}，父级id，存在循环依赖，请修复完数据后，再调用此接口")
                child_dashboard_ids.append(child.get('id'))
                result.append(child)
            self.__recursive_get_dashboard(child_dashboard_ids, result)
        return result


    def __get_data_list(self, parent_ids, parent_level_codes):
        lavel_code_or_cond = ''
        if parent_level_codes:
            conds = [" level_code like '{code}%' ".format(code = code) for code in parent_level_codes if code]
            if conds:
                lavel_code_or_cond = " or (1 = 1 and ({cond})) "
                lavel_code_or_cond = lavel_code_or_cond.format(cond = ' or '.join(conds))

        sql = '''select id, parent_id, level_code from {table} 
            where (parent_id in {parent_ids}) '''
        id_cond = "({ids})"
        ids = ",".join(["'{id}'".format(id=id) for id in parent_ids])
        id_cond = id_cond.format(ids = ids)
        sql = sql.format(table = self.table, parent_ids = id_cond)
        if lavel_code_or_cond:
            sql += lavel_code_or_cond
        sql += ' order by level_code '
        return repository.get_data_by_sql(sql, params = None)


    def __get_level_sequence(self, all_dashboard: list):
        dashboards_dict = {}
        level_sequence_list = []
        for item in all_dashboard:
            parent_id = item.get('id')
            childs = [item for item in all_dashboard if item.get('parent_id') == parent_id]
            if childs:
                level_sequence_list.append(
                    {'table_name': self.table, 'level_id': parent_id, 'max_sequence': len(childs)})
                dashboards_dict[parent_id] = childs
        return dashboards_dict, level_sequence_list


    def __recursive_set_dashboard_level_code(self, dashboards_dict: dict, parent_id, parent_level_code):
        """
        通过父级递归重置子集level_code
        :param dashboards_dict:
        :param parent_id:
        :param parent_level_code:
        :return:
        """
        child_dashboards = dashboards_dict.get(parent_id)
        if child_dashboards:
            for i, child in enumerate(child_dashboards):
                level_code = parent_level_code + str(i + 1).zfill(4) + "-"
                child['level_code'] = level_code
                if child.get('id'):
                    self.__recursive_set_dashboard_level_code(dashboards_dict, child.get('id'), level_code)
