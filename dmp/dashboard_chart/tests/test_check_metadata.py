#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
# @Time     : 2021-12-16 17:22:56
# <AUTHOR> lul05
# @File     : test_check_metadata.py
import json
import re
import requests

from base import repository
from dashboard_chart.services import metadata_service
from feed.services.dashboard_feeds_service import get_dashboard_url
from tests.base_test import BaseTest


class TestCheckMetadata(BaseTest):

    # 指定的默认比对节点
    default_node = 'first_report.chart_relations'

    # 群机器人消息发送地址
    wechat_web_hook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=54da7e51-03fa-4c78-9b32-cebcd514b7b9'

    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='cloud', account='cloud')

    def test_metadata_result(self):
        # dashboard_id = '39fe9229-6e77-f939-b206-513d3ad812f1'
        dashboard_id = '3a00e916-f797-6cd3-554a-30e7fd7380b9'
        # self._check_metadata(dashboard_id)
        self._check_metadata(dashboard_id, self.default_node)

    def _check_metadata(self, dashboard_id, assign_node=''):
        """
        检测报表元数据节点
        :param dashboard_id:
        :param assign_node:
        :return:
        """
        try:
            preview_result = self._get_preview_result(dashboard_id)
            release_result = self._get_release_result(dashboard_id)

            # 获取指定节点的数据进行比对
            if assign_node:
                preview_result, release_result = self._get_filter_result(preview_result, release_result, assign_node)

            preview_node_list = {}
            self._get_node_list(preview_result, '', preview_node_list)
            preview_keys_set = self._get_all_keys(preview_node_list)
            # s = json.dumps(preview_node_list, ensure_ascii=False)
            # print(s)
            # print(preview_keys_set)

            release_node_list = {}
            self._get_node_list(release_result, '', release_node_list)
            release_keys_set = self._get_all_keys(release_node_list)
            # s = json.dumps(release_node_list, ensure_ascii=False)
            # print(s)
            # print(release_keys_set)

            # 获取差异
            rs = self._check_node(preview_keys_set, release_keys_set)
            # todo 结果对比，规则校验。暂不进行规则校验，现阶段重点暴露差异点
            for item in rs:
                diff_rs = item.get("diff_rs")
                if diff_rs:
                    item['diff_rs'] = [assign_node+'.'+x if assign_node else x for x in diff_rs if x]
            print(rs)
            # 结果通知
            self._send_rs_msg(dashboard_id, rs)
        except Exception as e:
            msg = self._get_err_info(f"元数据比对异常(dashboard_id: {dashboard_id})，err:{str(e)}")
            self._send_wechat_msg(msg)

    def _send_rs_msg(self, dashboard_id, rs):
        """
        群机器人消息通知
        :param dashboard_id:
        :param rs:
        :return:
        """
        rs_list = ["\n" + item.get("msg") + "\n>".join([x for x in item.get("diff_rs", [])]) for item in rs]
        report_content = self._get_report_info(dashboard_id)
        content = "报表：" + report_content + "\n".join(rs_list)
        return self._send_wechat_msg(content)

    def _send_wechat_msg(self, content):
        """
        发送群机器人消息
        :param content:
        :return:
        """
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        return requests.post(self.wechat_web_hook_url, json=data)

    @staticmethod
    def _get_report_info(dashboard_id):
        dashboard = repository.get_one("dap_bi_dashboard", {"id": dashboard_id}, ["name", "terminal_type"])
        dashboard_name = dashboard.get("name")
        terminal_type = dashboard.get("terminal_type")
        report_url = get_dashboard_url(dashboard_id, terminal_type)
        return f"[{dashboard_name}]({report_url})"

    def _get_filter_result(self, preview_result, release_result, assign_node):
        if assign_node:
            preview_result = self._get_assign_node_result(preview_result, assign_node)
            release_result = self._get_assign_node_result(release_result, assign_node)
        return preview_result, release_result

    @staticmethod
    def _get_assign_node_result(result, assign_node):
        if result and assign_node:
            node_list = assign_node.split('.')
            for v in node_list:
                if result:
                    result = result.get(v, {})
        return result

    def _check_node(self, preview_keys_set, release_keys_set):
        diff_preview = preview_keys_set - release_keys_set
        diff_release = release_keys_set - preview_keys_set
        diff_preview_rs = sorted(list(diff_preview))
        diff_release_rs = sorted(list(diff_release))

        data = []
        if not diff_preview_rs and not diff_release_rs:
            msg = self._get_success_info('设计时，运行时元数据节点无差异')
            data.append({"msg": msg, "diff_rs": []})
        else:
            if diff_preview_rs:
                msg = self._get_err_info('设计时存在，而运行时不存在的节点：') + '\n>'
                data.append({"msg": msg, "diff_rs": diff_preview_rs, "type": "preview"})

            if diff_release_rs:
                msg = self._get_err_info('运行时存在，而设计时不存在的节点：') + '\n>'
                data.append({"msg": msg, "diff_rs": diff_release_rs, "type": "release"})
        return data

    @staticmethod
    def _get_err_info(msg):
        return f'<font color="warning">{msg}</font>'

    @staticmethod
    def _get_success_info(msg):
        return f'<font color="info">{msg}</font>'

    @staticmethod
    def _get_all_keys(node_list=dict):
        keys = list(node_list.keys())
        key_set = {re.sub(r'\.\d+\.', '.', x) for x in keys}
        return key_set

    def _get_preview_result(self, dashboard_id):
        _, preview_result = metadata_service.get_screens_preview_metadata_v2(dashboard_id=dashboard_id)
        preview_result = self._sort_data(preview_result)
        return preview_result

    def _get_release_result(self, dashboard_id):
        _, release_result = metadata_service.get_screens_release_metadata_v2(snapshot_id=dashboard_id, token_data={})
        release_result = self._sort_data(release_result)
        return release_result

    @staticmethod
    def _sort_data(data):
        if data and isinstance(data, dict):
            data_json = json.dumps(data, ensure_ascii=False, sort_keys=True)
            data = json.loads(data_json)
        return data

    def _get_node_list(self, result, init_key='', node_list=dict()):  # NOSONAR
        if result and isinstance(result, dict):
            for key, item in result.items():
                if init_key:
                    key = init_key + '.' + key
                item_val = None

                # 列表遍历
                if isinstance(item, list):
                    for i, item_dict in enumerate(item):
                        tmp_key = key + '.' + str(i)
                        self._get_node_list(item_dict, tmp_key, node_list)
                elif isinstance(item, dict):
                    self._get_node_list(item, key, node_list)
                else:
                    item_val = item
                node_list[key] = item_val
