#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from tests.base_test import patch_property
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
import pandas as pd
from dashboard_chart.data_query.widget.subtotal_query import SubtotalQuery
from dashboard_chart.convertor.field_types import FieldObj
from dashboard_chart.data_query.charts.statistic_table_chart import StatisticTableChart


class TestSubtotalQueryModel(TestDataQueryBaseModel):
    def get_models(self):
        mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"7f2472a1-fa43-11eb-8cdf-b1878405dec1","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd391-7254-9d8e-054f-4b4418a541cc","data_type":"字符串","col_name":"LX_8957975949","alias_name":"类型","order":1,"rank":1,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-da7c-5d10-18709d914703","data_type":"数值","col_name":"JE_7345642328","alias_name":"金额1","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-db36-60a2-3303732bfe1f","data_type":"数值","col_name":"JE_7345707864","alias_name":"金额2","order":3,"rank":3,"col_type":"num","is_show":1,"group":null}]},{"id":"8d07bd10-fa7e-11eb-bd19-a995f388098a","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[{"var_type":1,"value_type":2,"var_id":"39fd9139-d80b-c11f-6f54-64a927c15528","default_value_type":2,"value":"","value_source":"userdefined","value_identifier":""},{"var_type":2,"value_type":3,"var_id":"39fdf427-7225-ec20-0c87-595ea9f02564","default_value_type":2,"value":["2021-01-01 00:00:00","2021-12-31 23:59:59"],"value_source":"userdefined","value_identifier":""}],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd382-657d-fe39-f1b1-8b7104cdf7ed","dataset_field_id":"39fcd382-907c-3b53-3684-42706016c652","data_type":"字符串","col_name":"XM_6299689623","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd382-657d-fe39-f1b1-8b7104cdf7ed","dataset_field_id":"39fd913a-07f2-7d71-fefa-973ed8bb0f99","data_type":"字符串","col_name":"A_CS_7995442000","alias_name":"参数","order":1,"rank":1,"col_type":"num","is_show":1,"group":null}]},{"id":"98ca8d57-fb0d-11eb-80d1-c556e2276197","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]},{"id":"f6059972-fb11-11eb-a929-a129010e7a35","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]},{"id":"68b4418b-00bf-11ec-8f9b-43fdbf53d15e","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-037f-7cf9-eadc-bc8126258602","dataset_field_id":"39fcd383-2d1b-0339-7269-74d18c589d9a","data_type":"字符串","col_name":"XM_6343270961","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-037f-7cf9-eadc-bc8126258602","dataset_field_id":"39fcd383-2d1b-095e-8e3b-a575e6774ece","data_type":"数值","col_name":"JE_7563287433","alias_name":"金额2","order":1,"rank":1,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-037f-7cf9-eadc-bc8126258602","dataset_field_id":"39fcd383-2d1b-0889-a9de-d22d996504ff","data_type":"数值","col_name":"JE_7563221897","alias_name":"金额1","order":2,"rank":2,"col_type":"num","is_show":1,"group":null}]},{"id":"6d2ad580-00bf-11ec-8f9b-43fdbf53d15e","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[]}]}"""
        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][3])
        assign_dashboard_chart_model(c_model, no_data=True)
        chart = StatisticTableChart(c_model)
        return chart, c_model

    def test_data_query_statistic_table_get_chart_data(self):
        chart, c_model = self.get_models()

        r5 = chart.get_chart_data()
        self.assertIn('data', r5)

        c_model.enable_subtotal = 1
        c_model.enable_summary = 1

        dims = deepcopy(c_model.dims)
        dims[0]['is_subtotal_cate'] = 1
        with patch_property(c_model, 'dims', dims):
            r1 = chart.get_chart_data()
            self.assertIn('data', r1)

    def test_data_query_statistic_table_get_query_struct(self):
        chart, c_model = self.get_models()

        r4 = chart.get_query_struct()
        self.assertIn('query_structure', r4)

        c_model.enable_subtotal = 1
        c_model.enable_summary = 1

        dims = deepcopy(c_model.dims)
        dims[0]['is_subtotal_cate'] = 1
        with patch_property(c_model, 'dims', dims):
            r2 = chart.get_query_struct()
            self.assertIn('query_structure', r2)

    def test__compare_dict(self):
        chart, c_model = self.get_models()

        sq = SubtotalQuery(c_model, [], [], '')

        d1 = {self.fd('A'): self.fd('A-value-1')}
        d2 = {self.fd('A'): self.fd('A-value-2')}
        r3 = sq._compare_dict(d1, d2, [self.fd('A')])
        self.assertEqual(r3, False)

    def test__generate_count_all_field(self):
        r6 = SubtotalQuery._generate_count_all_field()
        self.assertIsInstance(r6, FieldObj)
