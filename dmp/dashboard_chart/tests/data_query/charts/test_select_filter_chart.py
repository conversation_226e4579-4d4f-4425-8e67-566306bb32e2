#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
from dashboard_chart.data_query.charts.select_filter_chart import SelectFilterChart


class TestSelectFilterModel(TestDataQueryBaseModel):


    def get_models(self):
        c_model = self._create_model('query_data_sample_1')
        assign_dashboard_chart_model(c_model, no_data=True)
        chart = SelectFilterChart(c_model)
        return chart, c_model

    # dashboard_chart/data_query/charts/test_select_filter_chart.py
    def test_data_query_select_filter_query_data(self):
        chart, c_model = self.get_models()

        r1 = chart.query_data()
        self.assertEqual(200, r1['code'])

    # dashboard_chart/data_query/charts/test_select_filter_chart.py
    def test_data_query_select_filter_get_chart_data(self):
        chart, c_model = self.get_models()

        r2 = chart.get_chart_data()
        self.assertIsInstance(r2, dict)

    # dashboard_chart/data_query/charts/test_select_filter_chart.py
    def test_data_query_select_filter_get_subtotal_for_struct(self):
        chart, c_model = self.get_models()

        r3 = chart.get_subtotal_for_struct()
        self.assertEqual({}, r3)

    # dashboard_chart/data_query/charts/test_select_filter_chart.py
    def test_data_query_select_filter_get_variable_chart_data(self):
        chart, c_model = self.get_models()

        r4 = chart.get_variable_chart_data()
        self.assertIn('data', r4)

        # penetrate_conditions = [
        #     {
        #         "col_name": "col1",
        #         "col_value": "第一事业部",
        #         "operator": "=",
        #         "dim": {
        #             "visible": 1,
        #             "dashboard_chart_id": "bfd64f08-3644-11e9-8dff-a1dee455e1fc",
        #             "alias_name": "事业部",
        #             "field_group": "维度",
        #             "expression": "",
        #             "dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
        #             "dataset_field_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
        #             "type": "普通",
        #             "dim": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
        #             "alias": "事业部",
        #             "content": "",
        #             "col_name": "col1",
        #             "data_type": "字符串",
        #             "id": "aba177cd-3645-11e9-8dff-a1dee455e1fc",
        #             "rank": 0,
        #             "formula_mode": "",
        #             "sort": "",
        #             "note": ""
        #         }
        #     }
        # ]

    # dashboard_chart/data_query/charts/test_select_filter_chart.py
    def test_data_query_select_filter_get_chart_data_1(self):
        chart, c_model = self.get_models()

        chart.chart_data_model.dims += chart.chart_data_model.dims
        chart.chart_data_model.penetrate_conditions = [{}]
        r5 = chart.get_chart_data()
        self.assertIsInstance(r5, dict)

        with patch.dict(chart.dataset_field, {}, clear=True):
            # with patch_property(chart, 'dataset_field', {}):
            r6 = chart.get_chart_data()
            self.assertEqual(r6, {})

        with patch.dict(chart.dataset_field, {}, clear=True):
            r7 = chart.get_variable_chart_data()
            self.assertEqual(r7, {})

        chart.chart_data_model.dims += chart.chart_data_model.dims
        r8 = chart.get_variable_chart_data()
        self.assertIsInstance(r8, dict)

