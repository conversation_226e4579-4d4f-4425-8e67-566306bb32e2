#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
from dashboard_chart.data_query.charts.comparision_line_chart import ComparisionLine<PERSON>hart, ChartDataModel
from dashboard_chart.convertor.limit.limit import Limit, LimitField
import functools


class TestComparisionLineModel(TestDataQueryBaseModel):


    def get_models(self):
        mock_data = """{"dashboard_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","chart_params":[{"id":"0ddfeed6-f4cb-11eb-ba21-1795d3520ff7","report_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","dashboard_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
        assign_dashboard_chart_model(c_model, no_data=True)
        _c_model = deepcopy(c_model)
        chart = ComparisionLineChart(c_model)
        return chart, c_model, _c_model

    # dashboard_chart/data_query/charts/comparision_line_chart.py
    def test_data_query_charts_comparision_get_chart_data(self):
        chart, c_model, _c_model = self.get_models()

        r1 = chart.get_chart_data()
        self.assertIsInstance(r1, dict)

    # dashboard_chart/data_query/charts/comparision_line_chart.py
    def test_data_query_charts_comparision_get_subtotal_for_struct(self):
        chart, c_model, _c_model, = self.get_models()

        r2 = chart.get_subtotal_for_struct()
        self.assertEqual(r2, {})

    # dashboard_chart/data_query/charts/comparision_line_chart.py
    def test_data_query_charts_comparision_get_limit_field(self):
        chart, c_model, _c_model, = self.get_models()

        mock_value = LimitField()
        mock_value.offset = -9999
        mock_value.limit = 100
        with patch.object(Limit, 'get_limit_field_for_default', return_value=mock_value):
            chart1 = ComparisionLineChart(c_model)
            r3 = chart1.get_limit_field()
            self.assertEqual(bool(r3), True)

    # dashboard_chart/data_query/charts/comparision_line_chart.py
    def test_data_query_charts_comparision__get_structure_data(self):
        chart, c_model, _c_model, = self.get_models()
        
        # dashboard_chart/data_query/charts/comparision_line_chart.py:45
        chart2 = ComparisionLineChart(c_model)
        chart2.chart_data_model.comparisons = [{}]
        r4 = chart._get_structure_data([])
        self.assertEqual(r4, [])

    # dashboard_chart/data_query/charts/comparision_line_chart.py
    def test_data_query_charts_comparision__get_structure_data_1(self):
        chart, c_model, _c_model, = self.get_models()

        with patch.object(functools, 'reduce', return_value=20000):
            chart3 = ComparisionLineChart(deepcopy(_c_model))
            chart3.chart_data_model.comparisons = []
            chart3.over_limit_flag = True
            r5 = chart3._get_structure_data([])
            self.assertEqual(r5, [])

