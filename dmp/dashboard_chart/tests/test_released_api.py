#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"""

import unittest
import logging
import json
from tests.base import BaseTest
from dashboard_chart.services import released_dashboard_service


logger = logging.getLogger(__name__)


class TestChartService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev_test', account='test')

    def test_released_api(self):

        chart_params = [
            {
                "id": "39e93594-b15e-89f5-b82e-19a1e272d6e5",
                "report_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
                "dashboard_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
                "chart_code": "select_filter",
                "data_logic_type_code": "assist",
                "conditions": [],
                "penetrate_conditions": [],
                "filter_conditions": [],
                "dashboard_conditions": [],
                "code": "test",
            }
        ]

        result = released_dashboard_service.batch_get_released_chart_result(chart_params)
        logger.error('released api result:::' + json.dumps(result))
        print('released api result:::' + json.dumps(result))


if __name__ == '__main__':
    unittest.main()
