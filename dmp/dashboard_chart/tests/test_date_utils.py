#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
test metadata service
"""

import json
import logging
import unittest

from dmplib.utils.errors import UserError
from tests.base import BaseTest
from dashboard_chart.services import metadata_service, screen_dashboard_service, dashboard_service

from components.date_utils import DateUtil
from dashboard_chart.agent.utils import get_datetime_from_word

logger = logging.getLogger(__name__)


class TestDashBoardService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='admin')

    def test_DateUtil(self):
        date_utils = DateUtil()
        print(0, date_utils.get_month_start(0))
        print(-2, date_utils.get_month_start(-2))
        print(-12, date_utils.get_month_start(-12))
        print(12, date_utils.get_month_start(12))

        print('--------------------------------')

        print(0, date_utils.get_month_end(0))
        print(-2, date_utils.get_month_end(-2))
        print(-12, date_utils.get_month_end(-12))
        print(12, date_utils.get_month_end(12))

    def test_get_datetime_from_word(self):
        word_list = [
            'today',
            'weekday_custom_0,1',
            'weekday_custom_-1,1',
            'weekday_custom_-2,1',
            # 本周最后一天
            'weekday_custom_0,-1',
            'month_nagative_1',  # 下月最后一天
            'month_nagative_0',  # 本月最后一天
            'month_nagative_-1', # 上月最后一天
            'month_nagative_-2',  # 上上月最后一天
            'month_nagative_-5',  # 5个月最后一天
            'month_nagative_-20', # 20个月前的最后一天

            # 本月第一天
            'monthday_custom_0,1',
            'monthday_custom_-1,1',
            'monthday_custom_-2,1',

            'quarterday_custom_0,1', # 本季度第一天
            'quarterday_custom_-1,1',  # 上季度第一天
            'quarterday_custom_-2,1',  # 上上季度第一天

            'yearday_custom_0,1',  # 本年第一天
            'yearday_custom_-2,1',  # 上上年第一天

            # 上月的年份的第一天
            'monthday_custom_-1,year,1',
            # 上上月的年份的第一天
            'monthday_custom_-2,year,1',
            # 上上月的年份的第100天
            'monthday_custom_-2,year,100',
            # 20个月前的年份的第一天
            'monthday_custom_-20,year,1',

        ]
        print("\n")
        for w in word_list:
            print(w, get_datetime_from_word(w))


if __name__ == '__main__':
    unittest.main()
