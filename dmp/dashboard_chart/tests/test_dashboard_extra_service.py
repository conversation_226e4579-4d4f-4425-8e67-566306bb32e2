#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
test dashboard extra
"""

import json
import logging
from tests.base import BaseTest
from dashboard_chart.services import dashboard_extra_service


logger = logging.getLogger(__name__)


class TestDashboardExtraService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev_test', account='test')

    # def test_dashboard_extra(self):
    #
    #     dashboard_id = ""
    #     res = dashboard_extra_service.reset_edit_and_released_on(dashboard_id)
    #
    #     result = 'test_dashboard_extra::::::' + json.dumps(res)
    #     print(result)
    #     logger.error(result)
