#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os

from unittest.mock import patch

os.environ['prometheus_multiproc_dir'] = '/tmp'
import inspect
import re
import time
from collections import defaultdict

from dashboard_chart.models import ReleaseModel
from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from tests.base_test import BaseTest
from dashboard_chart.metadata.metadata_builder import storage, builder, generators
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.metadata import common_metadata_model
from dashboard_chart.metadata import query_release_metadata_model
from dmplib.redis import conn as conn_redis


class TestMetadataReleaseModel(BaseTest):
    """
    解析json数据类的测试
    """

    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='test')

    def setUp(self):
        super().setUp()
        self.dashboard_id = '39fd437d-8b21-7793-7c70-cd9e2713c94e'
        self.snapshot_id = '39fd437d-8b21-7793-7c70-cd9e2713c94e'
        self.op_chart_id = '39fd437d-8b21-7c74-288c-01464efc954d'
        token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************.s8ijllLyrobkMUInVaxJdYeO0s4M7b5J4D03S7zc_Z4'
        g.cookie = {'token': token}
        g.code = 'local'

    # dashboard_chart/metadata/schema_validator.py
    @patch('dashboard_chart.metadata.schema_validator.SchemaBaseValidator._validate_data')
    def test_metadata_schema_validator_normal(self, mock_validate):
        from dashboard_chart.metadata import schema_validator

        # 正常情况
        metadata = self._get_json('update_meta').get('metadata')
        screens = metadata.get('screens')
        metadata['first_report'] = screens[0]
        metadata['screens'] = []
        mock_validate.return_value = True

        result = schema_validator.MetadataValidator(data=metadata).validate_screens_metadata()
        self.assertEqual(result[0], True)
        result = schema_validator.DashboardMetadataValidator(data=screens[0]).validate_metadata()
        self.assertEqual(result[0], True)
        result = schema_validator.PreviewMetadataValidator(data=metadata).validate_metadata()
        self.assertEqual(result[0], True)

    @patch('dashboard_chart.metadata.schema_validator.SchemaBaseValidator._validate_data')
    def test_metadata_schema_validator_except_1(self, mock_validate):
        from dashboard_chart.metadata import schema_validator

        metadata = self._get_json('update_meta').get('metadata')
        screens = metadata.get('screens')
        metadata['first_report'] = screens[0]
        metadata['screens'] = []
        mock_validate.return_value = True
        # 异常情况1 dashboard_chart/metadata/schema_validator.py:117
        data = {}
        result = schema_validator.MetadataValidator(data=data).validate_screens_metadata()
        self.assertEqual(result[0], False)
        result = schema_validator.DashboardMetadataValidator(data=data).validate_metadata()
        self.assertEqual(result[0], False)
        result = schema_validator.PreviewMetadataValidator(data=data).validate_metadata()
        self.assertEqual(result[0], False)

    @patch('dashboard_chart.metadata.schema_validator.SchemaBaseValidator._validate_data')
    def test_metadata_schema_validator_except_2(self, mock_validate):
        from dashboard_chart.metadata import schema_validator

        metadata = self._get_json('update_meta').get('metadata')
        screens = metadata.get('screens')
        metadata['first_report'] = screens[0]
        metadata['screens'] = []
        mock_validate.return_value = True

        # 异常情况2 dashboard_chart/metadata/schema_validator.py:119
        data = [1, 2]
        result = schema_validator.MetadataValidator(data=data).validate_screens_metadata()
        self.assertEqual(result[0], False)
        result = schema_validator.DashboardMetadataValidator(data=data).validate_metadata()
        self.assertEqual(result[0], False)
        result = schema_validator.PreviewMetadataValidator(data=data).validate_metadata()
        self.assertEqual(result[0], False)

    @patch('dashboard_chart.metadata.schema_validator.SchemaBaseValidator._validate_data')
    def test_metadata_schema_validator_except_3(self, mock_validate):
        from dashboard_chart.metadata import schema_validator

        metadata = self._get_json('update_meta').get('metadata')
        screens = metadata.get('screens')
        metadata['first_report'] = screens[0]
        metadata['screens'] = []
        mock_validate.return_value = True

        # 异常情况3 dashboard_chart/metadata/schema_validator.py:46
        try:
            result = schema_validator.MetadataValidator(
                data=self._get_json('update_meta')).validate_screens_metadata()
            self.assertEqual(result[0], False)
        except Exception as e:
            self.assertIsInstance(e, UserError)

    @classmethod
    def _create_editor(cls, editor):
        json_obj = cls._get_json()
        meta_sub = MetadataSubject(json_obj)
        return meta_sub.create_editor(editor)

    @staticmethod
    def _get_json(file_name='meta'):
        file_path = '{}/dashboard_chart/tests/metadata/{}.json'.format(config.root_path, file_name)
        with open(file_path, encoding='utf8') as f:
            return json.load(f)

    @staticmethod
    def fake_data(i):
        return 'fake-data-%s' % i
