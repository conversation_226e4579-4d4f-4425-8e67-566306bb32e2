#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
dashboard jump formatter
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
import copy

from base.errors import UserError
from components import functions
from dashboard_chart.formatter.formatter import Formatter
from base.enums import DatasetFieldDataType, ChartConditionType, ChartDimFormulaMode, SqlWhereOperator

logger = logging.getLogger(__name__)


class SelfServiceJumpFormatter(Formatter):
    """
    跳转
    :param self.dataset_field_dict: 数据集数据字典
    :param self.self_service_conditions: 条件数据
    """

    def __init__(self, model):
        self.dataset_field_dict = None
        self.self_service_conditions = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.dataset_field_dict = self.chart_data_model.dataset_field_dict or dict()
        self.self_service_conditions = self.chart_data_model.self_service_conditions or list()

    @staticmethod
    def deal_with_formula_params(orig_formula_mode, dataset_data_type, orig_operator, orig_data_value):
        """
        处理转换格式参数
        :param orig_formula_mode: 原始的fomula_model
        :param dataset_data_type: 数值类型
        :param orig_operator:  操作符
        :param orig_data_value:  原始值
        :return: dict()
        """
        field_func, field_func_format = "", ""
        operator = orig_operator
        result_dict = dict()

        # 已有formula_mode参数
        if orig_formula_mode:
            field_func = "date_format"
            field_func_format = functions.get_time_formula_mode(orig_formula_mode)
        elif dataset_data_type == DatasetFieldDataType.Datetime.value:
            field_func = "date_format"
            field_func_format = functions.get_time_formula_mode(ChartDimFormulaMode.Day.value)
        if field_func and not field_func_format:
            field_func = ""

        new_data_value = copy.deepcopy(orig_data_value)
        # 统一转换=和!=操作符
        if field_func == "date_format" and orig_data_value is not None:
            if orig_operator == SqlWhereOperator.Eq.value:
                operator = SqlWhereOperator.Like.value
            elif orig_operator == SqlWhereOperator.Neq.value:
                operator = SqlWhereOperator.Nlike.value

        if orig_data_value is None:
            if orig_operator == SqlWhereOperator.Eq.value:
                operator = SqlWhereOperator.IsNull.value
                new_data_value = None
            elif orig_operator == SqlWhereOperator.Neq.value:
                operator = SqlWhereOperator.IsNotNull.value
                new_data_value = None

        result_dict["field_func"] = field_func
        result_dict["field_func_format"] = field_func_format
        result_dict["operator"] = operator
        result_dict["col_value"] = new_data_value
        return result_dict


    def format(self):
        """
        获取转换后的报告条件
        :return:
        """
        for condition in self.self_service_conditions:
            cur_dataset_field_data = next(
                (item for item in self.dataset_field_dict.values() if item.get('id') == condition.get('dataset_field_id')),
                None)
            if not cur_dataset_field_data:
                raise UserError(message='数据异常，无法获取数据集！')

            # 处理formula_mode
            formula_mode = None
            if cur_dataset_field_data.get("data_type") == DatasetFieldDataType.Datetime.value:
                formula_mode = condition.get('formula_mode') or ChartDimFormulaMode.Day.value
            formula_result_dict = self.deal_with_formula_params(
                formula_mode,
                cur_dataset_field_data.get('data_type'),
                condition.get('operator'),
                condition.get('col_value'),
            )

            dim_data = dict(
                alias_name=cur_dataset_field_data.get('alias_name'),
                col_name=cur_dataset_field_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=formula_result_dict.get('operator'),
                dataset_field_id=cur_dataset_field_data.get('id'),
                data_type=cur_dataset_field_data.get('data_type'),
                format=cur_dataset_field_data.get('format'),
                formula_mode=formula_mode,
                condition_type=ChartConditionType.DashboardJumpFilter.value,
            )
            self.append_single_condition(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=cur_dataset_field_data,
                dn=dim_data,
            )
        return self
