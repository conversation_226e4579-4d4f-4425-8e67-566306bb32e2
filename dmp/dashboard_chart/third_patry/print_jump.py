import json
import logging

import hug
import jwt
import time

import requests
from loguru import logger

from dmplib.components.enums import SkylineApps
from dmplib.components import auth_util
from dmplib.components.app_hosts import AppHosts
from dashboard_chart.third_patry.jump import Jump
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError

from report_center.common.PrintConstant import OPEN_PRINT_URL_PATH_TREE_DATA, GET_PRINT_SSO_LOGIN_URL
from report_center.services.report_service import get_login_jwt_payload
from user.repositories import user_repository
from user.services.workbench_auth_service import Workbench


def request(method, url, params, headers):
    st = time.time()
    is_success = 0
    result = ''
    try:
        logger.error(f"套打请求:{url}, {headers}, {params}")
        response = requests.request(method, url, json=params, headers=headers, timeout=60)
        if response.status_code == 200:
            is_success = 1
            result = response.text
        else:
            result = f'{response.status_code}, {response.text}'
        logger.error(f"套打返回:{result}")
    except Exception as be:
        result = f'套打服务请求异常:{str(be)}'
        msg = "请检查网络是否正常:{}|url:{}|parameters{}".format(str(be), url, str(params))
        raise UserError(message=msg)
    finally:
        fast_logger_record(url, params, headers, None, result, is_success, st, time.time())

    if response.status_code == 200:
        return response.json()
    else:
        msg = "错误反馈信息：" + str(response.status_code) + ' , ' + str(response.reason)
        raise UserError(message=msg)

def get_print_role():
    return { "rename": 0, "history": 1, "copy": 1, "export": 1, "print": 1 }


def get_jwt_payload(host, page_type, temp_biz_data):
    view_time_out = int(config.get("ReportCenter.open_print_view_time_out", 7200))
    all_payload = get_login_jwt_payload(host, page_type)
    payload = {
      "iss": all_payload.get("iss"),
      "exp": all_payload.get("exp"),
      "iat": all_payload.get("iat"),
      "jti": all_payload.get("jti"),
      "aid": all_payload.get("aid"),
      "aud": all_payload.get("aud"),
      "authType": all_payload.get("authType"),
      "pageType": page_type,
    }
    if page_type == 'PRINT_FILE_VIEW':
        payload['bizData'] = {
            "userId": all_payload.get("sub"),
            "userName": all_payload.get("un"),
            "watermark": "",
            "avatarUrl": "",
            "expiresTime": view_time_out,
            "userAcl": get_print_role()
        }
        if temp_biz_data:
            payload['bizData']["templates"]=str(json.dumps(temp_biz_data))
    return payload


# 打印服务的跳转
class PrintJump(Jump):
    def check_role(self):
        project_data = user_repository.get_user_project_profile()
        return project_data.get("show_print")

    def get_tree_data(self):
        url = self.get_apaas_url() + OPEN_PRINT_URL_PATH_TREE_DATA
        params = {}
        return request("get", url, params, self.get_headers())

    def get_headers(self):
        headers = {
            "platformCode": config.get("ReportCenter.open_print_aid","mysoft_cy_saas"),
            "thirdTenantCode": g.code,
            "tenantCode": g.code,
        }
        if auth_util.is_enable_skyline_auth(g.code):
            headers[auth_util.AUTHORIZATION_KEY] = auth_util.gen_auth_token()
        else:
            raise UserError(message="当前租户或环境未开启统一认证")
        return headers

    def get_temp_biz_data(self):
        value_list = []
        target = self.kwargs.get("target")
        if not target:
            return None
        values = self.kwargs.get("values")
        extend = self.kwargs.get("extend") or '{}'
        targets = target.split("_", 1)
        if len(targets) == 2:
            target = targets[1]
        value_map = json.loads(values)
        extend_map = {}
        try:
            extend_map = json.loads(extend) or {}
        except Exception as e:
            logging.error("批量套打扩展参数格式化异常：" + str(extend))
        if value_map:
            first_key = list(value_map.keys())[0]
            first_value = value_map[first_key]
            if isinstance(first_value, list):
                # 多值的时候， printType = 2的单页套打 printType=1则是批量套打  printType不存在(历史数据，默认情况)的时候是批量套打
                if extend_map.get("printType") == 2 or extend_map.get("printType") == '2':
                    value_map[first_key] = ",".join(first_value)
                else:
                    for v in first_value:
                        value_list.append({
                            first_key: v,
                            "templateId": target
                        })
                    return value_list
        value_map["templateId"] = target
        value_list.append(value_map)
        return value_list

    def get_apaas_url(self, intranet=True):
        url = AppHosts.get(SkylineApps.APAAS, intranet)
        return url

    def redirect(self, page_type = 'PRINT_TEMPLATE_PAGE'):
        biz_data = self.get_temp_biz_data()
        payload = get_jwt_payload(self.request.forwarded_host, page_type, biz_data)
        if auth_util.is_enable_skyline_auth(g.code):
            redirect_url = self.get_redirect_url(page_type, payload.get('bizData', {}))
            return Workbench().to_login(redirect_url, g.code)
        else:
            raise UserError(message='当前租户或环境未开启统一认证')

    def get_redirect_url(self, page_type, biz_data):
        url = self.get_apaas_url()
        url = url + GET_PRINT_SSO_LOGIN_URL
        params = {
            "authType": config.get("ReportCenter.open_print_auth_type", "SAAS"),
            "expire": int(time.time()),
            "autoCreateUser": 1,
            "userCode": g.account,
            "serviceCode": "print",
            "pageType": page_type,
            "bizData": biz_data
        }
        res = request("post", url, params, self.get_headers())
        sso_url = res.get('data', {}).get('url')
        if not sso_url:
            raise UserError(message='获取套打地址失败')
        return sso_url



def fast_logger_record(url, params, headers, cookies, result, is_success, st, ed):
    """
    日志记录天眼
    :param url:
    :param params:
    :param cookies:
    :param result:
    :param is_success:
    :return:
    """
    try:
        start_time = int(st * 1000)
        end_time = int(ed * 1000)
        duration = end_time - start_time

        log_data = {
            "action": "request_print",
            "api_url": url,
            "start_time": str(start_time),
            "end_time": str(end_time),
            "duration": str(duration),
            "api_param": json.dumps(params, ensure_ascii=False) if params else '',
            "is_success": "1" if is_success else "0",
            "api_result": result,
            "cookies": json.dumps(cookies, ensure_ascii=False) if cookies else '',
            "org_code": g.code if hasattr(g, 'code') else "",
            "account": g.account if hasattr(g, 'account') else "",
            "header": json.dumps(headers, ensure_ascii=False) if headers else ''
        }
        from dmplib.components.fast_logger import FastLogger
        FastLogger.ApiFastLogger(**log_data).record()
    except Exception as e:
        logger.error("记录API请求日志失败：" + str(e))
