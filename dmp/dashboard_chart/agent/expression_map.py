#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/21
"""
from base.query_builder import expressions


class ExpressionMap:
    @staticmethod
    def get_expression_dict():
        return {
            'and': expressions.and_,
            'or': expressions.or_,
            'xor': expressions.xor,
            '+': expressions.add,
            '*': expressions.mul,
            '-': expressions.sub,
            '/': expressions.div,
            '%': expressions.mod,
            '<': expressions.lt,
            '<=': expressions.le,
            '!=': expressions.ne,
            '<>': expressions.ne,
            '>': expressions.gt,
            '>=': expressions.ge,
            '=': expressions.eq,
            'in': expressions.in_,
            'not in': expressions.notin,
            'as': expressions.as_,
            'is': expressions.is_,
            'is not': expressions.isnot,
            'like': expressions.like,
            'not like': expressions.notlike,
            'between': expressions.between,
            'not between': expressions.notbetween,
            'inv': expressions.inv,
            'all': expressions.all_,
            'any': expressions.any_,
            'some': expressions.some,
            'exists': expressions.exists,
            'asc': expressions.asc,
            'desc': expressions.desc,
        }

    @staticmethod
    def get_expression_class(expression_name):
        """
        获取表达式对应的类
        :param str expression_name: 表达式名称 < > !=
        :return:
        """
        expression_name = str(expression_name.strip()).lower() if expression_name else expression_name
        expression_dict = ExpressionMap.get_expression_dict()
        if not expression_name or expression_name not in list(expression_dict.keys()):
            return False
        return expression_dict.get(expression_name)
