#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/1/21 10:08
# <AUTHOR> caoxl
# @File     : external_service.py
from base.enums import DashboardTerminalType
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from .services import high_data_service
from urllib.parse import urljoin

from dmplib import config
from integrate.services import third_party_service


def add_hd_dashboard(report_name: str, appcode: str, report_id: str, report_type: str):
    """
    新建highData报告
    :param report_name:
    :param appcode:
    :param report_id:
    :param report_type:
    :return:
    """
    hd_service = high_data_service.HighDataService()
    return hd_service.add_report(report_name, appcode, report_id, report_type)


def delete_hd_dashboard(report_id: str, report_type: str):
    """
    删除highData报告
    :param report_id:
    :param report_type:
    :return:
    """
    hd_service = high_data_service.HighDataService()
    return hd_service.delete_report(report_id, report_type)


def rename_hd_dashboard_name(report_id: str, report_name: str, report_type: str):
    """
    重命名highData报告
    :param report_id:
    :param report_name:
    :param report_type:
    :return:
    """
    hd_service = high_data_service.HighDataService()
    return hd_service.rename_report_name(report_name, report_id, report_type)


def release_hd_dashboard(report_id: str, report_type: str):
    """
    发布highData报告
    :param report_id:
    :param report_type:
    :return:
    """
    hd_service = high_data_service.HighDataService()
    return hd_service.publish_report(report_id, report_type)


def get_hd_dashboard_type(terminal_type):
    """
    获取HighData所属报告类型
    :param terminal_type: DMP报告类型
    :return:
    """
    return "m_report" if terminal_type == DashboardTerminalType.Mobile.value else "dashboard"


def get_hd_report_url(report_id: str, terminal_type):
    """
    获取HighData报表的url地址
    :param report_id: 报表id:
    :param terminal_type: DMP报告类型:
    :return:
    """
    report_type = get_hd_dashboard_type(terminal_type)
    suffix_url = '/api/integrate/go_hd?report_id=%s&report_type=%s'
    suffix_url = suffix_url % (report_id, report_type)
    url = urljoin(base=AppHosts.get(SkylineApps.DP, False), url=suffix_url)
    return url


def go_hd_frontend_report_url(report_id: str, report_type: str):
    """
    获取到HighData报表的url地址
    """
    hd_service = high_data_service.HighDataService()
    return hd_service.go_hd_frontend_report_url(report_id, report_type)


def get_user_by_third_party_id_and_user_info(third_party_id: str, user_id: str = None, account: str = None):
    return third_party_service.get_user_by_third_party_id(third_party_id, user_id, account)


def go_hd_frontend_assign_url(redirect_uri: str):
    """
    获取到HighData、老移动报告平台的前台指定url地址
    """
    hd_service = high_data_service.HighDataService()
    return hd_service.go_hd_frontend_uri(redirect_uri)
