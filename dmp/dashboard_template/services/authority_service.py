#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401,W0613

# ---------------- 标准模块 ----------------
import jwt
from falcon import request_helpers
from hug.authentication import authenticator
from jwt import DecodeError, ExpiredSignatureError
import binascii
import json
import io
import os

from base.dmp_constant import JWT_SECRET
from dmplib.components import auth_util
from dmplib.conf_constants import DEFAULT_DASHBOARD_TEMPLATE_SECRET
# ---------------- 业务模块 ----------------
from dmplib import config
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.saas.project import set_correct_project_code
from dmplib.hug.application import auth_cookies_replacement


@authenticator
def verify_dashboard_template_handle(request, response, verify_user, **kwargs):
    """
    接口鉴权
    :param request:
    :return:
    """

    # 跳转无cookie
    # cookie_token = request.cookies.get("token")
    # if not cookie_token:
    #     cookie_token = request.get_header('Authorization')
    #     cookie_token = cookie_token.split(' ')[-1] if cookie_token else ''
    # if not cookie_token:
    #     return False
    auth_cookies_replacement(request)

    tenant_code, req_token = "", ""
    if request.method == "POST":
        body = request.bounded_stream.read()
        data = json.loads(body.decode('utf-8'))
        req_token = data.get("token")
        tenant_code = data.get("tenant_code")
        request._bounded_stream = request_helpers.BoundedStream(io.BytesIO(body), request.content_length or 0)
    elif request.method == "GET":
        req_token = request.get_param("token")
        tenant_code = request.get_param("tenant_code")
    if not req_token:
        raise UserError(401, "缺少鉴权TOKEN参数")

    decoded_token = _get_decoded_req_token(req_token)
    _verify_token_and_code(tenant_code, decoded_token)

    return True


def _get_decoded_req_token(req_token):
    """
    解析token
    :param req_token:
    :return:
    """
    secret = DEFAULT_DASHBOARD_TEMPLATE_SECRET
    try:
        decoded_token = jwt.decode(req_token, secret, algorithms="HS256")
    except DecodeError:
        raise UserError(403, "无效签名")
    except ExpiredSignatureError:
        raise UserError(403, "签名过期")
    except binascii.Error:
        raise UserError(403, "非法签名")
    except BaseException as e:
        raise UserError(403, str(e))
    return decoded_token


def check_token(token: str):
    """
    单独校验token
    :param token:
    :return:
    """
    if not token:
        raise UserError(code=403, message="未授权")
    verified = True
    errmsg = ""
    data = {}
    try:
        data = jwt.decode(token, JWT_SECRET, algorithms="HS256")
    except DecodeError:
        verified, errmsg = False, "无效的签名"
    except ExpiredSignatureError:
        verified, errmsg = False, "签名过期"
    except binascii.Error:
        verified, errmsg = False, "非法签名"
    except Exception as e:
        verified, errmsg = False, str(e)

    if not verified or not data:
        return False, errmsg, {}
    return True, errmsg, data


def _get_decoded_cookie_token(cookie_token):
    """
    解析token
    :param cookie_token:
    :return:
    """
    secret = JWT_SECRET
    decoded_token = jwt.decode(cookie_token, secret, algorithms="HS256")
    return decoded_token


def _verify_token_and_code(req_tenant_code, decoded_token):
    """
    校验token内容
    :param tenant_code:
    :param decoded_token:
    :return:
    """
    if not req_tenant_code:
        raise UserError(401, "缺少租户CODE参数")
    if not decoded_token:
        raise UserError(401, "缺少鉴权TOKEN参数")

    if req_tenant_code != decoded_token.get("tenant_code"):
        raise UserError(405, "租户CODE匹配失败")

    if not decoded_token.get("userid") or not decoded_token.get("account"):
        raise UserError(401, "缺少用户ID或账户名参数")

    current_client = auth_util.get_env_code()
    client = decoded_token.get("client")
    if client == "yunke_aliyun_prod_gray":
        client == "yumke_dmp"

    if not decoded_token.get("client") or decoded_token.get("client") != current_client:
        raise UserError(406, "请求方环境CODE已失效")

    setattr(g, "code", set_correct_project_code(decoded_token.get("tenant_code", "")))
    setattr(g, "userid", decoded_token.get("userid"))
    setattr(g, "account", decoded_token.get("account"))
