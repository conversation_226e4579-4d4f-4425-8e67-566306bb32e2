#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
    <NAME_EMAIL> on 2019/05/22.
"""

from dmplib.utils.strings import seq_id
from dmplib.saas.project import get_db
from base import repository
from image.models import CustomImageModel


def get_custom_image_by_oss_url(oss_url, type, biz_type):
    sql = '''
    SELECT
        id
    FROM
        dap_bi_custom_image_upload
    WHERE
        oss_url = %(oss_url)s and type = %(type)s and biz_type = %(biz_type)s
    LIMIT 1
    '''
    params = {'oss_url': oss_url, 'type': type, 'biz_type': biz_type}
    with get_db() as db:
        return db.query(sql, params)


def add_custom_image_record(oss_url, type, biz_type):
    model = CustomImageModel(
        id=seq_id(),
        oss_url=oss_url,
        type=type,
        biz_type=biz_type,
    )
    repository.add_model(
        CustomImageModel.__tablename__,
        model=model,
        fields=CustomImageModel.__slots__
    )


def get_custom_image_list(type, biz_type):
    wheres = {}
    if type:
        wheres['type'] = type
    if biz_type:
        wheres['biz_type'] = biz_type
    data = repository.get_list(
        CustomImageModel.__tablename__,
        conditions=wheres,
        fields=['id', 'oss_url', 'type', 'biz_type'],
        order_by=('created_on', 'desc')
    )
    return data


def delete_custom_image_by_id(image_id):
    return repository.delete_data(
        CustomImageModel.__tablename__,
        condition={'id': image_id}
    )
