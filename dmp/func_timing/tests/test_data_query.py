#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : test_data_query.py
# @Author: guq  
# @Date  : 2022/2/7
# @Desc  :
import os

os.environ['prometheus_multiproc_dir'] = '/tmp'

from tests.base_test import BaseTest
from dmplib.hug import g


class TestFuncTimingModel(BaseTest):

    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='test')

    def setUp(self):
        super().setUp()
        self.dashboard_id = '39fd437d-8b21-7793-7c70-cd9e2713c94e'
        self.snapshot_id = '39fd437d-8b21-7793-7c70-cd9e2713c94e'
        self.op_chart_id = '39fd437d-8b21-7c74-288c-01464efc954d'
        token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************.s8ijllLyrobkMUInVaxJdYeO0s4M7b5J4D03S7zc_Z4'
        g.cookie = {'token': token}
        g.code = 'local'

    def test_mysql_func(self):
        from base import repository
        from app_hook import func_timing_hook
        func_timing_hook()
        g._stack = 1

        result = repository.get_data_by_sql('select * from dap_bi_dashboard limit 10', params=())
        self.assertIsInstance(result, list)

        from dmplib.utils.time_util import STACK_TIME_MAP
        print('STACK_TIME_MAP', STACK_TIME_MAP)
