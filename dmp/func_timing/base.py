#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : base.py
# @Author: guq  
# @Date  : 2022/1/26
# @Desc  :


from importlib import import_module


def fake_func(name):
    wrapper = lambda x: x
    setattr(wrapper, '__name__', name)
    setattr(wrapper, '__qualname__', name)
    return wrapper


ff = fake_func


class Node:
    def __init__(self, func, level, prev=None, next=None, is_root=False, extra={}):  # NOSONAR
        self.prev = prev
        self.next = next
        self.level = level
        self.is_root = is_root
        self.func_name = func.__name__
        self.func = func
        self.extra = extra
        self.__register_next()
        self.hook_func = (self.__get_class_or_module(), func.__name__, level, extra)

    def __register_next(self):
        if not self.is_root:
            # if self.func != ff:
            if not self.prev.next:
                self.prev.next = [self]
            else:
                self.prev.next.append(self)

    def __get_class_or_module(self):
        # 模块中的函数 chart_service.adapt_chart_data_model.__qualname__  -> adapt_chart_data_model
        # 类中的函数   SqlQueryData.get_query_data.__qualname__   -> SqlQueryData.get_query_data
        if self.is_root:
            return
        slices = self.func.__qualname__.split('.')
        if len(slices) == 1:  # 模块
            return import_module(self.func.__module__)
        elif len(slices) == 2:  # 类
            return getattr(import_module(self.func.__module__), slices[0])
        else:
            raise RuntimeError(f'未知的情况：{self.func} {slices}')


root = Node(ff('root'), -1, is_root=True)


class SetNodeExtra:

    @staticmethod
    def after_change(node):
        node.hook_func = (*node.hook_func[:-1], node.extra)

    @staticmethod
    def set_ignore_args(node: Node):
        if not node.extra:
            node.extra = {}
        node.extra['ignore_args'] = True
        __class__.after_change(node)  # noqa
