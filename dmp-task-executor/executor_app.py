# --coding:utf-8--
from pyxxl import ExecutorConfig, PyxxlRunner
from pyxxl.ctx import g
import os

from components import config
from components.xxljob_util import get_executor_host
from producer import app_producer, celery_producer

executor_port = os.getenv("PORT") or 9999


config = ExecutorConfig(
    xxl_admin_baseurl=f"{config.get('XXL-JOB.admin_host')}/xxl-job-admin/api/",       # 调度器地址+路由前缀
    executor_app_name=config.get('XXL-JOB.default_group_appname', 'dmp-task-executor'),                             # 执行器名称
    max_workers=100,                                                    # 执行器线程池
    task_queue_length=10000,                                           # 任务队列长度
    executor_port=int(executor_port),                                  # 端口
    executor_host=get_executor_host(),                 # 执行器服务地址:端口， 默认9999, 生产环境配置为 dmp-task-executor, 执行器会自动注册到调度中心
    access_token=config.get("XXL-JOB.access_token"),                   # 令牌，非必填
    executor_listen_host="0.0.0.0",
    log_expired_days=0                                                 # 任务日志存储的本地的过期天数
)

app = PyxxlRunner(config)


@app.register(name="celeryJobHandler")
def celery_job_handler(*args, **kwargs):
    # 获取参数
    params = g.xxl_run_data.executorParams.split()

    celery_producer.producer(*params)

    return "成功..."


# 如果你代码里面没有实现全异步，请使用同步函数，不然会阻塞其他任务
@app.register(name="flowJobHandler")
def flow_job_handler():
    # 获取参数
    params = g.xxl_run_data.executorParams.split()

    # 向rabbitmq中丢任务
    app_producer.producer(*params)

    return "成功.."


if __name__ == '__main__':
    # 启动同步服务
    app.run_executor()

