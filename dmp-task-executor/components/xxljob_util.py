import requests
from loguru import logger

from components import config


def get_executor_host():
    try:
        from components import nacos_client
        host = nacos_client.NacosClient.get("dp-bi-server.insideUrl")
        if not host:
            response = requests.get(
                "http://localhost:8000/api/omp/nacos/get?key=dp-bi-server.insideUrl"
            )
            if response.status_code == 200:
                host = response.text.strip('"')
                host = host.replace("http://", "").split(':')[0]
            else:
                host = ''
        else:
            host = host.replace("http://", "").split(':')[0]

    except Exception as e:
        logger.error(f"get host error:{e}")
        host = ''
    return host or config.get("XXL-JOB.executor_host")
