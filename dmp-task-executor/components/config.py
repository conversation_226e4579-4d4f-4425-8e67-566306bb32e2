#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from loguru import logger
from configparser import RawConfigParser as ConfigParser, NoOptionError
from .nacos_client import NacosClient

from . import conf_constants, default_config
constants = conf_constants


class MissConfigValueException(Exception):
    def __init__(self, config_item):
        self.config_item = config_item
        super().__init__(config_item)

    def __str__(self):
        return "缺少配置项'%s'或值为空" % self.config_item


class Config:
    __instance = None

    def __init__(self, config_file_path=None):
        self.config_parser = ConfigParser()
        self.config_file_path = config_file_path or os.path.join(os.path.dirname(__file__), '../app.config')
        self.config_file_path = os.path.abspath(self.config_file_path)
        self.load_config()

    @staticmethod
    def get_instance():
        if Config.__instance:
            return Config.__instance
        Config.__instance = Config(os.environ.get('DMP_CFG_FILE_PATH'))
        return Config.__instance

    def load_config(self):
        if not os.path.isfile(self.config_file_path):
            # raise FileNotFoundError('配置文件不存在：' + str(self.config_file_path))
            logger.warning(f"app.config not exist：{self.config_file_path}")
        self.config_parser.read(self.config_file_path, 'utf-8')

    def get(self, key, default=None):
        """
        获取配置
        :param str key: 格式 [section].[key] 如：app.name
        :param Any default: 默认值
        :return:
        """
        map_key = key.split('.')
        if len(map_key) < 2:
            return default
        section = map_key[0]
        if not self.config_parser:
            return default
        if not self.config_parser.has_section(section):
            return default
        option = '.'.join(map_key[1:])
        try:
            return self.config_parser.get(section, option)
        except NoOptionError:
            return default

    def get_all_data(self):
        """
        获取所有配置
        :return:
        """
        result = dict()
        if not self.config_parser:
            return result
        for section in self.config_parser.sections():
            for key, value in self.config_parser.items(section):
                # 将 section 和 key 拼接为 'section.key' 的形式
                result[f'{section}.{key}'] = value

        return result


ALL_CONFIG = default_config.get_all_data()
ALL_CONFIG.update(Config.get_instance().get_all_data())
ALL_CONFIG.update(NacosClient.get_all_data())


def get(key, default=None):
    """
    获取配置
    :param str key: 格式 [section].[key] 如：app.name
    :param Any default: 默认值
    :return:
    """
    global ALL_CONFIG
    return ALL_CONFIG.get(key, default)


if __name__ == '__main__':
    obj = Config(config_file_path='./app.config')
    config_data = obj.get_all_data()
    print(config_data)
    print(ALL_CONFIG)
    print(get('App.name'))