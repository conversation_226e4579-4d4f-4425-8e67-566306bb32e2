
RET_STATE = {
    "KILL": 143,
    "FAIL": -1,
    "OK": 0,
    "RUN": 1,
    "RETRY": 2
}

OPENAPI_HOST = 'http://bigdata-api:8080/openapi'

FLOW_SCHEDULE = OPENAPI_HOST + '/schedule/run'

# 当前环境所有简讯使用的调度数据集集合列表（环境级）
SUBSCRIBE_MSG_USED_DATASET_ID_SET_KEY = 'subscribe_msg_used_dataset_id_list'

# 数据集对应队列缓存
QUEUE_OF_DATASET = '{code}:dataset_queue_name:dataset_id:{dataset_id}'

# redis模式
REDIS_SENTINEL = 'sentinel'
REDIS_STANDALONE = 'standalone'
REDIS_SINGLE = 'single'
REDIS_CLUSTER = 'cluster'
