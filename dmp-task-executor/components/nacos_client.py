from functools import lru_cache
import traceback

import nacos
from nacos_sdk_python import SkylineNacosClient, Config, ConfigSource
from nacos_sdk_python.constants import (
    ENV_SKYLINE_NACOS_SERVER_ADDRESSES, ENV_SKYLINE_NACOS_SECRET, ENV_SKYLINE_NACOS_DATA_ID,
    ENV_SKYLINE_NACOS_GROUP, ENV_SKYLINE_NACOS_USERNAME, ENV_SKYLINE_NACOS_PASSWORD,
    ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM, ENCRYPTION_ALGORITHM_NONE,
    ENV_SKYLINE_NACOS_NAMESPACE_ID
)
from nacos_sdk_python.parser import Parser
from configparser import RawConfigParser as ConfigParser, NoOptionError
import os
from components.nacos_utils import nacos_config_parser


def parse_properties(self, content: str):
    from nacos_sdk_python.cipher import Cipher

    if not content:
        return dict()

    rst = dict()
    cipher = Cipher(self._secret, algorithm=self._algorithm)
    for line in content.split("\n"):
        fields = line.split("=", 1)
        if len(fields) != 2:
            continue

        key = fields[0]
        value = fields[1]

        try:
            rst[key] = cipher.decrypt(value)
        except Exception as e:
            rst[key] = value

    return rst


Parser.parse_properties = parse_properties


class AppConfig:

    def __init__(self, content=""):
        self._conf = ConfigParser()
        self._conf.read_string(content)

    def get(self, key: str):
        section, key = key.split(".", 1)

        return self._conf.get(section, key)

    def get_all_data(self):
        """
        获取所有配置
        :return:
        """
        result = dict()
        if not self._conf:
            return result
        for section in self._conf.sections():
            for key, value in self._conf.items(section):
                # 将 section 和 key 拼接为 'section.key' 的形式
                result[f'{section}.{key}'] = value

        return result

    def refresh(self, content=""):
        conf = ConfigParser()
        conf.read_string(content)
        self._conf = conf


class SkylineNacosClientExtend(SkylineNacosClient):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def create_client_config(self, auto_refresh: bool = True, groups: list = None) -> Config:
        """创建一个配置对象"""
        config = Config()
        default_groups = [
            ("全局配置", "global.properties", "DEFAULT_GROUP"),
            ("全局路由配置", "global_url.properties", "DEFAULT_GROUP")
        ]
        if groups is not None:
            for name, data_id, group in groups:
                default_groups.append((name, data_id, group))

        for name, data_id, group in default_groups:
            config.add_config_source(self._build_config_source(name, data_id, group, auto_refresh))

        return config

    @classmethod
    def build_from_env_var(cls) -> 'SkylineNacosClientExtend':
        """从环境变量中构建"""
        nacos_addresses = cls.must_get_env(ENV_SKYLINE_NACOS_SERVER_ADDRESSES)
        data_id = cls.must_get_env(ENV_SKYLINE_NACOS_DATA_ID)
        group = cls.must_get_env(ENV_SKYLINE_NACOS_GROUP)
        username = os.getenv(ENV_SKYLINE_NACOS_USERNAME)
        password = os.getenv(ENV_SKYLINE_NACOS_PASSWORD)
        namespace = os.getenv(ENV_SKYLINE_NACOS_NAMESPACE_ID)

        if namespace:
            client = nacos.NacosClient(
                nacos_addresses, username=username, password=password, namespace=namespace)
        else:
            client = nacos.NacosClient(nacos_addresses, username=username, password=password)

        algorithm = os.getenv(ENV_SKYLINE_NACOS_ENCRYPTION_ALGORITHM, ENCRYPTION_ALGORITHM_NONE)
        if algorithm == ENCRYPTION_ALGORITHM_NONE:
            secret = os.getenv(ENV_SKYLINE_NACOS_SECRET)
        else:
            secret = cls.must_get_env(ENV_SKYLINE_NACOS_SECRET)

        parser = Parser(secret, algorithm=algorithm)
        return SkylineNacosClientExtend(parser=parser, client=client, data_id=data_id, group=group)


class NacosClient(object):
    __instance = None

    def __init__(self):
        self.client = SkylineNacosClientExtend.build_from_env_var()
        # auto_refresh = True 时会启动额外的线程监听 nacos 的配置变动, 自动更新自身
        self.client_config = self.client.create_client_config(
            auto_refresh=False,
            groups=[
                ("产品配置项", "resource_info.properties", "DEFAULT_GROUP"),
                ("实例配置项", "resource_instance.properties", "DEFAULT_GROUP")
            ]
        )
        try:
            # 应用配置不一定存在
            self.app_config = AppConfig(self.client.get_application_config_content())
        except:
            self.app_config = None
        # 注册配置变更后的回调操作
        # self.client_config.register_refresh_callback(NacosClient.refresh_callback)
        # self.client.register_app_config_refresh_callback(self.refresh_app_config)

    @staticmethod
    def get_instance():
        if NacosClient.__instance:
            return NacosClient.__instance
        NacosClient.__instance = NacosClient()
        return NacosClient.__instance

    def refresh_app_config(self, data_id: str, group: str, content: str):
        """应用配置变更时, 触发回调"""
        from components.config import ALL_CONFIG

        self.app_config.refresh(content)
        ALL_CONFIG.update(self.app_config.get_all_data())

    @staticmethod
    def refresh_callback(config: Config, source: ConfigSource):
        from components.config import ALL_CONFIG

        ALL_CONFIG.update(config.get_all_data())
        print(f"配置刷新了: {source.name}")

    @staticmethod
    def get(key: str, default: str = None):
        ins = NacosClient.get_instance()
        return ins.client_config.get(key, default=default)

    @staticmethod
    def get_all_data():

        try:
            ins = NacosClient.get_instance()
            config_data = ins.client_config.get_all_data()
            config_data.update(nacos_config_parser.parse(config_data))
            if ins.app_config:
                config_data.update(ins.app_config.get_all_data())
            return config_data
        except Exception as e:
            traceback.format_exc()
            return {}

    @staticmethod
    def getByTenant(key: str, tenant_code: str = None, default: str = None):
        ins = NacosClient()
        return ins.client_config.get(key, tenant_code=tenant_code, default=default)


def nacos_db_info():
    from components.nacos_utils.enums import ResourceUsedEnum, ResourceInstanceEnum
    ins = NacosClient()
    return {
        ResourceUsedEnum.DATASOURCE.value: ins.client_config.get(ResourceUsedEnum.DATASOURCE.value, default=''),
        ResourceInstanceEnum.DATASOURCE.value: ins.client_config.get(ResourceInstanceEnum.DATASOURCE.value, default='')
    }


@lru_cache()
def tenant_db_get(tenant_code):
    from components.nacos_utils.middleware_parser.db_parser import DBParser

    db_info = nacos_db_info()
    db_info = DBParser(db_info).tenant_db_parse(tenant_code)
    return {
        'host': db_info.host,
        'port': db_info.port,
        'database': db_info.database,
        'user': db_info.username,
        'password': db_info.password,
        'type': db_info.db_type,
        'db_type': db_info.db_type,
    }


@lru_cache()
def tenant_clean_db_get(tenant_code):
    from components.nacos_utils.middleware_parser.db_parser import DBParser

    db_info = nacos_db_info()
    db_info = DBParser(db_info).tenant_clean_db_parse(tenant_code)
    return {
        'host': db_info.host,
        'port': db_info.port,
        'database': db_info.database,
        'user': db_info.username,
        'password': db_info.password,
        'type': db_info.db_type,
        'db_type': db_info.db_type,
    }


if __name__ == "__main__":
    client = SkylineNacosClient.build_from_env_var()
    # 创建配置对象
    # auto_refresh = True 时会启动额外的线程监听 nacos 的配置变动, 自动更新自身
    client_config = client.create_client_config(auto_refresh=True)

    # 注册配置变更后的回调操作
    # client_config.register_refresh_callback(refresh_callback)

    # 获取配置, 不存在时返回 None
    client_config.get("runDeck.adminPassword")
    # 获取租户配置
    client_config.get("runDeck.adminPassword", tenant_code="zs")
    # 指定默认值
    client_config.get("runDeck.adminPassword", tenant_code="zs", default="LI93*3d")
    # 获取所有的配置
    client_config.get_all_data()
