#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试场景数据集信息获取接口
"""

import sys
import os

# 添加项目路径到sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dmp'))

def test_scene_dataset_info():
    """
    测试报表数据集信息获取功能
    """
    try:
        from ai.services.web_service import get_dashboard_dataset_info

        # 首先获取一个现有的dashboard_id进行测试
        print("正在测试报表数据集信息获取功能...")

        # 这里需要一个真实的dashboard_id来测试
        # 你可以从数据库中查询一个现有的dashboard_id
        test_dashboard_id = "test_dashboard_id"  # 替换为真实的dashboard_id

        print(f"测试报表ID: {test_dashboard_id}")

        # 测试获取数据集信息
        try:
            dataset_info = get_dashboard_dataset_info(test_dashboard_id)
            print("\n报表数据集信息获取成功:")
            print(f"  - 报表ID: {dataset_info['dashboard_id']}")
            print(f"  - 报表名称: {dataset_info['dashboard_name']}")
            print(f"  - 图表数量: {len(dataset_info.get('charts', []))}")

            # 显示全局参数信息
            global_params = dataset_info.get('global_params', {})
            if global_params.get('has_global_params'):
                print(f"\n=== 全局参数信息 ===")
                print(f"  - 全局参数数量: {global_params.get('params_count', 0)}")

                # 显示参数配置
                params = global_params.get('params', [])
                for i, param in enumerate(params[:3]):  # 只显示前3个参数
                    print(f"  参数 {i+1}:")
                    print(f"    - 参数ID: {param.get('id', '')}")
                    print(f"    - 参数名称: {param.get('name', '')}")
                    print(f"    - 别名: {param.get('alias_name', '')}")
                    print(f"    - 类型: {param.get('type', '')}")
                    print(f"    - 默认值: {param.get('default_value', '')}")
                    print(f"    - 描述: {param.get('description', '')}")
                    print(f"    - 必填: {param.get('required', False)}")

                if len(params) > 3:
                    print(f"  ... 还有 {len(params) - 3} 个参数")

                # 显示参数与数据集的关联关系
                dataset_relations = global_params.get('dataset_relations', [])
                if dataset_relations:
                    print(f"\n  参数与数据集关联关系:")
                    for i, relation in enumerate(dataset_relations[:2]):  # 只显示前2个关联
                        print(f"    关联 {i+1}:")
                        print(f"      - 参数: {relation.get('global_param_name', '')}")
                        print(f"      - 参数别名: {relation.get('alias_name', '')}")
                        print(f"      - 关联数据集数量: {len(relation.get('datasets', []))}")

                        for j, dataset in enumerate(relation.get('datasets', [])[:2]):  # 每个参数只显示前2个数据集
                            print(f"        数据集 {j+1}: {dataset.get('dataset_name', '')}")
                            print(f"          - 关联字段数量: {len(dataset.get('related_fields', []))}")

                            for k, field in enumerate(dataset.get('related_fields', [])[:2]):  # 每个数据集只显示前2个字段
                                print(f"            字段 {k+1}: {field.get('field_name', '')} ({field.get('field_type', '')})")

                    if len(dataset_relations) > 2:
                        print(f"    ... 还有 {len(dataset_relations) - 2} 个参数关联")
                else:
                    print("  - 无参数与数据集关联关系")
            else:
                error_msg = global_params.get('error', '无全局参数')
                print(f"\n=== 全局参数信息 ===")
                print(f"  - {error_msg}")

            # 显示图表信息（按图表分组）
            for i, chart in enumerate(dataset_info.get('charts', [])):
                print(f"\n=== 图表 {i+1} ===")
                print(f"  - 图表ID: {chart['chart_id']}")
                print(f"  - 图表名称: {chart['chart_name']}")
                chart_type = chart.get('chart_type', '未知类型')
                print(f"  - 图表类型: {chart_type}")
                print(f"  - 使用字段数量: {chart['field_count']}")

                # 显示字段分组信息
                fields_by_group = chart.get('fields_by_group', {})
                if fields_by_group:
                    print("  - 字段分组:")
                    for group_name, group_fields in fields_by_group.items():
                        if group_fields:
                            print(f"    * {group_name}: {len(group_fields)} 个字段")

                # 显示关联的数据集信息
                datasets = chart.get('datasets', [])
                print(f"  - 关联数据集数量: {len(datasets)}")
                for j, dataset in enumerate(datasets):
                    print(f"    数据集 {j+1}:")
                    print(f"      - 数据集ID: {dataset['dataset_id']}")
                    print(f"      - 数据集名称: {dataset['dataset_name']}")
                    print(f"      - 数据集描述: {dataset['dataset_description']}")
                    print(f"      - 数据集类型: {dataset['dataset_type']}")
                    print(f"      - 使用字段数量: {len(dataset['fields'])}")

                    # 显示字段详情（只显示前3个）
                    for k, field in enumerate(dataset['fields'][:3]):
                        print(f"        字段 {k+1}: {field['field_name']} - {field['field_description']}")

                    if len(dataset['fields']) > 3:
                        print(f"        ... 还有 {len(dataset['fields']) - 3} 个字段")

                # 显示SQL信息
                sql_info = chart.get('sql_info', {})
                if sql_info.get('has_sql'):
                    print("  - SQL信息:")
                    print(f"    * 数据集名称: {sql_info.get('dataset_name', '')}")
                    print(f"    * 数据集类型: {sql_info.get('dataset_type', '')}")
                    print(f"    * 字段统计: 维度{sql_info.get('dims_count', 0)}个, 度量{sql_info.get('nums_count', 0)}个")

                    # 显示数据集基础SQL
                    dataset_sql = sql_info.get('dataset_sql', '')
                    if dataset_sql:
                        sql_preview = dataset_sql[:100] + "..." if len(dataset_sql) > 100 else dataset_sql
                        print(f"    * 数据集SQL预览: {sql_preview}")

                    # 显示数据集基础SQL
                    dataset_sql = sql_info.get('dataset_sql', '')
                    if dataset_sql:
                        sql_preview = dataset_sql[:100] + "..." if len(dataset_sql) > 100 else dataset_sql
                        print(f"    * 数据集SQL预览: {sql_preview}")

                    # 显示生成的SQL
                    generated_sql = sql_info.get('generated_sql', '')
                    if generated_sql:
                        print(f"    * 生成的SQL:")
                        # 显示SQL的前200个字符，避免过长
                        sql_preview = generated_sql[:200] + "..." if len(generated_sql) > 200 else generated_sql
                        print(f"      {sql_preview}")
                    else:
                        print(f"    * 生成的SQL: 无")

                else:
                    print(f"  - SQL信息: {sql_info.get('error', '无SQL信息')}")
                    
        except Exception as e:
            print(f"获取数据集信息失败: {e}")

    except ImportError as e:
        print(f"导入模块失败: {e}")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_scene_dataset_info()
