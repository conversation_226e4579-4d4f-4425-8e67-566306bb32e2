"""
DMP MCP Server Package
动态工具自动刷新的 MCP 服务器
"""

from .models import ToolConfig, ServerConfig, ToolCallRequest, ToolCallResponse
from .services.dmp_service import DMPToolService
from .services.mcp_server import DMPMCPServer
from .services.tool_service import DynamicToolService

__version__ = "2.1.0"
__author__ = "DMP Team"
__description__ = "Dynamic MCP Server with auto-refresh tools"

__all__ = [
    'ToolConfig',
    'ServerConfig',
    'ToolCallRequest',
    'ToolCallResponse',
    'DMPToolService',
    'DMPMCPServer',
    'DynamicToolService'
]