#!/usr/bin/env python3
"""
DMP MCP 数据模型
定义工具配置和相关数据结构
"""

from typing import Any, Dict
from pydantic import BaseModel


class ToolConfig(BaseModel):
    """工具配置模型"""
    id: str
    name: str
    title: str
    description: str
    inputSchema: Dict[str, Any]

    class Config:
        """Pydantic 配置"""
        # 允许任意类型，用于处理复杂的 JSON Schema
        arbitrary_types_allowed = True


class ToolCallRequest(BaseModel):
    """工具调用请求模型"""
    tool_name: str
    arguments: Dict[str, Any]


class ToolCallResponse(BaseModel):
    """工具调用响应模型"""
    success: bool
    data: Any = None
    error: str = None


class ServerConfig(BaseModel):
    """服务器配置模型"""
    dmp_base_url: str = "http://localhost:8000"
    host: str = "0.0.0.0"
    port: int = 7000
    transport: str = "http"
    
    class Config:
        """Pydantic 配置"""
        env_prefix = "DMP_MCP_"  # 环境变量前缀
