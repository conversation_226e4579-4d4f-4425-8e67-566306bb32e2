# DMP MCP Server

一个基于 FastMCP 实现的 Model Context Protocol (MCP) 服务器，支持从 DMP 模块动态获取工具列表并自动刷新。

## 📋 目录

- [核心特性](#-核心特性)
- [项目架构](#-项目架构)
- [快速开始](#-快速开始)
- [配置选项](#️-配置选项)
- [API 接口](#-api-接口)
- [测试](#-测试)
- [客户端连接](#-客户端连接)
- [技术实现](#-技术实现)
- [开发指南](#-开发指南)
- [故障排除](#-故障排除)
- [更新日志](#-更新日志)

## 🚀 核心特性

- ✅ **自动刷新机制**：每次客户端访问 `tools/list` 时自动调用 `get_tool_list` 刷新工具列表
- ✅ **动态工具管理**：支持工具的实时增加、删除和修改，无需重启服务器
- ✅ **直接路由**：工具调用直接路由到 DMP 的 `get_data` 接口
- ✅ **类型安全**：根据工具的输入模式生成具体参数的函数，支持 FastMCP 的类型检查
- ✅ **模块化架构**：清晰的代码结构，易于维护和扩展
- ✅ **完整测试**：包含单元测试和集成测试
- ✅ **错误处理**：完善的异常处理和错误信息返回

## 🏗️ 项目架构

### 目录结构

```
dmp-mcp/
├── __init__.py              # 包初始化，导出主要类和接口
├── models.py                # 数据模型层：定义所有数据结构
├── main.py                  # 主入口文件：推荐的启动方式
├── demo.py                  # 演示脚本：展示架构和功能
├── services/                # 服务层：核心业务逻辑
│   ├── __init__.py
│   ├── dmp_service.py       # DMP API 服务：与后端交互
│   ├── mcp_server.py        # MCP 服务器：FastMCP 封装
│   └── tool_service.py      # 工具服务：动态工具管理
├── tests/                   # 测试层：单元测试和集成测试
│   ├── __init__.py
│   ├── test_dmp_service.py  # DMP 服务测试
│   └── test_mcp_server.py   # MCP 服务器测试
└── README.md                # 项目文档（本文件）
```

### 架构设计

#### 分层架构
- **数据模型层** (`models.py`)：定义数据结构和配置模型
- **服务层** (`services/`)：核心业务逻辑，包含 DMP 交互、工具管理、服务器封装
- **测试层** (`tests/`)：确保代码质量和功能正确性

#### 核心组件
1. **DMPToolService**：负责与 DMP 后端 API 交互
2. **DynamicToolService**：负责动态工具注册和管理
3. **DMPMCPServer**：FastMCP 服务器封装和管理

#### 数据流
```
客户端请求 → MCP服务器 → 工具服务 → DMP服务 → DMP后端API → 响应返回
```

## 🛠️ 安装依赖

```bash
pip install fastmcp httpx pydantic pytest
```

## 🚀 快速开始

### 方式一：命令行启动

```bash
# 使用默认配置启动
python main.py

# 指定 DMP 服务地址和传输方式
python main.py --dmp-url http://localhost:8000 --transport http --port 7000

# 使用 stdio 传输方式（适用于 Claude Desktop）
python main.py --transport stdio
```

### 方式二：编程方式使用

```python
from dmp_mcp import DMPMCPServer, ServerConfig

# 创建配置
config = ServerConfig(
    dmp_base_url="http://localhost:8000",
    host="0.0.0.0",
    port=7000,
    transport="http"
)

# 创建并启动服务器
server = DMPMCPServer(config)
server.run()

# 或者使用异步上下文管理器
async with DMPMCPServer(config) as server:
    await server.run_async()
```

## ⚙️ 配置选项

### 命令行参数

- `--dmp-url`: DMP 服务基础URL（默认：http://localhost:8000）
- `--host`: MCP 服务器监听地址（默认：0.0.0.0）
- `--port`: MCP 服务器监听端口（默认：7000）
- `--transport`: 传输方式，可选 stdio/http/sse（默认：http）

### 环境变量

- `DMP_BASE_URL`: DMP 服务基础URL
- `DMP_MCP_DMP_BASE_URL`: DMP 服务基础URL（优先级更高）
- `DMP_MCP_HOST`: MCP 服务器监听地址
- `DMP_MCP_PORT`: MCP 服务器监听端口
- `DMP_MCP_TRANSPORT`: 传输方式

## 🔧 API 接口

### DMP 服务接口

服务器依赖以下 DMP 接口：

1. **获取工具列表**
   ```
   POST /openapi/ai/get_tool_list
   Response: {
     "data": [
       {
         "id": "1",
         "name": "scene_example",
         "title": "示例工具",
         "description": "这是一个示例工具",
         "input_schema": { ... }
       }
     ]
   }
   ```

2. **调用工具**
   ```
   POST /openapi/ai/get_data
   Body: {
     "scene_id": "example",
     "params": "{\"param1\": \"value1\"}"
   }
   ```

## 🧪 测试

### 运行所有测试

```bash
cd dmp-mcp
python -m pytest tests/ -v
```

### 运行特定测试

```bash
# 测试 DMP 服务
python -m pytest tests/test_dmp_service.py -v

# 测试 MCP 服务器
python -m pytest tests/test_mcp_server.py -v
```

### 手动测试

```bash
# 测试导入
python -c "from dmp_mcp import DMPMCPServer; print('导入成功')"

# 测试服务器创建
python -c "from dmp_mcp import DMPMCPServer, ServerConfig; server = DMPMCPServer(); print('服务器创建成功')"
```

## 🔌 客户端连接

### Claude Desktop 配置

```json
{
  "mcpServers": {
    "dmp-mcp": {
      "command": "python",
      "args": ["path/to/dmp-mcp/main.py"],
      "env": {
        "DMP_BASE_URL": "http://localhost:8000"
      }
    }
  }
}
```

### MCP Inspector 连接

1. 启动服务器：
   ```bash
   python main.py --transport http --port 7000
   ```

2. 在 MCP Inspector 中连接：`http://localhost:7000/mcp/`

### HTTP 客户端连接

```python
from fastmcp import Client

async with Client("http://localhost:7000/mcp/") as client:
    # 获取工具列表（会自动刷新）
    tools = await client.list_tools()
    
    # 调用工具
    result = await client.call_tool("scene_example", {"param": "value"})
```

## 🔧 技术实现

### 自动刷新机制

服务器通过重写 FastMCP 的内部工具列表方法实现自动刷新：

```python
def setup_dynamic_tools(self):
    """设置动态工具处理机制"""

    async def dynamic_list_tools():
        """动态获取工具列表并注册新工具"""
        # 1. 获取最新工具列表
        tools = await self.dmp_service.get_tool_list()

        # 2. 注册新工具
        for tool in tools:
            if tool.name not in self._registered_tools:
                self._register_dynamic_tool(tool)
                self._registered_tools.add(tool.name)

        # 3. 返回有效工具
        return valid_tools

    # 替换 FastMCP 的内部方法
    self.mcp._list_tools = dynamic_list_tools
```

### 工作流程

1. **客户端请求工具列表** (`tools/list`)
   - 服务器自动调用 `get_tool_list()` 从 DMP 获取最新工具列表
   - 对于新工具，动态生成函数并使用 `@mcp.tool` 装饰器注册
   - 返回符合 MCP 协议的工具对象列表

2. **客户端调用工具** (`tools/call`)
   - FastMCP 调用已注册的工具函数
   - 工具函数内部调用 `call_get_data()` 路由到 DMP 接口
   - 将结果返回给客户端

### 设计原则

- **单一职责原则**：每个模块只负责一个特定功能
- **开闭原则**：对扩展开放，对修改封闭
- **依赖倒置原则**：高层模块不依赖低层模块
- **接口隔离原则**：提供最小化的接口

## 🔍 技术细节

### 工具名称映射

- DMP 工具名：`scene_xxx` → Scene ID：`xxx`
- 下划线自动转换为连字符：`scene_test_tool` → `test-tool`

### 动态函数生成

服务器根据工具的 `inputSchema` 动态生成具有具体参数的函数：

```python
# 输入模式
{
  "type": "object",
  "properties": {
    "message": {"type": "string"},
    "count": {"type": "integer", "default": 1}
  },
  "required": ["message"]
}

# 生成的函数签名
async def tool_scene_example(message: str, count: Optional[int] = 1) -> Dict[str, Any]:
    return await dmp_service.call_get_data("scene_example", message=message, count=count)
```

### 错误处理

- 网络错误：返回错误信息，不中断服务
- 工具不存在：返回相应错误消息
- 参数错误：由 FastMCP 和 DMP 接口处理并返回错误信息

### 性能考虑

- 每次 `tools/list` 请求都会调用 `get_tool_list`，确保数据最新
- 工具注册采用缓存机制，避免重复注册
- HTTP 客户端复用，减少连接开销
- 异步处理，支持并发请求

## 👨‍💻 开发指南

### 扩展新功能

#### 1. 添加新的工具类型

在 `services/tool_service.py` 中扩展类型映射：

```python
type_mapping = {
    'string': str,
    'integer': int,
    'number': float,
    'boolean': bool,
    'array': list,
    'object': dict,
    # 添加新类型
    'custom_type': CustomType
}
```

#### 2. 添加新的传输协议

在 `services/mcp_server.py` 中支持新协议：

```python
def run(self, transport="http"):
    if transport == "custom":
        # 实现自定义传输协议
        pass
```

#### 3. 添加新的配置项

在 `models.py` 中扩展配置：

```python
class ServerConfig(BaseModel):
    # 现有配置...

    # 添加新配置项
    custom_setting: str = "default"
    enable_feature: bool = False
```

### 代码规范

- 使用 Python 3.8+ 的类型注解
- 遵循 PEP 8 代码风格
- 使用 async/await 进行异步编程
- 为所有公共方法添加文档字符串
- 使用 Pydantic 进行数据验证

### 测试指南

#### 编写单元测试

```python
import pytest
from unittest.mock import AsyncMock, patch

class TestNewFeature:
    @pytest.mark.asyncio
    async def test_new_functionality(self):
        # 测试代码
        pass
```

#### 运行测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试文件
python -m pytest tests/test_dmp_service.py -v

# 运行测试并生成覆盖率报告
python -m pytest tests/ --cov=. --cov-report=html
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 贡献流程

1. **Fork 项目**
   ```bash
   git clone https://github.com/your-username/dmp-mcp-server.git
   cd dmp-mcp-server
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **开发和测试**
   ```bash
   # 安装依赖
   pip install -r requirements.txt

   # 运行测试
   python -m pytest tests/ -v

   # 运行演示
   python demo.py
   ```

4. **提交更改**
   ```bash
   git add .
   git commit -m "Add amazing feature"
   ```

5. **推送并创建 PR**
   ```bash
   git push origin feature/amazing-feature
   ```

### 贡献类型

- 🐛 **Bug 修复**：修复现有功能的问题
- ✨ **新功能**：添加新的功能或改进
- 📚 **文档**：改进文档和示例
- 🧪 **测试**：添加或改进测试用例
- 🎨 **代码优化**：重构和性能优化

### 代码规范

- 遵循 PEP 8 代码风格
- 使用类型注解
- 添加适当的文档字符串
- 确保所有测试通过
- 更新相关文档

## 📝 更新日志

### v2.1.0 - 模块化重构版本 (2024-01-01)

- ✅ 重构代码为模块化架构
- ✅ 分离数据模型、服务层和测试
- ✅ 修复 MCP Inspector 兼容性问题
- ✅ 改进动态工具注册机制
- ✅ 完善测试用例和文档
- ✅ 默认端口更改为 7000
- ✅ 移除兼容性入口文件

### v2.0.0 - 动态工具自动刷新版本 (2023-12-01)

- ✅ 实现自动刷新机制
- ✅ 移除静态工具注册
- ✅ 重写 FastMCP 内部处理器
- ✅ 添加完整的测试和演示

### v1.0.0 - 静态工具版本 (2023-11-01)

- ✅ 基础 MCP 服务器实现
- ✅ 静态工具注册
- ✅ DMP 接口集成

## 📄 许可证

本项目采用 MIT 许可证。

```
MIT License

Copyright (c) 2024 DMP Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🙏 致谢

- [FastMCP](https://github.com/jlowin/fastmcp) - 优秀的 MCP 框架
- [Model Context Protocol](https://modelcontextprotocol.io/) - MCP 协议规范
- [Pydantic](https://pydantic-docs.helpmanual.io/) - 数据验证库
- [httpx](https://www.python-httpx.org/) - 现代 HTTP 客户端

---

如果这个项目对你有帮助，请给我们一个 ⭐️！

## 🆘 故障排除

### 常见问题

#### 1. 导入错误
```bash
ModuleNotFoundError: No module named 'xxx'
```
**解决方案**：
- 确保所有依赖已安装：`pip install fastmcp httpx pydantic pytest`
- 确保在正确的目录中运行
- 检查 Python 版本是否为 3.8+

#### 2. 连接失败
```bash
Server error '502 Bad Gateway' for url 'http://localhost:8000/openapi/ai/get_tool_list'
```
**解决方案**：
- 检查 DMP 服务是否正在运行
- 验证 DMP 服务 URL 是否正确
- 检查网络连接和防火墙设置

#### 3. 工具不显示
**可能原因**：
- DMP 服务的 `get_tool_list` 接口返回空数据
- 工具配置格式不正确
- 权限问题

**解决方案**：
- 直接访问 DMP 接口验证数据：`curl -X POST http://localhost:8000/openapi/ai/get_tool_list`
- 检查工具配置的 JSON Schema 格式
- 查看服务器日志输出

#### 4. MCP Inspector 连接失败
**解决方案**：
- 确保使用 `--transport http` 启动服务器
- 检查端口是否被占用
- 验证防火墙设置

#### 5. 端口占用
```bash
OSError: [Errno 48] Address already in use
```
**解决方案**：
- 更换端口：`python main.py --port 7001`
- 查找占用端口的进程：`lsof -i :7000`（macOS/Linux）或 `netstat -ano | findstr :7000`（Windows）

### 调试技巧

#### 启用详细日志
```bash
python main.py --dmp-url http://localhost:8000 --transport http --port 7000
```

#### 使用演示脚本测试
```bash
python demo.py
```

#### 手动测试 DMP 接口
```bash
# 测试工具列表接口
curl -X POST http://localhost:8000/openapi/ai/get_tool_list

# 测试工具调用接口
curl -X POST http://localhost:8000/openapi/ai/get_data \
  -H "Content-Type: application/json" \
  -d '{"scene_id": "example", "params": "{\"param1\": \"value1\"}"}'
```

### 性能优化

#### 1. 减少工具列表刷新频率
如果工具变化不频繁，可以考虑添加缓存机制。

#### 2. 连接池优化
调整 HTTP 客户端的连接池设置：

```python
self.client = httpx.AsyncClient(
    timeout=30.0,
    limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
)
```

#### 3. 内存使用优化
对于大量工具的场景，考虑实现工具的懒加载机制。

如有其他问题，请查看日志输出或提交 Issue。
