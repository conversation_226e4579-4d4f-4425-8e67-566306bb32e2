#!/usr/bin/env python3.12
"""
DMP MCP Server 主入口
"""

import argparse
import os
from models import ServerConfig
from services.mcp_server import DMPMCPServer


def create_server_config(args) -> ServerConfig:
    """根据命令行参数创建服务器配置"""
    return ServerConfig(
        dmp_base_url=args.dmp_url,
        host=args.host,
        port=args.port,
        transport=args.transport
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="DMP MCP Server - 动态工具自动刷新版本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 使用默认配置启动
  python main.py

  # 指定 DMP 服务地址和传输方式
  python main.py --dmp-url http://localhost:8000 --transport http --port 7000

  # 使用 stdio 传输方式（适用于 Claude Desktop）
  python main.py --transport stdio
        """
    )
    
    parser.add_argument(
        "--dmp-url", 
        default=os.getenv("DMP_BASE_URL", "http://localhost:8000"), 
        help="DMP服务基础URL（默认：http://localhost:8000）"
    )
    parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="MCP服务器监听地址（默认：0.0.0.0）"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=7000,
        help="MCP服务器监听端口（默认：7000）"
    )
    parser.add_argument(
        "--transport", 
        choices=["stdio", "http", "sse"], 
        default="http", 
        help="传输方式（默认：http）"
    )
    parser.add_argument(
        "--version", 
        action="version", 
        version="DMP MCP Server v2.1"
    )
    
    args = parser.parse_args()
    
    # 创建服务器配置
    config = create_server_config(args)
    
    # 创建并启动服务器
    server = DMPMCPServer(config)
    
    try:
        server.run()
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")
        exit(1)


if __name__ == "__main__":
    main()
