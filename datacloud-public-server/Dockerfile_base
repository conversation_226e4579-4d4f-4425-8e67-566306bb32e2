
FROM docker-prod-registry.cn-hangzhou.cr.aliyuncs.com/dmp/datacloud-pub:python3.10-slim as builder

ARG PLATFORM=x86

ENV DM_HOME="/home/<USER>/webapp/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"

COPY requirements.txt ./

RUN pip config set global.index-url  https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip install --no-cache-dir -r requirements.txt && \
    sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org/debian-security|mirrors.ustc.edu.cn/debian-security|g' /etc/apt/sources.list.d/debian.sources  && \
    apt-get update && \
    apt-get install -y  wget  gcc unzip && \
    rm -rf /var/lib/apt/lists


RUN if [ "$PLATFORM" = "arm" ]; then \
        wget -P /dmp-agent https://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/config-agent/v5/agent-arm64 && mv /dmp-agent/agent-arm64 /dmp-agent/agent && \
        wget -P /home/<USER>/webapp/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/arm64/dmdbms.zip && \
        cd /home/<USER>/webapp/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip; \
    else \
        wget -P /dmp-agent -o agent http://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/agent && \
        wget -P /home/<USER>/webapp/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/x86/dmdbms.zip && \
        cd /home/<USER>/webapp/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip; \
    fi


RUN cd /home/<USER>/webapp/dmdbms/dmPython && python setup.py install && \
    cd /home/<USER>/webapp/dmdbms/sqlalchemy2.0.0 && python setup.py install


FROM docker-prod-registry.cn-hangzhou.cr.aliyuncs.com/dmp/datacloud-pub:python3.10-slim

ENV DM_HOME="/home/<USER>/webapp/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"


COPY --from=builder /usr/local/lib/python3.10 /usr/local/lib/python3.10
COPY --from=builder /dmp-agent/agent /dmp-agent/agent
COPY --from=builder /usr/local/bin/uvicorn /usr/local/bin/uvicorn
#COPY --from=builder /usr/local/bin/celery /usr/local/bin/celery
COPY --from=builder /home/<USER>/webapp/dmdbms /home/<USER>/webapp/dmdbms

RUN pip config set global.index-url  https://pypi.tuna.tsinghua.edu.cn/simple && \
    sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org/debian-security|mirrors.ustc.edu.cn/debian-security|g' /etc/apt/sources.list.d/debian.sources  && \
    apt-get update && \
    apt-get install -y --allow-unauthenticated --no-install-recommends libpq-dev vim curl && \
    chmod +x /dmp-agent/agent && \
    chmod +x /home/<USER>/webapp/dmdbms/bin/dimp && chmod +x /home/<USER>/webapp/dmdbms/bin/dexp && \
    rm -rf /var/lib/apt/lists
