#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json

import fastapi
from fastapi import APIRouter, Query, UploadFile, File, Form

from backend.app.import_dist.service.rdc import RDCDispatcher
from backend.app.proxy.api.proxy import ProxyData
from backend.app.publish_center.schema.rdc import DapImportStatus
from backend.common.domain import get_server_domain
from backend.common.log import log
from backend.common.request import HTTPRequestUtil
from backend.common.response.response_schema import response_base, ResponseModel

from backend.app.import_dist.schema.import_dist import RdcParams
from backend.consumers.tasks.upadate_error_template import UpdateErrorTemplateConsumer
from backend.database.db_util import CurrentSession
from backend.common.jwt import rdc_verify, CookiesUser

router = APIRouter()


@router.post('/push_product', summary='RDC的推包接口')
async def push_product(request: fastapi.Request, db: CurrentSession, file: UploadFile = File(...),
                            tenantCode: str = Form(...),
                            tenantType: str = Form(...),
                            taskId: str = Form(...),
                            appCode: str = Form(...),
                            appName: str = Form(...),
                            appType: str = Form(...),
                            appKey: str = Form(...),
                            envCode: str = Form(...),
                            operator: str = Form(...),
                            customerGuid: str = Form(...),
                            businessMode: str = Form(...),
                            upgradeScene: str = Form(...),
                            version: str = Form(...),
                       ):
    auth = request.headers.get('authorization') or ''
    rs, _ = await rdc_verify(auth)
    if not rs:
        return await response_base.fail(data='jwt校验失败!')
    params = RdcParams(
        appCode=appCode,
        appKey=appKey,
        appName=appName,
        appType=appType,
        businessMode=businessMode,
        customerGuid=customerGuid,
        envCode=envCode,
        operator=operator,
        taskId=f'{taskId}_{appCode}_{tenantCode}',  # 调整task_id为 task_id+appCode+tenantCode, 解决批量更新task_id一样，导致后续流程冲突的问题
        tenantCode=tenantCode,
        tenantType=tenantType,
        upgradeScene=upgradeScene,
        version=version,
    )
    # params.file_byte = await file.read()
    data = await RDCDispatcher(db, params).dispatch(file)
    result = {"result": True, "msg": "ok", "data": data}
    log.error(f"【{appCode}】【{appName}】【{version}】【{taskId}】处理rdc返回：{json.dumps(result, ensure_ascii=False)}")
    return result


@router.post('/dap_import_status', summary='代理请求dap状态的接口')
async def dap_import_status(request: fastapi.Request, params: DapImportStatus) -> ResponseModel:
    """
    用作RDC插件查询数芯的模板库导入状态的接口
    """
    auth = request.headers.get('authorization') or ''
    rs, _ = await rdc_verify(auth)
    if not rs:
        return await response_base.fail(data='jwt校验失败!')

    data = {
        'id': params.dap_task_id
    }
    dap_domain = get_server_domain('dap')
    user = CookiesUser(tenant_code='template')  # 标准空间使用template作为标识
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    kwargs = {
        'url': f'{dap_domain}/api/common/publish_center/import/task/process/get',
        'params': data,
        'headers': headers,
    }
    response = await HTTPRequestUtil.request('get', **kwargs)
    log.error(f"【{params.dap_task_id}】获取数芯公共空间导入的结果： {response.content.decode()}")
    data = response.json()
    # rs = data.get('result', False)
    # if rs is False:
    await UpdateErrorTemplateConsumer().push(**{
        'dap_task_id': params.dap_task_id,
        'data': data
    })
    return data


@router.get('/test', summary='代理请求dap状态的测试接口')
async def test1111():
    """
    用作RDC插件查询数芯的模板库导入状态的测试接口
    """
    return {'result': True, 'msg': 'ok'}
