#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import os
from datetime import datetime

from sqlalchemy import select, update, desc, and_, distinct, func
from sqlalchemy.orm import Session

from backend.app.import_dist.model.import_dist import ImportDist, RDC, ImportDistDetail
from backend.common.enums import ImportStatus, RDCTenantType
from backend.common.jwt import CookiesUser
from backend.common.pagination import PaginationParams
from backend.database.db_util import uuid4_str, CurrentSession


async def get_rdcs(db: CurrentSession, user: CookiesUser, pagination: PaginationParams, keyword: str):
    """
    获取所有rdc推送记录
    """
    query = db.query(RDC).filter(
        RDC.tenant_code == user.tenant_code,
    )
    if keyword:
        query = query.filter(
            RDC.app_name.like(f'%{keyword}%')
        )

    total = query.count()
    datas = (
        query.order_by(RDC.created_at.desc()).
        offset(pagination.offset).
        limit(pagination.limit).all()
    )
    result = []
    for rdc in datas:
        rdc_data = rdc.to_dict()
        try:
            rdc_data['log'] = json.loads(rdc_data['log'])
        except:
            rdc_data['log'] = {}
        result.append(rdc_data)
    return {'items': result, 'total': total}


async def get_imports(
        db: CurrentSession, user: CookiesUser, pagination: PaginationParams, keyword: str,
        app_code: str = '',
        created_at_start: str = '',
        created_at_end: str = '',
        status: str = '',
):
    """
    获取导入的列表list
    """
    # subquery = (
    #     db.query(RDC.rdc_task_id.label('rdc_task_id'), func.min(RDC.id).label('min_id'))
    #     .group_by(RDC.rdc_task_id)
    # )
    # if keyword:
    #     subquery = subquery.filter(
    #         RDC.app_name.like(f'%{keyword}%')
    #     )
    # subquery = subquery.subquery()

    query = db.query(ImportDist, RDC).join(
        RDC,
        RDC.id == ImportDist.rdc_record_id
        # ).join(
        #     RDC,
        #     RDC.id == subquery.c.min_id
    ).filter(
        ImportDist.tenant_code == user.tenant_code,
        ImportDist.tenant_type == RDCTenantType.tenant.value,  # 前台用户列表只展示租户库的导入记录
    )
    if keyword:
        query = query.filter(
            RDC.app_name.like(f'%{keyword}%')
        )
    if app_code:
        query = query.filter(
            RDC.app_code == app_code
        )
    if created_at_start:
        query = query.filter(
            RDC.created_at >= created_at_start
        )
    if created_at_end:
        query = query.filter(
            RDC.created_at <= created_at_end
        )
    if status:
        query = query.filter(
            ImportDist.status == status
        )

    total = query.count()
    datas = (
        query.order_by(ImportDist.created_at.desc()).
        offset(pagination.offset).
        limit(pagination.limit).all()
    )
    result = [
        dict(
            id=import_dist.id,
            tenant_code=import_dist.tenant_code,
            name=rdc.app_name,
            app_code=rdc.app_code,
            rdc_task_id=rdc.rdc_task_id,
            rdc_record_id=rdc.id,
            rdc_file_oss_url=rdc.file_oss_url,
            dap_oss_url=import_dist.dap_oss_url,
            dap_import_id=import_dist.dap_import_id,
            dmp_oss_url=import_dist.dmp_oss_url,
            app_name=rdc.app_name,
            version=rdc.version,
            request_args=import_dist.request_args,
            status=import_dist.status,
            execute_update_time=import_dist.execute_update_time,
            created_at=import_dist.created_at,
            updated_at=import_dist.updated_at,
            created_by=import_dist.created_by,
            updated_by=import_dist.updated_by,
            package_type=import_dist.package_type,
            backup_oss_url=import_dist.backup_oss_url,
            backup_args=import_dist.backup_args
        )
        for import_dist, rdc in datas
    ]
    return {'items': result, 'total': total}


async def has_unimport_tasks(db: CurrentSession, tenant_code):
    """
    获取租户是否有未执行的导入任务
    """
    data = db.query(ImportDist.id).filter(
        ImportDist.status == ImportStatus.not_import.value,
        ImportDist.tenant_code == tenant_code,
        ImportDist.tenant_type == RDCTenantType.tenant.value,
    ).count()
    return data > 0


async def get_import_detail(db: CurrentSession, id: str) -> ImportDist:
    """获取导入记录详情"""
    data = db.query(ImportDist).filter(
        ImportDist.id == id,
        # ImportDist.tenant_type == RDCTenantType.tenant.value,
    ).limit(1).scalar()
    return data


async def get_rdc_detail(db: CurrentSession, id: str) -> RDC:
    """获取导入记录详情"""
    data = db.query(RDC).filter(
        RDC.id == id,
    ).order_by(RDC.created_at.desc()).limit(1).scalar()
    return data


async def update_one_import_record(db: CurrentSession, import_id: str, export_data: dict):
    """
    更新导入记录的数据
    """
    db.query(ImportDist).filter(ImportDist.id == import_id).update(export_data)
    db.commit()


async def add_one_import_detail(db: CurrentSession, **kwargs) -> str:
    """
    添加一条导入明细日志
    """
    model_id = uuid4_str()
    add_model = ImportDistDetail(id=model_id, **kwargs)
    db.add(add_model)
    db.commit()
    return model_id


async def get_one_import_log(db: CurrentSession, import_id: str):
    """
    记录
    """
    data = db.query(ImportDist).filter(ImportDist.id == import_id).first()
    if data.space_type == 'public':
        data = db.query(RDC.log).filter(RDC.id == data.rdc_record_id).first()
    if data.log:
        log_data = json.loads(data.log)
    else:
        log_data = {}
    return log_data
