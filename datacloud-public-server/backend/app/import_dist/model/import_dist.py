#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from collections import namedtuple, OrderedDict
from datetime import datetime

from sqlalchemy import String, SmallInteger, Column, DateTime, Text, BLOB, INT
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects.mysql import MEDIUMTEXT, LONGTEXT

from backend.common.model import BaseModel, table, FormattedDateTime
from backend.core.conf import settings
from backend.database.db_util import uuid4_str
from backend.utils.timezone import timezone


class RDC(BaseModel):
    """导出表"""

    __tablename__ = table('rdc')
    __table_args__ = {'comment': '数据发布中心记录rdc的推送信息表'}

    id: Mapped[uuid4_str] = mapped_column(String(36), comment='id', primary_key=True, default=uuid4_str)

    tenant_code: Mapped[str] = mapped_column(String(128), comment='租户code', default='', nullable=True)
    tenant_type: Mapped[str] = mapped_column(String(256), comment='租户类型', default='', nullable=True)
    rdc_task_id: Mapped[str] = mapped_column(String(36), comment='rdc的taskid', default='', nullable=True)
    customer_guid: Mapped[str] = mapped_column(String(36), comment='customerGuid', default='', nullable=True)
    app_code: Mapped[str] = mapped_column(String(256), comment='appCode', default='', nullable=True)
    version: Mapped[str] = mapped_column(String(256), comment='version', default='', nullable=True)
    app_key: Mapped[str] = mapped_column(String(256), comment='appKey', default='', nullable=True)
    env_code: Mapped[str] = mapped_column(String(256), comment='envCode', default='', nullable=True)
    operator: Mapped[str] = mapped_column(String(256), comment='operator', default='', nullable=True)
    business_mode: Mapped[str] = mapped_column(String(256), comment='businessMode', default='', nullable=True)
    app_type: Mapped[str] = mapped_column(String(256), comment='appType', default='', nullable=True)
    app_name: Mapped[str] = mapped_column(String(256), comment='appName', default='', nullable=True)
    upgrade_scene: Mapped[str] = mapped_column(String(256), comment='upgradeScene', default='', nullable=True)

    file_oss_url: Mapped[str] = mapped_column(
        String(4096), comment='内部上传file的oss链接地址', default='', nullable=True
    )
    if settings.DB_TYPE.upper() == "DM":
        log: Mapped[str] = mapped_column(Text, comment='rdc推包更中心处理日志', nullable=True)
    else:
        log: Mapped[str] = mapped_column(MEDIUMTEXT, comment='rdc推包更中心处理日志', nullable=True)


class ImportDist(BaseModel):
    """导入的表"""

    __tablename__ = table('import_dist')
    __table_args__ = {'comment': '数据发布中心导入表', }

    id: Mapped[uuid4_str] = mapped_column(String(36), comment='导入数据包名称', primary_key=True, default=uuid4_str)

    rdc_record_id: Mapped[str] = mapped_column(String(36), comment='rdc表的关联id', default='', nullable=True)
    rdc_task_id: Mapped[str] = mapped_column(String(36), comment='rdc的taskid', default='', nullable=True)
    tenant_code: Mapped[str] = mapped_column(String(128), comment='租户code', default='', nullable=True)

    execute_update_time = Column(FormattedDateTime, default=None, nullable=True, comment='执行更新的时间')
    status: Mapped[str] = mapped_column(String(64), comment='导入状态', default='', nullable=True)

    dmp_oss_url = Column(String(2048), default='', comment='dmp的oss地址')
    dap_oss_url = Column(String(2048), default='', comment='dap的oss地址')
    dap_import_id = Column(String(36), default='', comment='dap的预先导入的任务id')

    tenant_type: Mapped[str] = mapped_column(String(256), comment='租户类型，什么类型的导入任务', default='TENANT',
                                             nullable=True)
    space_type: Mapped[str] = mapped_column(String(256), comment='空间类型。定制/公共', default='', nullable=True)
    has_import_dap: Mapped[str] = mapped_column(String(100), comment='是否调用过数芯的导入接口（判断是否是首次调用）',
                                                default='', nullable=True)
    if settings.DB_TYPE.upper() == "DM":
        log: Mapped[str] = mapped_column(Text, comment='请求导入的日志', nullable=True)
        request_args: Mapped[str] = mapped_column(Text, comment='导入请求的参数', nullable=True)
        backup_args: Mapped[str] = mapped_column(Text, comment='备份请求参数', nullable=True)
    else:
        log: Mapped[str] = mapped_column(MEDIUMTEXT, comment='请求导入的日志', nullable=True)
        request_args: Mapped[str] = mapped_column(MEDIUMTEXT, comment='导入请求的参数', nullable=True)
        backup_args: Mapped[str] = mapped_column(MEDIUMTEXT, comment='备份请求参数', nullable=True)

    space_type: Mapped[str] = mapped_column(String(256), comment='空间类型。定制/公共', default='', nullable=True)
    package_type: Mapped[str] = mapped_column(String(36), comment='导入包类型。产品更新包/定制包', default='产品更新包',
                                              nullable=True)
    backup_oss_url = Column(String(2048), default='', comment='定制包导入备份数据的oss地址')


class ImportDistDetail(BaseModel):
    """导入的表"""

    __tablename__ = table('import_dist_detail')
    __table_args__ = {'comment': '数据发布中心导入日志表'}

    id: Mapped[uuid4_str] = mapped_column(String(36), comment='id', primary_key=True, default=uuid4_str)

    import_id: Mapped[str] = mapped_column(String(36), comment='import_dist的id', default='', nullable=True)
    source: Mapped[str] = mapped_column(String(128), comment='产品来源', default='', nullable=True)
    status: Mapped[str] = mapped_column(String(64), comment='导入状态', default='', nullable=True)

    if settings.DB_TYPE.upper() == "DM":
        log: Mapped[str] = mapped_column(Text, comment='请求导入的日志', nullable=True)
    else:
        log: Mapped[str] = mapped_column(MEDIUMTEXT, comment='请求导入的日志', nullable=True)


# class RDCFileContent(BaseModel):
#     """RDC文件内容表"""
#
#     __tablename__ = 'rdc_file_content'
#     __table_args__ = {
#         'comment': '数据发布中心文件内容表'
#     }
#
#     id: Mapped[uuid4_str] = mapped_column(String(36), comment='id', primary_key=True, default=uuid4_str)
#     task_id: Mapped[str] = mapped_column(String(36), comment='import_dist的id', default='', nullable=True)
#     type: Mapped[str] = Column(String(32), nullable=False, default='', comment='数据类型')
#     data_id: Mapped[str] = Column(String(64), nullable=False, default='')
#     if settings.DB_TYPE.upper() == "DM":
#         content: Mapped[str] = mapped_column(Text, comment='json内容', nullable=False)
#     else:
#         content: Mapped[str] = mapped_column(LONGTEXT, comment='json内容', nullable=False)

class BiDirectionalDict:
    def __init__(self, key_to_value: dict = None, value_to_key: dict = None):
        self.key_to_value = key_to_value or {}
        self.value_to_key = value_to_key or {}
        self.simple_const = {}

    def construct_simple_dict_by_id(self, fields: list):
        for items in list(self.key_to_value.values()):
            for item in items:
                simple_dict = {}
                if self.simple_const.get(item.get('id')):
                    continue
                for field in fields:
                    field_val = item.get(field)
                    if field_val:
                        simple_dict[field] = field_val
                self.simple_const[item.get('id')] = simple_dict

        for items in list(self.value_to_key.values()):
            for item in items:
                simple_dict = {}
                if self.simple_const.get(item.get('id')):
                    continue
                for field in fields:
                    field_val = item.get(field)
                    if field_val:
                        simple_dict[field] = field_val
                self.simple_const[item.get('id')] = simple_dict

    def get_simple_const(self, id):
        if not self.simple_const:
            return None
        return self.simple_const.get(id)

    def add(self, key_obj, value_obj):
        key_id = key_obj.get('id')
        value_id = value_obj.get('id')

        if key_id not in self.key_to_value:
            self.key_to_value[key_id] = []
        self.key_to_value[key_id].append(value_obj)

        if value_id not in self.value_to_key:
            self.value_to_key[value_id] = []
        self.value_to_key[value_id].append(key_obj)

    def remove(self, key_obj, value_obj):
        key_id = key_obj.get('id')
        value_id = value_obj.get('id')

        if key_id in self.key_to_value:
            self.key_to_value[key_id].remove(value_obj)
            if not self.key_to_value[key_id]:
                del self.key_to_value[key_id]
        if value_id in self.value_to_key:
            self.value_to_key[value_id].remove(key_obj)
            if not self.value_to_key[value_id]:
                del self.value_to_key[value_id]

    def get_values_by_key(self, key_obj):
        key_id = key_obj.get('id')
        return self.key_to_value.get(key_id, [])

    def get_keys_by_value(self, value_obj):
        value_id = value_obj.get('id')
        return self.value_to_key.get(value_id, [])
