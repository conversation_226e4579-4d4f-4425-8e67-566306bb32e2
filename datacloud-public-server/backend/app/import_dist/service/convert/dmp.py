import json
import os
import shutil

from backend.common import utils


def convert(folder):
    # 读目录下的json文件
    json_files = [f for f in os.listdir(folder) if f.endswith('.json')]
    export_data = {}
    for file_name in json_files:
        with open(os.path.join(folder, file_name), 'r', encoding='utf-8') as file:
            r_data = file.read()
            if r_data:
                try:
                    export_data = json.loads(r_data)
                except:
                    raise Exception('zip压缩文件内容格式有误')
                break

    if not export_data:
        raise Exception('没有导入信息，导入不合法')

    # base dir
    data_dir = os.path.join(folder, 'convert/data/dmp/mysql/')
    base_dir = os.path.join(data_dir, 'base')
    if not os.path.exists(base_dir):
        os.makedirs(base_dir, exist_ok=True)

    base_info_path = os.path.join(base_dir, 'info.json')

    base_info = json.loads('{}')
    base_info['export_id'] = export_data['export_id']
    base_info['title'] = export_data['title']
    base_info['description'] = export_data['description']
    base_info['source_project'] = export_data['source_project']
    base_info['source_project_id'] = export_data['source_project_id']
    base_info['is_new_jump'] = export_data['is_new_jump']
    base_info['source_user_id'] = export_data['source_user_id']
    base_info['created_on'] = export_data['created_on']
    base_info['version'] = export_data['version']
    base_info['export_excel_data'] = export_data['export_excel_data']

    with open(base_info_path, 'w', encoding='utf-8') as f:
        f.write(json.dumps(base_info))
        f.close()

    # dashboards dir
    if export_data['dashboards']:
        dashboards_dir = os.path.join(data_dir, 'dashboards')
        if not os.path.exists(dashboards_dir):
            os.makedirs(dashboards_dir, exist_ok=True)

        for key, value in export_data['dashboards'].items():
            with open(os.path.join(dashboards_dir, f'{key}.json'), 'w', encoding='utf-8') as f:
                data = {
                    key: value
                }
                f.write(json.dumps(data))
                f.close()

    # datasets dir
    if export_data['datasets']:
        datasets_dir = os.path.join(data_dir, 'datasets')
        if not os.path.exists(datasets_dir):
            os.makedirs(datasets_dir, exist_ok=True)

        for dataset in export_data['datasets']:
            dataset_id = dataset['dataset']['id']
            with open(os.path.join(datasets_dir, f'{dataset_id}.json'), 'w', encoding='utf-8') as f:
                f.write(json.dumps(dataset))
                f.close()

    # applications dir
    if export_data['applications']:
        applications_dir = os.path.join(data_dir, 'applications')
        if not os.path.exists(applications_dir):
            os.makedirs(applications_dir, exist_ok=True)

        for key, value in export_data['applications'].items():
            with open(os.path.join(applications_dir, f'{key}.json'), 'w', encoding='utf-8') as f:
                data = {
                    key: value
                }
                f.write(json.dumps(data))
                f.close()

    # dist dir
    if os.path.exists(os.path.join(folder, 'dist')):
        dist_dir = os.path.join(data_dir, 'dist')
        if not os.path.exists(dist_dir):
            os.makedirs(dist_dir, exist_ok=True)
        for file in os.listdir(os.path.join(folder, 'dist')):
            source_path = os.path.join(os.path.join(folder, 'dist'), file)
            target_path = os.path.join(dist_dir, file)
            shutil.move(source_path, target_path)

    # excel dir
    excel_dir = os.path.join(data_dir, 'excel')
    for file in os.listdir(folder):
        if file.lower().endswith('.csv'):
            if not os.path.exists(excel_dir):
                os.makedirs(excel_dir, exist_ok=True)
            source_path = os.path.join(folder, file)
            target_path = os.path.join(excel_dir, file)
            shutil.move(source_path, target_path)

    # large_screens dir
    if export_data['large_screens']:
        large_screens_dir = os.path.join(data_dir, 'large_screens')
        if not os.path.exists(large_screens_dir):
            os.makedirs(large_screens_dir, exist_ok=True)

        for key, value in export_data['large_screens'].items():
            with open(os.path.join(large_screens_dir, f'{key}.json'), 'w', encoding='utf-8') as f:
                data = {
                    key: value
                }
                f.write(json.dumps(data))
                f.close()

    # 复杂报表 and 统计报表
    # report_center dir ? 暂时没有找到导入相关内容，不做处理
    if export_data.get('report_center', []) or export_data.get('active_reports', []):
        if os.path.exists(os.path.join(folder, 'active_reports.zip')):
            report_center_dir = os.path.join(data_dir, 'report_center')
            if not os.path.exists(report_center_dir):
                os.makedirs(report_center_dir, exist_ok=True)
            # 解压移动
            utils.unzip_files_to_folder(os.path.join(folder, 'active_reports.zip'), report_center_dir)
            # 写入基础信息
            ids = export_data.get('report_center', {}).get('ids', '')
            if not ids:
                ids = ','.join(export_data['active_reports']['ids'])
            report_center_base_info = {'ids': ids,
                                       'data': export_data['active_reports']['data']}
            with open(os.path.join(report_center_dir, 'base.json'), 'w', encoding='utf-8') as f:
                f.write(json.dumps(report_center_base_info))
                f.close()

    # 在线报告
    # ppt dir ? 暂时没有找到导入相关内容，不做处理
    # if export_data['ppt'] and os.path.exists(os.path.join(folder, 'ppt.zip')):

    # 数据订阅
    # feeds dir
    if export_data.get('feeds', {}):
        feeds_dir = os.path.join(data_dir, 'feeds')
        if not os.path.exists(feeds_dir):
            os.makedirs(feeds_dir, exist_ok=True)
        # 找到所有基础 id
        # 基于 id 逐个过滤
        for value in export_data['feeds']['dashboard_email_subscribe']:
            feed = {}
            ida = value['id']
            feed['dashboard_email_subscribe'] = [item for item in export_data['feeds']['dashboard_email_subscribe'] if
                                                 item['id'] == ida]
            feed['dashboard_subscribe_display_format'] = [item for item in
                                                          export_data['feeds']['dashboard_subscribe_display_format'] if
                                                          item['subscribe_id'] == ida]
            feed['mobile_subscribe_filter'] = [item for item in export_data['feeds']['mobile_subscribe_filter'] if
                                               item['email_subscribe_id'] == ida]
            feed['mobile_subscribe_rules'] = [item for item in export_data['feeds']['mobile_subscribe_rules'] if
                                              item['email_subscribe_id'] == ida]
            feed['mobile_subscribe_chapters'] = [item for item in export_data['feeds']['mobile_subscribe_chapters'] if
                                                 item['email_subscribe_id'] == ida]
            feed['flow'] = [item for item in export_data['feeds']['flow'] if item['id'] == ida]

            with open(os.path.join(feeds_dir, f'{ida}.json'), 'w', encoding='utf-8') as f:
                f.write(json.dumps(feed))
                f.close()

    return data_dir
