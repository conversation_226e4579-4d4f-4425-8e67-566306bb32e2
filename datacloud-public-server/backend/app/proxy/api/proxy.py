#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from fastapi import APIRouter, Query, Depends
from pydantic import BaseModel
from pympler.util.bottle import urlunquote

from backend.common.domain import get_server_domain
from backend.common.request import HTTPRequestUtil
from backend.common.response.response_schema import response_base, ResponseModel, CustomResponseCode
from backend.common.jwt import CookiesUser, DependsJwtUser

router = APIRouter()


class ProxyData(BaseModel):
    to: str
    path: str
    data: dict
    method: str


@router.post('/request', summary='代理请求dap的接口')
async def request(proxy_data: ProxyData, user: CookiesUser = DependsJwtUser) -> ResponseModel:
    """
    代理请求数芯的接口
    """
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    if proxy_data.to == 'dap':
        host = get_server_domain('dap')
    else:
        host = get_server_domain('dmp')
    kwargs = {
        'url': f'{host}{proxy_data.path}',
        'headers': headers,
    }
    if proxy_data.method.lower() == 'get':
        kwargs['params'] = proxy_data.data
    else:
        kwargs['json'] = proxy_data.data
    response = None
    try:
        response = await HTTPRequestUtil.request(proxy_data.method, **kwargs)
        rs = response.json()
    except Exception as e:
        rs = {'error': str(e), 'response': response.text if response else ''}
    curl = HTTPRequestUtil.httpx_request_to_curl(response.request)
    rs['curl'] = curl
    return await response_base.success(data=rs)


@router.get('/oss', summary='转换oss的加签地址')
async def request_oss_sign(url: str = Query()) -> ResponseModel:
    """
    代理请求数芯的接口
    """
    from backend.common.oss.oss import OSSFileProxy
    url = urlunquote(url)
    url = url.split('?')[0]

    url = OSSFileProxy().get_sigh_url(key=url, is_url=True, **{'sign_outer_url': True})
    return await response_base.success(data={'url': url})
