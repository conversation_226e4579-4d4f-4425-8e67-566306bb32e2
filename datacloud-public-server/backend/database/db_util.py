#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
import random
import sys
from uuid import uuid4

import math
import sqlparse
from fastapi import Depends
from sqlalchemy import URL, event
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from typing_extensions import Annotated
from sqlalchemy.engine import create_engine
from sqlalchemy.orm import sessionmaker, Session, scoped_session

from backend.common.model import Base
from backend.core.conf import settings
from backend.common.log import log

DB_TYPE = settings.DB_TYPE


# 格式化单个参数值
def format_parameter(value):
    if isinstance(value, str):
        return f"'{value}'"
    elif value is None:
        return 'NULL'
    elif isinstance(value, (int, float)):
        return str(value)
    # 其他类型的处理可以在这里添加
    return repr(value)


# 格式化 SQL 语句与参数
def format_sql(statement, parameters):
    if isinstance(parameters, list):
        # 如果 parameters 是列表，则逐一处理
        formatted_params = [format_parameter(param) for param in parameters]
        # 需要将 ? 占位符替换为每个参数
        for param in formatted_params:
            statement = statement.replace("?", param, 1)
    elif isinstance(parameters, dict):
        # 如果 parameters 是字典，则按键替换
        formatted_params = {key: format_parameter(value) for key, value in parameters.items()}
        for key, value in formatted_params.items():
            statement = statement.replace(f":{key}", value)

    return statement


# 定义事件监听器函数
def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    formatted_sql = format_sql(statement, parameters)
    log.debug(f"SQL executed: {formatted_sql}")
    log.debug(f"SQL parameters: {parameters}")


def create_engine_and_session(url: str, origin_echo=False):
    try:
        # 数据库引擎
        if DB_TYPE == 'DM':
            execution_options = {"schema_translate_map": {None: settings.DB_DBNAME}}
            engine = create_engine(url, echo=origin_echo, future=True, pool_pre_ping=True,
                                   execution_options=execution_options)
        else:
            engine = create_engine(url, echo=origin_echo, future=True, pool_pre_ping=True)
        # log.success('数据库连接成功')
        if settings.DB_ECHO:
            event.listen(engine, "before_cursor_execute", before_cursor_execute)
    except Exception as e:
        log.error('❌ 数据库链接失败 {}', e)
        sys.exit()
    else:
        if DB_TYPE == 'DM':
            db_session = sessionmaker(bind=engine, autoflush=False, expire_on_commit=False)
            # db_session = scoped_session(session_factory)
        else:
            db_session = sessionmaker(bind=engine, autoflush=False, expire_on_commit=False)
            # db_session = scoped_session(session_factory)
        return engine, db_session


if DB_TYPE == 'DM':
    SQLALCHEMY_DATABASE_URL = (
        f"dm+dmPython://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}"
    )
else:
    SQLALCHEMY_DATABASE_URL = (
        f'mysql+pymysql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:'
        f'{settings.DB_PORT}/{settings.DB_DBNAME}?charset={settings.DB_CHARSET}'
    )

engine, db_session = create_engine_and_session(SQLALCHEMY_DATABASE_URL, origin_echo=False)


async def get_db() -> AsyncSession:
    # async def get_db() -> Session:
    """session 生成器"""
    session = db_session()
    try:
        yield session
    except Exception as se:
        session.rollback()
        raise se
    finally:
        session.close()


# Session Annotated
CurrentSession = Annotated[AsyncSession, Depends(get_db)]


# CurrentSession = Annotated[Session, Depends(get_db)]


async def create_table():
    """创建数据库表"""
    engine, _ = create_engine_and_session(SQLALCHEMY_DATABASE_URL, origin_echo=True)
    log.error(f"--> 开始执行表迁移")
    Base.metadata.create_all(bind=engine)
    # if DB_TYPE == 'DM':
    #     Base.metadata.create_all(bind=engine)
    # else:
    #     async with engine.begin() as coon:
    #         await coon.run_sync(Base.metadata.create_all)
    # engine.dispose()
    # del engine
    log.error(f"<-- 结束执行表迁移")
    return engine


def _get_random_chars(char_length):
    chars = 'abcdef0123456789'
    i = 0
    res = ''
    while i < char_length:
        idx = math.floor(1 + random.random() * 16)
        res += chars[idx - 1: idx]
        i += 1
    return res


def uuid4_str() -> str:
    """
    数据库引擎 UUID 类型兼容性解决方案
    获取有序GUID与 db中fn_newSeqId 算法保持一致
    :return:str
    """
    now = datetime.datetime.utcnow().timestamp()
    ticks = hex(round(now * 1000000))[2:]
    old_ticks = hex(round(now * 1000 + 62135625600000))[2:]
    return '%s-%s-%s%s-%s-%s' % (
        old_ticks[:8],
        old_ticks[8:12],
        ticks[10:13],
        _get_random_chars(1),
        _get_random_chars(4),
        _get_random_chars(12),
    )
