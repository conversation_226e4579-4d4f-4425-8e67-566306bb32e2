from pydantic import BaseModel
from fastapi import Request
import contextvars

shared_data_var = contextvars.ContextVar("shared_data")


# 跨函数类似g的用法
class CrossData(BaseModel):
    curls: list[str] = []
    # logs: list[str] = []


# 辅助函数，获取共享数据
def get_shared_data() -> CrossData:
    """
    尝试获取当前的上下文数据，如果失败，返回一个新的值（当然也失去了上下文的意义）
    """
    try:
        return shared_data_var.get()
    except:
        return CrossData()


def profiling_log(log: str) -> None:
    """
    往log里面追加日志
    """
