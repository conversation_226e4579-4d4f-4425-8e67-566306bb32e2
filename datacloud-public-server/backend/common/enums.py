#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import Enum, IntEnum as SourceIntEnum
from typing import Type


class _EnumBase:
    @classmethod
    def get_member_keys(cls: Type[Enum]) -> list[str]:
        return [name for name in cls.__members__.keys()]

    @classmethod
    def get_member_values(cls: Type[Enum]) -> list:
        return [item.value for item in cls.__members__.values()]


class IntEnum(_EnumBase, SourceIntEnum):
    """整型枚举"""

    pass


class StrEnum(_EnumBase, str, Enum):
    """字符串枚举"""

    pass


class ExportTaskStatus(StrEnum):
    init = '已创建'
    running = '导出中'
    exportDone = '导出完成'
    exportError = '导出失败'


class ImportStatus(StrEnum):
    init = '初始化'
    not_import = '未更新'
    running = '更新中'
    importDone = '更新完成'
    importError = '更新失败'
    exception = '异常状态'
    skip = '已跳过'


class RDCTenantType(StrEnum):
    tenant = 'TENANT'
    public = 'PUBLIC'
    template = 'TEMPLATE'


class TenantType(StrEnum):
    public = 'public'  # 公共空间
    custom = 'custom'  # 定制空间


class TriggerDapImport(StrEnum):
    has_call = '成功调用过'


class ImportPackageType(StrEnum):
    product = '产品更新包'
    custom = '定制包'
