
def get_server_domain(server_name: str):
    """
    获取服务的domain
    """
    if server_name == 'dmp':
        return 'http://dp-bi-server:8000'

        # if not domain:
        # else:
        #     return domain
    elif server_name == 'dmp_admin':
        return 'http://dp-frontend/dp/dmp-admin'

        # domain = AppHosts.get(SkylineApps.DMP_ADMIN_GROUP).rstrip('/')
        # # domain = get_nacos_domain(SkylineApps.DMP_ADMIN_GROUP.value)
        # if not domain:
        # else:
        #     return domain
    elif server_name == 'dap':
        return 'http://dp-frontend'

        # domain = AppHosts.get(SkylineApps.DAP).rstrip('/')
        # # domain = get_nacos_domain(SkylineApps.DAP.value)
        # if not domain:
        #     return settings.DOMAIN_DAP
        # else:
        #     return 'http://dp-server:9999'
    else:
        return ''
#
# def get_nacos_domain(server, inside=True):
#     try:
#         if inside:
#             key = f'{server}.insideUrl'
#         else:
#             key = f'{server}.url'
#         return NacosClient.get(key, '')
#     except Exception as e:
#         log.error(f"nacos获取配置失败： {str(e)}")
#         return None
