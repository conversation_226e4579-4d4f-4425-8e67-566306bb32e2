import aio_pika
import redis
from aio_pika import connect_robust
from backend.core.conf import settings
from backend.database.db_redis import redis_client
from redis.asyncio.client import Redis

RABBITMQ_URL = f"amqp://{settings.RabbitMQ_USER}:{settings.RabbitMQ_PASSWOED}@{settings.RabbitMQ_HOST}:{settings.RabbitMQ_PORT}/{settings.RabbitMQ_VHOST}"


async def get_connection(**kwargs) -> (
        aio_pika.abc.AbstractRobustConnection,
        aio_pika.abc.AbstractRobustChannel,
        aio_pika.abc.AbstractRobustQueue
):
    """
    获取MQ连接
    """
    connection = await connect_robust(RABBITMQ_URL, **kwargs)
    channel = await connection.channel()
    queue = await channel.declare_queue(settings.RabbitMQ_QUEUE_NAME, durable=True)
    # await channel.set_qos(prefetch_count=2)
    return connection, channel, queue


def get_redis_connection(**kwargs) -> (
        Redis
):
    """
    获取Redis连接
    """
    return redis_client
