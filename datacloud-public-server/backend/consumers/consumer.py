import pkgutil
import importlib
import inspect
from typing import Dict, Type

from pydantic import BaseModel

from backend.common.log import log
from backend.consumers.rabbitmq_consumer import RabbitMqConsumer
from backend.consumers.redis_consumer import RedisConsumer

__ALL__ = ['MessageModel', 'Consumer', 'CONSUMERS']


class MessageModel(BaseModel):
    cls: str
    task: Dict


# CONSUMERS = {}
CURRENT_CONSUMER_TYPE = 'redis'

log.info(f"当前消费中间件类型：{CURRENT_CONSUMER_TYPE}")
Consumer = {
    'redis': RedisConsumer,
    'rabbitmq': RabbitMqConsumer,
}[CURRENT_CONSUMER_TYPE]


def import_subclasses(package_name: str, base_class: Type[Consumer]) -> Dict[str, Type[Consumer]]:
    """
    查找所有继承的子类实现（所有的消费者处理方法）
    """
    package = importlib.import_module(package_name)
    subclasses = {}
    for _, module_name, _ in pkgutil.iter_modules(package.__path__):
        module = importlib.import_module(f"{package_name}.{module_name}")
        for name, obj in inspect.getmembers(module):
            if inspect.isclass(obj) and issubclass(obj, base_class) and obj is not base_class:
                subclasses[name] = obj
    return subclasses


CONSUMERS = import_subclasses('backend.consumers.tasks', Consumer)
# CONSUMERS = import_subclasses('backend.app', Consumer)
