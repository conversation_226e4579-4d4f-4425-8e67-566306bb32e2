#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# import os
# os.environ['PROMETHEUS_MULTIPROC_DIR'] = '/tmp'
from functools import lru_cache
from typing import Literal
from urllib.parse import quote_plus
# from pydantic import model_validator
from pydantic_settings import BaseSettings
from backend.common import config


class Settings(BaseSettings):
    # model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8', case_sensitive=True)

    # Env Config
    ENVIRONMENT: Literal['dev', 'pro'] = 'dev'
    # LOG_LEVEL: Literal['debug', 'info', 'error'] = config.get('Log.level', 'info').lower()
    LOG_LEVEL:str = 'info'

    # APP
    APP__EXPORT_TIMEOUT: int = config.get('App.export_timeout') or 30 * 60
    APP__IMPORT_TIMEOUT: int = config.get('App.import_timeout') or 30 * 60

    # Env MySQL
    DB_TYPE: str = config.get('DB.db_type', 'mysql').upper()
    DB_TB_PREFIX: str = config.get('DB.db_tb_prefix') or 'dap_bi_datacloud_public_'
    DB_HOST: str = config.get('DB.host')
    DB_PORT: int = config.get('DB.port')
    DB_USER: str = config.get('DB.user')
    DB_PASSWORD: str = quote_plus(config.get('DB.password') or '')
    DB_DBNAME: str = config.get('DB.database')
    # MYSQL
    DB_ECHO: bool = config.get('DB.echo') == '1'
    # MYSQL_DATABASE: str = 'dmp_datacloud_public'
    DB_CHARSET: str = 'utf8mb4'

    # Domain
    DOMAIN_DMP: str = config.get('Domain.dmp') or ''
    DOMAIN_DMP_ADMIN: str = config.get('Domain.dmp_admin') or ''
    DOMAIN_DAP: str = config.get('Domain.dap') or ''

    # Env Redis
    # REDIS_HOST: str = config.get('Redis.host')
    # REDIS_PORT: int = config.get('Redis.port')
    # REDIS_PASSWORD: str = config.get('Redis.password')
    # REDIS_DATABASE: int = config.get('Redis.db')

    # RabbitMq
    # ！！！！！！！
    # ！！！！！！！
    # ！！！！！！！
    # 发布中心使用redis做消费者队列不再使用MQ
    # RabbitMQ_HOST: str = config.get('RabbitMQ.host')
    # RabbitMQ_PORT: str = str(config.get('RabbitMQ.port'))
    # RabbitMQ_USER: str = config.get('RabbitMQ.user')
    # RabbitMQ_PASSWOED: str = quote_plus(config.get('RabbitMQ.password') or '')
    # RabbitMQ_VHOST: str = quote_plus(config.get('RabbitMQ.vhost') or '')
    # RabbitMQ_QUEUE_NAME: str = config.get('RabbitMQ.queue_name') or ''

    # 注释代码
    RabbitMQ_HOST: str = ''
    RabbitMQ_PORT: str = ''
    RabbitMQ_USER: str = ''
    RabbitMQ_PASSWOED: str = ''
    RabbitMQ_VHOST: str = ''
    RabbitMQ_QUEUE_NAME: str = ''


    OSS_PUB_SERVER_BASE_FOLDER_NAME: str = 'datacloud-server'

    # OSS_CONFIG
    OSS_CONFIG__SERVICE: str = config.get('OSS_Config.service') or 'Minio'
    OSS_CONFIG__IS_PRIVATE: str = config.get('OSS_Config.is_private', '1') or ''
    OSS_CONFIG__ACCESS_EXPIRES: int = int(config.get('OSS_Config.access_expires') or 2592000)
    OSS_CONFIG__AUTO_MIME: str = config.get('OSS_Config.auto_mime') or '1'

    # OSS
    OSS__ACCESS_KEY_ID: str = config.get('OSS.access_key_id') or ''
    OSS__ACCESS_KEY_SECRET: str = config.get('OSS.access_key_secret') or ''
    OSS__BUCKET: str = config.get('OSS.bucket') or ''
    OSS__ENDPOINT: str = config.get('OSS.endpoint') or ''

    # Minio
    MINIO__ACCESS_KEY_ID: str = config.get('Minio.access_key_id') or ''
    MINIO__ACCESS_KEY_SECRET: str = config.get('Minio.access_key_secret') or ''
    MINIO__BUCKET: str = config.get('Minio.bucket') or ''
    MINIO__ENDPOINT: str = config.get('Minio.endpoint') or ''
    MINIO__INSIDE_ENDPOINT: str = config.get('Minio.inside_endpoint') or ''

    # Env Token
    TOKEN_SECRET_KEY: str = 'datacloud_public_server&87f))jfa$%VhfvafafaNHgfaf'  # 密钥 secrets.token_urlsafe(32)
    COOKIE_EXPIRE_MINUTES: int = 6 * 60  # 单位：分钟

    # FastAPI
    API_V1_STR: str = '/api/data_publish_upgrade'
    TITLE: str = 'FastAPI'
    VERSION: str = '0.0.1'
    DESCRIPTION: str = 'FastAPI SQLAlchemy MySQL'
    DOCS_URL: str = f'{API_V1_STR}/docs'
    REDOCS_URL: str = f'{API_V1_STR}/redocs'
    OPENAPI_URL: str = f'{API_V1_STR}/openapi'

    # @model_validator(mode='before')
    # @classmethod
    # def validator_api_url(cls, values):
    #     if values['ENVIRONMENT'] == 'pro':
    #         values['OPENAPI_URL'] = None
    #     return values

    # Static Server
    STATIC_FILE: bool = True

    # Limiter
    LIMITER_REDIS_PREFIX: str = 'fsm_limiter'

    # Uvicorn
    UVICORN_HOST: str = '127.0.0.1'
    UVICORN_PORT: int = 9000
    UVICORN_RELOAD: bool = True

    # DateTime
    DATETIME_TIMEZONE: str = 'Asia/Shanghai'
    DATETIME_FORMAT: str = '%Y-%m-%d %H:%M:%S'

    # Redis
    REDIS_TIMEOUT: int = 10

    # Captcha
    CAPTCHA_EXPIRATION_TIME: int = 60 * 5  # 过期时间，单位：秒

    # Log
    LOG_STDOUT_FILENAME: str = 'fsm_access.log'
    LOG_STDERR_FILENAME: str = 'fsm_error.log'

    # Token
    TOKEN_ALGORITHM: str = 'HS256'
    TOKEN_URL_SWAGGER: str = f'{API_V1_STR}/auth/login/swagger'
    TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 3  # 单位：m

    # 中间件
    MIDDLEWARE_CORS: bool = True
    MIDDLEWARE_GZIP: bool = True
    MIDDLEWARE_ACCESS: bool = False


@lru_cache
def get_settings():
    """读取配置优化写法"""
    return Settings()


settings = get_settings()
