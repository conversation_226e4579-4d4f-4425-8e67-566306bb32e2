# FastAPI SQLAlchemy MySQL

## 特征

- [x] FastAPI
- [x] Async design
- [x] Restful API
- [x] SQLAlchemy 2.0
- [x] DM
- [x] Pydantic 2.0
- [x] Docker
- [ ] ......

## 本地开发

* Python 3.10+
* Mysql 8.0+

1. 安装依赖项

   ```shell
   pip install -r requirements.txt
   ```

4. 进入 backend 目录

   ```shell
   cd backend
   ```
   
5. 创建一个 `.env` 文件

   ```shell
   touch .env
   cp .env.example .env
   ```

6. 按需修改配置文件 `core/conf.py` 和 `app.config`

8. 启动 fastapi 服务

   ```shell
   # 帮助
   fastapi --help
   
   # 开发模式
   fastapi dev main.py
   ```
   
9. 浏览器访问: http://127.0.0.1:8000/api/v1/docs


