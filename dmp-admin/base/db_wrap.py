import re
from typing import Dict, List, Optional, Union
from dmplib.saas import project
from dmplib.db import mysql_wrapper


class DbWrap:

    def __init__(self, code=None):
        if code:
            self.db = project.get_db(code)
        else:
            self.db = mysql_wrapper.get_db()

    def get_list(self,
                 table_name: str,
                 conditions: Dict[str, str],
                 fields: Optional[List[str]] = None,
                 order_by: None = None,
                 limit: Optional[int] = None,
                 **kwargs
                 ) -> List[Dict[str, Union[int, str]]]:
        """
        查询数据列表
        :param str table_name: 表名
        :param dict conditions: 查询条件
        :param str|list fields: 查询的字段
        :param str|list order_by: 排序
        :param str limit: limit 限制记录数
        :param list page: 分页
        :param str join: 连表
        :param str|list group: 分组字段
        :param str having: 分组条件
        :return: list
        """
        join = kwargs.get('join')
        group = kwargs.get('group')
        having = kwargs.get('having')
        page = kwargs.get('page')

        sql = self._parse_select(
            table_name, conditions, fields, order_by, limit, page=page, join=join, group=group,
            having=having
        )
        return self.db.query(sql, conditions)

    def get_one(self,
                table_name: str,
                conditions: Dict[str, str],
                fields: Optional[List[str]] = None,
                order_by: None = None,
                **kwargs
                ) -> Dict[str, Union[int, str]]:
        """取一条数据"""
        join = kwargs.get('join')
        group = kwargs.get('group')
        having = kwargs.get('having')
        result = self.get_list(
            table_name, conditions, fields, order_by, limit=1, join=join, group=group, having=having
        )
        if result:
            return result[0]
        return None

    def get_value(self, table_name, conditions, field, order_by=None, **kwargs):
        join = kwargs.get('join')
        group = kwargs.get('group')
        having = kwargs.get('having')
        result = self.get_one(table_name, conditions, field, order_by, join=join, group=group,
                              having=having)
        if result:
            return list(result.values())[0]
        return None

    def get_column(self, table_name, conditions, fields, order_by=None, limit=None, **kwargs):
        join = kwargs.get('join')
        group = kwargs.get('group')
        having = kwargs.get('having')
        page = kwargs.get('page')
        result = self.get_list(
            table_name,
            conditions,
            fields,
            order_by=order_by,
            limit=limit,
            page=page,
            join=join,
            group=group,
            having=having,
        )
        if result:
            return [list(i.values())[0] for i in result]
        return None

    def get_dict(self, table_name, conditions, fields, order_by=None, limit=None, **kwargs):
        join = kwargs.get('join')
        group = kwargs.get('group')
        having = kwargs.get('having')
        page = kwargs.get('page')
        result = self.get_list(
            table_name,
            conditions,
            fields,
            order_by=order_by,
            limit=limit,
            page=page,
            join=join,
            group=group,
            having=having,
        )
        if result:
            ret = {}
            if isinstance(fields, str):
                fields = fields.split(',')
            fields_len = None
            for v in result:
                if not fields_len:
                    fields_len = len(v)
                    index_key = self._find_index_key(v.keys(), fields[0])
                if fields_len == 2:
                    # 两个字段，字面量组成字典
                    key = v.pop(index_key)
                    ret[key] = list(v.values())[0]
                else:
                    key = v[index_key]
                    ret[key] = v
            return ret
        return None

    @staticmethod
    def _find_index_key(keys, index_str: str):
        index_str = index_str.strip()
        index_str_no_s = index_str.replace('`', '')
        # 完全匹配
        for key in keys:
            if key in (index_str, index_str_no_s):
                return key
        # 所有key中，只有一个key是index_str的子串
        ret = None
        for key in keys:
            if key in index_str:
                if ret:
                    ret = None
                    break
                else:
                    ret = key
        if ret:
            return ret
        # 匹配 as 的情况
        ma = re.match('.* (as )?(\w+)$', index_str_no_s, re.IGNORECASE)
        if ma and ma.group(2) in keys:
            return ma.group(2)
        # 匹配 table.field的情况
        ma = re.match('\w+\.(\w+)$', index_str_no_s.replace(' ', ''))
        if ma and ma.group(1) in keys:
            return ma.group(1)
        raise Exception(message='没有找到索引的key, index_str=%s in %s' % (index_str, list(keys)))

    def _parse_select(
            self,
            table_name: str,
            conditions: Dict[str, str],
            fields: Optional[List[str]] = None,
            order_by: None = None,
            limit: Optional[int] = None,
            **kwargs
    ) -> str:
        join = kwargs.get('join')
        group = kwargs.get('group')
        having = kwargs.get('having')
        page = kwargs.get("page")
        sql = 'SELECT {} FROM {}'.format(self._parse_fields(fields), self._parse_field(table_name))
        if join:
            sql += ' '
            if not re.match('(left|right|natural|inner)? ?join ', join, re.IGNORECASE):
                sql += 'JOIN '
            sql += join
        sql += self._parse_where(conditions)
        if group:
            sql += ' GROUP BY ' + self._parse_fields(group)
            if having:
                sql += ' HAVING ' + having
        sql += self._parse_order_by(order_by)
        if limit:
            sql += ' LIMIT ' + str(limit)
        elif page:
            page_index, page_size = page
            sql += ' LIMIT %s,%s' % ((page_index - 1) * page_size, page_size)
        return sql

    @staticmethod
    def _parse_field(field: str) -> str:
        """解析 select 的一个字段"""
        if not field or field == '*':
            return '*'
        field = field.strip()
        org_str = (' ', '(', '`')
        for v in org_str:
            if v in field:
                return field
        arr = ['`%s`' % v for v in field.split('.')]
        return '.'.join(arr)

    def _parse_fields(self, fields: List[str]) -> str:
        """
        解析 select 的字段，如果表达示中有英文逗号，必须使用 list 方式传参
        :param str|list fields:
        :return:
        """
        if not fields:
            return '*'
        if isinstance(fields, str):
            fields = fields.strip().split(',')
        arr = [self._parse_field(v) for v in fields]
        return ','.join(arr)

    def _parse_where(self, condition: Dict[str, str]) -> str:
        if not condition:
            return ''
        and_items = []
        for k, v in condition.items():
            field = k
            operator = '='
            if isinstance(v, list):
                operator = ' IN '
            elif v is None:
                operator = ' IS '
            else:
                ma = re.match('(.*?)(<>|!=|<=|>=|=|>|<| like| regexp)$', k, re.IGNORECASE)
                if ma:
                    field, operator = ma.groups()
                    operator_lower = operator.strip().lower()
                    operator_map = {'like': ' LIKE ', 'regexp': ' REGEXP '}
                    operator = operator_map.get(operator_lower, operator)
            ma = re.match('(.*?) (is not|not)$', field.strip(), re.IGNORECASE)
            if ma:
                field, _ = ma.groups()
                if operator == ' IS ':
                    operator += 'NOT '
                else:
                    operator = ' NOT' + operator
            and_items.append('{}{}%({})s'.format(self._parse_field(field), operator, k))
        return ' WHERE ' + ' AND '.join(and_items)

    def _parse_order_by(self, order_by: None) -> str:
        ret_arr = []
        if order_by:
            if isinstance(order_by, str):
                items = order_by.split(',')
                for item in items:
                    *fields, asc_or_desc = item.strip().split(' ')
                    ret_arr.append(self._parse_field(' '.join(fields)) + ' ' + asc_or_desc.upper())
            if isinstance(order_by, tuple):
                order_by = [order_by]
            if isinstance(order_by, list):
                ret_arr = [self._parse_field(ob[0]) + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)]
        return ' ORDER BY ' + ','.join(ret_arr) if ret_arr else ''

    def update(self, table_name, data, conditions):
        sql, params = self._parse_update_set(data)
        sql = 'UPDATE {} SET {}'.format(self._parse_field(table_name), sql)
        sql += self._parse_where(conditions)
        params.update(conditions)
        return self.db.exec_sql(sql, params)

    def _parse_update_set(self, data):
        sql_item = []
        new_data = dict()
        for k, v in data.items():
            new_key = '_' + k + '_'
            sql = '{}=%({})s'.format(self._parse_field(k), new_key)
            sql_item.append(sql)
            new_data[new_key] = v
        return ','.join(sql_item), new_data

    def delete_data(self, table_name, condition):
        """
        删除数据
        :param str table_name:
        :param dict condition:
        :param bool commit:
        :return int:
        """
        if not condition:
            return 0
        sql = "DELETE FROM `%s`" % table_name
        sql += self._parse_where(condition)
        return self.db.exec_sql(sql, condition)


