#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/30

"""
    租户数据库表操作类
"""

from dmplib.saas.project import get_db


def update_data(tencode, table_name, data, condition):
    """
    更新数据
    :param str tencode: 租户code
    :param str table_name:
    :param dict data:
    :param dict condition:
    :return int:
    """
    if not data:
        return 0
    with get_db(tencode) as db:
        return db.update(table_name, data, condition)


def delete_data(tenant_code, table_name, condition):
    """
    删除对应租户下面对应表的数据
    :param str tenant_code: 租户code
    :return:
    """
    if not condition:
        return 0
    with get_db(tenant_code) as db:
        return db.delete(table_name, condition)


def get_data_scalar(tenant_code, table_name, conditions, col_name):
    """
    获取第一行第一列数据
    :param str tenant_code: 租户code
    :param str table_name:
    :param dict conditions:
    :param str col_name:
    :return:
    """
    if not col_name:
        return None
    sql = 'SELECT `{col}` FROM {table_name} '.format(col=col_name, table_name=table_name)
    if conditions and isinstance(conditions, dict):
        sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    sql += ' LIMIT 1 '
    with get_db(tenant_code) as db:
        return db.query_scalar(sql, conditions)


def get_data(tenant_code, table_name, conditions, fields, multi_row=None, order_by=None):
    """
    获取表数据
    :param str tenant_code: 租户code
    :param str table_name: 表名
    :param dict conditions: 条件
    :param list fields: 列名
    :param bool multi_row: 是否返回多行，默认返回单行
    :param list order_by: 排序字段
    :return:
    """
    sql = 'SELECT {col} FROM {table_name} ' ''.format(
        col='`' + '`,`'.join(fields) + '`' if fields else '*', table_name=table_name
    )
    if conditions and isinstance(conditions, dict):
        sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    if order_by and isinstance(order_by, list):
        sql += ' ORDER BY ' + ','.join([ob[0] + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)])
    with get_db(tenant_code) as db:
        if multi_row:
            return db.query(sql, conditions)
        else:
            sql += ' LIMIT 1 '
            return db.query_one(sql, conditions)


def add_model(tenant_code, table_name, model, fields=None):
    """
    添加数据
    :param str tenant_code: 租户code
    :param str table_name:
    :param base.models.BaseModel model:
    :param list fields:
    :return bool:
    """
    data = model.get_dict(fields)
    return add_data(tenant_code, table_name, data)


def add_data(tenant_code, table_name, data):
    """
    添加数据
    :param str tenant_code: 租户code
    :param str table_name:
    :param dict data:
    :return bool:
    """
    if not data:
        return False
    with get_db(tenant_code) as db:
        return db.insert(table_name, data) == 1


def replace_data(tenant_code, table_name, data, fields, commit=False, condition_field=None):
    """
    replace方式存数据，有则更新，没有则插入
    :param str tenant_code: 租户code
    :param str table_name: 表名
    :param list data: 数据列表
    :param list fields: 字段列表
    :param bool commit: 是否提交，默认不提交
    :param bool condition_field: 是否提交，默认不提交
    :return:
    """
    if not data:
        return False
    with get_db(tenant_code) as db:
        return db.replace_multi_data(table=table_name, list_data=data, fields=fields, commit=commit, condition_field=condition_field)
