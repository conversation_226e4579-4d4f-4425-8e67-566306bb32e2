#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021-8-18 10:31:19
# <AUTHOR> lulei
import json
import logging
import traceback
import uuid
from urllib.parse import unquote

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.utils.errors import UserError
from base.enums import DistributeType
from issue_dashboard.services import level_sequence_service
from issue_dashboard.models import ProjectModel, DashboardLevelSequenceModel

logger = logging.getLogger(__name__)

DEFAULT_FOLDER_NAME = '大屏系统分发报告文件夹'
DEFAULT_FOLDER_ID = '00000000-1111-0000-2222-02420a0c0002'


class InitYkjScreenFolderCommand(UpgradeCommand):
    """
    初始化云空间的几个内置大屏文件夹
    """

    def exec(self):
        # data = {
        #     "39edb73d-1111-1111-40d0-6de765e234d2": "云资管",
        #     "39edb73e-1111-1111-ca00-c0ac6151c652": "云公寓",
        #     "39edb73e-1111-1111-a4c5-79007f6a9d9e": "云物业",
        #     "39f3b69f-1111-1111-10f8-ef81e63554e9": "资管中心",
        #     "3a0561d4-1111-1111-cfc0-dc29fe225cc3": "租赁中心2.0"
        # }
        # data = '%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20%2239edb73d-1111-1111-40d0-6de765e234d2%22%3A%20%22%E4%BA%91%E8%B5%84%E7%AE%A1%22%2C%0A%20%20%20%20%20%20%20%20%20%20%20%20%2239edb73e-1111-1111-ca00-c0ac6151c652%22%3A%20%22%E4%BA%91%E5%85%AC%E5%AF%93%22%2C%0A%20%20%20%20%20%20%20%20%20%20%20%20%2239edb73e-1111-1111-a4c5-79007f6a9d9e%22%3A%20%22%E4%BA%91%E7%89%A9%E4%B8%9A%22%2C%0A%20%20%20%20%20%20%20%20%20%20%20%20%2239f3b69f-1111-1111-10f8-ef81e63554e9%22%3A%20%22%E8%B5%84%E7%AE%A1%E4%B8%AD%E5%BF%83%22%2C%0A%20%20%20%20%20%20%20%20%20%20%20%20%223a0561d4-1111-1111-cfc0-dc29fe225cc3%22%3A%20%22%E7%A7%9F%E8%B5%81%E4%B8%AD%E5%BF%832.0%22%0A%20%20%20%20%20%20%20%20%7D'
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始创建内置大屏文件夹".format(tenant=self.tenant_code))
        data = self.get_data_from_url()
        with get_tenant_db(self.tenant_code) as db:
            try:
                db.begin_transaction()
                self._db = db
                self.create_folders(data)
                db.commit()  # noqa
            except:
                logging.error(f'创建文件夹失败。原因：{traceback.format_exc()}')
                db.rollback()
        logging.exception("租户 {tenant} 结束创建内置大屏文件夹".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def get_data_from_url(self):
        try:
            return json.loads(unquote(self.extra))
        except Exception as e:
            raise UserError(message=f'extra解析失败！{str(e)}')

    def _get_screen_distribute_folder(self):
        """
        返回创建一个酷炫大屏的系统分发文件夹
        """
        distribute_folder = self.get_dashboard_by_id(DEFAULT_FOLDER_ID)
        if distribute_folder:
            return distribute_folder
        else:
            # 创建一个大屏系统分发文件夹
            parent_id = ''
            distribute_folder = {
                'id': DEFAULT_FOLDER_ID,
                'name': DEFAULT_FOLDER_NAME,
                'parent_id': parent_id,
                'type': 'FOLDER',
                'theme': 'tech_blue',
                'application_type': 8,
                'level_code': self._get_level_code(parent_id),
                'biz_code': self._get_biz_code()
            }
            distribute_folder['user_group_id'] = '00000000-0000-0000-1111-000000000000'
            distribute_folder['distribute_type'] = DistributeType.Distribute.value
            self.add_dashboard(data=distribute_folder.copy())
            logging.info(f'创建了大屏系统分发文件夹：<{DEFAULT_FOLDER_NAME}>, id: {distribute_folder.get("id", "")}')
            return distribute_folder

    def _get_biz_code(self):
        return str(uuid.uuid4()).replace('-', '')

    def _get_level_code(self, parent_id):
        operate_model = DashboardLevelSequenceModel(level_id=parent_id)
        return level_sequence_service.generate_level_code(operate_model, conn=self._db)

    def create_folders(self, data):
        distribute_folder = self._get_screen_distribute_folder()

        for folder_id, name in data.items():
            folder = self.get_dashboard_by_id(folder_id)
            if folder:
                logging.info(f'已经创建过：<{name}>, id: {folder_id}，跳过')
                continue
            else:
                parent_id = distribute_folder.get('id') or ''
                folder = {
                    'id': folder_id,
                    'name': name,
                    'parent_id': parent_id,
                    'type': 'FOLDER',
                    'theme': 'tech_blue',
                    'application_type': 8,
                    'level_code': self._get_level_code(parent_id),
                    'biz_code': self._get_biz_code()
                }
                folder['user_group_id'] = '00000000-0000-0000-1111-000000000000'
                self.add_dashboard(data=folder.copy())
                logging.info(f'创建了大屏文件夹：<{name}>, id: {folder_id}')

    def add_dashboard(self, data):
        self._db.insert("dap_bi_dashboard", data, commit=False)

    def get_dashboard_by_id(self, dash_id):
        sql = """ select * from dap_bi_dashboard where id = %(id)s limit 1 """
        return self._db.query_one(sql, {'id': dash_id}) or {}
