#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
dashboard_chart_field_sort表的content字段修复
content字段的值存在多次反序列化的数据
"""

# ---------------- 标准模块 ----------------
import logging
import json
from json import JSONDecodeError

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from base.tenant_repository import update_data


logger = logging.getLogger(__name__)


class FixOrderContentCommand(UpgradeCommand):
    """

    """

    def exec(self):
        """

        :return:
        """
        query_data = self.get_table_data(self.tenant_code)
        if not query_data:
            return True
        try:
            for item in query_data:
                if not item:
                    continue
                content = item.get("content")
                if (
                    content
                    and isinstance(content, str)
                    and (r'\\' in content or '""' in content or r'\"' in content or '"{' in content or '}"' in content)
                ):
                    self.update_table_data(self.tenant_code, self.replace(content), item.get("id"))
        except BaseException as e:
            message = '''Error 租户code:{0} TaskId:{1} 自定义排序数据修复 异常信息{2}'''.format(self.tenant_code, self.task_id, str(e))
            logger.exception(message)
            raise e
        return True

    def replace(self, mystr):
        replace_map = {r'\\': '\\', '""': '"', r'\"': '"', '"{': '{', '}"': '}', r'\{': '{', r'\}': '}'}
        old = mystr
        for k, v in replace_map.items():
            mystr = mystr.replace(k, v)
        if old == mystr:
            return mystr
        return self.replace(mystr)

    @staticmethod
    def get_table_data(tenant_code):
        """
        获取数据
        :return:
        """
        sql = """SELECT id,content FROM dap_bi_dashboard_chart_field_sort where content !='' and content is not null  """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    @staticmethod
    def update_table_data(tenant_code, content, pk):
        """
        更新数据
        :return:
        """
        if not content:
            return False
        return update_data(tenant_code, "dap_bi_dashboard_chart_field_sort", {"content": content}, {"id": pk})
