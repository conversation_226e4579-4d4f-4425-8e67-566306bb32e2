#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2022-3-7 14:56:57
# <AUTHOR> lulei
import logging
import re
import json

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.config import get_env_code


logger = logging.getLogger(__name__)


class MsgUsedDatasetStatisticsCommand(UpgradeCommand):
    """
    租户级
    环境级统计数据，全部写入config库
    """
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        try:
            self._msg_used_dataset_tj()
        except Exception as e:
            logging.info(f"租户{self.tenant_code} 执行异常,err: {str(e)}")
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _msg_used_dataset_tj(self):
        msg_dataset_info = self._get_msg_dataset_info()
        if not msg_dataset_info:
            logging.info("该租户无简讯内容")
            return False

        tj_list = []
        for item in msg_dataset_info:
            try:
                dataset_ids = json.loads(item['dataset_ids'])
            except Exception as e:
                dataset_ids = []
                logging.exception(f"租户{self.tenant_code} id:{item.get('id')} dataset_ids转换异常,err: {str(e)}")
            dataset_ids = list(set(dataset_ids))
            times = len(dataset_ids)
            tmp = {
                "env_code": get_env_code(),
                "project_code": self.tenant_code,
                "email_subscribe_id": item['id'],
                # 标题去除html
                "subject_email": self._clear_subscribe_name_html(item['subject_email']),
                # 发送周期
                "send_frequency_str": self.get_send_frequency(item['send_frequency'], item['schedule']),
                "status": item['status'] if item['status'] else '未开始',
                "times": times,
            }
            tj_list.append(tmp)
        logging.info("该租户简讯数量： %s" % str(len(tj_list)))
        self._add_msg_tj_info(tj_list)

    @staticmethod
    def _clear_subscribe_name_html(message):
        """
        简讯标题中字段HTML去除
        :param message:
        :return:
        """
        message = re.sub('<samp.*?>|</samp>|style=".*?"|class=".*?"', '', message)
        message = re.sub('<div.*?>', '', message)
        message = re.sub('</div>', '', message)
        return message

    def get_send_frequency(self, send_frequency, schedule):
        """
        获取发送频率的字符串信息
        :param send_frequency:
        :param schedule:
        :return:
        """
        send_frequency_dict = {1: '立即发送', 2: '定时发送', 3: '周期发送'}
        send_val = send_frequency_dict.get(send_frequency)
        schedule_type = self.get_schedule_type(schedule)
        schedule_type_dict = {"month": "月", "week": "周", "day": "天"}
        schedule_type_name = schedule_type_dict.get(schedule_type)
        if send_frequency == 3 and schedule_type:
            send_val = f"{send_val}({schedule_type_name})"
        return send_val

    @staticmethod
    def get_schedule_type(schedule):
        schedule_type = ''
        if not schedule:
            return schedule_type
        schedule_list = schedule.split(' ')
        if schedule_list[5] == '?' and (not schedule_list[0].startswith('0/') or not schedule_list[1].startswith('0/')):
            schedule_type = 'month'
        elif schedule_list[5] != '*':
            schedule_type = 'week'
        elif schedule_list[5] == '*' and schedule_list[3] == '?' \
                and ('/' not in schedule_list[2] and '-' not in schedule_list[2]):
            schedule_type = 'day'
        return schedule_type

    def _add_msg_tj_info(self, tj_list):
        """
        将统计信息写入配置库
        先删后写
        :param tj_list:
        :return:
        """
        if not tj_list:
            return False
        from dmplib.db.mysql_wrapper import get_db
        fields = list(tj_list[0].keys())
        with get_db() as db:
            db.delete('dap_bi_msg_used_dataset_statistics', {"project_code": self.tenant_code, "env_code": get_env_code()})
            db.insert_multi_data('dap_bi_msg_used_dataset_statistics', tj_list, fields)
        logging.info(f"{self.tenant_code}统计数据写入成功")

    def _get_msg_dataset_info(self):
        """
        获取租户的简讯数据集使用信息
        :return:
        """
        with get_tenant_db(self.tenant_code) as db:
            sql = """
            SELECT s.id,s.subject_email,s.send_frequency,s.dataset_ids,
            f.`schedule`, f.`status` FROM `dap_bi_dashboard_email_subscribe` s left join 
            flow f 
            on s.id = f.id 
            where s.type = '简讯订阅'
            order by s.created_on desc;
            """
            return db.query(sql)
