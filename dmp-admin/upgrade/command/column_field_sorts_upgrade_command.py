#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/3/6 16:06
# <AUTHOR> caoxl
# @File     : column_field_sorts_upgrade.py

import json
import logging
from collections import defaultdict

from dmplib.utils.strings import seq_id
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db

logger = logging.getLogger(__name__)


class ColumnFieldSortsUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        charts = self._get_no_sort_charts()
        if not charts:
            logging.exception("租户 {tenant} 没有需要更新的数据，结束!".format(tenant=self.tenant_code))
        else:
            chart_ids = [chart["id"] for chart in charts]
            chart_dims_dict = self._get_dims_by_chart_ids(chart_ids)
            chart_nums_dict = self._get_nums_by_chart_ids(chart_ids)
            chart_desires_dict = self._get_desire_by_chart_ids(chart_ids)
            chart_comparisons_dict = self._get_comparisons_by_chart_ids(chart_ids)
            for chart in charts:
                chart_id = chart["id"]
                logging.exception("租户 {tenant} 开始更新单图 {chart_id}".format(tenant=self.tenant_code, chart_id=chart_id))
                dims = chart_dims_dict.get(chart_id, [])
                nums = chart_nums_dict.get(chart_id, [])
                desires = chart_desires_dict.get(chart_id, [])
                comparisons = chart_comparisons_dict.get(chart_id, [])
                if chart["dashboard_id"] == '39f3daa7-c25a-ea8d-8bb8-6e59b404baa2':
                    print("aaa")
                chart_field_sorts = self._generate_field_sorts(chart, dims, nums, desires, comparisons)
                if not chart_field_sorts:
                    logging.exception(
                        "租户 {tenant} 单图 {chart_id} 未生成排序为空!".format(tenant=self.tenant_code, chart_id=chart_id)
                    )
                    logging.exception(
                        "租户 {tenant} 结束更新单图 {chart_id}".format(tenant=self.tenant_code, chart_id=chart_id)
                    )
                    continue
                logging.exception(
                    "租户 {tenant} 单图 {chart_id} 新排序为 {field_sorts}".format(
                        tenant=self.tenant_code, chart_id=chart_id, field_sorts=json.dumps(chart_field_sorts)
                    )
                )
                result, msg = self._update_field_sorts(chart_id, chart_field_sorts)
                if not result:
                    logging.exception(
                        "租户 {tenant} 更新单图 {chart_id} 预览和发布数据失败，错误信息 {msg} !".format(
                            tenant=self.tenant_code, chart_id=chart_id, msg=msg
                        )
                    )
                    logging.exception(
                        "租户 {tenant} 结束更新单图 {chart_id}".format(tenant=self.tenant_code, chart_id=chart_id)
                    )
                    continue
                logging.exception("租户 {tenant} 结束更新单图 {chart_id}".format(tenant=self.tenant_code, chart_id=chart_id))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _update_field_sorts(self, chart_id: str, field_sorts: list) -> [bool, str]:
        """
        同一个事务中更新数据
        :param chart_id:
        :param field_sorts:
        :return:
        """
        with get_tenant_db(self.tenant_code) as db:
            try:
                fields = [
                    "id",
                    "dashboard_id",
                    "dashboard_chart_id",
                    "dataset_field_id",
                    "field_source",
                    "content",
                    "weight",
                    "sort",
                ]
                db.delete(table="dap_bi_dashboard_chart_field_sort", condition={"dashboard_chart_id": chart_id}, commit=False)
                db.update(
                    table="dap_bi_dashboard_released_snapshot_chart",
                    data={"field_sorts": json.dumps(field_sorts)},
                    condition={"id": chart_id},
                    commit=False,
                )
                db.insert_multi_data(table="dap_bi_dashboard_chart_field_sort", list_data=field_sorts, fields=fields, commit=False)
                db.commit()
                return True, ""
            except Exception as e:
                db.rollback()
                return False, str(e)

    def _generate_field_sorts(self, chart: dict, dims: list, nums: list, desires: list, comparisons: list):
        """
        生成单个chart的排序
        :param chart:
        :param dims:
        :param nums:
        :param desires:
        :param comparisons:
        :return:
        """
        field_sorts = []
        weight = 0
        data = [
            {"field_source": "comparisons", "data": self._sort_data(comparisons)},
            {"field_source": "desires", "data": self._sort_data(desires)},
            {"field_source": "nums", "data": self._sort_data(nums)},
            {"field_source": "dims", "data": self._sort_data(dims)},
        ]
        # 1. 如果为透视表(聚合 有对比维度 有数值)则数值不加入排序
        if chart["aggregation"] and comparisons and dims and nums:
            data = [
                {"field_source": "comparisons", "data": self._sort_data(comparisons)},
                {"field_source": "desires", "data": self._sort_data(desires)},
                {"field_source": "dims", "data": self._sort_data(dims)},
            ]
        # 1. weight 起始策略：聚合情况下对比维度从负值开始，其他类型字段从0开始
        if chart["aggregation"]:
            weight = -len(comparisons)
        for item in data:
            field_soruce = item["field_source"]
            list_data = item["data"]
            sorts, weight = self._get_field_sorts(chart["dashboard_id"], chart["id"], field_soruce, list_data, weight)
            field_sorts.extend(sorts)
        return field_sorts

    def _get_field_sorts(
        self, dashboard_id: str, dashboard_chart_id: str, field_source: str, data: list, weight: int) -> [list, int]:
        field_sorts = []
        for item in data:
            field_sorts.append(
                {
                    "id": seq_id(),
                    "dashboard_id": dashboard_id,
                    "dashboard_chart_id": dashboard_chart_id,
                    "dataset_field_id": item["dataset_field_id"],
                    "field_source": field_source,
                    "content": "",
                    "weight": weight,
                    "sort": "ASC" if not item.get("sort") else item.get("sort"),
                }
            )
            weight += 1
        return field_sorts, weight

    def _sort_data(self, data: list) -> list:
        if not data:
            return data
        return sorted(data, key=lambda item: item.get("rank"), reverse=True)

    def _get_no_sort_charts(self) -> list:
        """
        获取没有排序的按列返回的单图
        :return:
        """
        sql = """SELECT c.id, c.dashboard_id,c.chart_code,c.data_logic_type_code,c.source as dataset_id,c.aggregation 
                 FROM `dap_bi_dashboard_chart` c LEFT JOIN `dap_bi_dashboard_chart_field_sort` s ON s.dashboard_chart_id=c.id 
                 WHERE c.data_logic_type_code='column' AND s.dashboard_chart_id IS NULL"""
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql)

    def _get_nums_by_chart_ids(self, chart_ids: list) -> dict:
        """
        获取数值
        :param chart_ids:
        :return:
        """
        sql = """SELECT dashboard_chart_id, num as dataset_field_id,rank,sort  
                FROM `dap_bi_dashboard_chart_num` WHERE `dashboard_chart_id` IN %(chart_ids)s"""
        params = {"chart_ids": chart_ids}
        num_dict = defaultdict(list)
        with get_tenant_db(self.tenant_code) as db:
            nums = db.query(sql, params=params)
            for num in nums:
                num_dict[num["dashboard_chart_id"]].append(num)
            return dict(num_dict)

    def _get_dims_by_chart_ids(self, chart_ids: list) -> dict:
        """
        获取维度
        :param chart_ids:
        :return:
        """
        sql = """SELECT dashboard_chart_id, dim as dataset_field_id,rank,sort  
                FROM `dap_bi_dashboard_chart_dim` WHERE `dashboard_chart_id` IN %(chart_ids)s"""
        params = {"chart_ids": chart_ids}
        dim_dict = defaultdict(list)
        with get_tenant_db(self.tenant_code) as db:
            dims = db.query(sql, params=params)
            for dim in dims:
                dim_dict[dim["dashboard_chart_id"]].append(dim)
            return dict(dim_dict)

    def _get_comparisons_by_chart_ids(self, chart_ids: list) -> dict:
        """
        获取对比维度
        :param chart_ids:
        :return:
        """
        sql = """SELECT dashboard_chart_id, dataset_field_id,rank,sort  
                FROM `dap_bi_dashboard_chart_comparison` WHERE `dashboard_chart_id` IN %(chart_ids)s"""
        params = {"chart_ids": chart_ids}
        comparison_dict = defaultdict(list)
        with get_tenant_db(self.tenant_code) as db:
            comparisons = db.query(sql, params=params)
            for comparison in comparisons:
                comparison_dict[comparison["dashboard_chart_id"]].append(comparison)
            return dict(comparison_dict)

    def _get_desire_by_chart_ids(self, chart_ids: list) -> dict:
        """
        获取目标值
        :param chart_ids:
        :return:
        """
        sql = """SELECT dashboard_chart_id, dataset_field_id,rank,sort  
                FROM `dap_bi_dashboard_chart_desire` WHERE `dashboard_chart_id` IN %(chart_ids)s"""
        params = {"chart_ids": chart_ids}
        desire_dict = defaultdict(list)
        with get_tenant_db(self.tenant_code) as db:
            desires = db.query(sql, params=params)
            for desire in desires:
                desire_dict[desire["dashboard_chart_id"]].append(desire)
            return dict(desire_dict)
