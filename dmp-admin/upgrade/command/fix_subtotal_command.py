#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/4/23 11:08
# <AUTHOR> caoxl
# @File     : fix_subtotal_command.py
import json
import logging
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db

logger = logging.getLogger(__name__)


class FixSubtotalCommand(UpgradeCommand):
    """
    修复小计数据
    """

    def exec(self):
        logging.exception(msg="--------------------开始更新预览数据--------------------")
        result, msg = self._upgrade_preview_data()
        result_word = "成功" if result else "失败，错误信息 [{msg}]".format(msg=msg)
        logging.exception(msg="更新预览数据 {result_word}".format(result_word=result_word))
        logging.exception(msg="--------------------结束更新预览数据--------------------")
        logging.exception(msg="--------------------开始更新发布数据--------------------")
        self._upgrade_release_data()
        logging.exception(msg="--------------------结束更新发布数据--------------------")
        logging.exception(msg="开始更新预览数据")

    def _upgrade_preview_data(self):
        sql = """SELECT n.dashboard_chart_id,n.id, n.dashboard_id,n.subtotal_formula_mode,n.num  
                FROM dap_bi_dashboard_chart c 
                LEFT JOIN dap_bi_dashboard_chart_num n on n.dashboard_chart_id=c.id
                WHERE c.chart_code='dmp_handsontable_mobile' 
                """
        with get_tenant_db(self.tenant_code) as db:
            chart_nums = db.query(sql)
            chart_num_ids = []
            for chart_num in chart_nums:
                if not chart_num["subtotal_formula_mode"]:
                    continue
                logging.exception(msg="报告 {dashboard_id} 单图 {chart_id} 数值 {num} 发现异常小计数据".format(dashboard_id=chart_num["dashboard_id"], chart_id=chart_num["dashboard_chart_id"], num=chart_num["num"]))
                chart_num_ids.append(chart_num["id"])
            if not chart_num_ids:
                return True, ""
            sql = """UPDATE dap_bi_dashboard_chart_num SET subtotal_formula_mode='' WHERE id IN %(chart_num_ids)s"""
            try:
                result = db.exec_sql(sql, {"chart_num_ids": chart_num_ids})
                return result, ""
            except Exception as e:
                return False, str(e)

    def _upgrade_release_data(self):
        sql = """SELECT increment_id, id, dashboard_id, nums  
                FROM dap_bi_dashboard_released_snapshot_chart 
                WHERE chart_code='dmp_handsontable_mobile' 
              """
        with get_tenant_db(self.tenant_code) as db:
            charts = db.query(sql)
            for chart in charts:
                try:
                    chart_nums = json.loads(chart["nums"])
                except Exception as e:
                    logging.exception(
                        msg="报告 {dashboard_id} 单图 {chart_id} json数据无法解析，错误信息 [{msg}]".format(
                            dashboard_id=chart["dashboard_id"], chart_id=chart["id"], msg=str(e)
                        )
                    )
                    continue
                need_update = False
                for chart_num in chart_nums:
                    if chart_num["subtotal_formula_mode"]:
                        logging.exception(msg="报告 {dashboard_id} 单图 {chart_id} 数值 {num} 发现异常小计数据".format(dashboard_id=chart["dashboard_id"], chart_id=chart_num["dashboard_chart_id"], num=chart_num["num"]))
                        chart_num["subtotal_formula_mode"] = ""
                        need_update = True
                if need_update:
                    try:
                        re = db.update(
                            "dap_bi_dashboard_released_snapshot_chart",
                            {"nums": json.dumps(chart_nums)},
                            {"increment_id": chart["increment_id"]},
                        )
                        self._display_update_result(chart, re)
                    except Exception as e:
                        logging.exception(
                            msg="报告 {dashboard_id} 单图 {chart_id} 更新数据失败，错误信息 [{msg}]".format(
                                dashboard_id=chart["dashboard_id"], chart_id=chart["id"], msg=str(e)
                            )
                        )

    def _display_update_result(self, chart, re):
        if re:
            logging.exception(
                msg="报告 {dashboard_id} 单图 {chart_id} 更新数据成功".format(
                    dashboard_id=chart["dashboard_id"], chart_id=chart["id"]
                )
            )
        else:
            logging.exception(
                msg="报告 {dashboard_id} 单图 {chart_id} 更新数据失败".format(
                    dashboard_id=chart["dashboard_id"], chart_id=chart["id"]
                )
            )
