#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图表备注数据升级
注：可重复执行

eg.
https://dmp-admin-test.mypaas.com.cn/api/upgrade/run?command_name=tooltips_config_upgrade_command&type=include&tenants=test
"""

# ---------------- 标准模块 ----------------
import logging
from collections import defaultdict
import json

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from base.tenant_repository import update_data
from dmplib.db.mysql_wrapper import SimpleMysql


logger = logging.getLogger(__name__)


class TooltipsConfigUpgradeCommand(UpgradeCommand):
    def exec(self):
        if not self.tenant_code:
            return True

        # 单图记录数量过大的情况，分批处理
        rows = 1000
        try:
            db = get_tenant_db(self.tenant_code)
            db.begin_transaction()

            # dashboard_chart
            preview_offset = 0
            while True:
                logging.exception("++++----------------------------------------------------------------------++++")
                logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
                preview_dashboard_charts = self._get_preview_dashboard_charts(preview_offset, rows)
                if not preview_dashboard_charts:
                    break
                logger.exception("开始更新预览态数据 当前单图位移量: {}".format(preview_offset))
                update_data = self._parse_and_replace_configs(preview_dashboard_charts)
                self._update_charts(update_data, "dap_bi_dashboard_chart", db)
                logger.exception("更新预览态数据 {count} 条".format(count=len(update_data)))
                preview_offset += rows
                del update_data

            # dashboard_released_snapshot_chart
            release_offset = 0
            while True:
                rleased_dashboard_charts = self._get_release_dashboard_charts(release_offset, rows)
                if not rleased_dashboard_charts:
                    break
                logger.exception("开始更新发布态数据 当前单图位移量: {}".format(release_offset))
                update_data = self._parse_and_replace_configs(rleased_dashboard_charts)
                self._update_charts(update_data, "dap_bi_dashboard_released_snapshot_chart", db)
                logger.exception("更新发布态数据 {count} 条".format(count=len(update_data)))
                release_offset += rows
                del update_data

            db.commit()

        except BaseException as e:
            if db:
                db.rollback()
            message = '''Error 租户code:{0} TaskId:{1} 图表备注数据升级 异常信息{2}'''.format(self.tenant_code, self.task_id, str(e))
            logger.exception(message)
            return False

    def _get_preview_dashboard_charts(self, offset, rows) -> list:
        sql = """SELECT `id`,`config` FROM `dap_bi_dashboard_chart` WHERE `config` is not null 
                AND `config` != '' AND `config` LIKE '%%containerUpdateTime%%' LIMIT %(offset)s, %(rows)s """
        with get_tenant_db(self.tenant_code) as db:
            charts = db.query(sql, {"offset": offset, "rows": rows})
        return charts or []

    def _get_release_dashboard_charts(self, offset, rows) -> list:
        sql = """SELECT `id`, `config` FROM `dap_bi_dashboard_released_snapshot_chart` where `config` is not null 
                AND `config` != '' AND `config` LIKE '%%containerUpdateTime%%' LIMIT %(offset)s, %(rows)s """
        with get_tenant_db(self.tenant_code) as db:
            charts = db.query(sql, {"offset": offset, "rows": rows})
        return charts or []

    def _parse_and_replace_configs(self, dashboard_charts: list):
        new_dashboard_chart_configs = []
        for dashboard_chart in dashboard_charts:
            chart_id = dashboard_chart.get("id")
            if not chart_id:
                continue
            try:
                config = json.loads(dashboard_chart.get("config"))
            except Exception as e:
                logger.exception("解析单图 {chart_id} 配置失败， 错误信息 {msg}".format(chart_id=dashboard_chart["id"], msg=str(e)))
                continue
            new_config = self._generate_new_config(config)
            new_dashboard_chart_configs.append({"id": chart_id, "config": json.dumps(new_config)})
        return new_dashboard_chart_configs

    def _generate_new_config(self, old_config: dict):
        new_config = []
        for item in old_config:
            if item.get("field") != "containerUpdateTime":
                new_config.append(item)
                continue
            update_time_show = item.get("show", False)
            time_type_data = False
            time_position = "top-left"
            item_items = item.get("items")
            if item_items:
                time_type_data, time_position = self._get_orig_time_values(item_items)
            chart_remark_config = self._get_chart_remark_config(update_time_show, time_type_data, time_position)
            new_config.append(chart_remark_config)
        return new_config

    def _get_orig_time_values(self, items: dict):
        time_type_data = False
        time_position = "top-left"
        for i in items:
            if i.get("field") == "timeType":
                time_type_data = self._get_time_type_data(i)
            elif i.get("field") == "timePosition":
                time_position = i.get("data", "top-left")
        return time_type_data, time_position

    def _get_time_type_data(self, i: dict):
        time_type_data = False
        i_items = i.get("items")
        for i_item in i_items:
            if i_item.get("field") == "icon":
                time_type_data = i_item.get("data")
        return time_type_data

    def _get_chart_remark_config(self, update_time_show: bool, time_type_data: bool, time_position: str):
        chart_remark_config = {
            "title": "图表备注",
            "field": "chartRemarkConfig",
            "spread": True,
            "scope": "container.chartRemark",
            "items": [
                {
                    "field": "dataUpdate",
                    "label": "数据更新时间",
                    "scope": "container.chartRemark.dataUpdate",
                    "show": {"field": "checked", "data": update_time_show},
                    "items": [
                        {
                            "field": "dataTimeType",
                            "label": "时间展示方式",
                            "scope": "container.chartRemark.dataUpdate.timeType",
                            "data": time_type_data,
                        }
                    ],
                },
                {
                    "field": "remarkDescription",
                    "label": "备注说明",
                    "scope": "container.chartRemark.remarkDescription",
                    "items": [
                        {"field": "title", "label": "标题文本", "data": ""},
                        {"field": "textAreaLabel", "label": "内容文本", "data": ""},
                        {"field": "position", "label": "显示位置", "data": time_position},
                    ],
                },
            ],
        }
        return chart_remark_config

    def _update_charts(self, charts: list, table_name: str, db: SimpleMysql):
        for chart in charts:
            try:
                db.update(table_name, {"config": chart["config"]}, {"id": chart["id"]})
                logger.exception("更新单图 {chart_id} 数据成功".format(chart_id=chart["id"]))
            except Exception as e:
                logger.exception("更新单图 {chart_id} 数据失败，错误信息 {msg} ".format(chart_id=chart["id"], msg=str(e)))
