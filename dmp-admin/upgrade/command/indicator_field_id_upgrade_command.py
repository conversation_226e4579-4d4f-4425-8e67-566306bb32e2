#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/8/12 20:00
# <AUTHOR> songh02
import logging
import pymysql

from upgrade.command.command import UpgradeCommand
from base.db_wrap import DbWrap

logger = logging.getLogger(__name__)


class IndicatorFieldIdUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._add_field_map()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    @staticmethod
    def __escape_string(data):
        """
        转义
        :param data:
        :return:
        """
        try:
            data = pymysql.converters.escape_string(str(data).encode(encoding='utf-8').decode(encoding='utf-8'))
        except Exception:
            return data
        return data

    def _add_field_map(self):
        db = DbWrap(self.tenant_code)
        datasets = db.get_list('dap_bi_dataset', {'type': 'INDICATOR'}, ['id', 'external_id'])
        if datasets:
            id_map = {i.get('id'): i.get('external_id') for i in datasets}
            dataset_ids = list(id_map.keys())
            fields = db.get_list(
                'dap_bi_dataset_field',
                {"dataset_id": dataset_ids},
                fields=['id', 'external_id', 'field_group', 'origin_col_name', 'origin_table_name', 'dataset_id']
            ) or []
            for field in fields:
                field_group = field.get("field_group")
                if field_group == "度量":
                    sql = """
                        replace into dap_bi_indicator_field_map (`dataset_field_id`, `indicator_id`)
                        values (%r, %r)
                        """ % (field.get('id'), field.get('external_id'))
                else:
                    sql = """
                        replace into dap_bi_indicator_field_map (`dataset_field_id`, `table_name`, `field_name`, `class_id`)
                        values (%r, %r, %r, %r)
                        """ % (
                        field.get('id'),
                        self.__escape_string(field.get('origin_table_name')),
                        self.__escape_string(field.get('origin_col_name')),
                        id_map.get(field.get('dataset_id'))
                    )
                db.db.exec_sql(sql)


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)

    command = IndicatorFieldIdUpgradeCommand(tenant_code="indicator_test")
    command._add_field_map()
