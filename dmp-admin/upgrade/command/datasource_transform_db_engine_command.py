#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by wuhm@mingy<PERSON>yun on 2019年02月18日

"""
云客数据源，db引擎切换成 ads
"""

# ---------------- 标准模块 ----------------
import logging
import json
import os
from dmplib.redis import conn as conn_redis

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)


# ---------------- 业务模块 ----------------
from dmplib.utils.errors import UserError
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.db.mysql_wrapper import get_db

logger = logging.getLogger(__name__)


class DatasourceTransformDbEngineCommand(UpgradeCommand):
    """
       datasource_transform_db_engine
    """

    def exec(self):
        """
        批量更新数据源连接类型
        """
        if self.tenant_code == 'all':
            conditions = 1
        else:
            conditions = "code in ('{code}')".format(code=self.tenant_code)
        sql = "SELECT  id,code FROM dap_p_tenant where {conditions} ".format(conditions=conditions)
        with get_db() as db:
            projects = db.query(sql)
        if not projects:
            raise UserError(message='未找到项目记录')
        for project in projects:
            datasources = self.get_datasource(project.get('code'))
            if not datasources:
                continue
            for datasource in datasources:
                # 删除缓存
                self._del_datasource_cache(project.get('code'), datasource.get('id'))
                self._update_data(project.get('code'), datasource)

        return True

    def _update_data(self, code, datasource):
        """
        编辑conn_str并更新数据
        :param conn_str_dict:
        :return:
        """
        logging.exception(msg="开始更新租户 {tenant} 数据源 {datasource} ".format(tenant=code, datasource=datasource.get("id")))
        conn_str_dict = json.loads(datasource.get('conn_str'))
        extra = json.loads(self.extra)
        engine = {}

        engine['ads'] = {"key": "db_engine", "value": "ads", "type": "query", "name": "db_engine"}
        engine['rds'] = {"key": "db_engine", "value": "rds", "type": "query", "name": "db_engine"}

        has_engine_key = False
        for k, param in enumerate(conn_str_dict.get('params')):
            # 若原有的引擎已经是，不做任何处理
            if param.get('key').lower() == 'db_engine' and param.get('value').lower() == extra.get('to_engine').lower():
                has_engine_key = True
                return True

            # 若原有的引擎非目标，删除rds配置
            if param.get('key').lower() == 'db_engine' and param.get('value').lower() != extra.get('to_engine').lower():
                has_engine_key = True
                del conn_str_dict.get('params')[k]
                # 追加 db_engine=rds
                conn_str_dict.get('params').append(engine.get(extra.get('to_engine').lower()))

        # 两者都找不到的话，直接追加
        if not has_engine_key:
            conn_str_dict.get('params').append(engine.get(extra.get('to_engine').lower()))

        conn_str = json.dumps(conn_str_dict)

        sql = "update dap_m_data_source set conn_str='{conn_str}' where id= '{id}'".format(
            conn_str=conn_str, id=datasource.get('id')
        )
        with get_tenant_db(code) as db:
            rs = db.exec_sql(sql)
        if not rs:
            logging.exception(
                msg="更新租户 {tenant} 数据源 {datasource} 失败!".format(tenant=code, datasource=datasource.get("id"))
            )
            return False
        logging.exception(
            msg="更新租户 {tenant} 数据源 {datasource} 成功".format(tenant=code, datasource=datasource.get("id"))
        )
        return True

    @staticmethod
    def _del_datasource_cache(tenant_code, source_id):
        cache_key = "%s:%s:%s" % (tenant_code, "data_source:", source_id)
        redis_cache = conn_redis()
        return redis_cache.del_data(cache_key)

    @staticmethod
    def get_datasource(tenant_code):
        """
        获取所有数据源
        :param tenant_code:
        :return:
        """
        sql = "SELECT  id,code,conn_str FROM dap_m_data_source where type='API' "
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)
