#!/usr/bin/env python3
# -*- coding: utf-8 -*-


# ---------------- 标准模块 ----------------
import logging
import json

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.redis import conn as redis_conn
from dmplib.utils.errors import UserError


logger = logging.getLogger(__name__)


class YunkeDelAllExportDashboardCommand(UpgradeCommand):
    """
    删除分发的报告
    """

    def exec(self):
        """

        :return:
        """
        try:
            self.batch_del_dashboard_data(self.tenant_code)
        except BaseException as e:
            message = """Error 租户code:{0} TaskId:{1} 
                    ProjectCodes:{2} 删除报告异常! 异常信息{3}""".format(
                self.tenant_code, self.task_id, self.tenant_code, str(e)
            )
            logger.exception(message)
            raise UserError(message=message)

    def batch_del_dashboard_data(self, project_code):
        dashboards = self.get_dashboards(project_code)
        if not dashboards:
            return

        del_dashboard_id_list = [item.get("id") for item in dashboards if item.get("id")]
        # 没有dashboard_id的特殊表
        for table_name in ["dap_bi_dashboard_chart_filter_relation"]:
            self.del_specific_table_data(project_code, table_name, del_dashboard_id_list)

        # 需要删除的表
        tables_for_del = [
            "dap_bi_dashboard_chart",
            "dap_bi_screen_dashboard",
            "dap_bi_dashboard_chart_dim",
            "dap_bi_dashboard_chart_num",
            "dap_bi_dashboard_chart_filter",
            "dap_bi_dashboard_chart_field_sort",
            "dap_bi_dashboard_chart_markline",
            "dap_bi_dashboard_chart_comparison",
            "dap_bi_dashboard_chart_desire",
            "dap_bi_dashboard_chart_params",
            "dap_bi_dashboard_chart_params_jump",
            "dap_bi_dashboard_chart_penetrate_relation",
            "dap_bi_dashboard_chart_map",
            "dap_bi_dashboard_chart_layers",
            "dap_bi_dashboard_chart_colour",
            "dap_bi_dashboard_chart_selector",
            "dap_bi_dashboard_chart_selector_field",
            "dap_bi_dashboard_filter",
            "dap_bi_dashboard_filter_relation",
            "dap_bi_dashboard_jump_config",
            "dap_bi_dashboard_jump_relation",
            "dap_bi_dashboard_linkage",
            "dap_bi_dashboard_linkage_relation",
            "dap_bi_dashboard_filter_chart",
            "dap_bi_dashboard_filter_chart_relation",
            "dap_bi_dashboard_filter_chart_default_values",
            "dap_bi_dashboard_dataset_field_relation",
            "dap_bi_dashboard_dataset_vars_relation",
            "dap_bi_dashboard_vars_jump_relation",
            "dap_bi_dashboard_released_snapshot_chart",
            "dap_bi_dashboard_released_snapshot_dashboard",
            "dap_bi_dashboard_extra",
            "dap_bi_dashboard_metadata_history",
            "dap_bi_dashboard",
        ]
        # 有dashboard_id的表
        for table_name in tables_for_del:
            del_key = "dashboard_id"
            if table_name in ["dap_bi_dashboard", "dap_bi_dashboard_released_snapshot_dashboard"]:
                del_key = "id"
            self.batch_delete(project_code, table_name, del_dashboard_id_list, del_key)

        # 清理缓存
        for del_dashboard_id in del_dashboard_id_list:
            self.clear_cache(project_code, del_dashboard_id)

    def clear_cache(self, project_code, del_dashboard_id):
        for cache_class in ["dashboard", "dashboard_release_info"]:
            cache_key = "{0}:{1}:{2}:{3}".format(project_code, self.cache_prefix, cache_class, del_dashboard_id)
            redis_conn()._connection.delete(cache_key)

    @staticmethod
    def get_dashboards(tenant_code):
        sql = """select id from dap_bi_dashboard where level_code regexp '^(9000-[0-9])+' """
        with get_tenant_db(tenant_code) as db:
            return db.query(sql)

    @staticmethod
    def batch_delete(tenant_code, table_name, dashboard_id_list, del_key):
        if not dashboard_id_list:
            return
        sql = """delete from `{table_name}` where {del_key} in %(dashboard_id_list)s 
              """.format(table_name=table_name, del_key=del_key)
        params = {"dashboard_id_list": dashboard_id_list}
        with get_tenant_db(tenant_code) as db:
            return db.exec_sql(sql, params=params)

    @staticmethod
    def del_specific_table_data(tenant_code, table_name, dashboard_id_list):
        if not dashboard_id_list:
            return
        sql = """delete from `{table_name}` where dashboard_chart_id in 
                (select id from dap_bi_dashboard_chart where dashboard_id in %(dashboard_id_list)s) """.format(
            table_name=table_name
        )
        params = {"dashboard_id_list": dashboard_id_list}
        with get_tenant_db(tenant_code) as db:
            return db.exec_sql(sql, params=params)
