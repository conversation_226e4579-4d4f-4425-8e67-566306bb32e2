#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/8/12 20:00
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from components.dmp_api import DMPAPI

logger = logging.getLogger(__name__)


class SelfServiceReportListSort(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._sort_report_list()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _sort_report_list(self):
        logging.exception("开始调用dmp openapi 接口")
        dmp_api = DMPAPI(self.tenant_code)
        res = dmp_api.sort_self_service_report_list(reverse=1)
        logging.exception("调用dmp openapi结束， res: {}".format(res))


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)

    command = SelfServiceReportListSort(tenant_code="test")
    command._sort_report_list()