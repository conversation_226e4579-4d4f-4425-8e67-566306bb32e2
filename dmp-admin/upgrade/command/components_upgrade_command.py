#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图表备注数据升级
注：可重复执行

eg.
https://dmp-admin-test.mypaas.com.cn/api/upgrade/run?command_name=tooltips_config_upgrade_command&type=include&tenants=test
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from base import repository


logger = logging.getLogger(__name__)


class ComponentsUpgradeCommand(UpgradeCommand):
    tables = [{'table': 'dap_bi_component', 'condition_field': ['package']},
              {'table': 'dap_bi_component_data_logic_type', 'condition_field': ['code']},
              {'table': 'dap_bi_component_init_log', 'condition_field': ['client', 'tenant']},
              {'table': 'dap_bi_component_menu', 'condition_field': ['id']}
              ]

    def exec(self):
        if not self.tenant_code:
            return True

        # 组件记录数量过大的情况，分批处理
        rows = 1000
        for item in self.tables:
            try:
                db = get_tenant_db(self.tenant_code)
                db.begin_transaction()

                table = item['table']
                condition_field = item['condition_field']

                offset = 0
                while True:
                    logging.exception("++++----------------------------------------------------------------------++++")
                    logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
                    data = self._get_data_from_tenant(table, offset, rows)
                    if not data:
                        break
                    logger.exception("开始更新组件数据 当前组件位移量: {}".format(offset))
                    self._replace_data_into_config(table, data, condition_field=condition_field)
                    logger.exception("更新组件数据 {count} 条".format(count=len(data)))
                    offset += rows

                db.commit()

            except BaseException as e:
                if db:
                    db.rollback()
                message = '''Error 租户code:{0} TaskId:{1} 组件数据升级 异常信息{2}'''.format(self.tenant_code, self.task_id, str(e))
                logger.exception(message)
                return False

    def _get_data_from_tenant(self, table, offset, rows):
        sql = f"SELECT * FROM {table}"
        with get_tenant_db(self.tenant_code) as db:
            return db.query(sql, offset=offset, limit=rows)

    @staticmethod
    def _replace_data_into_config(table, data, condition_field=None):
        fields = data[0].keys() if data else ''
        return repository.replace_list_data(table, data, fields, condition_field=condition_field)

