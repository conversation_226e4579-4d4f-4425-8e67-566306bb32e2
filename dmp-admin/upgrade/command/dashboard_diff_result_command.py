#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
在某个环境中，基准租户的系统分发报表与其他租户的分发报表进行对比。识别出改动的报告。
"""

# ---------------- 标准模块 ----------------
import logging
import json
import os
from hashlib import md5

from dmplib.redis import conn as redis_conn

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)


# ---------------- 业务模块 ----------------
from dmplib.utils.errors import UserError
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.db.mysql_wrapper import get_db

logger = logging.getLogger(__name__)


class DashboardDiffResultCommand(UpgradeCommand):
    """
       dashboard_diff_result_command
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.dashboard_ids = []
        self.source_code = ""

    def exec(self):
        try:
            self._init()
            self._exec()
        except Exception as e:
            logger.error("对比统计执行异常,errs："+str(e))

    def _init(self):
        if self.extra:
            logger.error("请求参数："+self.extra)
            code, dashboard_ids = self.extra.split('|')
            dashboard_id_list = dashboard_ids.split(';')
            self.source_code = code
            self.dashboard_ids = dashboard_id_list
        if not self.source_code:
            raise UserError(message='未指定基准租户code')
        if not self.dashboard_ids:
            raise UserError(message='未指定报告id')

    @staticmethod
    def _get_all_project():
        # 测试
        # return [{'code': 'lulei_test1'}, {'code': 'luyy'}, {'code': 'poly'}]
        # return [{'code': 'poly'}]

        sql = "SELECT id,code FROM dap_p_tenant"
        with get_db() as db:
            projects = db.query(sql)
        return projects

    def _exec(self):
        """
        执行比对任务
        :return:
        """
        # 原始基准租户报告信息
        logger.error(f"=======================原始租户{self.source_code}数据准备=======================")
        source_dashboard_list = self.get_dashboard_list_data(self.source_code)
        logger.error(f"=======================原始租户报告数据=======================")
        print(json.dumps(source_dashboard_list, ensure_ascii=False))
        # 比对每个租户的报告信息
        project_list = self._get_all_project()
        compare_result = []
        for project in project_list:
            if project.get("code") == self.source_code:
                continue
            target_compare_rs = self._compare(project.get("code"), source_dashboard_list)
            if target_compare_rs:
                compare_result.extend(target_compare_rs)

        # 全部租户报告比对结果写入 redis
        self._write_redis(compare_result)

    def _write_redis(self, compare_result):
        if compare_result:
            cache_key = 'dmp-admin:dashboard_compare_result'
            return redis_conn()._connection.set(cache_key, json.dumps(compare_result, ensure_ascii=False), 7*86400)

    def _compare(self, target_code, source_dashboard_list):
        """
        报告比对逻辑
        通过两个报告中的报告元数据，组件，数据集数据进行差集对比
        :param target_code:
        :param source_dashboard_list:
        :return:
        """
        logger.error(f"=======================目标租户{target_code}开始比对=======================")
        target_dashboard_list = self.get_dashboard_list_data(target_code)
        # print(json.dumps(target_dashboard_list, ensure_ascii=False))
        target_compare_rs = []
        for dashboard_id, source_dashboard in source_dashboard_list.items():
            compare_rs = self._compare_dashboard(dashboard_id, source_dashboard, target_dashboard_list)
            compare_rs["target_code"] = target_code
            # print(json.dumps(compare_rs, ensure_ascii=False))
            # 存在变动报告，写入redis
            if compare_rs.get("dashboard"):
                target_compare_rs.append(compare_rs)

        logger.error(f"=======================目标租户{target_code}比对完成！=======================")
        return target_compare_rs

    def _compare_dashboard(self, dashboard_id, source_dashboard, target_dashboard_list):
        # 获取基准租户的主报告信息
        dashboard_info = source_dashboard.get("dashboard", {}).get(dashboard_id, {}).get("dashboard") or {}
        # 某个报告的明细结果
        compare_rs = {
            "dashboard_id": dashboard_id,
            "dashboard_name": dashboard_info.get("name", ""),
            "dashboard": False,
            "chart": {
                'status': False,
                'data': []
            },
            "dataset": {
                'status': False,
                'data': []
            },
            "msg": ""
        }
        if not dashboard_info:
            compare_rs["dashboard"] = True
            compare_rs["msg"] = "基准租户报告不存在"
            return compare_rs
        if not target_dashboard_list:
            compare_rs["dashboard"] = True
            compare_rs["msg"] = "目标租户报告不存在"
            return compare_rs
        if not target_dashboard_list.get(dashboard_id):
            compare_rs["dashboard"] = True
            compare_rs["msg"] = f"目标租户报告[{dashboard_id}]不存在"
            return compare_rs

        target_dashboard = target_dashboard_list.get(dashboard_id)
        # 比对报告
        chart_diff_rs = self._compare_dashboard_data(dashboard_id, source_dashboard, target_dashboard)
        # 比对数据集
        dataset_diff_rs = self._compare_dataset(source_dashboard, target_dashboard)
        # 数据集或组件发生变化，则表示报告发生变化
        if chart_diff_rs.get('status') or dataset_diff_rs.get('status'):
            compare_rs["dashboard"] = True

        compare_rs["chart"] = chart_diff_rs
        compare_rs["dataset"] = dataset_diff_rs

        logger.error(f"=======================报告[{dashboard_info.get('name')}][id:{dashboard_id}]比对结果=======================")
        logger.error(json.dumps(compare_rs, ensure_ascii=False))
        return compare_rs

    def _compare_dashboard_data(self, source_dashboard_id, source_dashboard, target_dashboard):
        chart_rs = {'status': False, 'data': []}
        try:
            source_dashboard_data = source_dashboard.get("dashboard")
            target_dashboard_data = target_dashboard.get("dashboard") or {}
            if not target_dashboard_data:
                raise UserError(message="目标租户指定报告不存在")

            chart_diff_rs = []
            # 遍历某个原始报告的所有报告（包括子报告）
            for dashboard_id, item in source_dashboard_data.items():
                target_item = target_dashboard_data.get(dashboard_id)
                if not target_item:
                    raise UserError(message=f"目标租户指定报告不存在，报告id：{source_dashboard_id}")

                # 报告的比对
                source_dashboard_info = item.get("dashboard")
                target_dashboard_info = target_item.get("dashboard")
                if source_dashboard_info != target_dashboard_info:
                    chart_diff_rs.append(f"报告：[{source_dashboard_info.get('name')}] 报告元数据不一样")

                source_chart = item.get("chart")
                target_chart = target_item.get("chart")
                diff_rs = self._compare_chart(source_dashboard_info, source_chart, target_chart)
                diff_rs and chart_diff_rs.extend(diff_rs)

            if chart_diff_rs:
                chart_rs['status'] = True
                chart_rs['data'] = chart_diff_rs
        except Exception as e:
            chart_rs['status'] = True
            chart_rs['data'].append(str(e))
        return chart_rs

    def _compare_chart(self, dashboard, chart_rs, target_chart_rs):
        source_chart_s = set(tuple(i.items()) for i in chart_rs.values())
        target_chart_s = set(tuple(i.items()) for i in target_chart_rs.values())
        delete_chart = source_chart_s - target_chart_s
        insert_chart = target_chart_s - source_chart_s
        chart_diff_rs = []
        diff_data = []
        if delete_chart or insert_chart:
            delete_chart and diff_data.extend([dict(d) for d in delete_chart])
            insert_chart and diff_data.extend([dict(d) for d in insert_chart])

            for item in diff_data:
                diff_msg = f"报告：[{dashboard.get('name')}] 组件：[{item.get('name')}] id：[{item.get('id')}]"
                chart_diff_rs.append(diff_msg)
            # 去除重复数据
            chart_diff_rs = list(set(chart_diff_rs))

        return chart_diff_rs

    def _compare_dataset(self, source_dashboard, target_dashboard):
        dataset_rs = {'status': False, 'data': []}
        try:
            target_dataset = target_dashboard.get("dataset") or []
            if not target_dataset:
                raise UserError(message="目标租户报告数据集不存在")

            source_dataset = source_dashboard.get("dataset")
            source_dataset_s = set(tuple(i.items()) for i in source_dataset)

            target_dataset_s = set(tuple(i.items()) for i in target_dataset)
            delete_dataset = source_dataset_s - target_dataset_s
            insert_dataset = target_dataset_s - source_dataset_s
            diff_data = []
            if delete_dataset or insert_dataset:
                delete_dataset and diff_data.extend([dict(d) for d in delete_dataset])
                insert_dataset and diff_data.extend([dict(d) for d in insert_dataset])

            if diff_data:
                dataset_rs['status'] = True
                for item in diff_data:
                    # 同一个组件比对的差异结果数是2条记录，提示信息没有差异md5值，需要去重后展示
                    diff_msg = f"数据集：[{item.get('name')}] id：[{item.get('id')}]"
                    dataset_rs['data'].append(diff_msg)

                # 去除重复数据
                dataset_rs['data'] = list(set(dataset_rs['data']))
        except Exception as e:
            dataset_rs['status'] = True
            dataset_rs['data'].append(str(e))
        return dataset_rs

    def get_dashboard_list_data(self, code):
        """
        按租户获取指定的报告数据（报告，组件，数据集）
        考虑到报告存在子报告，统一存放在主报告dashboard节点中
        示例结构：
        {
            "3a0937b9-f820-dd1e-e805-08e6929f1c42": {
                "dashboard": {
                        "3a0937b9-f820-dd1e-e805-08e6929f1c42": {
                            "dashboard": {
                                "id": "3a0937b9-f820-dd1e-e805-08e6929f1c42",
                                "name": "4.8.8-子报告复制修正",
                                "md5_val": "bdd1c511d4235d10088c2846a99882c5",
                                "data_type": "dashboard"
                            },
                            "chart": {
                                "f2963ab7-a5ec-11ed-94cb-9b029703c6a1": {
                                    "id": "f2963ab7-a5ec-11ed-94cb-9b029703c6a1",
                                    "name": "通用表格-1",
                                    "md5_val": "b10737365d5d6fe3e92eac026d718764",
                                    "data_type": "dashboard"
                                },
                                "f962ccad-a5ec-11ed-94cb-9b029703c6a1": {
                                    "id": "f962ccad-a5ec-11ed-94cb-9b029703c6a1",
                                    "name": "饼图-1",
                                    "md5_val": "f031557a52d6252ea7965995b6bdeb23",
                                    "data_type": "dashboard"
                                }
                            }
                        },
                        "3a0937bb-c5a1-d1d6-feaa-32d108329ce2": {
                            "dashboard": {
                                "id": "3a0937bb-c5a1-d1d6-feaa-32d108329ce2",
                                "name": "子报告一",
                                "md5_val": "728ff20cefb69d20a910a4efa23142a7",
                                "data_type": "dashboard"
                            },
                            "chart": {
                                "d8b38779-ac0d-11ed-87e0-299214f4d3cd": {
                                    "id": "d8b38779-ac0d-11ed-87e0-299214f4d3cd",
                                    "name": "柱形图-1",
                                    "md5_val": "3bec432bad742b85ea366d1b35f4b425",
                                    "data_type": "dashboard"
                                }
                            }
                        },
                },
                "dataset": [
                            {
                                "id": "3a04c596-78e0-933f-8828-4347d76a8859",
                                "name": "s1直连_d1",
                                "md5_val": "9a65c9bc9d7bc5add1198c05a867a160",
                                "data_type": "dataset"
                        }
                ]
            },
            "3a0937b9-f820-dd1e-e805-08e6929f1c41": {
                "dashboard": {},
                "dataset": []
            }
        }

        :param code:
        :return:
        """
        dashboard_list = {}
        try:
            for dashboard_id in self.dashboard_ids:
                data = self.get_dashboard(code, dashboard_id)
                # 报告存在
                if data.get("dashboard") or data.get("dataset"):
                    dashboard_list[dashboard_id] = data
        except Exception as e:
            msg = f"租户：{code} 报告数据获取异常，errs:" + str(e)
            logging.error(msg)
            print(msg)
        return dashboard_list

    def get_dashboard(self, code, dashboard_id):
        """
        获取报告信息
        :param code:
        :param dashboard_id:
        :return:
        """
        dashboard_info = {}
        with get_tenant_db(code) as db:
            root_level_code = db.query_scalar(
                """select level_code from dap_bi_dashboard_released_snapshot_dashboard where id = %(snapshot_id)s limit 1""",
                params={"snapshot_id": dashboard_id}
            )
            dashboard_sql = f"""select * from dap_bi_dashboard_released_snapshot_dashboard
where level_code like "{root_level_code}%" and data_type = 0"""
            all_dashboard = db.query(
                dashboard_sql
            )
            dataset_ids = []
            if all_dashboard:
                for dashboard in all_dashboard:
                    # 转换为对比数据
                    dashboard = self._format_record(dashboard, 'dashboard')
                    dashboard_data = {"dashboard": dashboard, "chart": {}}
                    chart_list = db.query(
                        'select * from dap_bi_dashboard_released_snapshot_chart where snapshot_id = %(snapshot_id)s',
                        params={'snapshot_id': dashboard.get("id")}
                    )
                    if chart_list:
                        for chart in chart_list:
                            # 解析组件引用的数据集
                            self._parase_chart_dataset_id(dataset_ids, chart)

                            chart = self._format_record(chart, 'dashboard')
                            dashboard_data["chart"][chart.get("id")] = chart
                    dashboard_info[dashboard.get("id")] = dashboard_data
            # 报告的数据集获取
            dataset_list = []
            if dataset_ids:
                dataset_ids = list(set(dataset_ids))
                dataset_rs = db.query(
                    'select id,name,content from dap_bi_dataset where id in %(dataset_ids)s',
                    params={'dataset_ids': dataset_ids}
                )
                for dataset in dataset_rs:
                    item = self._format_record(dataset, 'dataset')
                    dataset_list.append(item)

            data = {
                "dashboard": dashboard_info,
                "dataset": dataset_list
            }
        return data

    @staticmethod
    def _parase_chart_dataset_id(dataset_ids, chart):
        """
        获取报告组件引用的数据集
        :param dataset_ids:
        :param chart:
        :return:
        """
        chart.get("source") and dataset_ids.append(chart.get("source"))
        chart.get("dims") and dataset_ids.extend([i.get("dataset_id") for i in json.loads(chart.get("dims"))])
        chart.get("nums") and dataset_ids.extend([i.get("dataset_id") for i in json.loads(chart.get("nums"))])
        chart.get("filters") and dataset_ids.extend([i.get("dataset_id") for i in json.loads(chart.get("filters"))])
        penetrates = chart.get("penetrates")
        if penetrates:
            for item in json.loads(penetrates):
                dataset_ids.append(item.get("source"))
                dataset_ids.extend([i.get("dataset_id") for i in item.get("nums", [])])
                dataset_ids.extend([i.get("dataset_id") for i in item.get("dims", [])])
        component_filter = chart.get("component_filter")
        component_filter and dataset_ids.extend([i.get("dataset_id") for i in json.loads(component_filter)])

        var_relations = chart.get("var_relations")
        if var_relations:
            for item in json.loads(var_relations):
                dataset_ids.append(item.get("dataset_id"))

        chart.get("chart_vars") and dataset_ids.extend(
            [i.get("dataset_id") for i in json.loads(chart.get("chart_vars"))])

    @classmethod
    def _format_record(cls, data, data_type=None):
        """
        格式化报告，组件，数据集每行记录
        1、去除动态字段
        2、计算去除动态字段后记录的md5值，用于比对
        :param data:
        :param data_type:
        :return:
        """
        if not isinstance(data, dict):
            return False
        if data_type != 'dataset':
            cls._replace_assign_field(data)
        # data进行md5值处理
        sort_data = sorted(data.items(), key=lambda x: x[0])
        data_json = json.dumps(sort_data)
        new_data = {
            'id': data.get('id'),
            'name': data.get('name'),
            'md5_val': md5(data_json.encode()).hexdigest(),
            'data_type': data_type
        }
        return new_data

    @staticmethod
    def _replace_assign_field(data):
        if isinstance(data, dict):
            unset_fields = ['increment_id', 'created_on', 'created_by', 'modified_on', 'modified_by', 'level_code']
            for f in unset_fields:
                data.pop(f, None)
