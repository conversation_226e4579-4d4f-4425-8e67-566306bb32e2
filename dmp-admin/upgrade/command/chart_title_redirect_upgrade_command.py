#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
标题跳转数据升级
注：可重复执行

eg.
https://dmp-admin-test.mypaas.com.cn/api/upgrade/run?command_name=chart_title_redirect_upgrade_command&type=include&tenants=test
"""

# ---------------- 标准模块 ----------------
import logging
import json

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
from dmplib.db.mysql_wrapper import SimpleMysql

logger = logging.getLogger(__name__)


class ChartTitleRedirectUpgradeCommand(UpgradeCommand):
    def exec(self):
        if not self.tenant_code:
            return True
        logging.exception(msg="租户 {tenant} 开始升级数据".format(tenant=self.tenant_code))

        # 需要清空meta-history标志位
        self.truncate_meta_history = True

        # 单图记录数量过大的情况，分批处理
        rows = 1000
        try:
            db = get_tenant_db(self.tenant_code)
            db.begin_transaction()

            offset = 0
            while True:
                released_data = self._get_release_dashboard_charts(offset, rows)
                data_for_update = self._parse_and_get_update_data(released_data)
                if not data_for_update:
                    break
                logger.exception(msg="开始执行升级 当前升级位移量: {}".format(offset))
                self._update_data(data_for_update, "dap_bi_dashboard_released_snapshot_chart", db)
                logger.exception(msg="升级数据 {count} 条".format(count=len(data_for_update)))
                offset += rows
                del data_for_update

            db.commit()
        except Exception as e:
            if db:
                db.rollback()
            message = '''Error 租户code:{0} TaskId:{1} 标题跳转数据升级 异常信息{2}'''.format(self.tenant_code, self.task_id, str(e))
            logger.exception(message)
            return False

    def _get_release_dashboard_charts(self, offset, rows) -> list:
        sql = """SELECT `increment_id`, `jump`,`chart_params_jump` FROM `dap_bi_dashboard_released_snapshot_chart`
            where (`jump` is not null and `jump` != '[]') or (`chart_params_jump` is not null and `jump` != '[]') 
            ORDER BY created_on DESC LIMIT %(offset)s, %(rows)s """
        with get_tenant_db(self.tenant_code) as db:
            charts = db.query(sql, {"offset": offset, "rows": rows})
        return charts or []

    def _parse_and_get_update_data(self, data: list):
        data_for_update = []
        for record in data:
            jump = record.get("jump")
            chart_params_jump = record.get("chart_params_jump")
            new_jumps, new_chart_params_jump = None, None

            # jump字段数据升级
            if jump:
                new_jumps = self._parse_and_get_new_jump(jump)
            # chart_params_jump字段数据升级
            if chart_params_jump:
                new_chart_params_jump = self._parse_and_get_new_chart_params_jump(chart_params_jump)
            data_for_update.append(
                {
                    "increment_id": record.get("increment_id"),
                    "jump": new_jumps or jump,
                    "chart_params_jump": new_chart_params_jump or chart_params_jump,
                }
            )
        return data_for_update

    def _parse_and_get_new_jump(self, orig_data):
        new_jumps = []
        try:
            loaded_data = json.loads(orig_data)
        except Exception as e:
            logger.exception(msg="解析单图配置失败，错误信息 {msg}".format(msg=str(e)))
            return orig_data
        for item in loaded_data:
            if "dataset_field_id" in item.keys():
                item["source_id"] = item.get("dataset_field_id")
                item.pop('dataset_field_id')
            if "dataset_field_type" in item.keys():
                item["source_type"] = item.get("dataset_field_type")
                item.pop("dataset_field_type")
            new_jumps.append(item)
        return json.dumps(new_jumps)

    def _parse_and_get_new_chart_params_jump(self, orig_data):
        new_chart_params_jump = str(orig_data).replace('chart_dataset_field_id', 'source_id')
        return new_chart_params_jump

    def _update_data(self, data_for_update: list, table_name: str, db: SimpleMysql):
        for chart in data_for_update:
            try:
                db.update(
                    table_name,
                    {"jump": chart.get("jump"), "chart_params_jump": chart.get("chart_params_jump")},
                    {"increment_id": chart["increment_id"]},
                )
                logger.exception(msg="更新单图 {chart_id} 数据成功".format(chart_id=chart.get('id')))
            except Exception as e:
                logger.exception(msg="更新单图 {chart_id} 数据失败，错误信息 {msg} ".format(chart_id=chart.get('id'), msg=str(e)))
