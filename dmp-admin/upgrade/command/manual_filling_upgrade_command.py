#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/8/12 20:00
# <AUTHOR> songh02
import logging

from upgrade.command.command import UpgradeCommand
from dmplib.saas.project import get_db as get_tenant_db
import time

logger = logging.getLogger(__name__)

# 共享表
SHARE_TABLE = "{}_share"

# 暂存表
TMP_TABLE = "{}_tmp"


class ManualFillingUpgradeCommand(UpgradeCommand):
    def exec(self):
        logging.exception("++++----------------------------------------------------------------------++++")
        logging.exception("租户 {tenant} 开始更新数据".format(tenant=self.tenant_code))
        self._update_filling_table()
        logging.exception("租户 {tenant} 结束更新数据".format(tenant=self.tenant_code))
        logging.exception("++++----------------------------------------------------------------------++++")

    def _update_filling_table(self):
        with get_tenant_db(self.tenant_code) as db:
            # 查询直连api数据集存在的flow记录
            sql = """
            select * from dap_bi_filling_template where status!=0
            """
            filling_template_list = db.query(sql)
            for filling_template in filling_template_list:
                template_columns = db.query("select * from dap_bi_filling_template_column where template_id=%(template_id)s",
                                   params={"template_id": filling_template.get("id")})
                result_table = filling_template.get('table_name')
                share_table = SHARE_TABLE.format(result_table)
                tmp_table = TMP_TABLE.format(result_table)
                unique = filling_template.get("unique_columns", "")
                if not unique:
                    continue
                unique = ",".join([f"`{i}`" for i in unique.split(",")])
                template_columns.insert(1, {'column_name': 'batch_id', 'data_type': 'char(36)'})
                template_columns.insert(2, {'column_name': 'batch_name', 'data_type': 'varchar(64)'})
                template_columns.insert(3, {'column_name': 'batch_update_time', 'data_type': 'datetime'})
                template_columns.insert(4, {'column_name': 'cur_fill_user_id', 'data_type': 'char(36)'})
                primary_key = f"id_{int(time.time())} INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT"
                unique_key = f"UNIQUE KEY `unique_verify` (`batch_id`,{unique})"
                field_sql = ",".join(
                    [f"`{column.get('column_name')}`" + " " + column.get('data_type') + " null" for column in
                     template_columns])
                create_temp_table_sql = f"""
                create table if not exists {tmp_table}(
                {primary_key},
                {field_sql},
                {unique_key}
                ) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
                """
                try:
                    with get_tenant_db(self.tenant_code, "data") as data_db:
                        data_db.exec_sql(create_temp_table_sql)
                        data_db.exec_sql(f"alter table {share_table} add column cur_fill_user_id char(36) not null default ''")
                        data_db.exec_sql(f"alter table {result_table} add column cur_fill_user_id char(36) not null default ''")
                except Exception as e:
                    print(e)


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()

    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    _app_ctx_stack.push(g)
