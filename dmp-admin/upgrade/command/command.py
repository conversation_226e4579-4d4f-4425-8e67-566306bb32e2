#!/usr/local/bin python3
# -*- coding: utf-8 -*-
import logging
import traceback

from base import repository
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from base.errors import InvalidParamError, InvalidCallError
from abc import ABCMeta, abstractmethod
from upgrade.errors import UpgrageError, SameJobRunError
from upgrade.models import UpgradeLogModel
from dmplib.saas.project import get_db


class CommandGenerator:
    """
    命令生成器
    """

    def __init__(self, command_name: str):
        super(CommandGenerator, self).__init__()
        self._command_name = command_name

    def get_command_name(self):
        return self._command_name

    def command_is_exist(self, command_name: str, command_class) -> bool:
        """
        命令是否存在
        :param command_name:
        :return:
        """
        if not command_name.isidentifier():
            return False
        try:
            return isinstance(command_class(), Command)
        except Exception:
            return False

    def create_command(self, **kwargs):
        """
        创建命令对象
        :param kwargs:
        :return:
        """
        try:
            command_class_name = self._underline_to_camel(self._command_name)
            module = __import__("upgrade.command." + self._command_name, fromlist=[command_class_name])
            command_class = getattr(module, command_class_name)
            if not self.command_is_exist(self._command_name, command_class):
                logging.error(msg="升级命令不存在")
                raise InvalidParamError(message="升级命令不存在")
            return command_class(**kwargs)
        except Exception as e:
            _msg = "升级命令名称有问题，请检查:{msg}".format(msg=str(e))
            logging.error(msg=_msg)
            raise InvalidParamError(message=_msg)

    def _camel_to_underline(self, camel_format):
        """
        驼峰命名格式转下划线命名格式
        :param camel_format:
        :return:
        """
        underline_format = ""
        if isinstance(camel_format, str):
            for _s_ in camel_format:
                underline_format += _s_ if _s_.islower() else "_" + _s_.lower()
        return underline_format

    def _underline_to_camel(self, underline_format):
        """
        下划线命名格式驼峰命名格式
        :param underline_format:
        :return:
        """
        camel_format = ""
        if isinstance(underline_format, str):
            for _s_ in underline_format.split("_"):
                camel_format += _s_.capitalize()
        return camel_format


class Command:
    """
    命令抽象类
    """

    __metaclass__ = ABCMeta

    def __init__(self, **kwargs):
        self.task_id = kwargs.get("task_id")
        self.command_name = kwargs.get("command_name")
        self.tenant_code = kwargs.get("tenant_code")
        self.status = None
        self.extra =  kwargs.get("extra")
        self.project_codes = kwargs.get("project_codes", "")
        self.cache_class = kwargs.get("cache_class", "dashboard")
        self.cache_prefix = kwargs.get("cache_prefix", "")
        self.truncate_meta_history = False

    def start(self, **kwargs):
        pass

    @abstractmethod
    def exec(self):
        """
        所有子类继承该类并重写该方法
        :return:
        """
        raise InvalidCallError(message="请在子类中覆盖exec方法！")

    def mark_failed(self, msg, task_id=None):
        """
        标记任务失败
        :return:
        """
        pass

    def mark_success(self, task_id=None):
        """"
        标记任务成功
        """
        pass

    def mark_start(self):
        """
        标记开始
        :return:
        """
        pass

    def mark_stop(self):
        """
        标记终止
        :return:
        """
        pass


class UpgradeCommand(Command):
    """
    数据升级命令父类
    """

    def __init__(self, **kwargs):
        super(UpgradeCommand, self).__init__(**kwargs)
        self.upgrade_model = UpgradeLogModel()

    def mark_failed(self, error_msg, log_id=None):
        try:
            repository.update_data(
                "dap_bi_upgrade_log",
                {"status": self.upgrade_model.fail_status, "execute_msg": str(error_msg)},
                {"id": log_id if log_id else self.upgrade_model.id},
            )
        except BaseException as e:
            raise UpgrageError(message=self._error_msg("数据升级日志更新为失败状态时出错", str(e)))

    def mark_success(self, log_id=None):
        try:
            repository.update_data(
                "dap_bi_upgrade_log",
                {"status": self.upgrade_model.success_status},
                {"id": log_id if log_id else self.upgrade_model.id},
            )
        except BaseException as e:
            raise UpgrageError(message=self._error_msg("数据升级日志更新为成功状态时出错", str(e)))

    def record_log(self):
        """
        记录日志
        :param kwargs:
        :return:
        """
        try:
            self.upgrade_model.task_id = self.task_id
            self.upgrade_model.tenant_code = self.tenant_code
            self.upgrade_model.command_name = self.command_name
            self.upgrade_model.status = self.upgrade_model.running_status
            self.upgrade_model.execute_msg = ""
            self.upgrade_model.id = seq_id()
            self.upgrade_model.validate()
            fields = ["id", "task_id", "tenant_code", "command_name", "status", "error_msg"]
            repository.add_model("dap_bi_upgrade_log", self.upgrade_model, fields)
        except BaseException as e:
            raise UpgrageError(message=self._error_msg("数据升级日志记录异常", str(e)))

    def start(self):
        """
        任务执行入口
        :return:
        """
        try:
            # 0. 检查当前任务是否有正在进行，若有，则不允许同时执行
            logging.exception(f"租户 {self.tenant_code} 判断命令 {self.command_name} 是否执行")
            if not self.is_allow_multi_same_job() and self.check_running_same_job(self.task_id):
                logging.exception(f"租户 {self.tenant_code} 命令 {self.command_name} 已经在执行，退出")
                raise SameJobRunError(message="禁止同时运行相同任务，任务task_id：" + self.task_id)
            logging.exception(f"租户 {self.tenant_code} 命令 {self.command_name} 未执行，执行下一步")
            # 1. 记录日志
            logging.exception(f"租户 {self.tenant_code} 开始执行命令 {self.command_name}")
            logging.exception(f"租户 {self.tenant_code} 开始开始记录日志")
            self.record_log()
            logging.exception(f"租户 {self.tenant_code} 结束开始记录日志")
            # 2. 执行任务
            logging.exception(f"租户 {self.tenant_code} 开始执行程序")
            self.exec()
            logging.exception(f"租户 {self.tenant_code} 结束执行程序")
            # 3. 清空meta—history
            if self.truncate_meta_history:
                self.truncate_dashboard_metadata_history()
            # 4. 记录任务执行完毕并成功
            logging.exception(f"租户 {self.tenant_code} 开始标记执行结果")
            self.mark_success()
            logging.exception(f"租户 {self.tenant_code} 结束标记执行结果")
            logging.exception(f"租户 {self.tenant_code} 结束执行命令 {self.command_name}")
        except BaseException as e:
            # 5. 记录任务执行失败
            logging.error(e)
            logging.error('traceback.format_exc():\n%s' % traceback.format_exc())
            print('traceback.format_exc():\n%s' % traceback.format_exc())
            self.mark_failed(e)

    def check_running_same_job(self, task_id):
        """
        检查是否有同时运行的相同任务
        :param str task_id: 任务id
        :return:
        """
        job = repository.get_data(
            "dap_bi_upgrade_log",
            {
                "command_name": self.command_name,
                "tenant_code": self.tenant_code,
                "status": self.upgrade_model.running_status,
            },
            [],
            multi_row=True,
        )
        if job:
            return True
        return False

    def is_allow_multi_same_job(self):
        """
        钩子：是否允许同时多个相同任务。默认不允许。若允许，则子类实现其方法并返回True
        :return:
        """
        return False

    def exec(self):
        """
        所有子类继承该类并重写该方法,
        使用方式：新开文件，名称为下划线分开，类名为去除下划线后首字母大写类名，然后在新开类中的exec方法中编写自己业务即可
        :return:
        """
        pass

    def _error_msg(self, msg, err_str):
        """
        组装错误信息
        :param str msg: 自定义错误信息部分
        :param str err_str: 错误内容
        :return:
        """
        return msg + "，task_id:{task_id},命令名称：{command_name},租户：{tenant_code},错误信息：{err}".format(
            task_id=str(self.task_id), command_name=self.command_name, tenant_code=self.tenant_code, err=err_str
        )

    def truncate_dashboard_metadata_history(self):
        """
        清空`dashboard_metadata_history`元数据编辑历史记录
        :return:
        """
        sql = "TRUNCATE `dap_bi_dashboard_metadata_history`"
        with get_db(self.tenant_code) as db:
            return db.query(sql)
