import logging
import json
from collections import defaultdict

# ---------------- 业务模块 ----------------
from upgrade.command.command import UpgradeCommand
from base.db_wrap import DbWrap

from dmplib.utils.strings import seq_id

logger = logging.getLogger(__name__)


class DateRangeToNoSource(UpgradeCommand):
    """升级时间筛选组件"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.db = DbWrap(self.tenant_code)
        self.chart_id2fix_id = defaultdict(dict)

    def exec(self):
        """升级入口"""
        logger.error("start %s", self.tenant_code)
        # 先处理预态的元数据
        chart_ids = self.get_chart_list()
        if chart_ids:
            self.clear_dashboard_chart(chart_ids)
            self.clear_dashboard_chart_dim(chart_ids)
            self.deal_dashboard_filter_chart(chart_ids)
            self.deal_dashboard_filter_chart_default_values(chart_ids)
        # 处理发布后的数据
        self.exec_publish()

    def get_chart_list(self):
        """单图列表"""
        chart_list = self.db.get_list("dap_bi_dashboard_chart", {"chart_code": "date_interval_filter"})
        if chart_list:
            return [i['id'] for i in chart_list]
        return chart_list

    def clear_dashboard_chart(self, chart_ids):
        """单图source字段设置为空"""
        self.db.update("dap_bi_dashboard_chart", {"source": None, "data_logic_type_code": "nondataset"}, {"id": chart_ids})

    def clear_dashboard_chart_dim(self, chart_ids):
        """删除dim"""
        if chart_ids:
            logger.error("delete dap_bi_dashboard_chart_dim  %s", chart_ids)
            self.db.delete_data("dap_bi_dashboard_chart_dim", {"dashboard_chart_id": chart_ids})

    def deal_dashboard_filter_chart(self, chart_ids):
        """处理过滤关系"""
        logger.error("update dap_bi_dashboard_filter_chart  %s", chart_ids)
        for chart_id in chart_ids:
            dashboard_filter_chart_list = self.db.get_list("dap_bi_dashboard_filter_chart", {"chart_id": chart_id})
            if not dashboard_filter_chart_list:
                continue
            for dashboard_filter_chart_item in dashboard_filter_chart_list:
                new_id = seq_id()
                self.db.db.insert("dap_bi_dashboard_filter_chart_fixed_value", {
                    "id": new_id,
                    "chart_id": chart_id,
                    "value_type": "datetime",
                    "identifier": "date_range",
                    "name": "时间区间",
                })
                new_data = {"initiator_source": "fixed_value", "dataset_field_id": new_id}
                self.db.update("dap_bi_dashboard_filter_chart", new_data, {"id": dashboard_filter_chart_item['id']})
                self.chart_id2fix_id[chart_id][dashboard_filter_chart_item['dataset_field_id']] = new_id

    def deal_dashboard_filter_chart_default_values(self, chart_ids):
        """默认值"""
        self.db.update("dap_bi_dashboard_filter_chart_default_values",
                       {"dataset_field_id": None, "dataset_id": None},
                       {"chart_id": chart_ids})

    def deal_dashboard_dataset_vars_relation(self, chart_ids):
        self.db.update("dap_bi_dashboard_dataset_vars_relation", {"field_initiator_id": None},
                       {"chart_initiator_id": chart_ids})

    def exec_publish(self):
        """处理发布后的数据"""
        chart_list = self.get_chart_list_publish()
        if not chart_list:
            return
        for chart in chart_list:
            new_data = {
                # 清除dim source
                "dims": "[]",
                "source": "",
                "data_logic_type_code": "nondataset",
            }
            new_data.update(self.deal_chart_filter(chart))
            new_data.update(self.deal_var_relations(chart))
            self.db.update("dap_bi_dashboard_released_snapshot_chart", new_data, {"increment_id": chart["increment_id"]})

    def get_chart_list_publish(self):
        """发布后的单图列表"""
        return self.db.get_list("dap_bi_dashboard_released_snapshot_chart", {"chart_code": "date_interval_filter"})

    def deal_chart_filter(self, chart):
        """处理过滤关系"""

        data = chart.get("chart_filter")
        if not data:
            return {}
        try:
            data = json.loads(data)
            for item in data:
                new_id = self.chart_id2fix_id[chart['id']].get(item["field_initiator_id"]) or seq_id()
                item["dataset_id"] = None
                item["field_initiator_id"] = new_id
                item["available"] = 1
                item["initiator_source"] = "fixed_value"
                item["fixed_value_data"] = {
                    "id": new_id,
                    "chart_id": chart["id"],
                    "name": "时间区间",
                    "value_type": "datetime",
                    "identifier": "date_range",
                    "extra_data": ""
                }
            return {"chart_filter": json.dumps(data)}
        except Exception as e:
            logger.error("deal_chart_filter err %s", e)
            return {}

    @staticmethod
    def deal_var_relations(chart):
        if not chart["var_relations"]:
            return {}
        try:
            data = json.loads(chart['var_relations'])
            for item in data:
                item["field_initiator_id"] = None
            return {"var_relations": json.dumps(data)}
        except Exception as e:
            logger.error("deal_var_relations err %s", e)
            return {}
