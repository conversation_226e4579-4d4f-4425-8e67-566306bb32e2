#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/8/30
"""
from base.errors import InvalidParamError
from upgrade.command.command import CommandGenerator
from upgrade.command.executor import IncludeExecutor, ExcludeExecutor, AllExecutor
from upgrade.repositories import upgrade


def start_upgrade(**kwargs):
    """
    异步执行数据升级
    :param str task_id: 任务id
    :param str command_name: 执行命令类名
    :param str upgrade_type: 执行命令的类型（全量租户执行、包含租户执行、不包含租户执行）
    :param list tenants: 租户code数据
    :param dict extra: 附加升级信息，json输入
    :return:
    """
    task_id = kwargs.get("task_id")
    command_name = kwargs.get("command_name")
    upgrade_type = kwargs.get("upgrade_type")
    tenants = kwargs.get("tenants")
    extra = kwargs.get("extra")

    if not command_name:
        return False, "请指定升级命令"
    command_generator = CommandGenerator(command_name)
    if upgrade_type == "include":
        if not tenants:
            return False, "请指定需要升级的租户代码！"
        executor = IncludeExecutor(command_generator, tenants)
    elif upgrade_type == "exclude":
        # 排除
        if not tenants:
            return False, "请指定不需要升级的租户代码！"
        executor = ExcludeExecutor(command_generator, tenants)
    else:
        # 全量
        executor = AllExecutor(command_generator)
    data = {
        "task_id": task_id,
        "command_name": command_name,
        "upgrade_type": upgrade_type,
        "tenants": tenants,
        "extra": extra,
        "project_codes": kwargs.get("project_codes"),
        "cache_class": kwargs.get("cache_class"),
        "cache_prefix": kwargs.get("cache_prefix"),
    }
    executor.run(**data)
    return True, "已经成功执行命令！"


def get_upgrade_logs(command_name, tenant_code):
    return upgrade.get_upgrade_logs(command_name, tenant_code)


def convert_to_list_param(param):
    """
    将url传入的','参数转换为list
    :param param:
    :return:
    """
    if isinstance(param, str):
        param = param.strip(' ')
        if param == '':
            return []
        return param.split(',')
    if isinstance(param, list):
        return (','.join(param)).split(',')
    if isinstance(param, tuple):
        return (','.join(param)).split(',')
    raise InvalidParamError(message='非法的参数！')
