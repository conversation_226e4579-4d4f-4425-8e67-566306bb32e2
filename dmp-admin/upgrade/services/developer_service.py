#!/usr/local/bin python3
# -*- coding: utf-8 -*-

from base.db_wrap import DbWrap
from base import repository
from loguru import logger
import app_celery
from project.services.project_service import clear_project_cache
from dmplib.utils.strings import seq_id


def enable_developer(tenant_code_list: list):
    error_tenant_list = []
    sql = '''
            select t1.code,t1.name,t2.admin_email from dap_p_tenant t1 join dap_bi_tenant_setting t2 on t1.code = t2.code where t1.code in %(code)s
        '''
    tenant_list = repository.get_data_by_sql(sql, params={'code': tenant_code_list})
    for tenant_info in tenant_list:
        try:
            app_celery.send_admin_email.delay(**tenant_info)
        except Exception as e:
            error_tenant_list.append(tenant_info.get('code'))
            logger.error(str(e))
        DbWrap().update('dap_bi_tenant_setting', {'is_rdc_auth': 1}, {'code': tenant_info.get('code')})
        clear_project_cache(tenant_info.get('code'))
    return error_tenant_list


def enable_all_developer():
    error_tenant_list = []
    sql = '''
        select t1.code,t1.name,t2.admin_email from dap_p_tenant t1 join dap_bi_tenant_setting t2 on t1.code = t2.code where t2.is_rdc_auth = 0
    '''
    tenant_list = repository.get_data_by_sql(sql, params={})
    DbWrap().update('dap_bi_tenant_setting', {'is_rdc_auth': 1}, {})
    for tenant_info in tenant_list:
        try:
            app_celery.send_admin_email.delay(**tenant_info)
            clear_project_cache(tenant_info.get('code'))
        except Exception as e:
            error_tenant_list.append(tenant_info.get('code'))
            logger.error(str(e))
    return error_tenant_list


def add_printing_report(tenant_code_list: list):
    for tenant_code in tenant_code_list:
        try:
            func_id = seq_id()
            DbWrap().delete_data('dap_bi_project_value_added_func', {'project_code': tenant_code, 'func_code': 'printing-report'})
            repository.add_data('dap_bi_project_value_added_func', {'id': func_id, 'project_code': tenant_code, 'func_code': 'printing-report'})
            clear_project_cache(tenant_code)
        except Exception as e:
            logger.error(str(e))
            continue
    return True
