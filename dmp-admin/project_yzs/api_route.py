#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from dmplib.hug import APIWrapper
from project_yzs.models import ProjectYzsConfigModel, ProjectYzsConfigQueryModel
from project_yzs.services import project_yzs_service

api = APIWrapper(__name__)


@api.admin_route.get('/config_list')
def get_project_yzs_config_list(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/project_yzs/config_list
     @apiGroup  openapi
     @apiBodyParam {
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": [{
                     "name{名称}": "name",
                     "code":"code",
                     "tenant_id":"tenant_id",
                     "id":"id"
     }]
     */
     """
    return True, None, project_yzs_service.get_project_yzs_config_list(
        ProjectYzsConfigQueryModel(**kwargs)).get_result_dict()


@api.admin_route.get('/get_config_by_id')
def get_project_app(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/project_yzs/get_config_by_id
     @apiGroup  openapi
     @apiBodyParam {
        "id": "id"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": {
                     "name{名称}": "name",
                     "code":"code",
                     "tenant_id":"tenant_id",
                     "id":"id"
     }
     */
     """
    return True, None, project_yzs_service.get_project_yzs_config_by_id(kwargs.get('id'))


@api.admin_route.post('/add_config')
def add_project_yzs_config(request, response, **kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/project_yzs/add_config
     @apiGroup  openapi
     @apiBodyParam {
                     "name{名称}": "name",
                     "code":"code",
                     "tenant_id":"tenant_id",
                     "id":"id"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "id"
     }
     */
    """
    from user.models import UserLoginModel
    user_login = UserLoginModel(**kwargs)
    user_login.request = request
    user_login.response = response
    project_yzs = ProjectYzsConfigModel(**kwargs)
    return True, '添加成功', project_yzs_service.add_project_yzs_config(project_yzs, user_login)


@api.admin_route.post('/delete_config')
def delete_project_yzs_config(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/project_yzs/delete_config 删除
     @apiGroup  openapi
     @apiBodyParam {
                     "id": "id",
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "id"
     }
     */
     """
    return True, '删除成功', project_yzs_service.delete_project_yzs_config(kwargs.get('id'))
