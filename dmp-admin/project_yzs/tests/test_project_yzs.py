#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import unittest
from project_yzs.models import ProjectYzsConfigModel, ProjectYzsConfigQueryModel
from project_yzs.services import project_yzs_service
import logging

from tests.base import BaseTest

logger = logging.getLogger(__name__)


class TestProjectYzs(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev', account='admin')

    def test_add_project_yzs_config(self):
        for i in range(1, 10000):
            dict = {"name": "yudechao_name", "code": f"yudechao_code_{i}", "tenant_id": f"yudechao_tenant_id_{i}"}
            model = ProjectYzsConfigModel(**dict)
            reuslt = project_yzs_service.add_project_yzs_config(model)
            print(reuslt)

    def test_update_project_yzs_config(self):
        dict = {"name": "yudechao_name", "code": "xxxxxx", "tenant_id": "yyyyyyy1",
                'id': '39f972ee-296b-9e5e-6121-327f31f12c1d'}
        model = ProjectYzsConfigModel(**dict)
        reuslt = project_yzs_service.add_project_yzs_config(model)
        print(reuslt)

    def test_delete_project_yzs_config(self):
        result = project_yzs_service.delete_project_yzs_config('39f972e9-de00-0984-d4c5-04bef3142c98')
        print(result)

    def test_get_project_yzs_config_list(self):
        dict = {'keyword': 'yudechao_code_2', 'page': 3, 'page_size': 30}
        result = project_yzs_service.get_project_yzs_config_list(ProjectYzsConfigQueryModel(**dict))
        print(result)

    def test_get_project_yzs_config_by_id(self):
        result = project_yzs_service.get_project_yzs_config_by_id('39f976ea-7aa9-5f28-a1ce-0d032c15a879')
        print(result)


if __name__ == '__main__':
    unittest.main()
