#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from base import repository
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from project_yzs.repositories import project_yzs_config_repository


def get_project_yzs_config_list(query_model):
    """
    分页获取云助手集成配置信息
    :param query_model:
    :return:
    """
    return project_yzs_config_repository.get_project_yzs_config_list(query_model)


def add_project_yzs_config(project_yzs, user_login):
    """
    新增，修改云助手集成配置信息
    :param project_yzs.models.ProjectYzsConfigModel project_yzs:
    :param user_login:
    :return: model.id
    """
    from user.services import user_service
    user_service.login(user_login, check=False)

    # 新增，修改云助手集成配置信息
    project_yzs.id = project_yzs.id if project_yzs.id else seq_id()
    project_yzs.validate()
    exist_project_yzs_config = repository.get_data("dap_bi_project_yzs_config", {'id': project_yzs.id}, ['id'])
    same_code_yzs_config = repository.get_data("dap_bi_project_yzs_config", {'code': project_yzs.code}, ['id'])
    if project_yzs.tenant_id:
        same_tenant_id_yzs_config = repository.get_data("dap_bi_project_yzs_config", {'tenant_id': project_yzs.tenant_id}, ['id'])
    else:
        same_tenant_id_yzs_config = {}
    if exist_project_yzs_config:  # 修改
        if (same_code_yzs_config and same_code_yzs_config.get('id') != project_yzs.id) or (
                same_tenant_id_yzs_config and same_tenant_id_yzs_config.get('id') != project_yzs.id):
            raise UserError(message='云助手集成配置已存在，不允许修改')
        repository.update_model("dap_bi_project_yzs_config", project_yzs, {'id': project_yzs.id})
    else:  # 新增
        if same_code_yzs_config or same_tenant_id_yzs_config:
            raise UserError(message='云助手集成配置已存在，不允许添加')
        repository.add_model("dap_bi_project_yzs_config", project_yzs)
    return project_yzs.id


def delete_project_yzs_config(id):
    """
    删除云助手集成配置信息
    :param model:
    :return:
    """
    if not id or not repository.data_is_exists('dap_bi_project_yzs_config', {'id': id}):
        raise UserError(message='缺少云助手集成配置ID')
    repository.delete_data('dap_bi_project_yzs_config', {'id': id})
    return id


def get_project_yzs_config_by_id(id):
    if not id:
        raise UserError(message='缺少id字段信息')
    return project_yzs_config_repository.get_project_yzs_config_by_id(id)
