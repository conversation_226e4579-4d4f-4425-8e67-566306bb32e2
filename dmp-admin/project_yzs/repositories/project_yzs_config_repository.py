#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from dmplib.db.mysql_wrapper import get_db


def get_project_yzs_config_list(query_model):
    """
    按条件获取渠道用户，分页查询
    :param user.models.UserQueryModel query_model:
    :return user.models.UserQueryModel:
    """
    query_conditions = []
    params = {}
    if query_model.keyword:
        query_conditions.append(' p.`name` LIKE %(keyword)s OR pyc.`code` LIKE %(keyword)s ')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    sql = 'select pyc.`id`,p.`name`,pyc.`code`,pyc.`tenant_id`,pyc.`customer_id`,pyc.`secret`,pyc.enable_hd_report,pyc.enable_yzs_message,pyc.`created_on`,pyc.`created_by`,pyc.`modified_on`,pyc.`modified_by` ,pyc.hd_secret  from dap_bi_project_yzs_config pyc join dap_p_tenant p on p.code=pyc.code '
    sql_total = 'select count(pyc.id) from dap_bi_project_yzs_config pyc join dap_p_tenant p on p.code=pyc.code '
    if len(query_conditions) > 0:
        sql = ' '.join([sql, ' where ', ' and '.join(query_conditions)])
        sql_total = ' '.join([sql_total, 'where', ' and '.join(query_conditions)])
    else:
        params = None
    sql += ' order by pyc.created_on desc '
    sql += ' limit ' + str(query_model.skip) + ',' + str(query_model.page_size)
    with get_db() as db:
        query_model.items = db.query(sql, params)
        query_model.total = db.query_scalar(sql_total, params)
    return query_model


def get_project_yzs_config_by_id(id):
    sql = "select pyc.`id`,p.`name`,pyc.`code`,pyc.`tenant_id`,pyc.`customer_id`,pyc.`secret` ,pyc.enable_hd_report,pyc.enable_yzs_message,pyc.hd_secret" \
          " from dap_bi_project_yzs_config pyc " \
          " join dap_p_tenant p on p.code=pyc.code where pyc.id='{}'".format(id)
    with get_db() as db:
        return db.query_one(sql)
