#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    使用DES对称加密算法的CBC模式加密，解密
    <AUTHOR>
    @time 2021-3-22 17:52:25
"""
import json

import requests
from Crypto.Cipher import DES
from Crypto.Util.Padding import pad
from Crypto.Util.Padding import unpad
from binascii import b2a_hex, a2b_hex

phase = 'aOPCXImQ'


def encrypt(data: str, phase):
    # 使用DES对称加密算法的CBC模式加密
    key = iv = phase
    cipher = DES.new(key=key.encode(), mode=DES.MODE_CBC, iv=iv.encode())
    v = pad(data.encode(), DES.block_size)
    v = cipher.encrypt(v)
    return b2a_hex(v).decode('utf-8').upper()


def decrypt(data: str, phase):
    # 使用DES对称加密算法的CBC模式解密
    key = iv = phase
    cipher = DES.new(key=key.encode(), mode=DES.MODE_CBC, iv=iv.encode())
    bv = a2b_hex(data.lower())
    return unpad(cipher.decrypt(bv), DES.block_size).decode('utf-8')


if __name__ == '__main__':
    real_body = {'sql': 'select id from dap_bi_dashboard limit 10 ', 'code': 'luyy'}
    real_body = {'sql': 'select code from dap_p_tenant limit 10', 'is_config_db': 1}

    encrypted_body = encrypt(json.dumps(real_body), phase)
    body = {'data': encrypted_body}
    print('request body::', body)

    url = 'http://localhost:8001/internalapi/query'
    response = requests.post(url, data=body)

    print('response status_code::', response.status_code)

    try:
        print('response decrypt::', decrypt(response.text, phase))
    except:
        print('response text::', response.text)
