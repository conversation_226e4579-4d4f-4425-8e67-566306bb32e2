#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
报告相关配置表对应的ReplaceKeyModel类，用于记录每个表需要做id替换的字段名称
"""

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from dmplib.utils.model import BaseModel
from issue_dashboard.replace_key_models.base_model import ReplaceKeyBaseModel


class DashboardReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard
    """
    def _get_keys(self):
        self.dashboard_id_keys = ["id"]
        self.biz_code_keys = ["biz_code"]


class DashboardChartReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart
    """
    def _get_keys(self):
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["id", "parent_id"]


class DashboardChartDimReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_dim
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartNumReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_num
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartFilterReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_filter
    """
    def _get_keys(self):
        self.dashboard_chart_filter_id_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartFilterRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_filter_relation
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_chart_filter_id_keys = ["dashboard_chart_filter_id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartLayersReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_layer
    """
    def _get_keys(self):
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartMarklineReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_markline
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartDesireReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_desire
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartParamsReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_params
    """
    def _get_keys(self):
        self.primary_keys = ["param_id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartParamsJumpReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_params_jump
    """
    def _get_keys(self):
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]
        self.dashboard_filter_id_keys = ["dashboard_filter_id"]


class DashboardChartComparisonReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_comparison
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartPenetrateRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_penetrate_relation
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartFieldSortReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_field_sort
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardChartSelectorReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_selector
    """
    def _get_keys(self):
        self.selector_id_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["chart_initiator_id", "chart_responder_id"]


class DashboardChartSelectorFieldReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_chart_selector_field
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.selector_id_keys = ["selector_id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["chart_id"]


class DashboardFilterReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_filter
    """
    def _get_keys(self):
        self.dashboard_filter_id_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]


class DashboardFilterRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_filter_relation
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_filter_id_keys = ["dashboard_filter_id"]


class DashboardDatasetFieldRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_dataset_field_relation
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]


class ScreenDashboardReplaceKeyModel(ReplaceKeyBaseModel):

    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id", "screen_id"]


class DashboardFilterChartReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_filter_chart
    """
    def _get_keys(self):
        self.dashboard_filter_chart_id_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["chart_id"]


class DashboardFilterChartRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_filter_chart_relation
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_filter_chart_id_keys = ["filter_id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["chart_responder_id"]


class DashboardLinkageReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_linkage
    """
    def _get_keys(self):
        self.linkage_id_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["chart_id"]


class DashboardLinkageRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_linkage_relation
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.linkage_id_keys = ["link_id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["chart_responder_id"]


class DashboardFilterChartDefaultValuesReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_filter_chart_default_values
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["chart_id"]


class DashboardExtraReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_extra
    """
    def _get_keys(self):
        self.dashboard_id_keys = ["dashboard_id"]


class DashboardJumpConfigReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_jump_config
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id", "target"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]


class DashboardJumpRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_jump_relation
    """
    def _get_keys(self):
        self.primary_keys = ["jump_config_id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]
        self.dashboard_filter_id_keys = ["dashboard_filter_id"]


class DashboardVarsJumpRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_vars_jump_relation
    """
    def _get_keys(self):
        self.primary_keys = ["jump_config_id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["dashboard_chart_id"]
        self.dashboard_filter_id_keys = ["dashboard_filter_id"]


class DashboardDatasetVarsRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_dataset_vars_relation
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]
        self.dashboard_chart_id_keys = ["chart_initiator_id"]


class DashboardValueSourceReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_value_source
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["dashboard_id"]


class DashboardVarsValueSourceRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_vars_value_source
    """
    def _get_keys(self):
        self.primary_keys = ["value_source_id"]
        self.dashboard_id_keys = ["dashboard_id"]


class DashboardComponentFilterReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_component_filter
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dashboard_id_keys = ["chart_initiator_id", "chart_responder_id"]


class DashboardComponentFilterFieldRelationReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dashboard_component_filter_field_relation
    """
    def _get_keys(self):
        self.primary_keys = ["id", "filter_id"]
        self.dashboard_chart_id_keys = ["chart_id"]
