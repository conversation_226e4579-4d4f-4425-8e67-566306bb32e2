#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/07/17

"""
报告分发模块之报告模块route代码
"""
import logging

import hug

from dmplib.hug import APIWrapper, g
from dmplib.utils.errors import UserError
from issue_dashboard.models import ProjectModel
from issue_dashboard.services import dashboard_service, deliver_service, withdraw_service
import app_celery
from tenant.services.tenant_service import license_control, check_not_exist_code

api = APIWrapper(__name__)
logger = logging.Logger(__name__)


@api.admin_route.get('/issue_dashboard')
def issue_dashboard(**kwargs):
    """
    分发报告接口
    :param kwargs:
    :return:
    """
    model = ProjectModel(**kwargs)
    query_builder = dashboard_service.DashboardQueryBuilder(model)
    return True, None, query_builder.replace_multi_data(kwargs.get('data'))


@api.admin_route.post('/read_file')
def read_file(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api  {post} /api/issue_dashboard/read_file 解析上传分发文件
    @apiGroup  报告分发
    @apiBodyParam {
        "file_url{文件oss地址}": "http://xxx.zip"
    }
    @apiResponse 200{
        "result": true,
        "msg": "ok",
        "data": {
            "title{标题}": "年度销售大屏",
            "description{描述}": "年度",
            "source_project{项目代码}": "vanke",
            "created_on{创建时间}": "2018-01-02 15:03:04"
        }
    }
    **/
    """
    file_url = kwargs.get('file_url')
    if not file_url:
        raise UserError(400, '无效的文件地址')

    export_data = deliver_service.parse_data(file_url)
    del export_data['dashboards']
    del export_data['datasets']
    return True, 'ok', export_data


@api.admin_route.post('/deliver')
def deliver(**kwargs):
    """
    /**
    @apiVersion 1.0.2
    @api  {post} /api/issue_dashboard/deliver 报告分发
    @apiGroup  报告分发
    @apiBodyParam {
        "projects{选择要分发的项目}": ["vanke"],
        "is_all_projects{是否分发给所有项目}": 0,
        "file_url{文件oss路径}": "http://"
    }
    @apiResponse 200{
        "result": true,
        "msg": "ok"
    }
    **/
    """

    projects = kwargs.get('projects', [])
    file_url = kwargs.get('file_url')
    is_all_projects = kwargs.get('is_all_projects', 0)
    is_all_projects = int(is_all_projects)
    distribute_type = kwargs.get('distribute_type', 0)
    replace_data_source = kwargs.get('replace_source', 0)
    is_lock_dataset = kwargs.get('is_lock_dataset', 0)

    if is_all_projects == 0 and not projects:
        raise UserError(400, '参数错误. 需要is_all_projects=1或者指定projects')

    if is_all_projects:
        projects = []
    logger.error(f'======================={file_url}')
    not_exist_code = check_not_exist_code(projects, is_all_projects)
    if not_exist_code:
        raise UserError(400, f'租户{",".join(not_exist_code)}不存在')

    if not file_url:
        raise UserError(400, 'file_url参数不能为空')

    export_data = deliver_service.parse_data(file_url)
    # 存在dmp报告才校验license
    if export_data.get("dashboards", {}):
        license_control(projects, is_all_projects)

    title = export_data.get('title', '')
    description = export_data.get('description', '')
    export_id = export_data.get('export_id')
    source_project = export_data.get('source_project')
    source_project_id = export_data.get('source_project_id') or ''
    version = export_data.get('version')

    # 处理历史版本问题
    if not version or (version != "v1.0.0.0" and version != "v1.5.0.0"):
        raise UserError(400, '模板文件已过期，请重新导出后再导入')

    if not title:
        raise UserError(400, '缺少title字段')

    if not source_project:
        raise UserError(400, '分发包缺少source_project属性')

    if source_project_id:
        source_project = deliver_service.get_code_by_source_project_id(source_project_id)

    if len(projects) >= 1 and source_project in projects:
        raise UserError(400, '导出项目不能和目标项目相同')

    deliver_service.custom_import_valid_for_yk(import_data=export_data)

    params = {
        "export_id": export_id,
        "title": title,
        "description": description,
        "source_url": file_url,
        "source_project": source_project,
        "dest_projects": projects,
        "is_all_projects": is_all_projects,
        "distribute_type": distribute_type,
        "replace_data_source": replace_data_source,
        "operate_type": 1,
        "is_lock_dataset": is_lock_dataset,
    }
    deliver_id = deliver_service.insert_deliver(**params)
    try:
        args = {'deliver_id': deliver_id, 'distribute_type': distribute_type, 'is_lock_dataset': is_lock_dataset,
                "userid": g.userid, "is_async": True}
        # app_celery.deliver_dashboard.apply_async(kwargs=args)
        deliver_service.deliver_split(**args)


    except BaseException as e:
        logger.exception(e)
        deliver_service.update_deliver_status(deliver_id, '失败', ','.join(projects), '写入队列失败')

    return True, 'ok'


@api.admin_route.get('/deliver/{deliver_id}')
def get_deliver_detail(deliver_id):
    """
    /**
    @apiVersion 1.0.2
    @api  {get} /api/issue_dashboard/deliver/{deliver_id} 分发详情
    @apiGroup  报告分发
    @apiParam  path {string}  deliver_id ID标识
    @apiResponse 200 {
        "result": true,
        "msg": "ok",
        "data": {
            "description": "年度签约总金额",
            "source_url{分发的导出原文件}": "http://src",
            "export_id{导出的原记录id}": "39e7fcc5-cb50-2141-eae1-15fe3768b50f",
            "title": "年度签约",
            "id": "39e7fcc5-cb50-2141-eae1-15fe3768b50f",
            "created_on": "2018-07-30T17:24:38",
            "created_by": "huwl",
            "modified_by": "huwl",
            "modified_on": "2018-07-30T17:24:38",
            "status": "已创建",
            "is_all_projects{是否导出所有}": 0,
            "completed_on{分发任务完成时间}": "2018-07-30T17:24:38"
        }
    }
    **/
    """

    return True, '', deliver_service.get_deliver_detail(deliver_id)


@api.admin_route.post('/redeliver')
def redeliver(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {post} /api/issue_dashboard/redeliver 重新已有报告分发
    @apiGroup  报告分发
    @apiBodyParam {
        "id{重新分发的id(必填)}": "",
        "projects{选择要分发的项目(可空)}": ["vanke"]
    }
    @apiResponse 200{
        "result": true,
        "msg": "ok"
    }
    **/
    """
    deliver_id = kwargs.get('id')
    if not deliver_id:
        raise UserError(400, '无效的deliver_id')

    tmpl_deliver = deliver_service.get_deliver_detail(deliver_id)
    if not tmpl_deliver:
        raise UserError(404, '数据不存在')

    dest_projects = tmpl_deliver.get('dest_projects', '').split(',')

    params = {
        "export_id": tmpl_deliver['export_id'],
        "title": tmpl_deliver['title'],
        "description": tmpl_deliver['description'],
        "source_url": tmpl_deliver['source_url'],
        "source_project": tmpl_deliver['source_project'],
        "dest_projects": dest_projects,
        "is_all_projects": tmpl_deliver['is_all_projects'],
        "distribute_type": tmpl_deliver['distribute_type'],
        "replace_data_source": tmpl_deliver['replace_data_source'],
        "is_lock_dataset": tmpl_deliver['is_lock_dataset'],
        "operate_type": tmpl_deliver['operate_type'],
        "recovery_type": tmpl_deliver['recovery_type']
    }

    new_deliver_id = deliver_service.insert_deliver(**params)

    try:
        if tmpl_deliver['operate_type'] == 1:
            args = {'deliver_id': new_deliver_id, 'distribute_type': tmpl_deliver['distribute_type'],
                    'is_lock_dataset': tmpl_deliver['is_lock_dataset'], "userid": g.userid}
            # app_celery.deliver_dashboard.apply_async(kwargs=args)
            deliver_service.deliver_split(**args)
        else:
            # 参数修改
            args = {'withdraw_id': new_deliver_id}
            app_celery.withdraw_dashboard.apply_async(kwargs=args)
    except BaseException as e:
        logger.exception(e)
        deliver_service.update_deliver_status(new_deliver_id, '失败', tmpl_deliver['dest_projects'], '写入队列失败')
    return True, 'ok'


@api.admin_route.get('/delivers')
def list_deliver(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api  {get} /api/issue_dashboard/delivers 分发记录列表
    @apiGroup  报告分发
    @apiParam  query {string}  keywords  查询关键字
    @apiParam  query {number}  page  page
    @apiParam  query {number}  pagesize  pagesize
    @apiParam  query {string}  start_time  起始时间
    @apiParam  query {string}  end_time  结束时间
    @apiResponse 200{
        "result": true,
        "msg": "ok",
        "data": {
            "total": 44,
            "items": [{
            "description": "年度签约总金额",
            "source_url{分发的导出原文件}": "http://src",
            "export_id{导出的原记录id}": "39e7fcc5-cb50-2141-eae1-15fe3768b50f",
            "title": "年度签约",
            "id": "39e7fcc5-cb50-2141-eae1-15fe3768b50f",
            "created_on": "2018-07-30T17:24:38",
            "completed_on": "",
            "created_by": "huwl",
            "modified_by": "huwl",
            "modified_on": "2018-07-30T17:24:38",
            "status": "已创建",
            "completed_on{分发任务完成时间}": "2018-07-30T17:24:38",
            "is_deleted": 0
            }]
        }
    }
    **/
    """
    total, items = deliver_service.list_deliver(kwargs.get('keywords', ''),
                                                kwargs.get('start_time', None),
                                                kwargs.get('end_time', None),
                                                kwargs.get('operate_type', None),
                                                kwargs.get('page', 1),
                                                kwargs.get('pagesize', 20))

    return True, '', {'total': total, 'items': items}


@api.admin_route.get('/deliver/{deliver_id}/log')
def get_log(deliver_id: hug.types.text):
    """
        /**
        @apiVersion 1.0.1
        @api  {get} /api/issue_dashboard/deliver/{deliver_id}/log 日志
        @apiGroup  查看报告分发结果日志
        @apiParam  path {string}  deliver_id  ID标识
        @apiResponse 200 {
            "result": true,
            "msg": "ok",
            "data": {
                "content{日志内容}": ""
            }
        }
        **/
    """
    content = deliver_service.get_deliver_log(deliver_id)
    return True, 'ok', {'content': content}


@api.admin_route.post('/withdraw_dashboard')
def withdraw_dashboard(**kwargs):
    """
    删除报告,回收报告
    kwargs：
    """
    projects = kwargs.get('projects', [])
    is_all_projects = int(kwargs.get('is_all_projects', 0))
    dashboard_ids = kwargs.get('dashboard_ids', [])
    description = kwargs.get('description', '')
    export_id = kwargs.get('export_id', '')
    title = kwargs.get('title')
    # 回收报告类型
    recovery_type = kwargs.get('recovery_type')

    if not title:
        raise UserError(400, '缺少title字段')

    if is_all_projects == 0 and not projects:
        raise UserError(400, '参数错误. 需要is_all_projects=1或者指定projects')

    not_exist_code = check_not_exist_code(projects, is_all_projects)
    if not_exist_code:
        raise UserError(400, f'租户{",".join(not_exist_code)}不存在')

    if not dashboard_ids:
        raise UserError(400, 'dashboard_ids参数不能为空')

    params = {
        "export_id": export_id,
        "title": title,
        "description": description,
        "source_url": dashboard_ids,
        "operate_type": 2,
        "is_all_projects": is_all_projects,
        "dest_projects": projects,
        "recovery_type": recovery_type
    }
    withdraw_id = deliver_service.insert_deliver(**params)
    # 改造，直接读取回收表记录
    args = {'withdraw_id': withdraw_id}
    app_celery.withdraw_dashboard.apply_async(kwargs=args)
    return True, 'ok'


@api.admin_route.get('/deliver_log_list')
def deliver_log_list(**kwargs):
    deliver_id = kwargs.get('deliver_id', '')
    if not deliver_id:
        return False, '分发任务ID不能为空', {}
    data = deliver_service.deliver_log_list(
        deliver_id, kwargs.get('status', None), kwargs.get('page', 1), kwargs.get('pagesize', 20)
    )
    return True, '', data
