#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
import logging
import hashlib
from datetime import datetime

# ---------------- 业务模块 ----------------
from base.enums import DatasetFieldType
from issue_dashboard.install_handlers.base_handler import BaseInstallHandler, execute_logger
from dmplib.utils.errors import UserError
from dmplib.utils import strings
from dmplib.constants import DATASET_TABLE_NAME_PREFIX

logger = logging.getLogger(__name__)

# 跳过处理的键名，一般为数据表名，配置后将不收集这些表的id
EXCLUDE_TABLES = []


class DatasetInstallHandler(BaseInstallHandler):
    def __init__(self, package_data, exclude_dataset_ids, copy_dataset_ids):
        self.handler_source = "dataset"
        super().__init__(package_data=package_data, handler_source=self.handler_source)
        self.exclude_dataset_id_list = exclude_dataset_ids.split(",") if exclude_dataset_ids else []
        self.copy_dataset_id_list = copy_dataset_ids.split(",") if copy_dataset_ids else []
        self.collect_exclude_tables = EXCLUDE_TABLES

    def _init_model_file_name(self):
        return "dataset_replace_key_models"

    @staticmethod
    def _get_table_name_list_for_init(datasets: dict):
        return [i for i in datasets[0].keys() if i != "dataset_id"]

    @staticmethod
    def generate_dataset_table_name(dataset_id: str):
        """
        生成数据集表名
        注：此方法复制自dmp仓库的dataset\services\dataset_define_service.py
        :param dataset_id:
        :return:
        """
        return DATASET_TABLE_NAME_PREFIX + "_" + hashlib.md5(dataset_id.encode("utf-8")).hexdigest()[8:-8]

    @staticmethod
    def generate_new_col_name(dataset_id: str, col_name: str, table_name=None):
        """
        利用旧的字段名和新的dataset_id生成新的字段名（中文会生成以首字母的拼音）
        注：此方法复制自dmp仓库的\components\dateset_generate_col_name.py
        :param dataset_id:
        :param col_name:
        :param table_name:
        :return:
        """
        str4fletcher = table_name + dataset_id + ":" + col_name if table_name else dataset_id + ":" + col_name
        return "{}_{}".format(strings.get_first_pinyin_hanzi(col_name), strings.fletcher32(str4fletcher))

    def generate_advanced_col_name(self, dataset_id: str, alias_name: str):
        """
        生成高级字段新的字段名
        :param dataset_id:
        :param alias_name:
        :return:
        """
        # 高级字段col_name生成规则与普通字段一致,不再随机生成
        return "A" + "_" + self.generate_new_col_name(dataset_id, alias_name)

    def _filter_exclude_dataset(self, datasets: list):
        """
        过滤不需要安装的数据集
        :return:
        """
        new_datasets = []
        for operate_dataset in datasets:
            if (
                self.exclude_dataset_id_list
                and operate_dataset.get("dataset_id")
                and operate_dataset.get("dataset_id") in self.exclude_dataset_id_list
            ):
                continue
            new_datasets.append(operate_dataset)
        return new_datasets

    def _collect_dataset_table_name(self, dataset: dict):
        """
        处理table_name
        :param dataset:
        :return:
        """
        old_table_name = dataset.get("table_name", "")
        old_dataset_id = dataset.get("id", "")
        new_dataset_id = self.get_single_replace_key_new_value("dataset_id_keys", old_dataset_id)
        new_table_name = self.generate_dataset_table_name(new_dataset_id)
        self.add_single_replace_key_item("dataset_table_name_keys", old_table_name, new_table_name)

    def _collect_dataset_field_col_name(self, dataset_fields: list):
        """
        处理字段的col_name
        :param dataset_fields:
        :return:
        """
        for field in dataset_fields:
            old_dataset_id = field.get("dataset_id", "")
            new_dataset_id = self.get_single_replace_key_new_value("dataset_id_keys", old_dataset_id)
            if not new_dataset_id:
                raise UserError(message="数据集ID替换异常, ID:{}".format(old_dataset_id))
            old_col_name = field.get("col_name")
            field_table_name = field.get("origin_table_alias_name") or field.get("table_name")
            if field.get("type") in [DatasetFieldType.Customer.value, DatasetFieldType.Calculate.value]:
                old_alias_name = field.get("alias_name") or ""
                new_col_name = self.generate_advanced_col_name(new_dataset_id, old_alias_name)
            else:
                new_col_name = self.generate_new_col_name(new_dataset_id, old_col_name, field_table_name)
            self.add_single_replace_key_item("dataset_field_col_name_keys", old_col_name, new_col_name)

    @staticmethod
    def _replace_specific_dataset(dataset: dict, time4namesuffix: str):
        """
        处理dataset表的字段
        :param dataset:
        :return:
        """
        dataset["name"] = dataset.get("name", "") + "_" + time4namesuffix
        return dataset

    @staticmethod
    def _replace_specific_flow(flow: dict, time4namesuffix: str):
        """
        处理flow表的字段
        :param dataset:
        :return:
        """
        if flow.get("name"):
            flow["name"] = flow.get("name") + "_" + time4namesuffix
        return flow

    @staticmethod
    def _replace_specific_nodes(nodes: list, time4namesuffix: str):
        """
        处理nodes表的字段
        :param dataset:
        :return:
        """
        if not nodes:
            return nodes
        for node in nodes:
            if node.get("name"):
                node["name"] = node.get("name") + "_" + time4namesuffix
        return nodes

    def _replace_specific_fields(self, operate_dataset: dict):
        """
        单独处理一些表的字段
        :param operate_dataset:
        :return:
        """
        time4namesuffix = datetime.now().strftime('%Y%m%d%H%M%S')
        dataset = operate_dataset.get("dataset")
        if dataset:
            operate_dataset["dataset"] = self._replace_specific_dataset(dataset, time4namesuffix)
        flow = operate_dataset.get("flow")
        if flow:
            operate_dataset["flow"] = self._replace_specific_flow(flow, time4namesuffix)
        nodes = operate_dataset.get("nodes")
        if nodes:
            operate_dataset["nodes"] = self._replace_specific_nodes(nodes, time4namesuffix)

    def _collect_single_dataset(self, operate_dataset: dict):
        """
        收集并生成新旧id值
        :param operate_dataset:
        :return:
        """
        dataset = operate_dataset.get("dataset")
        dataset_fields = operate_dataset.get("dataset_fields")
        dataset_field_delete = operate_dataset.get("dataset_field_delete")

        self.collect_ids(operate_dataset)
        if dataset:
            self._collect_dataset_table_name(dataset)
        if dataset_fields:
            self._collect_dataset_field_col_name(dataset_fields)
        if dataset_field_delete:
            self._collect_dataset_field_col_name(dataset_field_delete)

    @execute_logger
    def execute(self):
        if not self.package_data:
            return self.package_data
        datasets = self.package_data.get("datasets")
        if not datasets:
            return self.package_data

        self._init_replace_key_models(self._get_table_name_list_for_init(datasets))
        datasets = self._filter_exclude_dataset(datasets)
        for operate_dataset in datasets:
            dataset_id = operate_dataset.get("dataset_id", "")
            if dataset_id in self.copy_dataset_id_list:
                self._collect_single_dataset(operate_dataset)
                self._replace_specific_fields(operate_dataset)

        # 在安装包数据替换新旧id前，先更新datasets的数据
        self.package_data["datasets"] = datasets
        return self.replace_data(self.package_data)
