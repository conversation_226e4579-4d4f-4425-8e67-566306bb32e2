#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
from collections import defaultdict
import json
import logging
import uuid
from abc import ABCMeta, abstractmethod
from functools import wraps

# ---------------- 业务模块 ----------------
from issue_dashboard.replace_key_models.base_model import ReplaceKeyBaseModel
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError

logger = logging.getLogger(__name__)


def execute_logger(func):
    """
    日志
    :param func:
    :return:
    """

    @wraps(func)
    def _handle(*args, **kwargs):
        try:
            logger.info(msg="模板安装模块【{}】开始执行...".format(args[0].handler_source))
            if args[0].handler_source == "dataset":
                logger.info(
                    msg="参数exclude_dataset_id_list: {0}; 参数copy_dataset_id_list: {1}".format(
                        json.dumps(args[0].exclude_dataset_id_list), json.dumps(args[0].copy_dataset_id_list)
                    )
                )
            res = func(*args, **kwargs)
            logger.info(msg="模板安装模块【{}】执行完毕...".format(args[0].handler_source))
            return res
        except Exception as e:
            logger.exception(msg="模板安装模块【{0}】执行异常,错误信息: {1}".format(args[0].handler_source, str(e)))

    return _handle


class BaseInstallHandler:

    __metaclass__ = ABCMeta

    def __init__(self, package_data, handler_source):
        self.package_data = package_data
        self.replace_key_models = {}
        self.replace_keys = defaultdict(dict)
        self.collect_exclude_tables = []

    @abstractmethod
    def _init_model_file_name(self):
        """
        初始化存放replace_key_models的文件名
        """

    @abstractmethod
    def execute(self):
        """
        执行入口
        """

    def get_single_replace_key_new_value(self, keys_name: str, old_value: str):
        """
        根据旧值获取新值
        :param keys_name:
        :param old_value:
        :return:
        """
        if not keys_name or not old_value:
            return None
        match_replace_keys = self.replace_keys.get(keys_name, {})
        return match_replace_keys.get(old_value) or None

    def add_single_replace_key_item(self, keys_name: str, old_value: str, new_value: str):
        """
        新增可替换的新旧值
        :param keys_name:
        :param old_value:
        :param new_value:
        :return:
        """
        if not keys_name or not old_value or not new_value:
            return False
        self.replace_keys[keys_name][old_value] = new_value
        return True

    @staticmethod
    def _generate_code(business_type=1):
        """
        生成业务代码
        :param business_type: 1 为获取业务代码code
        :return:
        """
        if business_type:
            return uuid.uuid4().__str__().replace('-', '')
        return uuid.uuid4().__str__()

    def _generate_new_value(self, key_name: str):
        """
        生成新的id值
        注：生成biz_code的方法与其他id不同
        :param key_name:
        :return:
        """
        return seq_id() if key_name != "biz_code_keys" else self._generate_code()

    def _init_replace_key_models(self, table_name_list: list):
        """
        批量初始化每个表名对应的replace_key类实例
        :return:
        """
        for table_name in table_name_list:
            try:
                replace_key_model = self._create_key_model(table_name)
            except BaseException:
                replace_key_model = None
            self.replace_key_models[table_name] = replace_key_model

    def _create_key_model(self, table_name: str):
        """
        根据报告的表名获取类的实例
        :param table_name:
        :return:
        """
        class_file_name = table_name + "_replace_key_model"
        class_name = ""
        name_arr = class_file_name.split("_")
        for name in name_arr:
            class_name += name.capitalize()

        if not self._init_model_file_name():
            raise UserError(message="获取不到replace_key_models文件名")
        model_file_path = "issue_dashboard.replace_key_models.{}".format(self._init_model_file_name())
        module_obj = __import__(model_file_path, fromlist=[class_name])
        generator_class = getattr(module_obj, class_name)
        return generator_class()

    def _get_model_by_table_name(self, table_name: str):
        """
        根据表名获取replace_key_model
        :param table_name:
        :return:
        """
        return self.replace_key_models.get(table_name)

    def _do_collect_in_list(self, replace_key_model: ReplaceKeyBaseModel, data_list: list):
        """
        执行id收集
        :param replace_key_model:
        :param data_list:
        :return:
        """
        for key_name, operate_key_list in replace_key_model.__dict__.items():
            for key in operate_key_list:
                for data_item in data_list:
                    old_value = data_item.get(key)
                    if old_value:
                        new_value = self._generate_new_value(key_name)
                        self.replace_keys[key_name].update({old_value: new_value})

    def _do_collect_in_dict(self, replace_key_model: ReplaceKeyBaseModel, data_dict: dict):
        """
        执行id收集
        :param replace_key_model:
        :param data_dict:
        :return:
        """
        for key_name, operate_key_list in replace_key_model.__dict__.items():
            for key in operate_key_list:
                old_value = data_dict.get(key)
                if old_value:
                    new_value = self._generate_new_value(key_name)
                    self.replace_keys[key_name].update({old_value: new_value})

    def collect_ids(self, data: dict):
        """
        收集每个表需要替换的id
        :param data:
        :return:
        """
        for table_name, table_data in data.items():
            if not table_data:
                continue
            if self.collect_exclude_tables and table_name in self.collect_exclude_tables:
                continue
            replace_key_model = self._get_model_by_table_name(table_name)
            if not isinstance(replace_key_model, ReplaceKeyBaseModel):
                continue
            if isinstance(table_data, list):
                self._do_collect_in_list(replace_key_model, table_data)
            elif isinstance(table_data, dict):
                self._do_collect_in_dict(replace_key_model, table_data)

    @staticmethod
    def _do_replace_string(str4replace: str, replace_keys: dict):
        """
        字符串替换
        :param str4replace:
        :param replace_keys:
        :return:
        """
        if not str4replace or not replace_keys:
            return str4replace
        for old, new in replace_keys.items():
            if old in str4replace and new:
                str4replace = str4replace.replace(old, new)
        return str4replace

    def replace_data(self, data):
        """
        在原始的字符串上执行replace，替换旧值
        :return:
        """
        str4replace = json.dumps(data)
        for replace_keys_dict in self.replace_keys.values():
            str4replace = self._do_replace_string(str4replace, replace_keys_dict)
        try:
            return json.loads(str4replace)
        except BaseException as e:
            msg = "基于字符串REPLACE操作异常，异常信息：{}".format(str(e))
            logger.exception(msg)
            raise UserError(msg)
