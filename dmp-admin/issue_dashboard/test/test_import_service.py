#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

import logging
from tests.base import BaseTest
import unittest
from issue_dashboard.services import import_service


logger = logging.getLogger(__name__)


class TestImportService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='admin')

    def test_import_data(self):
        kwargs = {
            "file_url": "https://oss-cn-shenzhen.aliyuncs.com/upload-file/****************/111_3_20200309175128.zip",
            "select_import_data": {
                "dashboards": [
                    {
                        "id": "39f3d0d0-20d1-0cb2-2598-0e0fd9e5cf83",
                        "name": "导出测试",
                        "type": "FILE",
                        "parent_id": "39f0dc0f-5435-0714-fe39-740b22c3b967",
                        "level_code": "2467-0104-",
                        "created_on": "2020-03-09T17:36:29",
                        "check_status": True,
                        "text": "导出测试",
                        "date": "2020-03-09 17:36:29",
                        "children": None,
                        "level": 1,
                    }
                ],
                "datasets": [
                    {
                        "id": "39f34556-04d0-b0cb-9ed5-ae91690326e9",
                        "name": "王成强-测试_多屏报告测试1_1",
                        "type": "UNION",
                        "parent_id": "39f0dc10-12f6-1867-ab7a-997bb198bffc",
                        "level_code": "1544-0015-",
                        "created_on": "2020-02-11T15:31:41",
                        "check_status": True,
                        "text": "王成强-测试_多屏报告测试1_1",
                        "date": "2020-02-11 15:31:41",
                        "children": None,
                        "level": 1,
                    },
                    {
                        "id": "39f34556-051a-b99c-4b8b-aa592d180131",
                        "name": "王成强-测试_多屏报告测试1_2",
                        "type": "UNION",
                        "parent_id": "39f0dc10-12f6-1867-ab7a-997bb198bffc",
                        "level_code": "1544-0016-",
                        "created_on": "2020-02-11T15:31:41",
                        "check_status": True,
                        "text": "王成强-测试_多屏报告测试1_2",
                        "date": "2020-02-11 15:31:41",
                        "children": None,
                        "level": 1,
                    },
                ],
                "applications": [
                    {
                        "id": "39f3d0c2-7bb3-16b8-f142-3c938b3094ef",
                        "name": "111",
                        "created_on": "2020-03-09T17:17:22",
                        "check_status": False,
                        "type": "",
                        "parent_id": "",
                        "level_code": "",
                        "text": "111",
                        "date": "2020-03-09 17:17:22",
                        "children": None,
                        "level": 0,
                    }
                ],
            },
            "target_dashboard_folder_id": "",
            "target_dataset_folder_id": "",
            "include_dashboard_folder": False,
            "include_dataset_folder": False,
            "project_code": "ycm1",
            "dashboard_import_id": "39f3d0e5-4a0a-5649-a953-58176b120716",
        }
        import_service.import_data(**kwargs)


if __name__ == '__main__':
    # unittest.main()
    s = unittest.TestSuite()
    s.addTest(TestImportService("test_import_data"))
    runner = unittest.TextTestRunner()
    runner.run(s)
