#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

import logging
from tests.base import BaseTest
from issue_dashboard.services import init_import_service


logger = logging.getLogger(__name__)


class TestInitImportService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='uitest', account='admin')

    def test_init_dashboard(self):
        kwargs = {
            "project_code": "uitest",
            "init_import_id": "3a049139-5ec9-fc61-14c8-914161f2d538"
        }
        init_import_service.init_dashboard(**kwargs)
