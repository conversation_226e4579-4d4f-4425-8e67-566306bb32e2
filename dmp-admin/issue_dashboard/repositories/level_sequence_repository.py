#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

"""
import time
from dmplib import config
from dmplib.components.enums import DBType


def get_cur_level_code(model, conn):
    """
    获取当前层级编码
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    sql = 'SELECT `%s` FROM `%s` WHERE `%s`=%%(level_id)s LIMIT 1;' % (
        model.table_level_code_field,
        model.table_name,
        model.table_level_id_field,
    )
    return conn.query_scalar(sql, {'level_id': model.level_id})


def add_sequence(model, conn):
    """
    增加序列
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    sql = """INSERT INTO `dap_bi_level_sequence`(`table_name`,`level_id`,`attach_identify`,`max_sequence`)
             SELECT `table_name`,`level_id`,`attach_identify`,`max_sequence` FROM  (
                    SELECT %(table_name)s AS `table_name` ,%(level_id)s AS `level_id` 
                    ,%(attach_identify)s AS `attach_identify`,0 as `max_sequence`
                  ) AS res 
             WHERE NOT EXISTS(SELECT 1 FROM `dap_bi_level_sequence` 
                      WHERE `table_name`=%(table_name)s AND `level_id`=%(level_id)s 
                      AND `attach_identify`=%(attach_identify)s
                  )"""
    return conn.exec_sql(
        sql, {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
    )


# def increase_sequence(model, conn):
#     """
#     递增序列
#     :param level_sequence.models.LevelSequenceBaseModel model:
#     :return:
#     """
#     if config.get('DB.db_type') == DBType.DM.value:
#         return increase_sequence_dm(model, conn)
#     sql = """UPDATE `level_sequence` SET `max_sequence` = (select @max_sequence:=`max_sequence`+1)
#              WHERE `table_name`=%(table_name)s AND level_id=%(level_id)s AND `attach_identify`=%(attach_identify)s"""
#     condition = {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
#     conn.exec_sql('SET @max_sequence := 0;')
#     conn.exec_sql(sql, condition)
#     cur_sequence = conn.query_scalar('SELECT @max_sequence;')
#     return cur_sequence


def increase_sequence(model, conn):

    sequence = conn.query_scalar(
        'select `max_sequence` from `dap_bi_level_sequence` where `table_name`=%(table_name)s and level_id=%(level_id)s and attach_identify=%(attach_identify)s',
        {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
    )

    cur_sequence = sequence + 1

    conn.update('dap_bi_level_sequence', {'max_sequence': cur_sequence}, {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify})

    return cur_sequence


def get_level_sequence_record(model, conn):
    """

    :param model:
    :param conn:
    :return:
    """
    sql = """select count(1) as cnt from dap_bi_level_sequence where table_name=%(table_name)s and level_id=%(level_id)s 
          and attach_identify=%(attach_identify)s"""
    params = {"table_name": model.table_name, "level_id": model.level_id, "attach_identify": model.attach_identify}
    return conn.query_one(sql, params)


def update_level_code(new_level_code, operate_dashboard_id, conn):
    """

    :param new_level_code:
    :param operate_dashboard_id:
    :param conn:
    :return:
    """
    sql = """update dap_bi_dashboard set level_code=%(new_level_code)s where id=%(operate_dashboard_id)s"""
    params = {"new_level_code": new_level_code, "operate_dashboard_id": operate_dashboard_id}
    return conn.exec_sql(sql, params)


def level_code_is_exist(level_code, table_name, conn):
    """
    判断level_code是否已经存在
    :param level_code:
    :param table_name:
    :param conn:
    :return:
    """
    sql = """select 1 from {table_name} where level_code=%(level_code)s""".format(table_name=table_name)
    params = {"level_code": level_code}
    return conn.exec_sql(sql, params)
