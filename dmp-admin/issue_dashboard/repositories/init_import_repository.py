from base import repository
from dmplib.saas.project import get_db


def update_init_import_status(project_code: str, import_id: str, status: str, message=''):
    """
    更新报告初始化任务状态
    :param project_code:
    :param import_id:
    :param status:
    :param message:
    :return:
    """
    with get_db(project_code) as db:
        return db.update("dap_bi_dashboard_init_imports", data={"status": status, "message": message},
                         condition={"id": import_id})


def get_init_import_task(project_code: str, init_import_id: str):
    """
    更新报告初始化任务状态
    :param project_code:
    :param init_import_id:
    :return:
    """
    with get_db(project_code) as db:
        sql = 'select * from dap_bi_dashboard_init_imports where id=%(id)s'
        return db.query_one(sql, {'id': init_import_id})


def get_init_dashboard_by_db_type(db_type, dashboard_id_list):
    """
    按数据库类型和报告id查找数据
    :param db_type:
    :param dashboard_id_list:
    :return:
    """
    if not db_type or not dashboard_id_list:
        return []
    sql = "select * from dap_bi_dashboard_init_data where dashboard_id in %(dashboard_id)s and db_type=%(db_type)s"
    params = {"dashboard_id": dashboard_id_list, "db_type": db_type}
    return repository.get_data_by_sql(sql, params)

