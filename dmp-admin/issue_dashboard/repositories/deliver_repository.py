import json

from base import repository
from dmplib.db import mysql_wrapper


def list_delivers(keywords, start_time=None, end_time=None, operate_type=None, page=1, pagesize=20):
    select = 'select id, title, export_id, description, source_url, status, dest_projects, is_all_projects, ' \
             'completed_on, created_on, created_by, modified_on, modified_by,distribute_type,is_lock_dataset,' \
             'operate_type, recovery_type from `dap_bi_deliver_dashboard` '

    select_count = 'select count(*) from `dap_bi_deliver_dashboard` '
    sql = ' where is_deleted=0 '
    params = {}

    if keywords:
        sql += ' and (`title` like %(keyword)s or description like %(keyword)s) '
        params['keyword'] = '%{}%'.format(keywords)

    if operate_type:
        sql += ' and operate_type = %(operate_type)s'
        params['operate_type'] = operate_type

    if start_time:
        sql += ' and created_on >= %(start_time)s '
        params['start_time'] = start_time if ':' in start_time else start_time + ' 00:00:00'

    if end_time:
        sql += ' and created_on =< %(end_time)s '
        params['end_time'] = end_time if ':' in end_time else end_time + ' 23:59:59'
    sql += ' order by created_on desc '
    pagination = repository.Pagination(page, pagesize)
    items = repository.get_data_by_sql(select + sql, params, pagination=pagination)
    total = repository.get_data_scalar_by_sql(select_count + sql, params)
    for item in items:
        item['projects'] = item.get("dest_projects").split(',') if item.get("dest_projects") else ''
    return total, items


def get_deliver_meta(deliver_id):
    if not deliver_id:
        return None

    db = mysql_wrapper.get_db()
    return db.query_one('select id,source_project,source_url,dest_projects,is_all_projects, status,replace_data_source, is_publish_center, export_id'
                        ' from dap_bi_deliver_dashboard where id=%(id)s', {'id': deliver_id})


def deliver_log_list(deliver_id, status=None, page=1, pagesize=20):
    select = 'select target_project_code, status, run_time, log_info from dap_bi_deliver_dashboard_log where deliver_id = %(deliver_id)s'

    select_count = 'select count(*) from `dap_bi_deliver_dashboard_log` where deliver_id = %(deliver_id)s'
    sql = ''
    params = {'deliver_id': deliver_id}
    if status:
        sql += ' and status = %(status)s'
        params['status'] = status
    sql += ' order by created_on desc '
    pagination = repository.Pagination(page, pagesize)
    items = repository.get_data_by_sql(select + sql, params, pagination=pagination)
    total = repository.get_data_scalar_by_sql(select_count + sql, params)
    return total, items


def deliver_log_group_count(deliver_id):
    sql = 'select count(*) as total, status from dap_bi_deliver_dashboard_log where deliver_id = %(deliver_id)s GROUP BY status'
    params = {'deliver_id': deliver_id}
    return repository.get_data_by_sql(sql, params)