#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/07/17

"""
数据填报分发模块之数据填报模块service代码
"""

# ---------------- 标准模块 ----------------
import re
from datetime import datetime
from loguru import logger

from dmplib import config
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from issue_dashboard.models import *
from issue_dashboard.models import ProjectModel
from base.sql_adapter import adapter_sql

DEFAULT_FOLDER_NAME = '系统分发填报文件夹'
FILLING_TEMPLATE = 'filling_template'
FILLING_TEMPLATE_FOLDER = 'filling_template_folder'
FILLING_TEMPLATE_COLUMN = 'filling_template_column'
FILLING_DATASET_FILTER = 'filling_dataset_filter'


class QueryBuilder:
    def __init__(self, model):
        """
        :param issue_filling.models.ProjectModel model:
        """
        self.model = model
        self.rds = get_db(self.model.code)

    def replace_data(self, **kwargs):
        pass

    def replace_multi_data(self, data):
        pass

    def insert_data(self, data):
        pass

    def insert_multi_data(self, data):
        pass

    def update_data(self, data):
        pass


class ModelFactory:
    """
    模型工厂
    """

    def __init__(self, table_name='', data=None, project_model=None):
        # delete_type 区分删除原表数据的方式 ignore: 不需要处理 direct: 直接根据id删除
        self.valid_table_name_dict = {
            FILLING_TEMPLATE: {'delete_type': 'direct'},
            FILLING_TEMPLATE_FOLDER: {'delete_type': 'direct'},
            FILLING_TEMPLATE_COLUMN: {'delete_type': 'direct'},
            FILLING_DATASET_FILTER: {'delete_type': 'direct'},
        }
        self.table_name = table_name
        self.data = data
        self.project_model = project_model

    def get_table_operations(self):
        """
        获取操作项
        :return:
        """
        return self.valid_table_name_dict.get(self.table_name, {})

    @staticmethod
    def _create_instance(field_dict):
        """
        实例化
        :param field_dict:
        :return:
        """
        model = BaseModel()
        for field, field_data in field_dict.items():
            # 优先取字段默认值
            default = field_data.get("Default")
            field_type = field_data.get("Type")
            # 字段值不能为空且没有设置默认值的情况
            if field_data.get("Null") == "NO" and default is None:
                if "int" in field_type:
                    default = 0
                elif "char" in field_type or "time" in field_type or "date" in field_type or "enum" in field_type:
                    default = ""
            if field in ["created_on", "modified_on"]:
                default = ""
            model.__setattr__(field, default)
        return model

    def initialize_table_model(self):
        """
        利用目标租户的数据表字段来实例化model
        :return:
        """
        table_model_dict = dict()
        if not self.project_model or not isinstance(self.project_model, ProjectModel):
            raise UserError(message='当前租户信息初始化异常，请重启分发任务')
        if not self.valid_table_name_dict:
            return table_model_dict
        conn = get_db(self.project_model.code)
        if not conn:
            return table_model_dict
        self._create_instance_for_table(conn, table_model_dict)
        return table_model_dict

    def _create_instance_for_table(self, conn, table_model_dict):
        """
        _create_instance_for_table
        :param conn:
        :param table_model_dict:
        :return:
        """
        for table_name in list(self.valid_table_name_dict.keys()):
            cur = None
            try:
                if table_name in [FILLING_TEMPLATE_FOLDER]:
                    continue
                # 获取表字段
                field_dict = dict()
                column_of_sql = adapter_sql('table_columns', conn.db_type).format(
                    owner=self.project_model.db_name
                )
                query_data = conn.query(column_of_sql, {"table_name": table_name})
                if not query_data:
                    continue
                for single_field in query_data:
                    field = single_field.get("name")
                    if not field:
                        continue
                    field_dict[field] = single_field
                table_model_dict[table_name] = self._create_instance(field_dict)
            except BaseException as e:
                if cur:
                    cur.close()
                raise UserError(message="获取目标租户表信息异常：{}".format(e))


class FillingQueryBuilder(QueryBuilder):
    def __init__(self, model):
        self.top_parent_id = ''
        self.default_parent_id = ''
        self.include_folder = True
        self.is_import = False
        super().__init__(model)

    @staticmethod
    def get_default_folder(conn):
        """
        获取默认文件夹
        :param conn:
        :return:
        """
        sql = 'select id from dap_bi_filling_template where display_name=%(display_name)s ' \
              "and type=%(type)s and parent_id='' limit 1"
        data = conn.query_one(sql, {"display_name": DEFAULT_FOLDER_NAME, "type": "folder"})
        return data

    def _pre_filling_folder_data(self, data):
        """
        重置填报的文件夹所属信息
        :param data:
        :return:
        """
        for filling_id, filling_table in data.items():
            if not filling_table:
                continue
            for filling_table_name, table_data in filling_table.items():
                if filling_table_name not in [FILLING_TEMPLATE, FILLING_TEMPLATE_FOLDER]:
                    continue
                self._set_filling_folder(table_data)

    def _set_filling_folder(self, table_data):  # NOSONAR
        """
        更新填报的文件夹信息
        :param table_data:
        :return:
        """
        if not table_data or not isinstance(table_data, list):
            return []
        for idx, item in enumerate(table_data):
            parent_id = item.get('parent_id')
            file_type = item.get('type')
            # 导入
            if self.is_import:
                # 填报文件夹类型的根文件夹挂载在指定文件夹下；填报文件类型且不导入文件夹，则挂载在指定文件夹下
                if (file_type == 'folder' and not parent_id) or (file_type == 'file' and not self.include_folder):
                    item['parent_id'] = self.top_parent_id
            else:
                # 分发场景，文件夹挂载在默认系统分发文件夹下；填报文件层级不变
                if file_type == 'folder' and not parent_id:
                    item['parent_id'] = self.default_parent_id

    def create_default_folder(self, conn):
        """
        创建数据填报分发默认文件夹
        :return:
        """
        filling_id = seq_id()
        data = {
            'id': filling_id,
            'display_name': DEFAULT_FOLDER_NAME,
            'parent_id': '',
            'status': 0,
            'is_needaudit': 0,
            'is_addinput_batch': 0,
            'version': 0,
            'type': 'folder',
            'created_by': self.model.code,
            'modified_by': self.model.code,
        }
        conn.insert('dap_bi_filling_template', data)
        default_filling_id_data = {'id': filling_id}
        return default_filling_id_data

    def rename_filling(self, filling_name):
        is_exist_names = self.get_son_filling_names(self.top_parent_id, self.rds) or []
        if is_exist_names:
            return self.generate_new_filling_name(filling_name, is_exist_names)
        return filling_name

    def generate_new_filling_name(self, name, is_exist_names, i=1):
        if name in is_exist_names:
            # 判断是否以 (1) 这种结尾
            int_list = re.findall(r'\((.*?)\)', name)
            if not int_list:
                return self.generate_new_filling_name(name + "(1)", is_exist_names)
            # 判断最后一个数字
            else:
                try:
                    end_num = int(int_list[-1])
                except ValueError:
                    return name + "(1)"
                # 如果最后一位比当前循环大，从名称中重新获取i
                i = (end_num if i < end_num else i) + 1
                new_name = name[:-3] + "(%s)" % i
                return self.generate_new_filling_name(new_name, is_exist_names, i=i)
        return name

    @staticmethod
    def get_son_filling_names(parent_id, conn):
        """
        :param parent_id:
        :param conn:
        :return:
        """
        if not parent_id:
            parent_id = ''
            # 查询根目录下所有
        sql = """select display_name from dap_bi_filling_template where parent_id=%(parent_id)s and type=%(type)s"""
        params = {"parent_id": parent_id, "type": 'file'}
        return conn.query_columns(sql, params)

    def pre_deal_with_table_data(self, table_name, table_data):
        """
        预处理表格数据
        :param table_name:
        :param table_data:
        :return:
        """
        result_table_data = list()
        for index, item in enumerate(table_data):
            self._deal_with_single_table_data(table_name, table_data, item, result_table_data)
        return result_table_data

    def _deal_with_single_table_data(self, table_name, table_data, item, result_table_data):
        """
        预处理表格的单条记录
        :param table_name:
        :param table_data:
        :param item:
        :param result_table_data:
        :return:
        """
        if not isinstance(table_data, list):
            return
        # 平铺的情况不处理文件夹
        if not self.include_folder and table_name == FILLING_TEMPLATE_FOLDER:
            return

        # 导入情况下，填报平铺，还需要处理数据填报重名 + 1问题
        if not self.include_folder and table_name == FILLING_TEMPLATE:
            item['display_name'] = self.rename_filling(item.get("display_name"))

        # 处理created_by and modified_by
        item['created_by'] = self.model.code
        item['modified_by'] = self.model.code
        # 处理created_on and modified_on
        item.update(
            {
                'created_on': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'modified_on': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }
        )
        result_table_data.append(item)

    @staticmethod
    def delete_multi_data(table_name, filling_id, table_operations, conn):
        """
        按条件删除旧数据
        :param table_name:
        :param filling_id:
        :param table_operations:
        :param conn:
        :return:
        """
        sql = ''
        if not table_name or not filling_id or not table_operations:
            return False
        delete_type = table_operations.get('delete_type', 'ignore')
        if delete_type == 'ignore':
            return True
        elif delete_type == 'direct':
            sql = '''delete from {0} where template_id='{1}' '''.format(table_name, filling_id)
            if table_name == FILLING_TEMPLATE:
                sql = '''delete from {0} where id='{1}' '''.format(table_name, filling_id)
            if table_name == FILLING_TEMPLATE_FOLDER:
                sql = ""
        if sql:
            conn.exec_sql(sql)
        return True

    def create_top_folder(self):
        # 默认文件夹id
        default_filling_id_data = self.get_default_folder(self.rds)
        # 没有则创建
        if not default_filling_id_data:
            default_filling_id_data = self.create_default_folder(self.rds)
            top_parent_id = default_filling_id_data.get("id")
        else:
            top_parent_id = default_filling_id_data.get("id")
        return top_parent_id

    @staticmethod
    def _assign_field_for_insert(table_data: list, model):
        """
        表字段
        :param table_data:
        :param model:
        :return:
        """
        if not table_data or not model:
            return table_data
        model_dict = model.get_dict()
        if not model_dict:
            return table_data
        for item in table_data:
            # 添加缺少的字段
            add_keys = set(list(model_dict.keys())) - set(list(item.keys()))
            if add_keys:
                for x in add_keys:
                    item[x] = model_dict[x]
            # 删除多余的导入字段
            diff_keys = set(list(item.keys())) - set(list(model_dict.keys()))
            if diff_keys:
                for y in diff_keys:
                    item.pop(y)

    def replace_data(self, filling_id, filling_table):
        """
        执行更新
        :param filling_id:
        :param filling_table:
        :return:
        """
        commit = False
        factory = ModelFactory(project_model=self.model)
        valid_table_name_list = list(factory.valid_table_name_dict.keys())
        table_model_dict = factory.initialize_table_model()
        if not table_model_dict:
            return False, "表字段初始化失败"
        # rds连接实例
        conn = self.rds
        try:
            for table_name in sorted(list(filling_table.keys())):
                table_data = filling_table.get(table_name, [])
                if table_name not in valid_table_name_list:
                    continue
                # 先删除其他表格的旧数据
                table_operations = ModelFactory(table_name).get_table_operations()
                self.delete_multi_data(table_name, filling_id, table_operations, conn)
                # 没有数据则不需要更新
                if not table_data:
                    continue
                # 预处理
                table_data_v2 = self.pre_deal_with_table_data(table_name, table_data)
                real_table_name = FILLING_TEMPLATE if table_name == FILLING_TEMPLATE_FOLDER else table_name
                model = table_model_dict.get(real_table_name)
                if not model:
                    continue

                # 暂不需要校验，以后填报的相关数据表结构有变化则再加上

                # 兼容场景：导入的数据缺少某些表字段
                self._assign_field_for_insert(table_data_v2, model)

                conn.replace_multi_data(real_table_name, table_data_v2, list(model.get_dict().keys()), commit=commit, condition_field=['id'])

            # 统一commit更改
            conn.commit()
            return True, ""
        except Exception as e:
            conn.close()
            conn.rollback()
            message = '分发数据填报到指定租户失败:' + str(e) + ',租户code:' + str(self.model.code) + ',table_name:' + table_name
            logger.error(message)
            raise UserError(message=message)

    def replace_multi_data(self, data):
        """
        批量更新数据填报数据
        :param data:
        :return:
        """
        if not isinstance(data, dict):
            return False

        # 设置填报模板文件夹
        try:
            self._pre_filling_folder_data(data)
        except BaseException as e:
            message = '数据填报分发中重置填报的文件夹所属信息异常:' + str(e) + ',租户code:' + str(self.model.code)
            logger.error(message)

        for filling_id, filling_table in data.items():
            if not filling_table:
                continue
            result, msg = self.replace_data(filling_id, filling_table)
            if not result:
                message = '数据填报分发异常: ' + msg + ',租户code:' + str(self.model.code)
                logger.error(message)
                raise UserError(message=message)


def deliver_filling_data(
        project_code, filling_data, target_folder_id=None, is_import=False, include_folder=True
):
    """
    处理待分发数据填报
    分发参数：project_code, filling_data
    导入参数：project_code, filling_data, target_folder_id=None, is_import=True, include_folder=False
    :param project_code:
    :param filling_data:
    :param target_folder_id:
    :param is_import:
    :param include_folder:
    :return:
    """
    db_name = config.get('DBInstaller.project_db_prefix', 'dmp') + '_' + project_code
    model = ProjectModel(code=project_code, db_name=db_name)
    logger.info('执行数据填报分发 租户code:{0} 租户数据库:{1}'.format(project_code, db_name))
    query_builder = FillingQueryBuilder(model)
    # 填报指定的文件夹
    query_builder.top_parent_id = target_folder_id if target_folder_id else ''
    # 不是分发，且没有指定文件夹，需要创建系统分发文件夹
    if not is_import and not target_folder_id:
        query_builder.default_parent_id = query_builder.create_top_folder()
    query_builder.include_folder = include_folder
    query_builder.is_import = is_import
    query_builder.replace_multi_data(filling_data)
    return True
