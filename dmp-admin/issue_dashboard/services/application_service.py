#!/usr/local/bin python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
import json
import logging
from datetime import datetime

# ---------------- 业务模块 ----------------
from base import repository
from base.enums import ApplicationDistributeType, DashboardTypeAccessReleased, TaskDataType
from dmplib.utils.strings import seq_id
from issue_dashboard.services import deliver_helper, tag_relation_service
from dmplib.db.mysql_wrapper import SimpleMysql
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from components.dmp_api import DMPAPI


logger = logging.getLogger(__name__)


class ApplicationDeliverHandler:

    # 门户分发导入涉及到的数据表
    op_table_names = ["dap_bi_application", "dap_bi_application_guide_img", "dap_bi_function", "dap_bi_function_icon"]

    def __init__(self, project_code, applications, distribute_type):
        self.project_code = project_code
        self.applications = applications
        self.distribute_type = distribute_type
        self.__handle_distribute_type()

    def __handle_distribute_type(self):
        if self.distribute_type == 0:
            self.distribute_type = ApplicationDistributeType.DistributeAndNotLock.value
        elif self.distribute_type == 1:
            self.distribute_type = ApplicationDistributeType.DistributeAndLock.value

    def _empty_table_data(self, db: SimpleMysql, application_id: str, is_exist_function_icon: bool):
        """
        清空门户id对应的表数据
        :return:
        """
        db.delete("dap_bi_application", {"id": application_id})
        db.delete("dap_bi_application_guide_img", {"application_id": application_id})
        db.delete("`dap_bi_function`", {"application_id": application_id})
        if is_exist_function_icon:
            db.delete("dap_bi_function_icon", {"created_by": "celery"})

    def _pre_handle_single_table_data(self, table_name, single_table_data):
        """
        插入表前预处理一些字段值
        :return:
        """
        for item in single_table_data:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            item["created_by"] = "celery"
            item["modified_by"] = "celery"
            item["created_on"] = current_time
            item["modified_on"] = current_time
            if table_name == "application":
                item["distribute_type"] = self.distribute_type
            if table_name in ["application", "function"]:
                item["theme"] = item.get('theme') or ''

    def deliver(self):
        """
        门户分发执行入口
        :return:
        """
        if not self.applications or not isinstance(self.applications, dict):
            return False
        target_table_field_models = deliver_helper.batch_get_table_fields_models(self.project_code, self.op_table_names)

        # 获取当前租户的报告、门户发布授权模式
        project_auth_mode = self.get_project_auth_mode(self.project_code)
        all_application_id = []

        db = get_db(self.project_code)
        try:
            db.begin_transaction()
            for application_id, table_data in self.applications.items():
                is_exist_function_icon = bool(table_data.get("function_icon", {}))
                self._empty_table_data(db, application_id, is_exist_function_icon)
                for table_name, single_table_data in table_data.items():
                    if table_name in ('tag_relation', 'tag_relations'):
                        continue
                    if not single_table_data:
                        continue
                    if not table_name.startswith('dap_bi_'):
                        table_name = 'dap_bi_' + table_name
                    self._pre_handle_single_table_data(table_name, single_table_data)
                    # 门户的发布方式修改
                    self._update_application_publish_way(project_auth_mode, table_name, single_table_data)
                    field_model = target_table_field_models.get(table_name)
                    if not field_model:
                        raise UserError(message="获取目标租户数据表`{}`异常".format(table_name))
                    target_fields = list(field_model.get_dict().keys())
                    single_table_data_fields = list(single_table_data[0].keys()) if single_table_data else []
                    target_table_field = list(set(target_fields).intersection(set(single_table_data_fields)))
                    db.replace_multi_data(table_name, single_table_data, target_table_field, condition_field=['id'])
                tag_relation_service.update_tag_relation(db, application_id, '2', table_data.get('tag_relation', []))
                all_application_id.append(application_id)
            db.commit()
            # 批量发布门户并同步第三方门户到基础数据平台
            self._sync_application(all_application_id)
        except UserError as e:
            db.rollback()
            message = "门户分发失败，异常信息:{}".format(str(e))
            raise UserError(message=message)
        except Exception as e:
            db.rollback()
            logger.error(e)
            raise e
        return True

    @staticmethod
    def get_project_auth_mode(project_code):
        from base import repository
        project = repository.get_data('dap_bi_tenant_setting', {'code': project_code}, fields=['auth_mode'], multi_row=False)
        return bool(int(project.get("auth_mode")))

    @staticmethod
    def _update_application_publish_way(project_auth_mode, table_name, single_table_data):
        """
        门户的发布方式修改
        :return:
        """
        for item in single_table_data:
            if table_name == "application" \
                    and project_auth_mode \
                    and item.get("type_access_released") == DashboardTypeAccessReleased.UserRole.value:
                item["type_access_released"] = DashboardTypeAccessReleased.ThirdParty.value

    @staticmethod
    def _get_third_publish_application(table_name, single_table_data):
        """
        获取第三方发布的门户id
        :param table_name:
        :param single_table_data:
        :return:
        """
        for item in single_table_data:
            if table_name == "application" \
                    and item.get("type_access_released") == DashboardTypeAccessReleased.ThirdParty.value:
                return True
        return False

    def _sync_application(self, application_id_list):
        """
        批量发布的鉴权门户
        :param application_id_list:
        :return:
        """
        if not application_id_list:
            logger.error("没有需要发布的第三方鉴权门户")
            return False
        try:
            dmp_api = DMPAPI(self.project_code, 3)
            sync_result = dmp_api.sync_application(application_id_list)
            if not sync_result.get('result'):
                raise UserError(message=f'同步门户到基础数据失败:{sync_result}')
        except Exception as e:
            records = []
            for id in application_id_list:
                records.append({'id': seq_id(),'data_id':id, 'data_type': TaskDataType.APPLICATION_PUBLISH_SYNC.value, 'tenant_code': self.project_code})
            fields = ['id','data_id','data_type','tenant_code']
            repository.add_list_data('dap_bi_specific_task_record', records, fields)
            raise e