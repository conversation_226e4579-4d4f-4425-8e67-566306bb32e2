#!/usr/bin/env python
# -*- coding: utf-8 -*-
# pylint: disable=W0703,W1202,W1201,W1401
import json
import sys
from dmplib.utils.errors import UserError
from base import repository
from base.enums import ProjectValueAddedFunc
from loguru import logger
from components.ppt_api import PptApi
from user.services.user_service import DEFAULT_USER_ID


class FactoryBizLink:

    BIZ_API_MAP = {
        # 在线报告
        ProjectValueAddedFunc.PPT.value: PptApi,
        # 明细报告
        ProjectValueAddedFunc.ACTIVE_REPORT.value: PptApi,
    }

    @staticmethod
    def get_instance(biz_type, project_code=None, user_id=None):
        try:
            if not biz_type:
                raise UserError(message="增值业务标识不能为空")
            if biz_type in FactoryBizLink.BIZ_API_MAP and FactoryBizLink.BIZ_API_MAP.get(biz_type):
                obj = FactoryBizLink.BIZ_API_MAP.get(biz_type)
            else:
                raise UserError(message=f'未知的增值业务系统，获取请求实例失败(biz_type:{biz_type})')
            account = user_name = ""
            if user_id:
                if user_id == DEFAULT_USER_ID:
                    account = user_name = 'dmp_admin'
                else:
                    # 兼容获取配置库用户信息
                    user_table_name = "dap_p_user" if project_code else "dap_p_admin_users"
                    user = repository.get_data(user_table_name, conditions={"id": user_id}, fields=None)
                    if user:
                        account = user.get('account')
                        user_name = user.get('name')
            return obj(biz_type, project_code, user_id, account, user_name)
        except Exception as e:
            logger.exception(f"增值业务系统请求实例获取失败，err：{str(e)}")
            raise e


class BizLinkService:

    API_LIST_MAP = {
        # 在线报告相关接口
        ProjectValueAddedFunc.PPT.value: {
            # 获取报告树形列表
            'get_report_tree': 'dirTree',
            # 获取报告关联的数据集id
            'get_biz_relation_dataset': 'getDataSetId',
            # 导出报告接口
            'export_report': 'exportReport',
            # 查询指定id的报告接口
            'get_report_by_id': 'findReport',
            # 报告导入接口
            'import_report': 'distribute',

        },
        # 明细报告相关接口
        ProjectValueAddedFunc.ACTIVE_REPORT.value: {
            'get_report_tree': 'report_list',
            'get_biz_relation_dataset': 'query_dataset_ids',
            'export_report': 'export_metadata',
            'get_report_by_id': 'report_list',
            'import_report': 'import_metadata',
            # 新增报告删除接口
            'delete_report': 'delete_report',
            'get_deliver_folder': 'report_center/deliver_folder',
        }
    }

    API_DESC_MAP = {
        'get_report_tree': '获取报告树形列表',
        'get_biz_relation_dataset': '查询报告关联数据集id',
        'export_report': '报告导出接口',
        'get_report_by_id': '查询指定id的报告',
        'import_report': '报告导入接口',
        'delete_report': '报告回收接口',
        'get_deliver_folder': '获取统计报表分发目录',
    }

    def __init__(self, biz_type, project_code=None, user_id=None):
        if not biz_type:
            raise UserError(message="业务系统标识不能为空")
        self.biz_type = biz_type.lower()
        self.project_code = project_code
        self.user_id = user_id
        self.biz_api = self.get_biz_api()
        self.biz_name = PptApi.BIZ_NAME_MAP.get(self.biz_type, "")

    def get_biz_api(self):
        """
        获取增值业务系统的请求实例
        :return:
        """
        try:
            return FactoryBizLink.get_instance(self.biz_type, self.project_code, self.user_id)
        except Exception as e:
            logger.exception(f"获取增值业务系统请求实例获取失败，err：{str(e)}")
            raise e

    def get_report_tree(self, params=None):
        """
        获取报告的列表
        :param params:
        :return:
        """
        method_name = sys._getframe().f_code.co_name
        self.show_api_name(method_name)
        return self.biz_api.get(self.get_api_action_name(method_name), params)

    def get_biz_relation_dataset(self, ids):
        """
        获取报告的相关数据集id列表
        :param ids:
        :return:
        """
        method_name = sys._getframe().f_code.co_name
        params = {
            "ids": ids
        }
        self.show_api_name(method_name)
        return self.request_post(self.get_api_action_name(method_name), params)

    def export_report(self, export_id, ids):
        """
        报告的导出接口
        :return:
        """
        params = {"export_id": export_id, "ids": ids}
        method_name = sys._getframe().f_code.co_name
        self.show_api_name(method_name)
        return self.request_post(self.get_api_action_name(method_name), params)

    def get_report_by_id(self, ids):
        """
        根据报告id获取报告列表
        :param ids:
        :return:
        """
        method_name = sys._getframe().f_code.co_name
        params = {"ids": ids}
        self.show_api_name(method_name)
        return self.request_post(self.get_api_action_name(method_name), params)

    def import_report(self, ids, import_type, import_id, import_data, is_all_project=False, is_sync=False):
        """
        报告的导入接口
        :param ids: 选择的报告id列表
        :param import_type: 类型，import：导入，deliver：分发
        :param import_id: 导入或分发id
        :param import_data: {
            tenant_code_list: 目标租户，如果是空列表则为当前租户,
            file_url: 导入分发的oss文件url,
            target_dashboard_folder_id: 目标文件夹id
            source_project: 原租户code
        }
        :param is_all_project: 是否全部租户
        :return:
        """

        import_params = self.get_report_import_params(ids, import_type, import_id, import_data, is_all_project,is_sync)
        method_name = sys._getframe().f_code.co_name
        self.show_api_name(method_name)
        return self.request_post(self.get_api_action_name(method_name), import_params)

    def delete_report(self, ids, tenant_code):
        """
        统计报告的回收接口
        :param ids: 选择的报告id字符串，用,连接。例如：123123,22222,333333
        :param tenant_code: 租户code
        :return:
        """
        delete_params = {
            "tenant_code": tenant_code,
            "dashboard_ids": ids
        }
        method_name = sys._getframe().f_code.co_name
        self.show_api_name(method_name)
        return self.request_post(self.get_api_action_name(method_name), delete_params)

    @staticmethod
    def get_report_import_params(ids, import_type, import_id, import_data, is_all_project=False, is_sync=False):
        """
        导入报告的接口参数获取
        :param ids: 报告的id列表
        :param import_type: 类型，import：导入，deliver：分发
        :param import_id: 导入或分发id
        :param import_data: {
            tenant_code_list: 目标租户，如果是空列表则为当前租户,
            file_url: 导入分发的oss文件url,
            target_dashboard_folder_id: 目标文件夹id
            source_project: 原租户code
        }
        :param is_all_project: 是否全部租户
        :return:
        """
        tenant_code = import_data.get("tenant_code_list", [])
        file_url = import_data.get("file_url", "")
        target_dashboard_folder_id = import_data.get("target_dashboard_folder_id", "")
        source_project = import_data.get("source_project", "")
        import_params = {
            "action_type": import_type,
            "tenant_code": tenant_code if tenant_code else [],
            "is_all": is_all_project,
            "import_id": import_id,
            "ids": ids,
            "is_sync": is_sync,
            "import_data_oss_url": file_url,
            "target_folder_id": target_dashboard_folder_id,
            "source_tenant_code": source_project,
            "source_user_id": import_data.get("source_user_id", ""),
            "is_report_center": import_data.get("is_report_center", False)
        }
        return import_params

    def request_post(self, action_name, params):
        """
        请求接口
        :param action_name:
        :param params:
        :return:
        """
        result = self.biz_api.post(action_name, params)
        logger.info("post result:"+json.dumps(result, ensure_ascii=False))
        return result

    def show_api_name(self, method_name):
        """
        api请求接口名称
        :param method_name:
        :return:
        """
        desc = self.API_DESC_MAP.get(method_name, "")
        logger.info(f"{self.biz_name}{desc}")

    def get_api_action_name(self, method_name):
        """
        获取请求业务系统的接口名称
        :param method_name:
        :return:
        """
        api_map = BizLinkService.API_LIST_MAP.get(self.biz_type)
        action_name = api_map.get(method_name)
        if not action_name:
            raise UserError(message="业务系统不支持的接口，请求错误")
        return action_name

    def get_deliver_folder(self):
        method_name = sys._getframe().f_code.co_name
        params = {}
        self.show_api_name(method_name)
        return self.biz_api.get(self.get_api_action_name(method_name), params)

