#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import collections
import logging
import json
import xmltodict
import re
from dmplib import redis
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from issue_dashboard import DELIVER_STATUS_RUNNING, DELIVER_STATUS_SUCCESS, DELIVER_STATUS_FAILURE
from issue_dashboard.repositories import import_repository
from issue_dashboard.services import dataset_service
from issue_dashboard.services import deliver_service
from issue_dashboard.services import application_service
from issue_dashboard.services import feed_service
from issue_dashboard.services import backup_before_import
from . import dashboard_service
from base.enums import ApplicationDistributeType, DashboardType, ImportFileType
from issue_dashboard.models import DashboardRestoreResultModel

logger = logging.getLogger(__name__)


def import_data(**kwargs):
    """
    提供给celery的报告数据集导入函数
    :return:
    """
    from issue_dashboard.services.deliver_service import ErrorDeliverDashboard, ErrorDeliverDataset, post_process

    project_code = kwargs.get("project_code", None)
    dashboard_import_id = kwargs.get('dashboard_import_id')
    file_url = kwargs.get('file_url')
    file_type = kwargs.get('file_type', ImportFileType.ZIP.value if not file_url.lower().endswith(
        '.rptx') else ImportFileType.RPTX.value)
    select_import_data = kwargs.get('select_import_data') if kwargs.get('select_import_data') else {}
    target_dashboard_folder_id = kwargs.get('target_dashboard_folder_id')
    target_large_screen_folder_id = kwargs.get('target_large_screen_folder_id')
    target_data_reporting_folder_id = kwargs.get('target_data_reporting_folder_id')
    target_dataset_folder_id = kwargs.get('target_dataset_folder_id')
    # 是否包含文件夹
    include_dashboard_folder = kwargs.get('include_dashboard_folder', False)
    include_large_screen_folder = kwargs.get('include_large_screen_folder', False)
    include_data_reporting_folder = kwargs.get('include_data_reporting_folder', False)
    include_dataset_folder = kwargs.get('include_dataset_folder', False)

    if not project_code:
        logger.error("[导入报告] project_code为空")
        return

    if not dashboard_import_id:
        logger.error("[导入报告] dashboard_import_id为空")
        return

    # 备份导出的资源
    backup_before_import.backup_import_resources(project_code,**select_import_data)
    # 更新状态
    import_repository.update_import_status(project_code, dashboard_import_id, DELIVER_STATUS_RUNNING, "")
    try:
        # 如果是报表1.0，则只解析数据集
        if file_type == ImportFileType.RPTX.value:
            zip_meta = {}
        else:
            zip_meta = deliver_service.parse_data(file_url)
            if not zip_meta:
                raise Exception("[导入报告] 没有读取到zip包的数据, zip文件地址: %s" % file_url)
        is_new_jump = zip_meta.get('is_new_jump')
        export_excel_data = zip_meta.get('export_excel_data') or []
        prepare_datasets(zip_meta.get('datasets', None), select_import_data.get('data_source', {}), project_code)
        dashboards = filter_dashboards(
            target_dashboard_folder_id,
            zip_meta.get('dashboards'),
            select_import_data.get("dashboards", []),
            include_folder=include_dashboard_folder,
            project_code=project_code
        )
        large_screens = filter_dashboards(
            target_large_screen_folder_id,
            zip_meta.get('large_screens'),
            select_import_data.get("large_screens", []),
            include_folder=include_large_screen_folder,
            project_code=project_code
        )
        data_reporting = filter_dashboards(
            target_data_reporting_folder_id,
            zip_meta.get('data_reporting'),
            select_import_data.get("data_reporting", []),
            include_folder=include_data_reporting_folder,
            project_code=project_code
        )

        if file_type == ImportFileType.RPTX.value:
            # 报表1.0会自行构建数据源和数据集
            datasets = get_datasets_from_report_1_0(file_url, select_import_data, target_dataset_folder_id)
        else:
            datasets = filter_datasets(
                target_dataset_folder_id,
                zip_meta.get('datasets'),
                select_import_data.get("datasets", []),
                include_folder=include_dataset_folder,
                project_code=project_code
            )
        applications = filter_applications(
            zip_meta.get('applications'),
            select_import_data.get("applications", [])
        )
        feeds, feed_list = filter_feeds(
            zip_meta.get('feeds'),
            select_import_data.get('feeds', [])
        )
        if dashboards:
            try:
                # 导入dashboard
                dashboard_service.deliver_dashboard_data(
                    project_code,
                    dashboards,
                    0,
                    target_dashboard_folder_id=target_dashboard_folder_id,
                    is_import=True,
                    include_folder=include_dashboard_folder,
                    is_new_jump=is_new_jump,
                )
            except BaseException as dae:
                logging.exception(dae)
                import_repository.update_import_status(project_code, dashboard_import_id, DELIVER_STATUS_FAILURE,
                                                       dae.__str__())
                raise ErrorDeliverDashboard('导入报告出错! error: %s' % dae.__str__())
        if large_screens:
            try:
                # 导入酷炫大屏
                dashboard_service.deliver_large_screen_data(
                    project_code,
                    large_screens,
                    distribute_type=0,
                    target_large_screen_folder_id=target_large_screen_folder_id,
                    is_import=True,
                    include_large_screen_folder=include_large_screen_folder,
                    is_new_jump=is_new_jump,
                )
            except BaseException as dae:
                logging.exception(dae)
                import_repository.update_import_status(project_code, dashboard_import_id, DELIVER_STATUS_FAILURE,
                                                       dae.__str__())
                raise ErrorDeliverDashboard('导入酷炫大屏出错! error: %s' % dae.__str__())
        if data_reporting:
            try:
                # 导入数据报表
                dashboard_service.deliver_data_reporting_data(
                    project_code,
                    data_reporting,
                    distribute_type=0,
                    target_data_reporting_folder_id=target_data_reporting_folder_id,
                    is_import=True,
                    include_data_reporting_folder=include_data_reporting_folder,
                    is_new_jump=is_new_jump,
                )
            except BaseException as dae:
                logging.exception(dae)
                import_repository.update_import_status(project_code, dashboard_import_id, DELIVER_STATUS_FAILURE,
                                                       dae.__str__())
                raise ErrorDeliverDashboard('导入数据报表出错! error: %s' % dae.__str__())
        if datasets:
            try:
                # 导入dataset
                dataset_service.multi_import_dataset_data(
                    project_code,
                    datasets,
                    parent_id=target_dataset_folder_id,
                    is_import=True,
                    include_folder=include_dataset_folder,
                    oss_file=file_url,
                    export_excel_data=export_excel_data,
                )
            except BaseException as dse:
                logging.exception(dse)
                import_repository.update_import_status(project_code, dashboard_import_id, DELIVER_STATUS_FAILURE,
                                                       dse.__str__())
                raise ErrorDeliverDataset('导入数据集出错! error: %s' % dse.__str__())
        if applications:
            try:
                import_distribute_type = ApplicationDistributeType.ImportAndNotLock.value
                application_service.ApplicationDeliverHandler(project_code, applications,
                                                              import_distribute_type).deliver()
            except Exception as e:
                logging.exception(e)
                import_repository.update_import_status(project_code, dashboard_import_id, DELIVER_STATUS_FAILURE,
                                                       e.__str__())
                raise ErrorDeliverDataset('导入门户出错! error: %s' % e.__str__())
        if feeds:
            try:
                feed_service.FeedDeliverHandler(project_code, feeds, feed_list).import_feed()
            except Exception as e:
                logging.exception(e)
                import_repository.update_import_status(project_code, dashboard_import_id, DELIVER_STATUS_FAILURE,
                                                       e.__str__())
                raise ErrorDeliverDataset('导入简讯出错! error: %s' % e.__str__())
        # 更新状态
        import_repository.update_import_status(project_code, dashboard_import_id, DELIVER_STATUS_SUCCESS, 'success')
        # 导入完成后执行sql
        post_process(project_code)
    except BaseException as e:
        logger.exception(e)
        # 更新状态
        import_repository.update_import_status(project_code, dashboard_import_id, DELIVER_STATUS_FAILURE, e.__str__())
    finally:
        # 关闭连接
        conn = get_db(project_code)
        conn.end()


def prepare_datasets(datasets: list, data_source: dict, project_code):
    if not datasets:
        return
    data_source = check_dataset_source(datasets, data_source, project_code)
    for dataset in datasets:
        try:
            if not dataset.get("data_source"):
                continue
            source_id = dataset.get('data_source', {}).get('id', None)
            if source_id and data_source.get(source_id, None):
                dataset['data_source'] = data_source.get(source_id)
            content = json.loads(dataset['dataset']['content']) if dataset.get('dataset', {}).get('content', None) \
                else dict()
            data_source_id = content.get('data_source_id', None)
            if data_source_id and data_source.get(data_source_id, None):
                content['data_source_id'] = data_source.get(data_source_id).get('id')
                dataset['dataset']['content'] = json.dumps(content)
        except Exception as e:
            logger.exception(e)
            continue


def convert_report_1_0_ds_define_sql(sql, dataset_id, variable_filter):
    """
    转换报表1.0sql，并返回变量
    """
    variables = dict()
    if variable_filter is not None:
        for report_var in variable_filter:
            value_type = None
            var_name = report_var
            pattern = r'\bin\s*\(\s*@' + var_name + r'\s*\)'
            replacement = r'in ${' + var_name + '}'
            new_sql, count = re.subn(pattern, replacement, sql, flags=re.IGNORECASE)
            if count > 0:
                value_type = 1
                sql = new_sql
            if value_type is None:
                pattern = r'@' + var_name + r'(?![\w\u4e00-\u9fff])'
                replacement = r'${' + var_name + '}'
                new_sql, count = re.subn(pattern, replacement, sql, flags=re.IGNORECASE)
                if count > 0:
                    value_type = 2
                    sql = new_sql
            if value_type is not None and var_name not in variables:
                variables[var_name] = {
                    'id': seq_id(),
                    'name': var_name,
                    'var_type': 1,
                    'value_type': value_type,
                    'accurate_type': 1,
                    'dataset_id': dataset_id,
                    'default_value': '[\"\"]' if value_type == 1 else '\"\"',
                    'value_source': 'userdefined',
                    'default_value_type': value_type
                }
    else:

        pattern = r'\(\s*\[(?P<name>[^\]]+)\]\s*\)'
        matches = []

        def multi_parameter_replacement(match):
            name = match.group("name").strip()
            matches.append({
                'name': name,
                'value_type': 1,
                'default_value': '\"\"',
                'default_value_type': 1
            })
            return "${" + name + "}"

        replaced_sql = re.sub(pattern, multi_parameter_replacement, sql, flags=re.IGNORECASE)
        sql = replaced_sql
        pattern = r'\[\s*(?P<name>[^\]]+)\s*\]'

        def single_parameter_replacement(match):
            name = match.group("name").strip()
            matches.append({
                'name': name,
                'value_type': 2,
                'default_value': '[\"\"]',
                'default_value_type': 2
            })
            return "${" + name + "}"

        replaced_sql = re.sub(pattern, single_parameter_replacement, sql, flags=re.IGNORECASE)
        sql = replaced_sql
        for match_variable in matches:
            variable_name = match_variable['name']
            if variable_name not in variables:
                variables[variable_name] = {
                    'id': seq_id(),
                    'name': variable_name,
                    'var_type': 1,
                    'value_type': match_variable['value_type'],
                    'accurate_type': 1,
                    'dataset_id': dataset_id,
                    'default_value': match_variable['default_value'],
                    'value_source': 'userdefined',
                    'default_value_type': match_variable['default_value_type']
                }

    return sql, list(variables.values())


def get_datasets_from_report_1_0(file_url, selected_data, target_folder_id):
    """
    解析报表1.0数据集，并转换为可导入结构
    """

    def report_1_0_postprocessor(path, key, value):
        if isinstance(value, dict) and '@p4:nil' in value and value['@p4:nil'] == 'true':
            return key, None
        return key, value

    file_data = deliver_service.read_file_from_oss(file_url)
    report_package_dict = xmltodict.parse(file_data, postprocessor=report_1_0_postprocessor, encoding='utf-8')
    report_package_reports = report_package_dict["ReportPackage"].get("Reports", {})
    report_details = report_package_reports.get("ReportDetail", []) if report_package_reports else []
    if isinstance(report_details, collections.OrderedDict):
        report_details = [report_details]
    report_package_keywords = report_package_dict["ReportPackage"].get("ReportKeywords", {})
    report_keywords = report_package_keywords.get("ReportKeyword", []) if report_package_keywords else []
    if isinstance(report_keywords, collections.OrderedDict):
        report_keywords = [report_keywords]
    data_source = selected_data['data_source']

    dataset_mapping = {}
    result = []
    for item in selected_data['datasets']:
        dataset_mapping[item['name']] = item['id']
    for report_detail in report_details:
        ds_define_json = report_detail["DsDefine"]
        ds_define_list = json.loads(ds_define_json)
        for ds in ds_define_list:
            ds_key = f'{report_detail["RptCName"]}_{ds["Name"]}'
            if ds_key in dataset_mapping:
                dataset_id = dataset_mapping[ds_key]
                sql, variables = convert_report_1_0_ds_define_sql(ds['CommandText'], dataset_id,
                                                                  [item['Name'] for item in
                                                                   json.loads(report_detail['RptVar'])])
                import_data_set = create_import_dataset_model(dataset_id, ds_key,
                                                              data_source[ds["DataSourceGuid"]]['id'], sql,
                                                              target_folder_id,
                                                              variables)
                result.append(import_data_set)

    processed_keyword_set = dict()
    for report_keyword in report_keywords:
        keyword_name = f'{report_keyword["DataSourceName"]}_{report_keyword["MyKeywordName"]}'
        if keyword_name not in processed_keyword_set:
            if keyword_name in dataset_mapping:
                dataset_id = dataset_mapping[keyword_name]
                sql, variables = convert_report_1_0_ds_define_sql(report_keyword['SqlText'], dataset_id, None)
                import_data_set = create_import_dataset_model(dataset_id, keyword_name,
                                                              data_source[report_keyword["DataSourceGuid"]]['id'], sql,
                                                              target_folder_id, variables)
                result.append(import_data_set)
    return result


def create_import_dataset_model(dataset_id, name, data_source_id, sql, folder_id, variables):
    """
    报表1.0构建导入数据集
    """
    import_data_set = {
        'dataset': {
            'id': dataset_id,
            'name': name,
            'type': 'SQL',
            'edit_mode': 'sql',
            'connect_type': '直连',
            'content': json.dumps({
                'data_source_id': data_source_id,
                'sql': sql,
                'bind_source_id': data_source_id
            }),
            'parent_id': folder_id,
            'is_complex': 1,
            'is_lock': 0,
            'clear_time': 30,
            'disable_procedure': 0,
            'is_need_procedure': 2,
        },
        'dataset_id': dataset_id,
        'dataset_vars': variables
    }
    return import_data_set


def check_dataset_source(datasets, datasource, project_code):
    source_type = 'MysoftNewERP'
    with get_db(project_code) as db:
        for dataset in datasets:
            data_source = dataset.get('data_source') or {}
            if data_source.get('type') == source_type:
                data_source_id = data_source.get('id')
                app_code = data_source.get('app_level_code')
                # 是否已经设置了数据源映射，找到了映射直接跳过
                if datasource.get(data_source_id):
                    continue
                # 查询导入的租户中是否含有对应类型的数据源
                result = db.query_one(
                    'select id from dap_m_data_source where id = %(id)s and app_level_code = %(app_code)s',
                    {'id': data_source_id, 'app_code': app_code}
                )
                if result:
                    continue
                result = db.query_one(
                    'select * from dap_m_data_source where app_level_code = %(id)s and type = %(type)s',
                    {'id': app_code, 'type': source_type}
                )
                if result:
                    datasource[data_source_id] = result
    return datasource


def filter_dashboards(target_dashboard_folder_id: str, dashboards: dict, select_dashboards: list,
                      include_folder=False, project_code=''):
    new_dashboards = {}

    if not dashboards or not select_dashboards:
        return new_dashboards

    if not include_folder:
        for dashboard in dashboards.values():
            dashboard["dap_bi_dashboard_folders"] = []
            if dashboard.get("dashboard") and dashboard.get("dashboard")[0].get(
                    'type') != DashboardType.CHILD_FILE.value:
                dashboard.get("dashboard")[0]['parent_id'] = target_dashboard_folder_id

    new_dashboard_ids = []
    for select_dashboard in select_dashboards:
        if dashboards.get(select_dashboard.get("id")):
            new_dashboards[select_dashboard.get("id")] = dashboards.get(select_dashboard.get("id"))
            new_dashboard_ids.append(select_dashboard.get("id"))

    if new_dashboard_ids and len(new_dashboard_ids) > 0 and project_code:
        with get_db(project_code) as db:
            distribute_type_list = db.query("select id, distribute_type from dap_bi_dashboard where id in %(id_list)s",
                                            {'id_list': new_dashboard_ids})
            print(distribute_type_list)
            for row in distribute_type_list:
                if new_dashboards.get(row.get("id")):
                    dashboard_list = new_dashboards[row.get("id")]["dashboard"]
                    for dashboard in dashboard_list:
                        if dashboard.get('id') and dashboard.get('id') == row.get("id"):
                            dashboard["distribute_type"] = row.get("distribute_type")
    return new_dashboards


def filter_dashboards_for_install(target_dashboard_folder_id: str, dashboards: dict, include_folder=False):
    if not dashboards:
        return dashboards

    if not include_folder:
        for dashboard in dashboards.values():
            dashboard["dap_bi_dashboard_folders"] = []
            dashboard.get("dashboard")[0]['parent_id'] = target_dashboard_folder_id

    return dashboards


def filter_datasets(target_dataset_folder_id: str, datasets: list, select_datasets: list, include_folder=False,
                    project_code=''):
    new_datasets = []

    if not datasets or not select_datasets:
        return new_datasets

    if not include_folder:
        for dataset in datasets:
            dataset["dataset_folders"] = []
            dataset.get("dataset")["parent_id"] = target_dataset_folder_id

    new_dataset_ids = []
    new_dataset_map = {}
    for select_dataset in select_datasets:

        for dataset in datasets:
            if dataset.get("dataset_id") == select_dataset.get("id"):
                new_datasets.append(dataset)
                new_dataset_ids.append(dataset.get("dataset_id"))
                new_dataset_map[dataset.get("dataset_id")] = dataset
                if dataset.get("dataset"):
                    dataset["dataset"]["is_lock"] = 0
                break

    if new_dataset_ids and len(new_dataset_ids) > 0 and project_code:
        with get_db(project_code) as db:
            dataset_lock_list = db.query("select id,is_lock  from dap_bi_dataset where id in %(id_list)s",
                                         {'id_list': new_dataset_ids})
            print(dataset_lock_list)
            for row in dataset_lock_list:
                if new_dataset_map.get(row.get("id")):
                    dataset = new_dataset_map[row.get("id")]["dataset"]
                    dataset["is_lock"] = row.get("is_lock")
    return new_datasets


def filter_applications(applications: dict, select_applications: list):
    new_applications = {}
    if not applications or not select_applications:
        return new_applications
    for select_application in select_applications:
        if applications.get(select_application.get("id")):
            new_applications[select_application.get("id")] = applications.get(select_application.get("id"))
    return new_applications


def filter_feeds(feeds: dict, select_feeds: list):
    select_feeds_list = [feed.get('id') for feed in select_feeds] or []
    table_field_config = {
        'dashboard_email_subscribe': 'id', 'dashboard_subscribe_display_format': 'subscribe_id',
        'mobile_subscribe_filter': 'email_subscribe_id',
        'mobile_subscribe_rules': 'email_subscribe_id', 'mobile_subscribe_chapters': 'email_subscribe_id', 'flow': 'id',
        'tag_relation': 'relation_id',
    }
    new_feeds = {}
    if not select_feeds_list or not feeds:
        return new_feeds, select_feeds_list
    for table, feed in feeds.items():
        if not new_feeds.get(table):
            new_feeds.update({table: []})
        field_key = table_field_config.get(table)
        if not field_key:
            continue
        for row in feed:
            if row.get(field_key) in select_feeds_list:
                new_feeds[table].append(row)
    return new_feeds, select_feeds_list


def filter_datasets_for_install(target_dataset_folder_id: str, datasets: list, include_folder=False):
    if not datasets:
        return datasets

    if not include_folder:
        for dataset in datasets:
            dataset["dataset_folders"] = []
            dataset.get("dataset")["parent_id"] = target_dataset_folder_id

    return datasets


def import_data_for_install(**kwargs):
    """
    提供给celery的报告数据集安装入口
    :return:
    """
    from issue_dashboard.services.deliver_service import ErrorDeliverDashboard, ErrorDeliverDataset, post_process

    project_code = kwargs.get("project_code", None)
    file_url = kwargs.get('file_url')
    target_dashboard_folder_id = kwargs.get('target_dashboard_folder_id')
    target_dataset_folder_id = kwargs.get('target_dataset_folder_id')
    package_data = kwargs.get("package_data", None)

    if not project_code:
        logger.error("[安装报告] project_code为空")
        return

    if not target_dashboard_folder_id or not target_dataset_folder_id:
        logger.error("[安装报告] 目标报告文件夹或目标数据集文件夹不存在")
        return

    try:
        if not package_data:
            raise Exception("[安装报告] 没有读取到zip包的数据, zip文件地址: %s" % file_url)
        dashboards = filter_dashboards_for_install(target_dashboard_folder_id, package_data.get('dashboards'))
        datasets = filter_datasets_for_install(target_dataset_folder_id, package_data.get('datasets'))
        is_new_jump = package_data.get('is_new_jump')
        if dashboards:
            try:
                # 导入dashboard
                dashboard_service.deliver_dashboard_data(
                    project_code,
                    dashboards,
                    0,
                    target_dashboard_folder_id=target_dashboard_folder_id,
                    is_import=True,
                    include_folder=False,
                    is_new_jump=is_new_jump,
                )
            except BaseException as dae:
                logging.exception(dae)
                raise ErrorDeliverDashboard('安装报告出错! error: %s' % dae.__str__())
        if datasets:
            try:
                # 导入dataset
                dataset_service.multi_import_dataset_data(
                    project_code, datasets, parent_id=target_dataset_folder_id, is_import=True, include_folder=False,
                    oss_file=file_url,
                )
            except BaseException as dse:
                logging.exception(dse)
                raise ErrorDeliverDataset('安装数据集出错! error: %s' % dse.__str__())
    except BaseException as e:
        logger.exception(e)
    finally:
        # 关闭连接
        conn = get_db(project_code)
        conn.end()


def restore_dashboard_design_data(**kwargs):
    """
    提供给celery的报告运行时数据还原到设计时逻辑
    :return:
    """
    project_code = kwargs.get("project_code", None)
    dashboard_id = kwargs.get('dashboard_id')
    task_id = kwargs.get('task_id')
    is_new_jump = kwargs.get('is_new_jump')
    # 操作报告还原的修改者
    modified_account = kwargs.get('modified_account')

    try:
        if not project_code:
            raise UserError(message="[报告还原] project_code为空")

        if not dashboard_id:
            raise UserError(message="[报告还原] dashboard_id为空")

        # 更新状态为运行中
        _set_dashboard_restore_status(project_code, task_id, 0, DELIVER_STATUS_RUNNING)

        # 查找报告在当前租户是否存在
        dashboard_rs = import_repository.get_dashboard_by_id(project_code, dashboard_id)
        if not dashboard_rs:
            raise UserError(message="报告不存在，不能还原")
        target_dashboard_folder_id = dashboard_rs.get("parent_id")
        # 获取报告的设计时数据进行还原
        released_design_data = import_repository.get_dashboard_released_design_data(project_code, dashboard_id)
        if released_design_data:
            try:
                restore_info = {
                    'is_restore': True,
                    'modified_account': modified_account
                }
                dashboard_data_json = released_design_data.get("dashboard_data")
                dashboard_data = json.loads(dashboard_data_json)
                deal_restore_dashboard_type(dashboard_data, dashboard_rs)
                # 还原报告
                dashboard_service.deliver_dashboard_data(
                    project_code,
                    dashboard_data,
                    0,
                    target_dashboard_folder_id=target_dashboard_folder_id,
                    is_import=True,
                    include_folder=False,
                    is_new_jump=is_new_jump,
                    extra_info=restore_info
                )

            except BaseException as dae:
                logging.exception(dae)
                raise UserError(message='报告还原出错! error: %s' % dae.__str__())

        # 更新状态为成功
        _set_dashboard_restore_status(project_code, task_id, 1, DELIVER_STATUS_SUCCESS)
    except BaseException as e:
        err_msg = str(e)
        err_msg.replace('分发', '还原')
        logger.exception(e)
        # 更新为失败状态
        _set_dashboard_restore_status(project_code, task_id, 2, DELIVER_STATUS_FAILURE, err_msg)
    finally:
        # 关闭连接
        conn = get_db(project_code)
        conn.end()


def deal_restore_dashboard_type(dashboard_data: dict, parent_dashboard):
    """
    处理报告还原的类型，报告还原不修改现在的报告类型
    父子报告一起同时处理
    """
    platform = parent_dashboard.get('platform')
    new_layout_type = parent_dashboard.get('new_layout_type')
    application_type = parent_dashboard.get('application_type')

    for dashboard_id, table_datas in dashboard_data.items():
        for table_name, datas in table_datas.items():
            if table_name == 'dashboard':
                for data in datas:
                    data['platform'] = platform
                    data['new_layout_type'] = new_layout_type
                    data['application_type'] = application_type


def _set_dashboard_restore_status(project_code, task_id, status, msg, error_msg=''):
    """
    更新报告还原任务状态
    :param project_code:
    :param task_id:
    :param status:
    :param msg:
    :param error_msg:
    :return:
    """
    dashboard_restore_model = DashboardRestoreResultModel(task_id=task_id)
    dashboard_restore_model.status = status
    dashboard_restore_model.msg = msg
    if error_msg:
        dashboard_restore_model.error_msg = error_msg
    task_id = _set_task_id_project_code(project_code, task_id)
    _set_task_data(task_id, dashboard_restore_model)


def _set_task_data(task_id, dashboard_restore_model: DashboardRestoreResultModel):
    """
    任务数据写入Redis
    :param task_id:
    :param dashboard_restore_model:
    :return:
    """
    redis.conn().set(task_id,
                     value=redis.RedisCache._dumps(dashboard_restore_model.get_dict()),
                     time=60 * 60)


def _set_task_id_project_code(project_code, task_id):
    # 因dmp-admin导入时g.code 无值，task Redis数据写入时不会加上租户code前缀。这里手动加上
    task_id = project_code + ':' + task_id
    return task_id
