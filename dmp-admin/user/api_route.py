#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/8/4.
"""

from dmplib.hug import APIWrapper
from user.models import UserLoginModel, UserModel, UserQueryModel
from user.services import user_service

api = APIWrapper(__name__)


@api.route.post('/login')
def login(request, response, **kwargs):
    """
    用户登录
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    model = UserLoginModel(**kwargs)
    model.request = request
    model.response = response
    if model.account:
        model.account = model.params_decrypt(model.account)
    if model.password:
        model.password = model.params_decrypt(model.password)
    if not model.sign:
        return False, '验证码不合法，请重新生成', ''
    return True, '登录成功', user_service.login(model)


@api.route.post('/captcha')
def generate_captcha():
    """
    获取验证码
    :return:str base64
    """
    return True, '获取验证码成功', user_service.generate_captcha()


@api.route.post('/check_need_captcha')
def check_need_captcha(request, response, **kwargs):
    """
    校验是否需要验证码
    :param kwargs: account用户名 tenant_code企业代码
    :return:str  1 0
    """
    model = UserLoginModel(**kwargs)
    model.request = request
    model.response = response
    return True, '', user_service.check_need_captcha(model)


@api.admin_route.post('/logout')
def logout(request, response):
    """
    用户登出
    :param request:
    :param response:
    :return:
    """
    return True, '登出成功', user_service.logout(request, response)


@api.admin_route.post('/add')
def add_user(**kwargs):
    """
    添加用户
    :return:
    """
    model = UserModel(**kwargs)
    return True, '添加成功', user_service.add_user(model)


@api.admin_route.post('/save')
def add_user(**kwargs):
    """
    添加用户
    :return:
    """
    model = UserModel(**kwargs)
    return True, '添加成功', user_service.save_user(model)


@api.admin_route.post('/delete')
def add_user(**kwargs):
    """
    删除用户
    :return:
    """
    user_id = kwargs.get('user_id')
    if not user_id:
        return False, '请选择删除用户', []
    return True, '删除成功', user_service.delete_user(user_id)


@api.admin_route.get('/list')
def get_project_list(**kwargs):
    """
    获取项目列表
    :param kwargs:
    :return:
    """
    return True, None, user_service.get_list(UserQueryModel(**kwargs)).get_result_dict()


@api.admin_route.get('/role_list')
def get_role_list():
    """
    获取管理员类型列表
    :return:
    """
    return True, None, user_service.get_role_list()
