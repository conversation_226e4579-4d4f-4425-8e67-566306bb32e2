#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401
import os
import json
from loguru import logger
import traceback
import datetime
from abc import ABCMeta, abstractmethod
from functools import wraps

from base.constants import APP_NAME

from dmplib.components import auth_util
from dmplib.hug import g
from dmplib import config
from dmplib.utils.fast_logger import FastLoggerUtil, fast_logger_instance

from base.models import BaseModel

"""
天眼日志记录处理类
扩展新的日志类型：
建立一个新的日志实现类，继承 BaseFastLogger 基类。
在子类实现 set_log_name 日志类型名称定义，set_folder_name 日志类型文件夹名称定义
在子类定义字段列表，需要与天眼的自定义库的字段一一对应

操作示例：
    from components.fast_logger import FastLogger
    data = {
        'action': '测试',
        'env_code': '111',
        'org_code': '2222',
        'api_url': 'http://dmp-test5.mypaas.com.cn/dataview-mobile/design/39f47aa0-a221-6234-2d47-fcb205011251/3a04828c-ac69-bd23-6570-60f8a0e5c109',
    }
    FastLogger.ApiFastLogger(**data).record()
"""


class BaseFastLogger(BaseModel, metaclass=ABCMeta):
    def __init__(self, **kwargs):
        try:
            self._init_log()
            self._logger_instance = fast_logger_instance(self._log_name, self._log_folder_name)  # type: Logger
            super().__init__(**kwargs)
        except Exception as e:
            logger.error(f"天眼日志对象实例错误，errs：{str(e)} , traceback: \n{traceback.format_exc()}")

    def _init_log(self):
        log_name = self.set_log_name()
        self._log_name = self._add_log_name_prefix(log_name)
        self._log_folder_name = self.set_folder_name()

    @abstractmethod
    def set_log_name(self):
        raise Exception("该方法只能在子类实例上调用！")

    @abstractmethod
    def set_folder_name(self):
        raise Exception("该方法只能在子类实例上调用！")

    @staticmethod
    def _add_log_name_prefix(name):
        return FastLoggerUtil.NAME_PREFIX + '_' + name

    @staticmethod
    def _add_default_field(data):
        """
        日志通用附属字段信息
        :param data:
        :return:
        """
        # 租户编码
        if not data.get("org_code"):
            data["org_code"] = g.code if hasattr(g, 'code') else ""
        # 用户账号
        if not data.get("account"):
            data["account"] = g.account if hasattr(g, 'account') else ""
        now = datetime.datetime.now()
        # 环境名称
        data["env_code"] = auth_util.get_env_code()
        # 添加服务名称
        if not data.get("app_name"):
            data["app_name"] = APP_NAME
        # 日志写入时间戳
        data["unix_time"] = int(now.timestamp() * 1000)
        # 日志写入日期时间
        data["date_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

    def record(self):
        """
        日志写入文件
        :return:
        """
        try:
            data = self.get_dict()
            data = json.dumps(data, ensure_ascii=False)
            return self._logger_instance.error(data)
        except Exception as e:
            logger.error(f"天眼日志写入错误，errs：{str(e)} , traceback: \n{traceback.format_exc()}")

    @classmethod
    def decorator(cls, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"天眼日志实例化错误，errs：{str(e)} , traceback: \n{traceback.format_exc()}")

        return wrapper


class OMPTenantsFastLogger(BaseFastLogger):
    """
    租户开户相关
    """
    __slots__ = [
        'app_code',
        'log_type',
        'log_time',
        'log_level',
        'thread',
        'traceId',
        'taskId',
        'msg',
        'stack_trace',
        'extend_json',
    ]

    @BaseFastLogger.decorator
    def __init__(self, **kwargs):
        # 产品编码：跟各BU事先约定好的固定值
        self.app_code = ''
        # 开户日志固定值：tenantlog
        self.log_type = 'tenantlog'
        # 要精确到毫秒，便于精准排序
        self.log_time = ''
        self.log_level = 'INFO'
        self.thread = None
        # 链路id：上游传递（变量名traceId）
        self.traceId = ''
        # 任务Id：上游传递（变量名taskId）
        self.taskId = ''
        self.msg = ''
        self.stack_trace = None
        self.extend_json = {'env_code': '', 'action': 'create'}
        super().__init__(**kwargs)

    # 日志类型名称
    def set_log_name(self):
        return 'omp_tenants'

    # 日志存储文件夹名称
    def set_folder_name(self):
        return 'omp_tenants_out'


class PublishCenterFastLogger(BaseFastLogger):
    """
    业务错误类日志处理
    """
    __slots__ = [
        'module_type',
        'biz_type',
        'biz_id',
        'biz_name',
        'exec_from',
        'error_level',
        'error_type',
        'error_lable1',
        'error_lable2',
        'error_lable3',
        'error_code',
        'error_data_id',
        'error_msg',
        'error_traceback',
        'extra_info',
        'org_code',
        'is_key',
        'is_success'
    ]

    @BaseFastLogger.decorator
    def __init__(self, **kwargs):
        app_code = kwargs.get('appCode') or kwargs.get('app_code') or ""
        app_key = kwargs.get('appKey') or ""
        version = kwargs.get('version') or ""
        self.module_type = '报告分发管理'
        self.biz_type = '更新中心-分发'
        self.biz_id = kwargs.get('taskId') or kwargs.get('deliver_id')
        self.biz_name = app_code + '_' + app_key + "_" + version
        self.exec_from = ''
        self.error_level = ''
        self.error_type = ''
        self.error_lable1 = ''
        self.error_lable2 = ''
        self.error_lable3 = ''
        self.error_code = ''
        self.error_data_id = ''
        self.error_msg = ''
        self.error_traceback = ''
        self.extra_info = ''
        # 租户code
        self.org_code = kwargs.get('envCode')
        self.is_key = 0
        self.is_success = 0
        super().__init__(**kwargs)

    # 日志类型名称
    def set_log_name(self):
        return 'biz_error'

    # 日志存储文件夹名称
    def set_folder_name(self):
        return 'biz_error_data'


class FastLogger:
    """
    日志操作入口类
    """

    OMPTenantsFastLogger = OMPTenantsFastLogger
    PublishCenterFastLogger = PublishCenterFastLogger
