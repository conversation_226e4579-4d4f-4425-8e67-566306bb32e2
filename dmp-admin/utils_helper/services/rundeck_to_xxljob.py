from loguru import logger

from dmplib.db.mysql_wrapper import SimpleMysql
from dmplib import config
from dmplib.components.enums import DBType
from components.xxljob import XxlScheduler
from dmplib.utils.model import BaseModel


class FlowModel(BaseModel):
    """
    流程
    """

    __slots__ = [
        'id',
        'name',
        'description',
        'type',
        'build_in',
        'schedule',
        'status',
        'depend_flow_id',
        'depend_flow_name',
        'nodes',
        'lines',
        'state_trigger',
        'run_status',
    ]

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.name = None
        self.description = None
        self.type = None
        self.build_in = 0
        self.schedule = None
        self.status = '启用'
        self.depend_flow_id = None
        self.depend_flow_name = None
        self.state_trigger = None
        # 流程节点
        self.nodes = None
        # 流程线
        self.lines = None
        self.run_status = None
        super().__init__(**kwargs)


def rundeck_2_xxljob():
    if config.get('DB.db_type') == DBType.DM.value:
        return "当前是达梦环境，不需要处理"
    # 判断当前rundeck数据库名称
    with SimpleMysql(
                host=config.get('DB.host'), port=int(config.get('DB.port')), db='mysql', user=config.get('DB.user'),
                passwd=config.get('DB.password')
            ) as db:
        if db.query_one("show DATABASES where `Database` = 'dmp_rundeck4'"):
            database = 'dmp_rundeck4'
        else:
            database = 'rundeck'
    # 1、查询所有的rundeck任务
    with SimpleMysql(
                host=config.get('DB.host'), port=int(config.get('DB.port')), db=database, user=config.get('DB.user'),
                passwd=config.get('DB.password')
            ) as db:
        task_list = db.query(
            """
            select 
            concat(se.seconds, ' ', se.minute, ' ', se.hour, ' ', se.day_of_month, ' ', se.month, ' ', se.day_of_week, ' ', se.`year`) as cron,
            se.uuid as job_id,
            se.group_path as tenant_code,
            ws.adhoc_remote_string as command
            from scheduled_execution as se 
            inner join workflow_workflow_step as wws on wws.workflow_commands_id=se.workflow_id
            INNER JOIN workflow_step as ws on ws.id=wws.workflow_step_id
            where se.scheduled=1 
            and se.uuid not in ('00000000-1000-0000-0001-000000000000', '00000000-1000-0000-0002-000000000000', '00000000-1000-0000-0003-000000000000')
            and se.group_path not in ('DMP-Sysevent', 'dmp-proc')
            """
        ) or []
        logger.error(f"待迁移的定时任务数量：{len(task_list)}")

        schedule = XxlScheduler(flow_model=FlowModel(
            type=None,
            name=''
        ))

        error_list = []

        for task in task_list:
            job_id = task.get('job_id')
            cron = task.get('cron')
            # tenant_code = task.get('tenant_code')
            command = task.get('command')
            schedule.flow_model.schedule = cron

            try:
                if schedule.job_is_exists(job_id):
                    # schedule.update_job(command=command, job_id=job_id)
                    logger.info("任务已存在， 不更新")
                else:
                    schedule.add_job(command=command, job_id=job_id)
            except Exception as e:
                logger.error(f"任务{job_id}迁移失败，原因：{e}")
                if "调度类型非法" in getattr(e, 'message', ''):
                    continue
                error_list.append({'job_id': job_id, 'error': str(e)})
        logger.error('******************end*********************')
    return {'total': len(task_list), 'error_list': error_list}


