# -*- coding: utf-8 -*-
# @Time : 2022/1/7 16:08
# <AUTHOR> songh02
# @Email : <EMAIL>
# @File : dataset_used_table_service.py
# @Project : dmp-admin


import logging
import os.path
import xlsxwriter
import datetime
import traceback

from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from utils_helper.repositories import dataset_used_table_repository
from dmplib import config
from urllib.parse import urljoin


logger = logging.getLogger(__name__)


def generate_sheet(workbook, data, sheet_name):
    """
    生成sheet
    """
    sheet = workbook.add_worksheet(sheet_name)
    f = workbook.add_format({
        "align": "center",
        "border": 1
    })
    f.set_text_wrap()
    sheet.set_column(1, len(data[0]), 50)
    for i, item in enumerate(data):
        sheet.write_row(i, 0, item, f)


def export_to_table(response, tenants=None, table_name=""):
    """
    导出数据
    """
    import app_celery
    return app_celery.export_data_of_used_table.apply_async(kwargs={"tenants": tenants, "table_name": table_name})
    # app_celery.export_data_of_used_table(tenants=tenants, table_name=table_name)


def get_data(tenants):
    """
    get data
    """
    if tenants:
        if isinstance(tenants, str):
            tenants = tenants.split(',')
    else:
        tenants = dataset_used_table_repository.get_codes()
    data = [["租户", "宽表", "数据集", "报表"]]
    for code in tenants:
        try:
            logger.error(f"租户code: {code}")
            data += get_relation_data(code, "")
        except Exception as e:
            logger.error(f"获取数据错误：{e}")
            logger.error(traceback.format_exc())
    return data


# 0:不限制,1:通过密码,2:限制用户 3:三方控制 4:角色控制
RELEASE_TYPE = {
   0: "不限制",
   1: "通过密码",
   2: "限制用户",
   3: "三方控制",
   4: "角色控制"
}


def get_relation_data(code, name):  # NOSONAR
    """
    get relation data
    """
    data = []
    # 获取关联关系
    dataset_used_table = dataset_used_table_repository.get_dataset_used_table(code) or []
    logger.error(f"dataset_used_table: {len(dataset_used_table)}")
    # 获取数据集关联的报告
    for item in dataset_used_table:
        with_table = item.get("table_name")
        dataset_id = item.get("dataset_id")
        dataset = dataset_used_table_repository.get_dataset(code, dataset_id)
        sql = dataset_used_table_repository.get_query_sql(dataset)
        dashboards = dataset_used_table_repository.get_dashboards(code, dataset_id) or []
        if not dashboards:
            data.append([
                code,
                name,
                with_table,
                dataset_id,
                dataset.get("name", ""),
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                sql
            ])
        for dashboard in dashboards:
            app_name = dataset_used_table_repository.get_application_name(code, dashboard.get("id", ''))
            is_multiple_screen = dashboard.get("is_multiple_screen")
            apply_type = ["dashboard"]
            release_type = "是" if dashboard.get("status") == 1 else "否"
            if app_name:
                apply_type.append("application_portal")
            if is_multiple_screen:
                apply_type.append("multi_screen")
            data.append([
                code,
                name,
                with_table,
                dataset_id,
                dataset.get("name", ""),
                dashboard.get("id", ''),
                release_type,
                RELEASE_TYPE.get(int(dashboard.get("type_access_released", 0)), "不限制") if release_type == "是" else "",
                dashboard.get("dashboard_type", ""),  # 报告类型
                dashboard.get("name", ""),
                "、".join(apply_type),  # 报告应用方式
                urljoin(AppHosts.get(SkylineApps.DP, False),f"bi-visual/dataview/preview/{dashboard.get('id', '')}"),  # 报告url地址
                dashboard.get("parent_name", ""),  # 文件夹名称
                app_name,   # 门户名称
                sql
            ])
    return data


def export_data_excel(response, tenants=None):
    """
    导出数据
    """
    file_name = f"dataset_used_table_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    file_path = os.path.join(os.path.dirname(__file__), "tmp", file_name)
    workbook = xlsxwriter.workbook.Workbook(filename=file_path)

    data = get_data(tenants)

    generate_sheet(workbook, data, "dataset_used_table")
    workbook.close()

    if response:
        response.set_header('Content-Type', 'application/octet-stream;charset=UTF-8')
        response.set_header('Content-disposition', 'attachment; filename=' + file_name)
    with open(file_path, 'rb') as file:
        response.body = file.read()

    os.remove(file_path)


def data_to_table(tenants, table_name):
    from dmplib.saas.project import get_db
    projects = dataset_used_table_repository.get_all_project_code(tenants)
    table_name = dataset_used_table_repository.create_table(table_name)
    for project in projects:
        try:
            code = project.get("code", "")
            name = project.get("name", "")
            logger.error(f"租户code: {code}")
            data = get_relation_data(code, name)
            logger.error(f"租户{code}预计同步{len(data)}条数据")
            dataset_used_table_repository.data_2_table(data, table_name)
            logger.error(f"租户{code}共同步{len(data)}条数据")
        except Exception as e:
            logger.error(f"获取数据错误：{e}")
            logger.error(traceback.format_exc())
        finally:
            get_db(project.get("code", "")).end()

    logger.error("结束。")
