# -*- coding: utf-8 -*-
# @Time : 2022/1/7 16:21
# <AUTHOR> songh02
# @Email : <EMAIL>
# @File : dataset_used_table_repository.py
# @Project : dmp-admin

import logging
import json

from dmplib import config
from base import tenant_repository
from dmplib.conf_constants import IS_GRAY_ENV
from dmplib.redis import RedisCache
from dmplib.saas.project import get_db


logger = logging.getLogger(__name__)


def get_codes():
    codes = config.get('Grayscale.project_list', '')
    gray_codes = codes.split(',') if codes else []
    if IS_GRAY_ENV == 1:
        return gray_codes
    from project.models import ProjectQueryModel
    from project.services import project_service

    # 云客环境租户超过5000个， 修改为取10000个
    page_data = {'page': 1, 'page_size': 10000}
    tenant_data = project_service.get_project_list(ProjectQueryModel(**page_data)).get_result_dict()
    code_list = [item.get("code") for item in tenant_data.get("items")]
    rv = []
    for code in code_list:
        if code not in gray_codes:
            rv.append(code)
    return rv


def get_dataset_used_table(code):
    """
    get dataset_used_table
    """
    return tenant_repository.get_data(code, "dap_bi_dataset_used_table", conditions={}, fields=[], multi_row=True)


def get_dataset(code, dataset_id):
    """
    get dataset.name
    """
    key = f"dataset_used_table:dataset_name:{dataset_id}"
    expire = 30
    cache = RedisCache(key_prefix=code)
    dataset = cache.get_data(key)
    if dataset:
        return dataset
    dataset = tenant_repository.get_data(
        code, "dap_bi_dataset", {"id": dataset_id}, fields=["type", "edit_mode", "content", "name", "relation_sql"]
    )
    cache.set_data(key, dataset)
    cache.expire(key, expire)
    return dataset or {}


def get_dashboards(code, dataset_id):
    """
    get dashboard
    """
    key = f"dataset_used_table:dashboards:{dataset_id}"
    expire = 60
    cache = RedisCache(key_prefix=code)
    dashboards = cache.get_data(key)
    if dashboards:
        return dashboards

    dashboards_chart = tenant_repository.get_data(
        code, "dap_bi_dashboard_chart", {"source": dataset_id}, fields=['dashboard_id'], multi_row=True
    )
    dashboard_ids = [item.get("dashboard_id") for item in dashboards_chart]
    if dashboard_ids:
        with get_db(code) as db:
            dashboard_ids = ",".join([f'"{i}"' for i in dashboard_ids])
            sql = f"""
            select 
            d1.*, 
            d2.name as parent_name,
            (
             case 
                 when d1.platform='pc' and d1.new_layout_type=0 then
                     '大屏'
                 when d1.platform='pc' and d1.new_layout_type=1 then
                     'PC仪表板'
                 when d1.platform='mobile' and d1.new_layout_type=1 then
                     '小屏(新移动)'
                 when d1.platform='mobile' and d1.new_layout_type=0 then
                     '小屏(老移动)'
                 else
                     ''
             end
             ) as dashboard_type
            from dap_bi_dashboard as d1 
            left join dap_bi_dashboard as d2 on d2.id=d1.parent_id 
            where d1.id in ({dashboard_ids})
            group by d1.id
            """
            dashboards = db.query(sql, {"dashboard_ids": dashboard_ids})
    else:
        dashboards = []
    cache.set_data(key, dashboards)
    cache.expire(key, expire)
    return dashboards


def get_application_name(code, dashboard_id):
    """
    获取门户名称
    """
    key = f"dataset_used_table:app:{dashboard_id}"
    expire = 60
    cache = RedisCache(key_prefix=code)
    app_name = cache.get(key)
    if app_name:
        return app_name
    # app_name = tenant_repository.get_data(
    #     code, "application", {"url": dashboard_id}, fields=["name"]
    # )
    with get_db(code) as db:
        sql = """
        SELECT name FROM `dap_bi_application` where url=%(dashboard_id)s
        union 
        select a.name as name from dap_bi_application as a inner join `dap_bi_function` as f  on f.application_id=a.id  where f.url=%(dashboard_id)s group by a.id
        """
        app_name = db.query_columns(sql, {"dashboard_id": dashboard_id})
    app_name = ", ".join(list(set(app_name))) if app_name else ""
    cache.set(key, app_name if app_name else "", expire)
    return app_name


def get_query_sql(data_set):
    if not data_set:
        return ""
    data_set_content = json.loads(data_set.get("content") or "{}")
    if data_set.get('edit_mode') == 'relation':
        relation_sql_content = data_set.get('relation_sql', "{}") or "{}"
        sql = json.loads(relation_sql_content).get('sql', "")
    elif data_set.get('type') == 'UNION':
        sql = data_set_content.get('replace_sql')
    else:
        sql = data_set_content.get('sql')
    return sql


def get_all_project_code(codes):
    """
    获取code list
    """
    from dmplib.db.mysql_wrapper import get_db
    with get_db() as db:
        sql = "select code, name from dap_p_tenant"
        params = {}
        if codes and isinstance(codes, str):
            codes = codes.split(',')
            # codes = ",".join(["%r"%code for code in codes])
            sql += " where code in (%(codes)s)"
            params.update({"codes": codes})
        return db.query(sql, params=params)


def create_table(table_name):
    from dmplib.db.mysql_wrapper import get_db
    if table_name:
        table_name = f"dap_bi_width_table_{table_name}"
    else:
        table_name = "dap_bi_width_table_relation_obj"
    with get_db() as db:
        db.exec_sql(f"drop table IF EXISTS {table_name};")
        create_sql = """
        create table IF NOT EXISTS {table_name}(
        id int(11) not null auto_increment primary key,
        `租户code` varchar(255) not null default '',
        `租户名称` varchar(255) not null default '',
        `宽表名称` varchar(255) not null default '',
        `数据集id` varchar(255) not null default '',
        `数据集名称` varchar(255) not null default '',
        `报告id` varchar(255) not null default '',
        `报告是否发布` varchar(255) not null default '',
        `报告发布方式` varchar(255) not null default '',
        `报告类型` varchar(255) not null default '',
        `报告名称` varchar(255) not null default '',
        `报告应用方式` varchar(255) not null default '',
        `报告url地址` text not null default '',
        `报告所属文件夹名称` text not null default '',
        `报告所属门户名称` text not null default '',
        `数据集sql` text not null default '',
        created_by varchar(255) not null default "",
        created_on timestamp NOT NULL default CURRENT_TIMESTAMP,
        modified_by varchar(255) not null default "",
        modified_on timestamp NOT NULL default CURRENT_TIMESTAMP,
        key `idx_dataset_id` (`数据集id`),
        key `idx_code` (`租户code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
        """.format(
            table_name=table_name
        )
        db.exec_sql(create_sql)
    return table_name


def data_2_table(data, table_name):
    from dmplib.db.mysql_wrapper import get_db
    with get_db() as db:
        exist = 'SHOW TABLES LIKE "%s"' % table_name
        if not db.query(exist):
            raise logger.error(f"{table_name} 表不存在")
        for d in data:
            if len(d) >= 15:
                d_dict = {
                    "租户code": d[0],
                    "租户名称": d[1],
                    "宽表名称": d[2],
                    "数据集id": d[3],
                    "数据集名称": d[4],
                    "报告id": d[5],
                    "报告是否发布": d[6],
                    "报告发布方式": d[7],
                    "报告类型": d[8],
                    "报告名称": d[9],
                    "报告应用方式": d[10],
                    "报告url地址": d[11],
                    "报告所属文件夹名称": d[12],
                    "报告所属门户名称": d[13],
                    "数据集sql": d[14]
                }
                db.insert(table_name, d_dict, commit=False)
        db.commit()


