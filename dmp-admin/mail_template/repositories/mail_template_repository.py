#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/12/8.
"""
from dmplib.db.mysql_wrapper import get_db


def get_mail_template_list(query_model):
    """
    获取邮件模板列表
    :param mail_template.models.EmailTemplateQueryModel query_model:
    :return: mail_template.models.EmailTemplateQueryModel
    """
    sql = 'SELECT `id`,`name`,`type`,`subject`,`content`,`send_mode` FROM `dap_bi_email_template` '
    params = {}
    wheres = []
    if query_model.keyword:
        wheres.append('( `name` LIKE %(keyword)s OR `title` LIKE %(keyword)s OR `code` LIKE %(keyword)s)')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    sql += (' WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY created_on DESC'
    with get_db() as db:
        query_model.total = db.query_scalar('select count(*) as total from ({}) a'.format(sql), params)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model
