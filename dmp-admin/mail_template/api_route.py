#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/12/8.
"""
from dmplib.hug import APIWrapper
from mail_template.models import EmailTemplateModel, EmailTemplateQueryModel
from mail_template.services import mail_template_service

api = APIWrapper(__name__)


@api.admin_route.get('/list')
def get_mail_template_list(**kwargs):
    """
    获取邮件模板列表
    :param kwargs:
    :return:
    """
    return True, None, mail_template_service.get_mail_template_list(EmailTemplateQueryModel(**kwargs)).get_result_dict()


@api.admin_route.get('/get')
def get_mail_template(**kwargs):
    """
    获取邮件模板列表
    :param kwargs:
    :return:
    """
    return True, None, mail_template_service.get_mail_template(kwargs.get('id'))


@api.admin_route.post('/add')
def add_mail_template(**kwargs):
    """
    添加邮件模板
    :param kwargs:
    :return:
    """
    return True, '添加成功', mail_template_service.add_mail_template(EmailTemplateModel(**kwargs))


@api.admin_route.post('/update')
def update_mail_template(**kwargs):
    """
    修改邮件模板
    :param kwargs:
    :return:
    """
    return True, '修改成功', mail_template_service.update_mail_template(EmailTemplateModel(**kwargs))
