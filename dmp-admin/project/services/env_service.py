#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/3/2.
"""
import smtplib
from dmplib import config

from pika.adapters.blocking_connection import BlockingConnection
from pika.connection import ConnectionParameters
from pika.credentials import PlainCredentials

from dmplib.db.mysql_wrapper import get_db
from components.oss import OSSFileProxy
from components.oss_sts import OssStsFileProxy

from components.rundeck import get_rundeck_client
from dmplib.redis import RedisCache


class EnvService:
    code = None

    def verify_env(self, code=None):
        """
        :验证私有环境是否正确
        :param code:
        :return tuple:
        """

        if code:
            self.code = code

        check_list = {
            'DB': get_db,
            'OSS': self.check_oss,
            'OSS_STS': OssStsFileProxy().get_security_token,
            'Email': self.check_email,
            'Redis': self.check_redis,
            'RabbitMQ': self.check_mq,
            'Rundeck': self.check_rundeck,
        }

        check_result = []

        for k, v in check_list.items():
            check_result.append(self.common_check_function(k, v))

        return check_result

    @staticmethod
    def common_check_function(module, test_function):
        """
         通用检测方法
        :return dict:
        """
        try:
            test_function()
            return {module: 'ok'}
        except BaseException as e:
            return {module: ' Fail ! Exception msg:' + str(e)}

    @staticmethod
    def check_email():
        """
         验证私有环境email 配置是否正确
        :return obj:
        """
        server = config.get('Email.smtp_server')
        port = int(config.get('Email.smtp_port'))
        use_ssl = int(config.get('Email.smtp_enable_ssl')) == 1
        server = smtplib.SMTP_SSL(server, port, timeout=5) if use_ssl else smtplib.SMTP(server, port, timeout=5)
        server.login(config.get('Email.account'), config.get('Email.password'))
        return server

    @staticmethod
    def check_oss():
        """
        验证私有环境 oss 配置是否正确
        :return tuple:
        """
        oss = OSSFileProxy()
        return oss.object_list(root='/')

    @staticmethod
    def clean_rundeck():

        """
        清理rundeck 历史数据
        """

        rundeck = get_rundeck_client()

        rundeck.get_project_history('dmp')

        return rundeck.system_info()

    @staticmethod
    def check_rundeck():
        """
        检查rundeck 历史数据
        """

        rundeck = get_rundeck_client()

        return rundeck.system_info()

    @staticmethod
    def check_redis():
        return RedisCache().add('_test_connect', 'test', 1)

    @staticmethod
    def check_mq():
        host = config.get('RabbitMQ.host')
        port = int(config.get('RabbitMQ.port', 5672))
        user = config.get('RabbitMQ.user', 'guest')
        password = config.get('RabbitMQ.password', 'guest')
        credentials = PlainCredentials(user, password)
        return BlockingConnection(
            parameters=ConnectionParameters(host, port, credentials=credentials, heartbeat_interval=0)
        )
