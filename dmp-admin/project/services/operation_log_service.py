from base import repository
from project.models import AdminOperationLogModel
from dmplib.hug import g

def save_log(model:AdminOperationLogModel):
    return repository.add_data('dap_bi_admin_operation_log', model.get_dict())


def extract_log_info(request=None):
    model = AdminOperationLogModel()
    model.domain = getattr(request, "host", "")
    model.path = getattr(request, "path", "")
    model.browser = getattr(request, "user_agent", "")
    model.url = getattr(request, "url", "")
    model.ip = getattr(request, "remote_addr", "")

    model.account = getattr(g, 'account', '')
    model.org_code = ''
    return model

