import datetime

from dmplib.db import mysql_wrapper
from dmplib.utils.strings import seq_id


def save_project_setting(project, key, value, account):
    with mysql_wrapper.get_db() as db:
        sql_args = {
            'code': project,
            'key': key,
            'value': value,
            'account': account,
            'time': datetime.datetime.now()
        }
        rows = db.exec_sql('''update `dap_bi_project_setting` 
            set `value`=%(value)s,`modified_by`=%(account)s,`modified_on`=%(time)s 
            where `code`=%(code)s and `key`=%(key)s''', sql_args)
        if rows == 0:
            sql_args['id'] = seq_id()
            db.exec_sql('''insert into `dap_bi_project_setting` (`id`, `code`, `key`, `value`, description,
             created_on, created_by, modified_on, modified_by) values (%(id)s,%(code)s,%(key)s,%(value)s,null,
             %(time)s,%(account)s,null,null)''', sql_args)


def get_project_setting(project, key, default_value):
    with mysql_wrapper.get_db() as db:
        val = db.query_scalar('select `value` from `dap_bi_project_setting` where `code`=%(code)s and `key`=%(key)s', {
            'code': project,
            'key': key
        })
        if not val:
            return default_value
        return val
