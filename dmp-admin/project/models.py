#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/8/1.
"""
import datetime
import re
from odps.core import ODPS
from odps.errors import ODPSError

from base.models import BaseModel, QueryBaseModel
from dmplib import config
from dmplib.utils.errors import UserError


class ProjectModel(BaseModel):  # pylint: disable=R0902
    def __init__(self, **kwargs):
        self.id = ''
        self.title = ''
        self.name = ''
        self.code = ''
        self.logo_uri = ''
        self.small_logo = ''
        self.portal_logo = ''
        self.small_portal_logo = ''
        self.site_icon = ''
        self.site_title = ''
        self.odps_proj = ''
        self.odps_access_id = ''
        self.odps_access_secret = ''
        # odps访问oss的角色权限
        self.odps_oss_role_id = ''
        self.odps_endpoint = ''
        self.tenant_db_name = ''
        self.rds_id = ''
        self.description = ''
        self.form_mode = 'add'
        self.consumer_id = ''
        self.is_domain_account = ''
        self.account_mode = ''
        self.erp_data_source_code = ''
        self.select_code = ''
        self.external_secret_key = ''

        # 初始密码admin_pwd
        self.admin_pwd = ''
        self.admin_email = ''
        # 是否需要移动报表
        self.need_mreporting = 0
        self.type = ''
        # 可制作的报告权限
        self.allow_dashboard_type = ''

        # 业财租户信息
        self.customer_id = None    # 为erp数见租户使用，为业财开租户也有个customer_id
        self.cust_name = None
        self.top_customer_id = None
        self.top_customer_name = None
        # self.contract_number = None
        self.buguid = None
        self.admin_name = None
        self.dmp_env_sign = None
        self.admin_phone = None
        self.admin_mail = None
        self.use_license = 0
        self.license = {}  # 租户级license信息
        self.erp_secret = ''  # yzs_config里面的config

        self.erpapi_host = ''  # ERP接口地址
        self.erpapi_access_id = ''  # 访问ERP接口的Access Key ID
        self.erpapi_access_secret = ''  # 访问ERP接口的Access Key Secret
        # 三云通过接口创建租库，如果配置了mysoft_cloudapp_default_env，以mysoft_cloudapp_default_env值为准，没有配置，取默认值saas_cloud
        self.dmp_env_sign = config.get('Product.mysoft_cloudapp_default_env', 'saas_cloud')
        if not self.dmp_env_sign:
            self.dmp_env_sign = 'saas_cloud'
        # 环境类型 hd: for HighData, shujian: for数见，cloud: for 三云，dmp: 招商、华宇、荣盛'
        self.storage_type = 'cloud'  # 拍照数据集存储类型 cloud: 云端存储, local: 本地存储
        self.is_rdc_auth = None
        self.value_added_func = []  # 增值服务功能
        # 数据集权限方式：alone, role
        self.dataset_permission_model = config.get('Permission.dataset_permission_model', 'alone') or "alone"
        # 租户对应数芯板块的code
        self.pulsar_project_code = ''
        self.pulsar_app_key = ''
        self.pulsar_app_secret = ''
        # 请求来源
        self.from_init = None
        # 租户所属区域
        self.area_id = 0
        # 需要初始化报告的产品编码
        self.app_code_list = []
        self.local_dataset_clean_max_time_type = 0
        # erp对应op,saas的接口管家地址配置
        self.erp_api_info = []
        # 管理员账号
        self.account = None
        # 开户是否发送邮件
        self.is_send_email = 1
        # 是否开启重点大屏租户（0：不是，1：是）
        self.is_key_screen = 1
        # 队列类型
        self.flow_queue_name = 'Flow'
        # 用于开租户标识来源，from_init=erpsaas，ERP_Language ： 1 或 2  （ 1: Net版， 2:Java信创版）
        self.erp_language = None
        # 开租户的时候传来的集成平台信息
        # mip_info: {
        #     "MIPCloudUrl": "xxxxxx",
        #     "Serviceurl": "xxxxxx",
        #     "AppKey": "xxxxxx",
        #     "AppSecret": "xxxxxx"
        # }
        self.mip_info = {}
        # 开户记录ID
        self.create_record_id = None
        # 租户报告权限模式，0：老模式（数见和第三方都可选）1：基础数据的权限体系（第三方可选）
        self.auth_mode = 0
        # 租户是否过期
        self.is_expire = 0
        # 简讯的动态字段内容是否截断，1：不截断，0：截断。新开租户全部不截断
        self.mobile_subscribe_filed_disable_break = 1
        # 开租户时传递的超级APP相关信息
        self.superportal = {}
        # 租户是否禁用，启用 0：禁用，1：启用
        self.project_enabled = 1
        # 租户禁用原因
        self.project_disable_reason = ''
        # 是否开启数据云1.5
        self.is_data_cloud_1_5_enabled = 0
        # 是否开启导入导出
        self.is_metadata_export_enabled = 1
        # 运营平台开户task_id
        self.task_id = ''
        # 初始化1.5子系统
        self.init_1_5_app = 0
        # 如果为1表示分发或增购时仅分发1.5的模板库资源，并且增购会强制覆盖1.5子系统，并且增购不分发简讯
        self.overwrite_1_5_app = 0
        # OMP开户日志结构
        self.omp_tenants_log = None
        # 环境code
        self.env_code = ''
        # 是否初始化数据库，从开户平台调用的不初始化
        self.init_db = True
        # 默认关键字本人账号
        self.default_account = 'admin'
        super().__init__(**kwargs)

    @property
    def data_db_name(self):
        """
        获取项目data库名称
        :return : str
        """
        project_data_db_name_suffix = config.get('DBInstaller.project_data_db_suffix')
        if not project_data_db_name_suffix:
            raise UserError(message='项目Data库后缀未配置')
        return self.tenant_db_name + '_' + project_data_db_name_suffix

    @property
    def odps(self):
        """
        根据配置获取ODPS实例
        :return: odps.core.ODPS
        """
        # 新版会自动连接service.odps.aliyun.com，并重试四次，私有化环境接口会卡住
        # return ODPS(self.odps_access_id, self.odps_access_secret, self.odps_proj)
        return None

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append((['title', 'name'], 'string', {'max': 64}))
        if self.form_mode == 'add':
            rules.append(('code', 'string', {'max': 254}))
            rules.append(('rds_id', 'string', {'max': 36}))

        if self.type == '平台':
            rules.append(('odps_proj', 'validate_odps'))
            rules.append(
                (
                    ['odps_proj', 'odps_access_id', 'odps_access_secret'],
                    'string',
                    {'max': 254},
                )
            )

        rules.append(('description', 'string', {'max': 1000, 'required': False}))
        if not self.select_code:
            rules.append(('type', 'string', {'max': 200}))
        rules.append(('external_secret_key', 'string', {'max': 200, 'required': False}))
        rules.append(('admin_pwd', 'validate_password', {}))
        return rules

    def validate_password(self, *_args):
        if not self.admin_pwd:
            raise UserError(message='请输入项目密码')
        if self.form_mode == "update":
            return
        if len(self.admin_pwd) < 8:
            raise UserError(message='为了您的账号安全，密码长度须不少于8位字符')
        pattern = r'(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[\W_]).{8,}'
        if re.match(pattern, self.admin_pwd) is None:
            raise UserError(message='为了您的账号安全，密码须为字母、数字和特殊字符组合')
        if self.code in self.admin_pwd:
            raise UserError(message='为了您的账号安全，密码中不可包含用户名信息')

        for idx in range(2, len(self.admin_pwd)):
            pre2 = self.admin_pwd[idx - 2]
            pre1 = self.admin_pwd[idx - 1]
            cur = self.admin_pwd[idx]
            if pre2 == pre1 == cur:
                raise UserError(message='为了您的账号安全，密码中不可存在连续数字或字母')
            if pre2.isalnum() and pre1.isalnum() and cur.isalnum() and (ord(pre2) + 2 == ord(pre1) + 1 == ord(cur)):
                raise UserError(message='为了您的账号安全，密码中不可存在连续数字或字母')

    def validate_odps(self, *args):
        try:
            res = self.odps.exist_project(args[2])
            if not res:
                raise UserError(message='ODPS项目不存在')
        except ODPSError as e:
            raise UserError(message='OPDS项目配置错误' + str(e)) from e


class ProjectQueryModel(QueryBaseModel):
    def __init__(self, **kwargs):
        self.account_mode = ''
        self.type = ''
        super().__init__(**kwargs)


class UserQueryModel(QueryBaseModel):
    __slots__ = ['group_id', 'nor_group_id', 'project_id']

    def __init__(self, **kwargs):
        self.project_id = None
        self.group_id = None
        # 不在该用户组
        self.nor_group_id = None
        super().__init__(**kwargs)


class SetProjectFunctionModel(BaseModel):
    def __init__(self, **kwargs):
        self.project_id = ''
        self.function_list = []
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('project_id', 'string', {'max': 36}))
        return rules


class ProjectInitResultModel(BaseModel):
    __slots__ = ['task_id', 'status', 'error_msg']

    def __init__(self, **kwargs):
        self.task_id = ''
        self.status = 0
        self.error_msg = ''
        super().__init__(**kwargs)


class TenantLicenseModel(BaseModel):
    __slots__ = [
        'product_start',
        'product_end',
        'report_num',
        'code',
    ]

    def __init__(self, **kwargs) -> None:
        self.product_start = None
        self.product_end = None
        self.report_num = None
        self.code = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['product_start, product_start'], 'is_timestamp', {'required': True}))
        rules.append((['report_num'], 'is_int', {'required': True}))

        self.product_start = self.trans_to_time_str(self.product_start)
        self.product_end = self.trans_to_time_str(self.product_end)

        return rules

    @staticmethod
    def trans_to_time_str(stamp):
        if isinstance(stamp, (int, float)):
            dt = datetime.datetime.fromtimestamp(stamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        else:
            return stamp

    @staticmethod
    def is_timestamp(config, attr_name, value):
        if not isinstance(value, (float, int)):
            msg = config.get('msg')
            raise UserError(message=msg if msg else attr_name + '必须为时间戳')
        return True

    @staticmethod
    def is_int(config, attr_name, value):
        msg = config.get('msg')
        if not isinstance(value, (float, int)):
            raise UserError(message=msg if msg else attr_name + '必须是整数')
        if int(value) < 0:
            raise UserError(message=msg if msg else attr_name + '必须是正整数')
        return True




class AdminOperationLogModel(BaseModel):
    __slots__ = [
        "account", "org_code", "module", "action", "content", "ip",
        "domain", "path", "url", "do_object", "browser", "extra"
    ]

    def __init__(self, **kwargs):
        self.account = None
        self.org_code = None
        self.module = None
        self.action = None
        self.content = None
        self.ip = None
        self.domain = None
        self.path = None
        self.url = None
        self.do_object = None
        self.browser = None
        self.extra = None
        super().__init__(**kwargs)



class InitializeTenantModel(BaseModel):

    def __init__(self, **kwargs):
        self.task_id = None
        self.tenant_code = None
        self.app_list = None
        self.omp_tenants_log = None
        self.init_1_5_app = 1
        self.overwrite_1_5_app = 0
        self.env_code = None
        self.customer_id = None
        self.from_init = None
        self.erp_lang = None
        self.record_id = None
        super().__init__(**kwargs)


