# -*- coding: utf-8 -*-
"""
    Errors
"""


class Error(Exception): pass  # 错误基类


class UserError(Error):
    def __init__(self, code=500, message=''):
        """
        业务异常基类, 返回message给用户
        :param int code:
        :param str message:
        """
        self.code = code
        self.message = message


class InvalidArgumentError(UserError): pass  # 无效参数异常


class ServerError(Error): pass
