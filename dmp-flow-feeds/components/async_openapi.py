#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    异步请求OpenAPI接口
"""
import aiohttp
from requests.exceptions import RequestException
from components.errors import UserError
from components.constants import OPENAPI_HOST
from components import config


def singleton(cls):
    instances = {}

    def _singleton(*args, **kargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kargs)
        return instances[cls]

    return _singleton


@singleton
class AsyncRequest:

    def __init__(self, project_code, host=None):
        """
        :param str host:接口地址
        """
        host = host if host else OPENAPI_HOST
        host = host[: len(host) - 1] if host.endswith('/') else host
        self.host = host
        self.timeout = int(config.get('Email.get_message_timeout', 60))
        self.headers = {'X-TENANT': project_code}

    async def async_request(self, open_api, request_type='post', params=dict()):
        """
        异步请求接口
        :param open_api: OpenAPI 的路由
        :param request_type: 请求类型
        :param params: 请求参数
        :return:
        """
        try:
            open_api = open_api if open_api.startswith('/') else '/' + open_api
            url = self.host + open_api
            rs = await self._request(url, params, request_type)
            return rs
        except RequestException as e:
            raise UserError(message='连接失败:' + str(e))

    async def _request(self, url, params, request_type='get'):
        async with aiohttp.ClientSession() as session:
            if request_type.lower() == 'get':
                resp = await session.get(url, params=params, headers=self.headers, timeout=self.timeout)
            else:
                resp = await session.post(url, json=params, headers=self.headers, timeout=self.timeout)
            try:
                rs = await resp.json()
                if not rs.get("result"):
                    raise UserError(message=str(rs.get("msg")))
                return rs.get("data")
            except Exception as e:
                msg = e.message if isinstance(e, UserError) else str(e)
                raise UserError(message="请求异常，err:" + msg)


async def async_openapi_request(project_code, open_api_route, params=None, request_type='post'):
    """
    异步请求消息
    :param project_code:
    :param open_api_route:
    :param params:
    :param request_type:
    :return:
    """
    request = AsyncRequest(project_code)
    return await request.async_request(open_api_route, params=params, request_type=request_type)

