#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/8/9.
"""
import socket
import struct
import threading
import time
import logging
import datetime

from pika.exceptions import ConnectionClosed
from pika.adapters.blocking_connection import BlockingConnection
from pika.connection import ConnectionParameters
from pika.credentials import PlainCredentials

from components import config as app_config
from components.HTPClient.tlqapi import *


class RabbitMQ:
    def __init__(self, host=None, port=None, user=None, password=None):
        self.host = host or app_config.get('RabbitMQ.host', self._loopback_address)
        self.port = port or int(app_config.get('RabbitMQ.port', 5672))
        self.user = user or app_config.get('RabbitMQ.user', 'guest')
        self.password = password or app_config.get('RabbitMQ.password', 'guest')
        self.vhost = app_config.get('RabbitMQ.vhost', 'vhost_tj_sj')
        self.priority_args = {"x-max-priority": 10} if bool(self.vhost != '/') else None

    @property
    def _loopback_address(self):
        return socket.inet_ntoa(struct.pack('!I', socket.INADDR_LOOPBACK))

    def get_connection(self):
        """
        获取BlockingConnection
        :return:pika.adapters.blocking_connection.BlockingConnection
        """
        credentials = PlainCredentials(self.user, self.password)
        return BlockingConnection(parameters=ConnectionParameters(
            self.host, self.port, credentials=credentials, heartbeat=60, virtual_host=self.vhost))

    def send_message(self, queue, body, durable=None):
        """
        发送消息
        :param str queue: 队列名称
        :param str body: 消息内容
        :param bool durable: 消息是否持久
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_declare(queue=queue, durable=durable or False, arguments=self.priority_args)
            channel.basic_publish(exchange='', routing_key=queue, body=body)

    def receive_message(self, queue, consumer_callback, durable=None):
        """
        接收消息
        :param str queue: 队列名称
        :param function consumer_callback:处理消息函数
        :param bool durable: 消息是否持久
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_declare(queue=queue, durable=durable or False, arguments=self.priority_args)
            channel.basic_qos(prefetch_count=1)
            channel.basic_consume(on_message_callback=consumer_callback, queue=queue, auto_ack=False)
            # 增加心跳检测
            heartbeat = Heartbeat(connection)
            heartbeat.start()
            channel.start_consuming()

    def receive_message_timeout(self, queue, consumer_callback, deadline, timeout_callback, durable=None):
        """
        接收消息
        :param str queue: 队列名称
        :param function consumer_callback:处理消息函数
        :param int deadline:截止期限
        :param function timeout_callback:超时处理函数
        :param bool durable: 消息是否持久
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_declare(queue=queue, durable=durable or False, arguments=self.priority_args)
            channel.basic_qos(prefetch_count=1)
            channel.basic_consume(on_message_callback=consumer_callback, queue=queue, auto_ack=False)
            connection.add_timeout(deadline=deadline, callback_method=timeout_callback)
            channel.start_consuming()

    def queue_delete(self, queue):
        """
        删除队列
        :param str queue:队列名称
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_delete(queue=queue)

    def get_consumer_count(self, queue):
        """
        获取队列总数
        :param queue:
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            this = channel.queue_declare(queue=queue, arguments=self.priority_args)
            return this.method.consumer_count

class Heartbeat(threading.Thread):
    def __init__(self, connection):
        super().__init__()
        self.lock = threading.Lock()
        self.connection = connection
        self.heartbeat_interval = int(getattr(connection, '_impl').params.heartbeat / 4)
        self.setDaemon(True)
        logging.info(f'heartbeat interval {self.heartbeat_interval}')

    def run(self):
        while True:
            time.sleep(self.heartbeat_interval)

            self.lock.acquire()
            try:
                self.connection.process_data_events()
            # 心跳检测连接关闭不记录错误日志，cmsk等客户环境流程是正常的出现很多错误日志
            except ConnectionClosed:
                pass
            except BaseException as ex:
                logging.exception(ex)
            finally:
                self.lock.release()


class TongHTP:

    def __init__(self, host=None, port=None, user=None, password=None, **kwargs):
        self.host = host or app_config.get('RabbitMQ.host', '127.0.0.1')
        self.port = port or int(app_config.get('RabbitMQ.port', 9888))
        self.user = user or app_config.get('RabbitMQ.user', 'guest')
        self.password = password or app_config.get('RabbitMQ.password', 'guest')
        self.vhost = app_config.get('RabbitMQ.vhost', 'vhost_tj_sj')
        self.err = TLQError()
        self._config = None
        self.client_id = '123456'
        self.address = f"tcp://{self.host}:{self.port}"
        self.group = 'dmp'

    @property
    def config(self):
        if self._config is None:
            self._config = TLQConfig()
        return self._config

    def producer_destory(self, config: TLQConfig, producer: TLQLightProducer, message: TLQMessage):
        if message.pData != None:
            TLQMessageDestroy(message, self.err)
        if producer.pData != None:
            TLQLightProducerDestroy(producer, self.err)
        # if config.pData != None:
        #     TLQConfigDestroy(config, self.err)

    def consumer_destory(self, config: TLQConfig, consumer: TLQLightPullConsumer, multiMessage: TLQMultiMessage):
        if multiMessage.messageCount > 0:
            TLQMultiMessageDestroy(multiMessage, self.err)
        if consumer.pData != None:
            TLQLightPullConsumerDestroy(consumer, self.err)
        # if config.pData != None:
        #     TLQConfigDestroy(config, self.err)

    def send_message(self, queue, body, durable=None, headers=None, auto_delete=False, arguments=None):
        producer = TLQLightProducer()
        config = TLQConfig()
        message = TLQMessage()

        if isinstance(body, str):
            body = body.encode()

        data = create_string_buffer(body)

        ret = TLQConfigInit(config, self.err)
        if ret != 0:
            print("TLQConfigInit error")
            return -1

        ret = TLQConfigSetClientID(config, f"dmp_feeds_producer_{self.client_id}", self.err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(config, producer, message)
            return -1

        ret = TLQConfigSetManager(config, self.address, self.err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(config, producer, message)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                return -1

        ret = TLQLightProducerInit(config, producer, self.err)
        if ret != 0:
            print("TLQLightProducerInit error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(config, producer, message)
            return -1

        ret = TLQLightProducerSetDomain(producer, self.vhost, self.err)
        if ret != 0:
            print("TLQLightProducerSetDomain error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(config, producer, message)
            return -1

        ret = TLQLightProducerSetTopic(producer, queue, self.err)
        if ret != 0:
            print("TLQLightProducerSetTopic error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(config, producer, message)
            return -1

        ret = TLQLightProducerStart(producer, self.err)
        if ret != 0:
            print("TLQLightProducerStart error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(config, producer, message)
            return -1

        ret = TLQMessageInit(message, self.err)
        if ret != 0:
            print("TLQMessageInit error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" %
                  (self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(config, producer, message)
            return -1

        ret = TLQMessageSetData(message, data, len(data)+1, self.err)
        if ret != 0:
            print("TLQMessageInit error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" %
                  (self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(config, producer, message)
            return -1

        ret = TLQLightPutMessage(producer, message, self.err)
        if ret != 0:
            print("TLQLightPutMessage error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(config, producer, message)
            return -1

        print("Put message success.")

        self.producer_destory(config, producer, message)

    def receive_message(self, queue, consumer_callback, durable=None):
        while True:
            try:
                self._receive_message(queue, consumer_callback, durable=durable)
            except Exception as e:
                print(f'consume fail, error: {e}, retry ...')
            time.sleep(3)

    def _receive_message(self, queue, consumer_callback, durable=None):

        if self.query_topic(queue) == -1:
            self.create_topic(queue)

        def consume():
            config = TLQConfig()
            consumer = TLQLightPullConsumer()
            multiMessage = TLQMultiMessage()
            data = c_char_p()
            length = c_int(0)

            ret = TLQConfigInit(config, self.err)
            if ret != 0:
                print("TLQConfigInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                return -1

            ret = TLQConfigSetClientID(config, self.client_id, self.err)
            if ret != 0:
                print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return -1

            ret = TLQConfigSetManager(config, self.address, self.err)
            if ret != 0:
                print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return -1

            if all([self.user, self.password]):
                ret = TLQConfigSetUsernamePassword(config, self.user, self.password, self.err)
                if ret != 0:
                    print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                        self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                    self.consumer_destory(config, consumer, multiMessage)
                    return -1

            ret = TLQLightPullConsumerInit(config, consumer, self.err)
            if ret != 0:
                print("TLQLightPullConsumerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return -1

            ret = TLQLightPullConsumerSetAckMode(consumer, 1, self.err)
            if ret != 0:
                print("TLQLightPullConsumerSetAckMode error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return -1

            ret = TLQLightPullConsumerSetDomain(consumer, self.vhost, self.err)
            if ret != 0:
                print("TLQLightPullConsumerSetDomain error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return -1

            ret = TLQLightPullConsumerSetTopic(consumer, queue, self.err)
            if ret != 0:
                print("TLQLightPullConsumerSetTopic error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return -1

            ret = TLQLightPullConsumerSetGroup(consumer, self.group, self.err)
            if ret != 0:
                print("TLQLightPullConsumerSetGroup error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return -1

            err_sleep = 1
            normal_sleep = 0.01

            ret = TLQLightPullConsumerStart(consumer, self.err)
            if ret != 0:
                print("TLQLightPullConsumerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                if 20 <= int(self.err.tlq_errno) <= 38:
                    return -1
                return err_sleep

            # ret = TLQLightPullMultiRollbackMessage(consumer, -1, 1, multiMessage, self.err)
            ret = TLQLightPullMultiMessage(consumer, 1, multiMessage, self.err)
            if (ret == -1):
                print("TLQLightPullMultiMessage error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return err_sleep

            if multiMessage.messageCount > 0:
                ret = TLQMessageGetData(multiMessage.messageArray[0], data, length, self.err)
                if ret == -1:
                    print("TLQMessageGetData error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                        self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                    self.consumer_destory(config, consumer, multiMessage)
                    return err_sleep
                print("Message data=", data.value, ",data length=", length.value, ",message count=",
                      multiMessage.messageCount)

                consumer_callback('', '', '', data.value)

                TLQLightPullConsumerAckMessage(consumer, self.err)

            print("Pull message count %d." % (multiMessage.messageCount))
            self.consumer_destory(config, consumer, multiMessage)
            return normal_sleep

        while True:
            interval = consume()
            if interval == -1:
                break
            time.sleep(interval)

    def receive_message_timeout(
            self, queue, consumer_callback, deadline, timeout_callback, durable=None, auto_delete=False
    ):
        if self.query_topic(queue) == -1:
            self.create_topic(queue)

        consumer = TLQLightPullConsumer()
        multiMessage = TLQMultiMessage()
        config = TLQConfig()
        data = c_char_p()
        length = c_int(0)

        ret = TLQConfigInit(config, self.err)
        if ret != 0:
            print("TLQConfigInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            return -1

        ret = TLQConfigSetClientID(config, "123456", self.err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(config, consumer, multiMessage)
            return -1

        ret = TLQConfigSetManager(config, self.address, self.err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(config, consumer, multiMessage)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return -1

        ret = TLQLightPullConsumerInit(config, consumer, self.err)
        if ret != 0:
            print("TLQLightPullConsumerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(config, consumer, multiMessage)
            return -1

        ret = TLQLightPullConsumerSetAckMode(consumer, 1, self.err)
        if ret != 0:
            print("TLQLightPullConsumerSetAckMode error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(config, consumer, multiMessage)
            return -1

        ret = TLQLightPullConsumerSetDomain(consumer, self.vhost, self.err)
        if ret != 0:
            print("TLQLightPullConsumerSetDomain error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(config, consumer, multiMessage)
            return -1

        if self.query_topic(queue) == -1:
            self.create_topic(queue)

        ret = TLQLightPullConsumerSetTopic(consumer, queue, self.err)
        if ret != 0:
            print("TLQLightPullConsumerSetTopic error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(config, consumer, multiMessage)
            return -1

        ret = TLQLightPullConsumerSetGroup(consumer, self.group, self.err)
        if ret != 0:
            print("TLQLightPullConsumerSetGroup error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(config, consumer, multiMessage)
            return -1

        err_sleep = 1
        normal_sleep = 0.01

        def consume():
            ret = TLQLightPullConsumerStart(consumer, self.err)
            if ret != 0:
                print("TLQLightPullConsumerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return err_sleep

            # ret = TLQLightPullMultiRollbackMessage(consumer, -1, 1, multiMessage, self.err)
            ret = TLQLightPullMultiMessage(consumer, 1, multiMessage, self.err)
            if (ret == -1):
                print("TLQLightPullMultiRollbackMessage error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(config, consumer, multiMessage)
                return err_sleep

            if multiMessage.messageCount > 0:
                ret = TLQMessageGetData(multiMessage.messageArray[0], data, length, self.err)
                if ret == -1:
                    print("TLQMessageGetData error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                        self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                    return err_sleep
                print("Message data=", data.value, ",data length=", length.value, ",message count=",
                      multiMessage.messageCount)

                consumer_callback('', '', data.value)

                TLQLightPullConsumerAckMessage(consumer, self.err)

                self.consumer_destory(config, consumer, multiMessage)

                return 0

            else:

                self.consumer_destory(config, consumer, multiMessage)
                return err_sleep

        end_time = datetime.datetime.now() + datetime.timedelta(seconds=deadline)

        while end_time >= datetime.datetime.now():
            interval = consume()
            if interval == 0:
                break
            time.sleep(interval)
        else:
            timeout_callback()

        if auto_delete:
            self.query_topic(queue)

    def queue_delete(self, queue):

        manager = TLQManager()
        config = TLQConfig()

        def destory(_config: TLQConfig, _manager: TLQManager):
            if _manager.pData != None:
                TLQManagerDestroy(_manager, self.err)
            # if _config.pData != None:
            #     TLQConfigDestroy(_config, self.err)

        ret = TLQConfigInit(config, self.err)
        if ret != 0:
            print("TLQConfigInit error")
            return -1

        ret = TLQConfigSetClientID(config, self.client_id, self.err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(config, manager)
            return -1

        ret = TLQConfigSetManager(config, self.address, self.err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(config, manager)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                return -1

        ret = TLQManagerInit(config, manager, self.err)
        if ret != 0:
            print("TLQManagerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerStart(manager, self.err)
        if ret != 0:
            print("TLQManagerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerDeleteTopic(manager, queue, self.vhost, self.err)
        if ret != 0:
            print("TLQManagerDeleteTopic error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(config, manager)
            return -1

        print("Delete topic success.")
        destory(config, manager)
        return 0

    def get_consumer_count(self, queue):
        return 0

    def create_domain(self, domain):

        config = TLQConfig()
        manager = TLQManager()
        err = TLQError()

        def destory(_config: TLQConfig, _manager: TLQManager):
            if _manager.pData != None:
                TLQManagerDestroy(_manager, err)
            # if _config.pData != None:
            #     TLQConfigDestroy(_config, err)

        ret = TLQConfigInit(config, err)
        if ret != 0:
            print("TLQConfigInit error")
            return -1

        ret = TLQConfigSetClientID(config, self.client_id, err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQConfigSetManager(config, self.address, err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                return -1

        ret = TLQManagerInit(config, manager, err)
        if ret != 0:
            print("TLQManagerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerStart(manager, err)
        if ret != 0:
            print("TLQManagerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerCreateDomain(manager, domain, err)
        if ret != 0:
            print("TLQManagerCreateDomain error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        print("Create domain success.")
        destory(config, manager)
        return 0

    def query_topic(self, topic):
        err = TLQError()
        def destory(config: TLQConfig, manager: TLQManager):
            if manager.pData != None:
                TLQManagerDestroy(manager, err)
            # if config.pData != None:
            #     TLQConfigDestroy(config, err)

        config = TLQConfig()
        manager = TLQManager()

        ret = TLQConfigInit(config, err)
        if ret != 0:
            print("TLQConfigInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            return -1

        ret = TLQConfigSetClientID(config, self.client_id, err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQConfigSetManager(config, self.address, err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                return -1

        ret = TLQManagerInit(config, manager, err)
        if ret != 0:
            print("TLQManagerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" %
                  (err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerStart(manager, err)
        if ret != 0:
            print("TLQManagerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerQueryTopic(manager, topic, self.vhost, err)
        if ret != 0:
            print("TLQManagerQueryTopic error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        print("Query topic success.")
        destory(config, manager)
        return 0

    def create_topic(self, _topic):
        config = TLQConfig()
        manager = TLQManager()
        err = TLQError()

        def destory(config: TLQConfig, manager: TLQManager):
            if manager.pData != None:
                TLQManagerDestroy(manager, err)
            # if config.pData != None:
            #     TLQConfigDestroy(config, err)

        ret = TLQConfigInit(config, err)
        if ret != 0:
            print("TLQConfigInit error")
            return -1

        ret = TLQConfigSetClientID(config, self.client_id, err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQConfigSetManager(config, self.address, err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                return -1

        ret = TLQManagerInit(config, manager, err)
        if ret != 0:
            print("TLQManagerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" %
                  (err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerStart(manager, err)
        if ret != 0:
            print("TLQManagerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerCreateTopic(manager, _topic, self.vhost, 0, err)
        if ret != 0:
            print("TLQManagerCreateTopic error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        print("Create topic success.")
        destory(config, manager)
        return 0

class MessageQueue:

    def __init__(self, *args, **kwargs):
        queue_type = app_config.get('RabbitMQ.type')
        if queue_type == 'tonghtp':
            self.queue_instance = TongHTP(*args, **kwargs)
        else:
            self.queue_instance = RabbitMQ(*args, **kwargs)

    def send_message(self, queue, body, **kwargs):
        return self.queue_instance.send_message(queue, body, **kwargs)

    def receive_message(self, queue, callback, **kwargs):
        return self.queue_instance.receive_message(queue, callback, **kwargs)

    def queue_delete(self, queue):
        return self.queue_instance.queue_delete(queue)

    def get_consumer_count(self, queue):
        return self.queue_instance.get_consumer_count(queue)

    def receive_message_timeout(
            self, queue, consumer_callback, deadline, timeout_callback, durable=None, auto_delete=False
    ):
        return self.queue_instance.receive_message_timeout(queue, consumer_callback, deadline, timeout_callback, durable=durable, auto_delete=auto_delete)
