from enum import Enum, unique


@unique
class ResourceCode(Enum):
    DP = 'dp'
    DP_DATACENTER = 'dp_datacenter'


@unique
class ResourceUsedEnum(Enum):
    CACHE = 'cacheUsed'
    MQ = 'mqUsed'
    STORAGE = 'storageUsed'
    CONSUL = 'consulUsed'
    SCHEDULER = 'xxljobUsed'
    DATASOURCE = 'dbUsed'


@unique
class ResourceInstanceEnum(Enum):
    CACHE = 'cacheInstances'
    MQ = 'mqInstances'
    STORAGE = 'storageInstances'
    CONSUL = 'consulInstances'
    SCHEDULER = 'xxljobInstances'
    DATASOURCE = 'dbInstances'
    DATASOURCE_USER = 'dbInstanceUsers'


@unique
class DbBusinessEnum(Enum):
    CONFIG = 'config'
    TEMPLATE = 'template'
    TENANT = 'tenant'
    LOG = 'log'


@unique
class DbNodeEnum(Enum):
    MASTER = 'master'
    SLAVE = 'slave'


@unique
class RedisMode(Enum):
    STANDALONE = 'standalone'
    CLUSTER = 'cluster'
    SENTINEL = 'sentinel'
