import os
import importlib.util
import inspect


def parse(config_data):
    """
    自动导入指定目录下所有后缀为 '_parser.py' 文件中的所有后缀为 'Parser' 的类
    """
    data = {}
    target_directory = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'middleware_parser')
    # 遍历目标目录，查找符合条件的文件
    for filename in sorted(os.listdir(target_directory)):
        if filename.endswith('_parser.py'):  # 只考虑 '_parser.py' 后缀的文件
            # 构建模块的完整路径
            module_path = os.path.join(target_directory, filename)
            module_name = filename[:-3]  # 去掉后缀，获取模块名

            # 动态导入模块
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # 查找模块中所有以 'Parser' 结尾的类
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if name.endswith('Parser') and name != 'MiddlewareBaseParser':
                    sub_config = obj(config_data).parse()
                    data.update(sub_config)
    return data
