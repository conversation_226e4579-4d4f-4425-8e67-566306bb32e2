# -*- coding: UTF-8 -*-
"""
Created on 2016年8月31日

@author: chenc04
"""
import os
os.environ.setdefault('PROMETHEUS_MULTIPROC_DIR', '/tmp')
os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), 'app.config')
from components.utils import jwt_patch  # noqa: E402
jwt_patch()
import logging

from components.loggers import init_logging
init_logging()
logger = logging.getLogger(__name__)
import sys
from flow.flow_launcher import FlowLauncher

RET_STATE = {
    "KILL": 143,
    "FAIL": -1,
    "OK": 0,
    "RUN": 1,
    "RETRY": 2
}

if __name__ == '__main__':
    args = sys.argv
    if len(args) <= 3:
        print("请输入项目code和流程id和流程实例id")
        logger.error("请输入项目code和流程id和流程实例id")
        sys.exit(RET_STATE['FAIL'])
    data = {
        "project_code": args[1],
        "flow_id": args[2],
        "flow_instance_id": args[3],
        "test_run": args[4],
    }
    logger.info("进入流程")
    FlowLauncher(**data).run_flow()
