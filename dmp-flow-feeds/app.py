# -*- coding: UTF-8 -*-
"""
Created on 2017年1月3日

@author: chenc04
"""
import os
os.environ.setdefault('PROMETHEUS_MULTIPROC_DIR', '/tmp')
os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), 'app.config')
from components.utils import jwt_patch  # noqa: E402
jwt_patch()
import json
import logging
from components.loggers import init_logging

init_logging()
from components import config
from components.message_queue import MessageQueue
from flow.flow_control import FlowControl
from log_handler import remove_mysql_log


def flow_consumer_callback(*args):
    """
    Flow 消费者回调
    :param args:
            consumer_callback(channel, method, properties, body)
                    channel: BlockingChannel
                    method: spec.Basic.Deliver
                    properties: spec.BasicProperties
                    body: str or unicode
    :return:
    """
    channel = args[0]
    method = args[1]
    body = args[3]
    try:
        FlowControl(**json.loads(body.decode('utf-8'))).run()
        remove_mysql_log()
    except json.JSONDecodeError as e:
        logging.error('消息体序列化错误：' + str(e))
    except BaseException as e:
        logging.error(e)
    finally:
        # 应答消息已经消费
        channel and channel.basic_ack(delivery_tag=method.delivery_tag)


def check_and_create_vhost():
    import requests
    from requests.auth import HTTPBasicAuth

    vhost_name = config.get('RabbitMQ.vhost')
    if vhost_name and vhost_name != '/' and config.get('RabbitMQ.type') != 'tonghtp':
        try:
            auth = HTTPBasicAuth(config.get('RabbitMQ.user'), config.get('RabbitMQ.password'))
            base_url = f"http://{config.get('RabbitMQ.host')}:{config.get('RabbitMQ.ui_port', 15672)}"
            headers = {"content-type": "application/json"}
            # 判断vhost是否存在
            res = requests.get(
                f'{base_url}/api/vhosts/{vhost_name}', headers=headers, auth=auth, timeout=10
            ).json()
            if res and res.get('name') == vhost_name:
                return
            # 不存在则创建
            requests.put(
                f'{base_url}/api/vhosts/{vhost_name}', headers=headers, auth=auth, timeout=10
            )
        except Exception as e:
            logging.error(f'check_and_create_vhost error: {e}')


"""流程执行入口"""
if __name__ == '__main__':
    # 启动
    sys_name = config.get('App.name')
    logging.info("----------------------{sys_name}启动流程控制中心---------------------------".format(sys_name=sys_name, ))

    check_and_create_vhost()

    queue_name = config.get('RabbitMQ.queue_name_flow_feeds', 'dmp_flow_feeds')
    mq = MessageQueue()
    mq.receive_message(queue_name, flow_consumer_callback, durable=False)
