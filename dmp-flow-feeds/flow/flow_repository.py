# -*- coding: UTF-8 -*-
"""
Created on 2016年8月31日

@author: chenc04
"""

import datetime
import random
import uuid
import logging

from node.node import Node, NodeInstance

logger = logging.getLogger(__name__)


class FlowRepository:
    def __init__(self, context):
        self.context = context

    def update_flow_run_status(self, status):
        with self.context.get_project_db() as db:
            return db.update('dap_bi_flow', {'run_status': status}, {'id': self.context.flow_id})

    @staticmethod
    def get_unique_str():
        """
        获取唯一字符串
        :return:
        """
        key = ['qwertyuio123456789klzxcvbnm', 'zaq147wsx258edc369rfv456tgb753', '123456789abcdefghijklmnopqrs']
        tmp_str = ''
        i = 0
        while i < 20:
            rd = key[random.randint(0, 99999) % 2]
            tmp_str += rd[random.randint(0, 21)]
            i += 1
        rel = uuid.uuid3(uuid.NAMESPACE_DNS, tmp_str).urn.split(':')
        return rel[2]

    def get_flow(self, flow_id):

        with self.context.get_project_db() as db:
            return db.query_one(
                'SELECT `id`,`name`,`description`,`type`,`schedule`,`status` FROM dap_bi_flow WHERE `id` = %(flow_id)s',
                {'flow_id': flow_id})

    def get_depend_flow(self, flow_id):
        """
        根据flow_id获取依赖流程
        :param flow_id: 流程id
        :return: flow
        """

        with self.context.get_project_db() as db:
            return db.query(
                'SELECT id,name,description,type,schedule,status,depend_flow_id FROM dap_bi_flow WHERE depend_flow_id = %(flow_id)s',
                {'flow_id': flow_id})

    def create_flow_instance(self, flow_data):
        logger.info("创建流程实例")
        with self.context.get_project_db() as db:
            flow_instance_id = self.get_unique_str()
            flow_instance_data = {'id': flow_instance_id,
                                  'flow_id': flow_data.get('id'),
                                  'name': flow_data.get('name'),
                                  'type': flow_data.get('type'),
                                  'startup_time': self.get_now_time(),
                                  'status': '已创建',
                                  'message': ''}
            db.insert("dap_bi_instance", flow_instance_data)
            return flow_instance_id

    def get_last_flow_instance(self, flow_id):
        """
        根据flow_id获取最新的流程实例
        :param flow_id: 流程id
        :return: instance
        """
        with self.context.get_project_db() as db:
            return db.query_one(
                'SELECT id,flow_id,name,type,startup_time,end_time,status,message FROM dap_bi_instance WHERE flow_id = %(flow_id)s order by startup_time desc',
                {'flow_id': flow_id})

    def get_flow_instance_by_id(self, instance_id):
        """
        根据instance_id获取流程实例
        :param instance_id: 流程实例id
        :return: instance
        """
        with self.context.get_project_db() as db:
            return db.query_one(
                'SELECT id,flow_id,name,type,startup_time,end_time,status,message FROM dap_bi_instance WHERE id = %(instance_id)s ',
                {'instance_id': instance_id})

    def updata_flow_instance(self, flow_instance_data, flow_instance_id):
        with self.context.get_project_db() as db:
            db.update('dap_bi_instance', flow_instance_data, dict(id=flow_instance_id))
        if flow_instance_data.get('status'):
            self.update_flow_run_status(flow_instance_data.get('status'))

    def create_node_instance(self, flow_instance_id, node_data):

        logger.info("创建节点实例")

        node_instance_data = {}
        node_instance_id = self.get_unique_str()

        node_instance_data["id"] = node_instance_id
        node_instance_data["instance_id"] = flow_instance_id
        node_instance_data["node_id"] = node_data.id
        node_instance_data["is_start"] = node_data.is_start
        node_instance_data["is_end"] = node_data.is_end
        node_instance_data["type"] = node_data.type
        node_instance_data["content"] = node_data.content
        node_instance_data["startup_time"] = self.get_now_time()
        node_instance_data["activity_data"] = ""
        node_instance_data["status"] = "已创建"
        node_instance_data["message"] = ""

        with self.context.get_project_db() as db:
            db.insert("dap_bi_activity", node_instance_data)

        return self.node_instance_dict_to_object(node_instance_data)

    def updata_node_instance(self, node_instance_data, node_instance_id):
        with self.context.get_project_db() as db:
            db.update('dap_bi_activity', node_instance_data, dict(id=node_instance_id))

    def updata_activity_by_instance(self, node_instance_data, condition_data):
        with self.context.get_project_db() as db:
            db.update('dap_bi_activity', node_instance_data, condition_data)

    def get_root_node(self, flow_id):
        """
        根据flow_id查找根节点

        :param flow_id: 流程ID
        :return: node
        :rtype: dist

        """
        sql = 'SELECT id,description,name,flow_id,is_start,is_end,type,content ' \
              'FROM dap_bi_node ' \
              'WHERE flow_id = %(flow_id)s and is_start=1'
        with self.context.get_project_db() as db:
            return db.query_one(sql, {'flow_id': flow_id})

    def get_nodes(self, flow_id):
        """
        根据flow_id获取所有的节点

        :param flow_id: 流程ID
        :return: node
        :rtype: list

        """
        sql = 'SELECT `id`,`description`,`name`,`flow_id`,`is_start`,`is_end`,`type`,`content` FROM dap_bi_node WHERE flow_id = %(flow_id)s'
        with self.context.get_project_db() as db:
            return db.query(sql, {'flow_id': flow_id})

    def get_after_node_ids(self, node_id):
        """
        根据节点ID获取所有前置节点的ID

        :param node_id: 节点ID
        :return: ids
        :rtype: list

        """

        with self.context.get_project_db() as db:
            return db.query('select ahead_node_id from dap_bi_line where behind_node_id = %(node_id)s ', {'node_id': node_id})

    def get_lines(self, flow_id):
        """
        根据flow_id获取所有的线

        :param flow_id: 流程ID
        :return: line
        :rtype: list

        """

        with self.context.get_project_db() as db:
            return db.query('SELECT id,flow_id,ahead_node_id,behind_node_id FROM dap_bi_line WHERE flow_id = %(flow_id)s ', {'flow_id': flow_id})

    def get_after_line(self, flow_id, previous_id):
        """
        根据flow_id和上一个节点id获取所有的线

        :param flow_id: 流程ID
        :param previous_id: 前一个节点id
        :return: line
        :rtype: list

        """

        with self.context.get_project_db() as db:
            return db.query(
                'SELECT id,flow_id,ahead_node_id,behind_node_id FROM dap_bi_line WHERE flow_id = %(flow_id)s and ahead_node_id=%(previous_id)s ', {
                    'flow_id': flow_id,
                    'previous_id': previous_id
                })

    @staticmethod
    def dict_to_node(dict_data):
        node = Node(id=dict_data.get('id'),
                    name=dict_data.get('name'),
                    flow_id=dict_data.get('flow_id'),
                    type=dict_data.get('type'),
                    is_start=dict_data.get('is_start'),
                    is_end=dict_data.get('is_end'),
                    content=dict_data.get('content'))
        return node

    def list_dict_to_dict_node(self, list_dict_node):
        flow_nodes = {}
        for d in list_dict_node:
            node = self.dict_to_node(d)
            flow_nodes[node.id] = node
        return flow_nodes

    @staticmethod
    def get_now_time():
        return datetime.datetime.now().strftime("%y-%m-%d %H:%M:%S")

    @staticmethod
    def node_instance_dict_to_object(node_instance_data):
        node_instance = NodeInstance(id=node_instance_data["id"], instance_id=node_instance_data["instance_id"],
                                     node_id=node_instance_data["node_id"], is_start=node_instance_data["is_start"],
                                     is_end=node_instance_data["is_end"], type=node_instance_data["type"],
                                     content=node_instance_data["content"],
                                     startup_time=node_instance_data["startup_time"],
                                     status=node_instance_data["status"], message=node_instance_data["message"],
                                     activity_data=node_instance_data["activity_data"])
        return node_instance

