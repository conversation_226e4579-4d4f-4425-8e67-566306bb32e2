# -*- coding: UTF-8 -*-
import os
import time
import traceback
from urllib.parse import urlparse
from PIL import Image
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service

from node.node_result import NodeResult


class EmailFeedsNode():
    def __init__(self, context):
        self.context = context
        self.html_image1_id = 'img1'

    def execution(self):
        # beta
        data = {
            'id': '39fd4098-f18c-b244-4f12-baf10343b912',
            'dashboard_id': '39fd4097-2021-fab2-ee95-b181ee3a9918',
            'release_url': 'http://dmp-dbeta.mypaas.com.cn/dataview/share/39fd4097-2021-fab2-ee95-b181ee3a9918?code=beta',
            'user_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************.LLepEdSJAwe695pUnUCtm9yG_jq9xnyfQ-kEJMFV4CA',
        }


        try:
            self.run(data)
        except:
            print('邮件订阅失败')
            raise
        return NodeResult(True, '邮件订阅成功')

    def run(self, email_subscribe_data):
        img_name = 'email.png'

        img_path = os.path.join(os.path.realpath(os.path.join(os.path.dirname(os.path.realpath(__file__)), '../')),
                                img_name)
        # 判断是否需要截图
        release_url = email_subscribe_data.get("release_url")
        self.screenshot(release_url + "&email=1", img_path, email_subscribe_data)
        print("报告截图：" + img_path)
        print("成功，更新数据。")

    def screenshot(self, url, imgpath, email_subscribe_data):
        path = '/Users/<USER>/Library/Application Support/pyppeteer/local-chromium/588429/chrome-mac/Chromium.app/Contents/MacOS/Chromium'

        # path = '/usr/lib/chromium/chromium'
        # '/home/<USER>/'

        service = Service('./chromedriver')
        service.start()
        capabilities = {'chrome.binary': path}
        driver = None
        try:
            # 设置浏览器长度高度(mac下设置906，其他设置1006)
            pix_w, pix_h = 1920, 1006
            try:
                driver = self.create_webdriver(service, capabilities, email_subscribe_data, url, pix_w, pix_h)
                time.sleep(20)
                self.screenshot_many(driver, imgpath, pix_w, pix_h)
            except BaseException:
                if driver:
                    driver.quit()
                print("chrome not reachable, 60 seconds to try again.")
                time.sleep(2)
                driver = self.create_webdriver(service, capabilities, email_subscribe_data, url, pix_w, pix_h)
                time.sleep(2)
                # 失败之后使用老的截屏
                driver.save_screenshot(imgpath)
        except BaseException as ex:
            print(traceback.format_exc())
            raise Exception('截图失败，错误内容：' + str(ex))
        finally:
            if driver:
                driver.quit()
            time.sleep(1)
            self.kill_crawler()

    def create_webdriver(self, service, capabilities, email_subscribe_data, url, pix_w=1920, pix_h=1080):
        options = webdriver.ChromeOptions()
        path = '/Applications/Chromium.app/Contents/MacOS/Chromium'
        # path = '/Users/<USER>/Library/Application Support/pyppeteer/local-chromium/588429/chrome-mac/Chromium.app/Contents/MacOS/Chromium'
        # 去除安全提示
        options.add_argument('disable-infobars')
        options.binary_location = path
        driver = webdriver.Remote(service.service_url, capabilities, options=options)
        driver.implicitly_wait(10)
        _url = urlparse(url)
        domain = '.' + _url.netloc
        driver.get(_url.scheme + '://' + _url.netloc)
        time.sleep(10)
        # 用户组发布报告加入cookie
        token = email_subscribe_data.get('user_token')
        expiry = int(time.time()) + int(7200) + 3600 * 24 * 100000
        c1 = {'domain': domain, 'name': 'token', 'value': token, 'expires': expiry, 'path': '/',
              'httpOnly': False, 'secure': False}
        driver.add_cookie(c1)
        driver.set_window_size(pix_w, pix_h+105)
        driver.set_page_load_timeout(60)
        driver.set_script_timeout(60)
        print("报告地址：" + url)
        driver.get(url)
        return driver

    def screenshot_many(self, driver, filename, pix_w, pix_h):
        """chrome 截屏（支持多次截屏合并成一张）
        base_url- 要截屏的url
        pix_w- 窗口宽
        pix_h- 窗口高
        filename-生成截图的路径文件名
        """
        img_list = []
        try:
            img_list = self.generate_img(driver, img_list, pix_w, pix_h)
            self.image_merge(img_list, filename)
        except Exception as ex:
            print(traceback.format_exc())
            raise Exception('截图失败，错误内容：' + str(ex))
        finally:
            self.remove_temp_img(img_list)

    def generate_img(self, driver, img_list, pix_w, pix_h):
        i = 0
        last_t = 0
        while True:
            # 获取滚动高度
            scroll_h = str(i * pix_h)
            print('scroll_h: ' + scroll_h)
            print(f'pix_h: {pix_h}')
            print(f'pix_w: {pix_w}')

            move_js = """document.getElementsByClassName("multiscreen-detail-page")[0].scrollTop=%s""" % (scroll_h)
            driver.execute_script(move_js)

            # 获取滚动高度和实际高度
            get_scroll_h = """return document.getElementsByClassName("multiscreen-detail-page")[0].scrollHeight.toString() +
            ',' + document.getElementsByClassName("multiscreen-detail-page")[0].scrollTop.toString()"""
            js1_result = driver.execute_script(get_scroll_h)
            real_scroll_h, real_top = js1_result.split(',')[0], js1_result.split(',')[1]
            print(f'real_scroll_h, real_top: {real_scroll_h}, {real_top}')
            # real_scroll_h, real_top 是当前滚动条长度和当前滚动条的top，作为是否继续执行的依据，
            # 由于存在滚动条向下拉动后会加载新内容的情况，所以需要以下的判断
            # 如果这次设置的top成功，则继续滚屏
            if real_top == str(i * pix_h):
                i += 1
                driver.save_screenshot('./' + str(i) + '.png')
                img_list.append('./' + str(i) + '.png')
                last_t = real_top
                print('if1')
                print(f'real_top: {real_top}')
                print(f'last_t: {last_t}')
            else:
                print('else1')
                print(f'real_top: {real_top}')
                print(f'last_t: {last_t}')
                # 如果本次设置失败，看这次的top和上一次记录的top值是否相等，
                # 相等则说明没有新加载内容，且已到页面底，跳出循环
                if real_top != last_t:
                    if int(real_scroll_h) < pix_h:
                        break
                    last_t = real_top
                    # break
                else:
                    img_filename = './' + str(i + 1) + '.png'
                    driver.save_screenshot(img_filename)
                    # 算出截取位置
                    img_list.append(self.calculate_position(i, pix_h, pix_w, real_scroll_h, img_filename))
                    break

        print(img_list)
        return img_list

    @staticmethod
    def calculate_position(i, pix_h, pix_w, real_scroll_h, img_filename):
        try:
            y = (i + 1) * pix_h - int(real_scroll_h)
            region = (0, y, pix_w, pix_h)
            print(y)
            print(region)
            img = Image.open(img_filename)
            crop_img = img.crop(region)
            crop_img.save(img_filename)
        # 有可能会超出范围，如果存在一个图表内部有下拉的情况下，会出现这种错。暂时处理方法就不截图了(也不做处理)
        except Exception as e:
            print(str(e))
        return img_filename


    @staticmethod
    def remove_temp_img(img_list):
        # 程序报错后，还是会删除临时图片
        try:
            for single_img_path in img_list:
                os.remove(single_img_path)
        except:
            pass

    @staticmethod
    def image_resize(img, size=(1920, 1080)):
        """调整图片大小"""
        try:
            if img.mode not in ('L', 'RGB'):
                img = img.convert('RGB')
            img = img.resize(size)
        except:
            pass
        return img


    def image_merge(self, images, image_path, restriction_max_width=None,
                    restriction_max_height=None):
        """垂直合并多张图片
        images - 要合并的图片路径列表
        image_path - 文件最终位置
        restriction_max_width - 限制合并后的图片最大宽度，如果超过将等比缩小
        restriction_max_height - 限制合并后的图片最大高度，如果超过将等比缩小
        """
        max_width = 0
        total_height = 0
        # 计算合成后图片的宽度（以最宽的为准）和高度
        for img_path in images:
            if os.path.exists(img_path):
                img = Image.open(img_path)
                width, height = img.size
                if width > max_width:
                    max_width = width
                total_height += height

        # 产生一张空白图
        new_img = Image.new('RGB', (max_width, total_height), 255)
        # 合并
        x = y = 0
        for img_path in images:
            if os.path.exists(img_path):
                img = Image.open(img_path)
                width, height = img.size
                new_img.paste(img, (x, y))
                y += height

        if restriction_max_width and max_width >= restriction_max_width:
            # 如果宽带超过限制
            # 等比例缩小
            ratio = restriction_max_height / float(max_width)
            max_width = restriction_max_width
            total_height = int(total_height * ratio)
            new_img = self.image_resize(new_img, size=(max_width, total_height))

        if restriction_max_height and total_height >= restriction_max_height:
            # 如果高度超过限制
            # 等比例缩小
            ratio = restriction_max_height / float(total_height)
            max_width = int(max_width * ratio)
            total_height = restriction_max_height
            new_img = self.image_resize(new_img, size=(max_width, total_height))

        new_img.save(image_path)
        for single_img_path in images:
            os.remove(single_img_path)
        return image_path


    def kill_crawler(self):
        """
        kill chromium
        :return:
        """
        cmd = 'ps -ef | grep chromium'
        lines = os.popen(cmd)
        for line in lines:
            if line.find('grep mx_skyfile_serv') != -1:
                continue
            vars = line.split()
            pid = vars[1]  # get pid
            proc = ''.join(vars[7:])  # get proc description 1
            out = os.system('kill -9 ' + pid)
            if out == 0:
                print('success! kill ' + pid + ' ' + proc)
            else:
                print('failed! kill ' + pid + ' ' + proc)


class Context:
    project_code = 'beta'

if __name__ == '__main__':
    context = Context()

    node = EmailFeedsNode(context)
    print(datetime.now())
    node.execution()
    print(datetime.now())

