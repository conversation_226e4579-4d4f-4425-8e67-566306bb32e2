#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试简化后的get_scene_dataset_info返回格式
"""

def test_simplified_structure():
    """
    测试简化后的数据结构是否符合预期格式
    """
    # 模拟简化后的返回数据结构
    expected_structure = {
        "data": {
            "charts": [
                {
                    "chart_id": "23505c99-2fc4-11f0-b4c0-e9c7d5e2dac5",
                    "chart_name": "部门到访量分布",
                    "chart_type": "图表",
                    "datasets": [
                        {
                            "dataset_description": "",
                            "dataset_id": "3a19da81-6e73-fb32-0d80-6eef8f42ff63",
                            "dataset_name": "浩翔测试-企业到访数据",
                            # 注意：dataset_content 字段已被移除
                            "fields": [
                                {
                                    "col_name": "DFRS_12078933974",
                                    "data_type": "数值",
                                    "field_description": "到访人数",
                                    "field_group": "度量",
                                    "field_id": "3a19da81-6f86-27d4-8811-918ae648b3f2",
                                    "field_name": "到访人数",
                                    "origin_col_name": "到访人数"
                                }
                            ]
                        }
                    ],
                    "field_count": 2,
                    "fields_by_group": {
                        "dims": [
                            {
                                "alias_name": "到访部门",
                                "data_type": "字符串",
                                "field_group": "维度",
                                "field_id": "3a19da81-6f85-1548-c949-665aedd23bd4"
                            }
                        ],
                        "nums": [
                            {
                                "alias_name": "到访人数",
                                "data_type": "数值",
                                "field_group": "度量",
                                "field_id": "3a19da81-6f86-27d4-8811-918ae648b3f2"
                            }
                        ]
                    },
                    "sql_info": {
                        # 简化后只保留这三个字段
                        "dataset_id": "3a19da81-6e73-fb32-0d80-6eef8f42ff63",
                        "dataset_name": "浩翔测试-企业到访数据",
                        "generated_sql": "SELECT `DFBM_10817824726` AS `DFBM_10817824726`,SUM(`DFRS_12078933974`) AS `sum_DFRS_12078933974` FROM dataset_1e509312e133dd33  GROUP BY `DFBM_10817824726` LIMIT 0, 1000000000"
                        # 移除的字段：dataset_sql, dataset_type, has_sql, field_count, dims_count, nums_count, error
                    }
                }
            ],
            "dashboard_id": "3a19d9db-7e37-589c-d1ae-d65f16d650cc",
            "dashboard_name": "浩翔测试",
            "global_params": {
                "dataset_relations": [
                    {
                        "alias_name": "到访人员，例如：张三",
                        "datasets": [
                            {
                                "dataset_description": "",
                                "dataset_id": "3a19da81-6e73-fb32-0d80-6eef8f42ff63",
                                "dataset_name": "浩翔测试-企业到访数据",
                                "related_fields": [
                                    {
                                        "col_name": "DFXM_12159608790",
                                        "field_id": "3a19da81-6f85-00fa-8c1e-843badc70c5b",
                                        "field_name": "到访姓名",
                                        "field_type": "字符串"
                                    }
                                ]
                            }
                        ],
                        "global_param_id": "05cf0ac4-3561-11f0-a77f-ab1ac2c861a5",
                        "global_param_name": "visitor"
                    }
                ],
                "params": [
                    {
                        # 简化后只保留这些字段
                        "alias_name": "部门",
                        "description": "",
                        "id": "8ee6fa8b-3560-11f0-b1fd-45473a9be927",
                        "name": "dep",
                        "default_value": ""
                        # 移除的字段：type, required, options
                    }
                ]
                # 移除的字段：has_global_params, params_count, error
            }
        },
        "message": "获取成功",
        "success": True
    }
    
    print("=== 简化后的数据结构说明 ===")
    print("\n1. sql_info 部分简化:")
    print("   保留: dataset_id, dataset_name, generated_sql")
    print("   移除: dataset_sql, dataset_type, has_sql, field_count, dims_count, nums_count, error")
    
    print("\n2. global_params.params 部分简化:")
    print("   保留: id, name, alias_name, description, default_value")
    print("   移除: type, required, options")
    
    print("\n3. datasets 部分简化:")
    print("   移除: dataset_content")
    
    print("\n4. global_params 部分简化:")
    print("   移除: has_global_params, params_count, error")
    
    print("\n=== 修改完成 ===")
    print("get_dashboard_dataset_info 函数已按要求简化返回数据结构")
    
    return True

if __name__ == "__main__":
    test_simplified_structure()
